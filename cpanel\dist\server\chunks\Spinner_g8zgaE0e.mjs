import { ref, watch, defineComponent, useSSRContext, computed, mergeProps, withCtx, createVNode, createTextVNode, toDisplayString, createBlock, createCommentVNode, openBlock, Fragment, renderList } from 'vue';
import { C as Card } from './Card_aE2_b9LT.mjs';
import { S as SecondaryButton } from './SecondaryButton_B0hmlm1n.mjs';
import { S as Select } from './Select_DIHmHCCM.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
/* empty css                           */
import { _ as _export_sfc } from './ClientRouter_avhRMbqw.mjs';
import { Motion } from 'motion-v';

function useDebounce(source, delay = 300) {
  const debounced = ref(source.value);
  let timeout = null;
  watch(source, (newValue) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      debounced.value = newValue;
    }, delay);
  }, { immediate: false });
  return debounced;
}

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CatalogPagination",
  props: {
    currentPage: {},
    totalItems: {},
    itemsPerPage: {},
    enableVirtualScroll: { type: Boolean, default: false },
    hasMoreItems: { type: Boolean, default: false },
    loadingMore: { type: Boolean, default: false }
  },
  emits: ["pageChange", "itemsPerPageChange", "loadMore"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const FirstPageIcon = () => "\u23EE";
    const PrevPageIcon = () => "\u25C0";
    const NextPageIcon = () => "\u25B6";
    const LastPageIcon = () => "\u23ED";
    const props = __props;
    const emit = __emit;
    const itemsPerPageOptions = [
      { label: "10", value: 10 },
      { label: "20", value: 20 },
      { label: "50", value: 50 },
      { label: "100", value: 100 }
    ];
    const totalPages = computed(() => Math.ceil(props.totalItems / props.itemsPerPage));
    const startItem = computed(() => {
      if (props.totalItems === 0) return 0;
      return (props.currentPage - 1) * props.itemsPerPage + 1;
    });
    const endItem = computed(() => {
      const end = props.currentPage * props.itemsPerPage;
      return Math.min(end, props.totalItems);
    });
    const visiblePages = computed(() => {
      const pages = [];
      const total = totalPages.value;
      const current = props.currentPage;
      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        if (current > 3) {
          pages.push(-1);
        }
        const start = Math.max(2, current - 1);
        const end = Math.min(total - 1, current + 1);
        for (let i = start; i <= end; i++) {
          if (!pages.includes(i)) {
            pages.push(i);
          }
        }
        if (current < total - 2) {
          pages.push(-2);
        }
        if (!pages.includes(total)) {
          pages.push(total);
        }
      }
      return pages.filter((page) => page > 0);
    });
    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
        emit("pageChange", page);
      }
    };
    const changeItemsPerPage = (count) => {
      emit("itemsPerPageChange", count);
    };
    const loadMore = () => {
      emit("loadMore");
    };
    const __returned__ = { FirstPageIcon, PrevPageIcon, NextPageIcon, LastPageIcon, props, emit, itemsPerPageOptions, totalPages, startItem, endItem, visiblePages, goToPage, changeItemsPerPage, loadMore, Card, SecondaryButton, Select };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-pagination" }, _attrs))} data-v-ff69da72>`);
  _push(ssrRenderComponent($setup["Card"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex flex-col sm:flex-row items-center justify-between gap-4" data-v-ff69da72${_scopeId}><div class="text-sm text-surface-600 dark:text-surface-300" data-v-ff69da72${_scopeId}> \u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E ${ssrInterpolate($setup.startItem)}-${ssrInterpolate($setup.endItem)} \u0438\u0437 ${ssrInterpolate($props.totalItems)} \u0437\u0430\u043F\u0438\u0441\u0435\u0439 </div><div class="flex items-center gap-2" data-v-ff69da72${_scopeId}>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          disabled: $props.currentPage === 1,
          onClick: ($event) => $setup.goToPage(1),
          "icon-only": "",
          title: "\u041F\u0435\u0440\u0432\u0430\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
        }, {
          icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["FirstPageIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["FirstPageIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          disabled: $props.currentPage === 1,
          onClick: ($event) => $setup.goToPage($props.currentPage - 1),
          "icon-only": "",
          title: "\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0430\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
        }, {
          icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["PrevPageIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["PrevPageIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`<div class="hidden sm:flex items-center gap-1" data-v-ff69da72${_scopeId}><!--[-->`);
        ssrRenderList($setup.visiblePages, (page) => {
          _push2(ssrRenderComponent($setup["SecondaryButton"], {
            key: page,
            class: {
              "bg-primary text-primary-contrast": page === $props.currentPage,
              "text-primary": page !== $props.currentPage
            },
            onClick: ($event) => $setup.goToPage(page),
            size: "small"
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`${ssrInterpolate(page)}`);
              } else {
                return [
                  createTextVNode(toDisplayString(page), 1)
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
        });
        _push2(`<!--]--></div><div class="sm:hidden flex items-center gap-2" data-v-ff69da72${_scopeId}><span class="text-sm text-surface-600 dark:text-surface-300" data-v-ff69da72${_scopeId}> \u0421\u0442\u0440\u0430\u043D\u0438\u0446\u0430 ${ssrInterpolate($props.currentPage)} \u0438\u0437 ${ssrInterpolate($setup.totalPages)}</span></div>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          disabled: $props.currentPage === $setup.totalPages,
          onClick: ($event) => $setup.goToPage($props.currentPage + 1),
          "icon-only": "",
          title: "\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0430\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
        }, {
          icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["NextPageIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["NextPageIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          disabled: $props.currentPage === $setup.totalPages,
          onClick: ($event) => $setup.goToPage($setup.totalPages),
          "icon-only": "",
          title: "\u041F\u043E\u0441\u043B\u0435\u0434\u043D\u044F\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
        }, {
          icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["LastPageIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["LastPageIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div><div class="flex items-center gap-2 text-sm" data-v-ff69da72${_scopeId}><span class="text-surface-600 dark:text-surface-300" data-v-ff69da72${_scopeId}>\u041D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0435:</span>`);
        _push2(ssrRenderComponent($setup["Select"], {
          "model-value": $props.itemsPerPage,
          options: $setup.itemsPerPageOptions,
          "onUpdate:modelValue": $setup.changeItemsPerPage,
          class: "w-20"
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
        if ($props.enableVirtualScroll) {
          _push2(`<div class="mt-4" data-v-ff69da72${_scopeId}><div class="flex items-center justify-center" data-v-ff69da72${_scopeId}>`);
          if ($props.hasMoreItems) {
            _push2(ssrRenderComponent($setup["SecondaryButton"], {
              loading: $props.loadingMore,
              onClick: $setup.loadMore,
              outlined: ""
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` \u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0435\u0449\u0435 `);
                } else {
                  return [
                    createTextVNode(" \u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0435\u0449\u0435 ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div></div>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          createVNode("div", { class: "flex flex-col sm:flex-row items-center justify-between gap-4" }, [
            createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-300" }, " \u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E " + toDisplayString($setup.startItem) + "-" + toDisplayString($setup.endItem) + " \u0438\u0437 " + toDisplayString($props.totalItems) + " \u0437\u0430\u043F\u0438\u0441\u0435\u0439 ", 1),
            createVNode("div", { class: "flex items-center gap-2" }, [
              createVNode($setup["SecondaryButton"], {
                disabled: $props.currentPage === 1,
                onClick: ($event) => $setup.goToPage(1),
                "icon-only": "",
                title: "\u041F\u0435\u0440\u0432\u0430\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
              }, {
                icon: withCtx(() => [
                  createVNode($setup["FirstPageIcon"])
                ]),
                _: 1
              }, 8, ["disabled", "onClick"]),
              createVNode($setup["SecondaryButton"], {
                disabled: $props.currentPage === 1,
                onClick: ($event) => $setup.goToPage($props.currentPage - 1),
                "icon-only": "",
                title: "\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0430\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
              }, {
                icon: withCtx(() => [
                  createVNode($setup["PrevPageIcon"])
                ]),
                _: 1
              }, 8, ["disabled", "onClick"]),
              createVNode("div", { class: "hidden sm:flex items-center gap-1" }, [
                (openBlock(true), createBlock(Fragment, null, renderList($setup.visiblePages, (page) => {
                  return openBlock(), createBlock($setup["SecondaryButton"], {
                    key: page,
                    class: {
                      "bg-primary text-primary-contrast": page === $props.currentPage,
                      "text-primary": page !== $props.currentPage
                    },
                    onClick: ($event) => $setup.goToPage(page),
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(page), 1)
                    ]),
                    _: 2
                  }, 1032, ["class", "onClick"]);
                }), 128))
              ]),
              createVNode("div", { class: "sm:hidden flex items-center gap-2" }, [
                createVNode("span", { class: "text-sm text-surface-600 dark:text-surface-300" }, " \u0421\u0442\u0440\u0430\u043D\u0438\u0446\u0430 " + toDisplayString($props.currentPage) + " \u0438\u0437 " + toDisplayString($setup.totalPages), 1)
              ]),
              createVNode($setup["SecondaryButton"], {
                disabled: $props.currentPage === $setup.totalPages,
                onClick: ($event) => $setup.goToPage($props.currentPage + 1),
                "icon-only": "",
                title: "\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0430\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
              }, {
                icon: withCtx(() => [
                  createVNode($setup["NextPageIcon"])
                ]),
                _: 1
              }, 8, ["disabled", "onClick"]),
              createVNode($setup["SecondaryButton"], {
                disabled: $props.currentPage === $setup.totalPages,
                onClick: ($event) => $setup.goToPage($setup.totalPages),
                "icon-only": "",
                title: "\u041F\u043E\u0441\u043B\u0435\u0434\u043D\u044F\u044F \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
              }, {
                icon: withCtx(() => [
                  createVNode($setup["LastPageIcon"])
                ]),
                _: 1
              }, 8, ["disabled", "onClick"])
            ]),
            createVNode("div", { class: "flex items-center gap-2 text-sm" }, [
              createVNode("span", { class: "text-surface-600 dark:text-surface-300" }, "\u041D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0435:"),
              createVNode($setup["Select"], {
                "model-value": $props.itemsPerPage,
                options: $setup.itemsPerPageOptions,
                "onUpdate:modelValue": $setup.changeItemsPerPage,
                class: "w-20"
              }, null, 8, ["model-value"])
            ])
          ]),
          $props.enableVirtualScroll ? (openBlock(), createBlock("div", {
            key: 0,
            class: "mt-4"
          }, [
            createVNode("div", { class: "flex items-center justify-center" }, [
              $props.hasMoreItems ? (openBlock(), createBlock($setup["SecondaryButton"], {
                key: 0,
                loading: $props.loadingMore,
                onClick: $setup.loadMore,
                outlined: ""
              }, {
                default: withCtx(() => [
                  createTextVNode(" \u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0435\u0449\u0435 ")
                ]),
                _: 1
              }, 8, ["loading"])) : createCommentVNode("", true)
            ])
          ])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/catalog/CatalogPagination.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const CatalogPagination = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1], ["__scopeId", "data-v-ff69da72"]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Spinner",
  props: {
    size: { default: "md" },
    variant: { default: "default" },
    label: {},
    centered: { type: Boolean, default: false }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const containerClasses = computed(() => {
      const base = "flex items-center gap-2";
      const centered = props.centered ? "justify-center" : "";
      const direction = props.label ? "flex-col" : "";
      return `${base} ${centered} ${direction}`.trim();
    });
    const spinnerClasses = computed(() => {
      const sizes = {
        xs: "w-3 h-3",
        sm: "w-4 h-4",
        md: "w-6 h-6",
        lg: "w-8 h-8",
        xl: "w-12 h-12"
      };
      const variants = {
        default: "text-[--color-foreground]",
        primary: "text-[--color-primary]",
        secondary: "text-[--color-secondary]",
        success: "text-[--color-success]",
        warning: "text-[--color-warning]",
        danger: "text-[--color-danger]"
      };
      return `${sizes[props.size]} ${variants[props.variant]}`;
    });
    const labelClasses = computed(() => {
      const sizes = {
        xs: "text-xs",
        sm: "text-sm",
        md: "text-sm",
        lg: "text-base",
        xl: "text-lg"
      };
      return `${sizes[props.size]} text-[--color-muted] font-medium`;
    });
    const __returned__ = { props, containerClasses, spinnerClasses, labelClasses, get Motion() {
      return Motion;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: $setup.containerClasses }, _attrs))}>`);
  _push(ssrRenderComponent($setup["Motion"], {
    animate: { rotate: 360 },
    transition: { duration: 1, repeat: Infinity, ease: "linear" },
    class: $setup.spinnerClasses
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<svg viewBox="0 0 24 24" fill="none"${_scopeId}><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="60 40" class="opacity-25"${_scopeId}></circle><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="15 85" class="opacity-75"${_scopeId}></circle></svg>`);
      } else {
        return [
          (openBlock(), createBlock("svg", {
            viewBox: "0 0 24 24",
            fill: "none"
          }, [
            createVNode("circle", {
              cx: "12",
              cy: "12",
              r: "10",
              stroke: "currentColor",
              "stroke-width": "2",
              "stroke-linecap": "round",
              "stroke-dasharray": "60 40",
              class: "opacity-25"
            }),
            createVNode("circle", {
              cx: "12",
              cy: "12",
              r: "10",
              stroke: "currentColor",
              "stroke-width": "2",
              "stroke-linecap": "round",
              "stroke-dasharray": "15 85",
              class: "opacity-75"
            })
          ]))
        ];
      }
    }),
    _: 1
  }, _parent));
  if ($props.label) {
    _push(`<div class="${ssrRenderClass($setup.labelClasses)}">${ssrInterpolate($props.label)}</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/ui/Spinner.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Spinner = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { CatalogPagination as C, Spinner as S, useDebounce as u };

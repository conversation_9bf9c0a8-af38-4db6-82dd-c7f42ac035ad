import C from"./Button.DrThv2lH.js";import b from"./Card.C4y0_bWr.js";import{I as _}from"./InputText.DOJMNEP-.js";import{u as k}from"./useTheme.DwNY0gjL.js";import{_ as B}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as E,c as s,e as r,w as f,h as F,o as l,a as t,b as p,f as D}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as n,t as m}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./index.6ykohhwZ.js";import"./index.BaVCXmir.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./index.COq_zjeV.js";const V=E({__name:"HelloVue",setup(x,{expose:e}){e();const c=n(""),u=n(!1),o=n(0),{themeName:d}=k(),a=F(()=>u.value?"Нажать еще раз":"Нажми меня!"),i={name:c,clicked:u,clickCount:o,themeName:d,buttonLabel:a,handleClick:()=>{u.value=!0,o.value++},Button:C,Card:b,InputText:_};return Object.defineProperty(i,"__isScriptSetup",{enumerable:!1,value:!0}),i}}),v={class:"p-6 max-w-md mx-auto"},y={class:"mb-4 p-3 bg-surface-100 dark:bg-surface-200 rounded-md"},h={class:"text-sm text-surface-600 dark:text-surface-400 mb-2"},A={class:"font-semibold"},N={class:"mb-4"},w={key:0,class:"text-surface-600 dark:text-surface-400 mb-4"},T={key:1,class:"text-primary-600 dark:text-primary-400 mt-2 text-center"};function g(x,e,c,u,o,d){return l(),s("div",v,[r(u.Card,{class:"mb-4"},{title:f(()=>e[1]||(e[1]=[t("h2",{class:"text-2xl font-bold text-primary"},"Добро пожаловать в PartTec!",-1)])),content:f(()=>[e[5]||(e[5]=t("p",{class:"text-surface-700 dark:text-surface-300 mb-4"}," Система управления каталогом взаимозаменяемых запчастей ",-1)),t("div",y,[t("p",h,[e[2]||(e[2]=D(" Текущая тема: ")),t("span",A,m(u.themeName),1)]),e[3]||(e[3]=t("p",{class:"text-xs text-surface-500 dark:text-surface-500"}," Этот блок демонстрирует адаптивность к темам ",-1))]),t("div",N,[e[4]||(e[4]=t("label",{for:"name",class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Ваше имя: ",-1)),r(u.InputText,{id:"name",modelValue:u.name,"onUpdate:modelValue":e[0]||(e[0]=a=>u.name=a),placeholder:"Введите ваше имя",class:"w-full"},null,8,["modelValue"])]),u.name?(l(),s("p",w," Привет, "+m(u.name)+"! 👋 ",1)):p("",!0),r(u.Button,{label:u.buttonLabel,onClick:u.handleClick,class:"w-full"},null,8,["label"]),u.clicked?(l(),s("p",T," Кнопка была нажата "+m(u.clickCount)+" раз! ",1)):p("",!0)]),_:1})])}const R=B(V,[["render",g]]);export{R as default};

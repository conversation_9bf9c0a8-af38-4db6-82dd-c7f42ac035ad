import { defineComponent, useSSRContext, ref, onErrorCaptured, computed } from 'vue';
import { RefreshCw, RotateCcw, AlertTriangle } from 'lucide-vue-next';
import { ssrRenderAttrs, ssrRenderClass, ssrRenderComponent, ssrInterpolate, ssrRenderSlot } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_avhRMbqw.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ErrorBoundary",
  props: {
    title: {},
    message: {},
    variant: { default: "default" },
    showActions: { type: Boolean, default: true },
    showReload: { type: Boolean, default: true },
    showDetails: { type: Boolean, default: false },
    onRetry: {}
  },
  emits: ["error", "retry"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const hasError = ref(false);
    const errorDetails = ref("");
    onErrorCaptured((error) => {
      hasError.value = true;
      errorDetails.value = error.stack || error.message;
      emit("error", error);
      return false;
    });
    const retry = () => {
      hasError.value = false;
      errorDetails.value = "";
      if (props.onRetry) {
        props.onRetry();
      }
      emit("retry");
    };
    const reload = () => {
      window.location.reload();
    };
    const errorContainerClasses = computed(() => {
      const base = "w-full";
      const variants = {
        default: "p-6 bg-[--color-card] border border-[--color-border] rounded-lg",
        minimal: "p-4",
        detailed: "p-8 bg-[--color-card] border border-[--color-border] rounded-xl shadow-sm"
      };
      return `${base} ${variants[props.variant]}`;
    });
    const errorContentClasses = computed(() => {
      return props.variant === "minimal" ? "flex items-start gap-3" : "flex flex-col items-center text-center";
    });
    const iconClasses = computed(() => {
      const base = "flex-shrink-0";
      const variants = {
        default: "text-[--color-danger] mb-4",
        minimal: "text-[--color-danger] mt-0.5",
        detailed: "text-[--color-danger] mb-6 p-3 bg-red-50 dark:bg-red-950/20 rounded-full"
      };
      return `${base} ${variants[props.variant]}`;
    });
    const iconSizeClasses = computed(() => {
      const variants = {
        default: "w-8 h-8",
        minimal: "w-5 h-5",
        detailed: "w-12 h-12"
      };
      return variants[props.variant];
    });
    const titleClasses = computed(() => {
      const base = "font-semibold text-[--color-foreground]";
      const variants = {
        default: "text-lg mb-2",
        minimal: "text-base mb-1",
        detailed: "text-xl mb-3"
      };
      return `${base} ${variants[props.variant]}`;
    });
    const messageClasses = computed(() => {
      const base = "text-[--color-muted]";
      const variants = {
        default: "text-sm",
        minimal: "text-sm",
        detailed: "text-base max-w-md"
      };
      return `${base} ${variants[props.variant]}`;
    });
    const detailsClasses = computed(() => {
      return "mt-2 p-3 bg-[--color-background] border border-[--color-border] rounded text-xs text-[--color-muted] overflow-auto max-h-32";
    });
    const retryButtonClasses = computed(() => {
      return "inline-flex items-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium";
    });
    const reloadButtonClasses = computed(() => {
      return "inline-flex items-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium";
    });
    const __returned__ = { props, emit, hasError, errorDetails, retry, reload, errorContainerClasses, errorContentClasses, iconClasses, iconSizeClasses, titleClasses, messageClasses, detailsClasses, retryButtonClasses, reloadButtonClasses, get AlertTriangle() {
      return AlertTriangle;
    }, get RotateCcw() {
      return RotateCcw;
    }, get RefreshCw() {
      return RefreshCw;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(_attrs)}>`);
  if ($setup.hasError) {
    _push(`<div class="${ssrRenderClass($setup.errorContainerClasses)}"><div class="${ssrRenderClass($setup.errorContentClasses)}"><div class="${ssrRenderClass($setup.iconClasses)}">`);
    _push(ssrRenderComponent($setup["AlertTriangle"], { class: $setup.iconSizeClasses }, null, _parent));
    _push(`</div><div class="flex-1"><h3 class="${ssrRenderClass($setup.titleClasses)}">${ssrInterpolate($props.title || "Произошла ошибка")}</h3><p class="${ssrRenderClass($setup.messageClasses)}">${ssrInterpolate($props.message || "Что-то пошло не так. Пожалуйста, попробуйте обновить страницу.")}</p>`);
    if ($props.showDetails && $setup.errorDetails) {
      _push(`<details class="mt-4"><summary class="cursor-pointer text-sm text-[--color-muted] hover:text-[--color-foreground]"> Подробности ошибки </summary><pre class="${ssrRenderClass($setup.detailsClasses)}">${ssrInterpolate($setup.errorDetails)}</pre></details>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
    if ($props.showActions) {
      _push(`<div class="flex gap-2 mt-4"><button class="${ssrRenderClass($setup.retryButtonClasses)}">`);
      _push(ssrRenderComponent($setup["RotateCcw"], { class: "w-4 h-4" }, null, _parent));
      _push(` Повторить </button>`);
      if ($props.showReload) {
        _push(`<button class="${ssrRenderClass($setup.reloadButtonClasses)}">`);
        _push(ssrRenderComponent($setup["RefreshCw"], { class: "w-4 h-4" }, null, _parent));
        _push(` Обновить страницу </button>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div></div>`);
  } else {
    ssrRenderSlot(_ctx.$slots, "default", {}, null, _push, _parent);
  }
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/ui/ErrorBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ErrorBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { ErrorBoundary as E };

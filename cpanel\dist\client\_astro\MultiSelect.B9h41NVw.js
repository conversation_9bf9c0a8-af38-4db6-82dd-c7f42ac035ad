import{b as Q,a as X,d as ce,e as pe,c as fe}from"./index.DPMtieGJ.js";import{a as Z}from"./index.6ykohhwZ.js";import{s as he,a as ee}from"./index.By2TJOuX.js";import{B as me,a as F,n as be,z as ge,M as B,k as G,u as ye,o as ve,q as Oe,r as Ie,D as ke,S as Se,b as V,L as xe,v as we,w as Ce,c as T}from"./index.BaVCXmir.js";import{x as N}from"./index.CLs7nh7g.js";import{F as Le}from"./index.BpXFSz0M.js";import{C as Fe}from"./index.S_9XL1GF.js";import{s as Ve}from"./index.D4QD70nN.js";import{s as Me}from"./index.uDWUdklz.js";import{s as Te,a as Ke}from"./index.COq_zjeV.js";import{O as De}from"./index.n7VWMPJ9.js";import{s as Ae}from"./index.BZ4rDiaJ.js";import{R as Ee,f as j}from"./index.BH7IgUdp.js";import{T as Pe}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{P as S,k as Re,c,o,a as v,p as m,b as y,e as I,m as s,F as K,f as E,r as $,w as b,g as O,M as D,n as te,l as ze,d as He,q as Be}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as k,n as x,b as ie,r as Ge}from"./reactivity.esm-bundler.BQ12LWmY.js";import{p as je}from"./utils.BUKUcbtE.js";import{_ as Ue}from"./_plugin-vue_export-helper.DlAUqK2U.js";var Ne=`
    .p-multiselect {
        display: inline-flex;
        cursor: pointer;
        position: relative;
        user-select: none;
        background: dt('multiselect.background');
        border: 1px solid dt('multiselect.border.color');
        transition:
            background dt('multiselect.transition.duration'),
            color dt('multiselect.transition.duration'),
            border-color dt('multiselect.transition.duration'),
            outline-color dt('multiselect.transition.duration'),
            box-shadow dt('multiselect.transition.duration');
        border-radius: dt('multiselect.border.radius');
        outline-color: transparent;
        box-shadow: dt('multiselect.shadow');
    }

    .p-multiselect:not(.p-disabled):hover {
        border-color: dt('multiselect.hover.border.color');
    }

    .p-multiselect:not(.p-disabled).p-focus {
        border-color: dt('multiselect.focus.border.color');
        box-shadow: dt('multiselect.focus.ring.shadow');
        outline: dt('multiselect.focus.ring.width') dt('multiselect.focus.ring.style') dt('multiselect.focus.ring.color');
        outline-offset: dt('multiselect.focus.ring.offset');
    }

    .p-multiselect.p-variant-filled {
        background: dt('multiselect.filled.background');
    }

    .p-multiselect.p-variant-filled:not(.p-disabled):hover {
        background: dt('multiselect.filled.hover.background');
    }

    .p-multiselect.p-variant-filled.p-focus {
        background: dt('multiselect.filled.focus.background');
    }

    .p-multiselect.p-invalid {
        border-color: dt('multiselect.invalid.border.color');
    }

    .p-multiselect.p-disabled {
        opacity: 1;
        background: dt('multiselect.disabled.background');
    }

    .p-multiselect-dropdown {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        background: transparent;
        color: dt('multiselect.dropdown.color');
        width: dt('multiselect.dropdown.width');
        border-start-end-radius: dt('multiselect.border.radius');
        border-end-end-radius: dt('multiselect.border.radius');
    }

    .p-multiselect-clear-icon {
        position: absolute;
        top: 50%;
        margin-top: -0.5rem;
        color: dt('multiselect.clear.icon.color');
        inset-inline-end: dt('multiselect.dropdown.width');
    }

    .p-multiselect-label-container {
        overflow: hidden;
        flex: 1 1 auto;
        cursor: pointer;
    }

    .p-multiselect-label {
        white-space: nowrap;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: dt('multiselect.padding.y') dt('multiselect.padding.x');
        color: dt('multiselect.color');
    }

    .p-multiselect-display-chip .p-multiselect-label {
        display: flex;
        align-items: center;
        gap: calc(dt('multiselect.padding.y') / 2);
    }

    .p-multiselect-label.p-placeholder {
        color: dt('multiselect.placeholder.color');
    }

    .p-multiselect.p-invalid .p-multiselect-label.p-placeholder {
        color: dt('multiselect.invalid.placeholder.color');
    }

    .p-multiselect.p-disabled .p-multiselect-label {
        color: dt('multiselect.disabled.color');
    }

    .p-multiselect-label-empty {
        overflow: hidden;
        visibility: hidden;
    }

    .p-multiselect-overlay {
        position: absolute;
        top: 0;
        left: 0;
        background: dt('multiselect.overlay.background');
        color: dt('multiselect.overlay.color');
        border: 1px solid dt('multiselect.overlay.border.color');
        border-radius: dt('multiselect.overlay.border.radius');
        box-shadow: dt('multiselect.overlay.shadow');
        min-width: 100%;
    }

    .p-multiselect-header {
        display: flex;
        align-items: center;
        padding: dt('multiselect.list.header.padding');
    }

    .p-multiselect-header .p-checkbox {
        margin-inline-end: dt('multiselect.option.gap');
    }

    .p-multiselect-filter-container {
        flex: 1 1 auto;
    }

    .p-multiselect-filter {
        width: 100%;
    }

    .p-multiselect-list-container {
        overflow: auto;
    }

    .p-multiselect-list {
        margin: 0;
        padding: 0;
        list-style-type: none;
        padding: dt('multiselect.list.padding');
        display: flex;
        flex-direction: column;
        gap: dt('multiselect.list.gap');
    }

    .p-multiselect-option {
        cursor: pointer;
        font-weight: normal;
        white-space: nowrap;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        gap: dt('multiselect.option.gap');
        padding: dt('multiselect.option.padding');
        border: 0 none;
        color: dt('multiselect.option.color');
        background: transparent;
        transition:
            background dt('multiselect.transition.duration'),
            color dt('multiselect.transition.duration'),
            border-color dt('multiselect.transition.duration'),
            box-shadow dt('multiselect.transition.duration'),
            outline-color dt('multiselect.transition.duration');
        border-radius: dt('multiselect.option.border.radius');
    }

    .p-multiselect-option:not(.p-multiselect-option-selected):not(.p-disabled).p-focus {
        background: dt('multiselect.option.focus.background');
        color: dt('multiselect.option.focus.color');
    }

    .p-multiselect-option.p-multiselect-option-selected {
        background: dt('multiselect.option.selected.background');
        color: dt('multiselect.option.selected.color');
    }

    .p-multiselect-option.p-multiselect-option-selected.p-focus {
        background: dt('multiselect.option.selected.focus.background');
        color: dt('multiselect.option.selected.focus.color');
    }

    .p-multiselect-option-group {
        cursor: auto;
        margin: 0;
        padding: dt('multiselect.option.group.padding');
        background: dt('multiselect.option.group.background');
        color: dt('multiselect.option.group.color');
        font-weight: dt('multiselect.option.group.font.weight');
    }

    .p-multiselect-empty-message {
        padding: dt('multiselect.empty.message.padding');
    }

    .p-multiselect-label .p-chip {
        padding-block-start: calc(dt('multiselect.padding.y') / 2);
        padding-block-end: calc(dt('multiselect.padding.y') / 2);
        border-radius: dt('multiselect.chip.border.radius');
    }

    .p-multiselect-label:has(.p-chip) {
        padding: calc(dt('multiselect.padding.y') / 2) calc(dt('multiselect.padding.x') / 2);
    }

    .p-multiselect-fluid {
        display: flex;
        width: 100%;
    }

    .p-multiselect-sm .p-multiselect-label {
        font-size: dt('multiselect.sm.font.size');
        padding-block: dt('multiselect.sm.padding.y');
        padding-inline: dt('multiselect.sm.padding.x');
    }

    .p-multiselect-sm .p-multiselect-dropdown .p-icon {
        font-size: dt('multiselect.sm.font.size');
        width: dt('multiselect.sm.font.size');
        height: dt('multiselect.sm.font.size');
    }

    .p-multiselect-lg .p-multiselect-label {
        font-size: dt('multiselect.lg.font.size');
        padding-block: dt('multiselect.lg.padding.y');
        padding-inline: dt('multiselect.lg.padding.x');
    }

    .p-multiselect-lg .p-multiselect-dropdown .p-icon {
        font-size: dt('multiselect.lg.font.size');
        width: dt('multiselect.lg.font.size');
        height: dt('multiselect.lg.font.size');
    }
`,$e={root:function(e){var i=e.props;return{position:i.appendTo==="self"?"relative":void 0}}},qe={root:function(e){var i=e.instance,n=e.props;return["p-multiselect p-component p-inputwrapper",{"p-multiselect-display-chip":n.display==="chip","p-disabled":n.disabled,"p-invalid":i.$invalid,"p-variant-filled":i.$variant==="filled","p-focus":i.focused,"p-inputwrapper-filled":i.$filled,"p-inputwrapper-focus":i.focused||i.overlayVisible,"p-multiselect-open":i.overlayVisible,"p-multiselect-fluid":i.$fluid,"p-multiselect-sm p-inputfield-sm":n.size==="small","p-multiselect-lg p-inputfield-lg":n.size==="large"}]},labelContainer:"p-multiselect-label-container",label:function(e){var i=e.instance,n=e.props;return["p-multiselect-label",{"p-placeholder":i.label===n.placeholder,"p-multiselect-label-empty":!n.placeholder&&!i.$filled}]},clearIcon:"p-multiselect-clear-icon",chipItem:"p-multiselect-chip-item",pcChip:"p-multiselect-chip",chipIcon:"p-multiselect-chip-icon",dropdown:"p-multiselect-dropdown",loadingIcon:"p-multiselect-loading-icon",dropdownIcon:"p-multiselect-dropdown-icon",overlay:"p-multiselect-overlay p-component",header:"p-multiselect-header",pcFilterContainer:"p-multiselect-filter-container",pcFilter:"p-multiselect-filter",listContainer:"p-multiselect-list-container",list:"p-multiselect-list",optionGroup:"p-multiselect-option-group",option:function(e){var i=e.instance,n=e.option,r=e.index,l=e.getItemOptions,d=e.props;return["p-multiselect-option",{"p-multiselect-option-selected":i.isSelected(n)&&d.highlightOnSelect,"p-focus":i.focusedOptionIndex===i.getOptionIndex(r,l),"p-disabled":i.isOptionDisabled(n)}]},emptyMessage:"p-multiselect-empty-message"},We=me.extend({name:"multiselect",style:Ne,classes:qe,inlineStyles:$e}),_e={name:"BaseMultiSelect",extends:Ke,props:{options:Array,optionLabel:null,optionValue:null,optionDisabled:null,optionGroupLabel:null,optionGroupChildren:null,scrollHeight:{type:String,default:"14rem"},placeholder:String,inputId:{type:String,default:null},panelClass:{type:String,default:null},panelStyle:{type:null,default:null},overlayClass:{type:String,default:null},overlayStyle:{type:null,default:null},dataKey:null,showClear:{type:Boolean,default:!1},clearIcon:{type:String,default:void 0},resetFilterOnClear:{type:Boolean,default:!1},filter:Boolean,filterPlaceholder:String,filterLocale:String,filterMatchMode:{type:String,default:"contains"},filterFields:{type:Array,default:null},appendTo:{type:[String,Object],default:"body"},display:{type:String,default:"comma"},selectedItemsLabel:{type:String,default:null},maxSelectedLabels:{type:Number,default:null},selectionLimit:{type:Number,default:null},showToggleAll:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},checkboxIcon:{type:String,default:void 0},dropdownIcon:{type:String,default:void 0},filterIcon:{type:String,default:void 0},loadingIcon:{type:String,default:void 0},removeTokenIcon:{type:String,default:void 0},chipIcon:{type:String,default:void 0},selectAll:{type:Boolean,default:null},resetFilterOnHide:{type:Boolean,default:!1},virtualScrollerOptions:{type:Object,default:null},autoOptionFocus:{type:Boolean,default:!1},autoFilterFocus:{type:Boolean,default:!1},focusOnHover:{type:Boolean,default:!0},highlightOnSelect:{type:Boolean,default:!1},filterMessage:{type:String,default:null},selectionMessage:{type:String,default:null},emptySelectionMessage:{type:String,default:null},emptyFilterMessage:{type:String,default:null},emptyMessage:{type:String,default:null},tabindex:{type:Number,default:0},ariaLabel:{type:String,default:null},ariaLabelledby:{type:String,default:null}},style:We,provide:function(){return{$pcMultiSelect:this,$parentInstance:this}}};function P(t){"@babel/helpers - typeof";return P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(t)}function W(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),i.push.apply(i,n)}return i}function _(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?W(Object(i),!0).forEach(function(n){w(t,n,i[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):W(Object(i)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(i,n))})}return t}function w(t,e,i){return(e=Ye(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Ye(t){var e=Je(t,"string");return P(e)=="symbol"?e:e+""}function Je(t,e){if(P(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var n=i.call(t,e);if(P(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Y(t){return et(t)||Ze(t)||Xe(t)||Qe()}function Qe(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xe(t,e){if(t){if(typeof t=="string")return q(t,e);var i={}.toString.call(t).slice(8,-1);return i==="Object"&&t.constructor&&(i=t.constructor.name),i==="Map"||i==="Set"?Array.from(t):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?q(t,e):void 0}}function Ze(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function et(t){if(Array.isArray(t))return q(t)}function q(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}var ne={name:"MultiSelect",extends:_e,inheritAttrs:!1,emits:["change","focus","blur","before-show","before-hide","show","hide","filter","selectall-change"],inject:{$pcFluid:{default:null}},outsideClickListener:null,scrollHandler:null,resizeListener:null,overlay:null,list:null,virtualScroller:null,startRangeIndex:-1,searchTimeout:null,searchValue:"",selectOnFocus:!1,data:function(){return{clicked:!1,focused:!1,focusedOptionIndex:-1,filterValue:null,overlayVisible:!1}},watch:{options:function(){this.autoUpdateModel()}},mounted:function(){this.autoUpdateModel()},beforeUnmount:function(){this.unbindOutsideClickListener(),this.unbindResizeListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.overlay&&(N.clear(this.overlay),this.overlay=null)},methods:{getOptionIndex:function(e,i){return this.virtualScrollerDisabled?e:i&&i(e).index},getOptionLabel:function(e){return this.optionLabel?T(e,this.optionLabel):e},getOptionValue:function(e){return this.optionValue?T(e,this.optionValue):e},getOptionRenderKey:function(e,i){return this.dataKey?T(e,this.dataKey):this.getOptionLabel(e)+"_".concat(i)},getHeaderCheckboxPTOptions:function(e){return this.ptm(e,{context:{selected:this.allSelected}})},getCheckboxPTOptions:function(e,i,n,r){return this.ptm(r,{context:{selected:this.isSelected(e),focused:this.focusedOptionIndex===this.getOptionIndex(n,i),disabled:this.isOptionDisabled(e)}})},isOptionDisabled:function(e){return this.maxSelectionLimitReached&&!this.isSelected(e)?!0:this.optionDisabled?T(e,this.optionDisabled):!1},isOptionGroup:function(e){return this.optionGroupLabel&&e.optionGroup&&e.group},getOptionGroupLabel:function(e){return T(e,this.optionGroupLabel)},getOptionGroupChildren:function(e){return T(e,this.optionGroupChildren)},getAriaPosInset:function(e){var i=this;return(this.optionGroupLabel?e-this.visibleOptions.slice(0,e).filter(function(n){return i.isOptionGroup(n)}).length:e)+1},show:function(e){this.$emit("before-show"),this.overlayVisible=!0,this.focusedOptionIndex=this.focusedOptionIndex!==-1?this.focusedOptionIndex:this.autoOptionFocus?this.findFirstFocusedOptionIndex():this.findSelectedOptionIndex(),e&&V(this.$refs.focusInput)},hide:function(e){var i=this,n=function(){i.$emit("before-hide"),i.overlayVisible=!1,i.clicked=!1,i.focusedOptionIndex=-1,i.searchValue="",i.resetFilterOnHide&&(i.filterValue=null),e&&V(i.$refs.focusInput)};setTimeout(function(){n()},0)},onFocus:function(e){this.disabled||(this.focused=!0,this.overlayVisible&&(this.focusedOptionIndex=this.focusedOptionIndex!==-1?this.focusedOptionIndex:this.autoOptionFocus?this.findFirstFocusedOptionIndex():this.findSelectedOptionIndex(),!this.autoFilterFocus&&this.scrollInView(this.focusedOptionIndex)),this.$emit("focus",e))},onBlur:function(e){var i,n;this.clicked=!1,this.focused=!1,this.focusedOptionIndex=-1,this.searchValue="",this.$emit("blur",e),(i=(n=this.formField).onBlur)===null||i===void 0||i.call(n)},onKeyDown:function(e){var i=this;if(this.disabled){e.preventDefault();return}var n=e.metaKey||e.ctrlKey;switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"PageDown":this.onPageDownKey(e);break;case"PageUp":this.onPageUpKey(e);break;case"Enter":case"NumpadEnter":case"Space":this.onEnterKey(e);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e);break;case"ShiftLeft":case"ShiftRight":this.onShiftKey(e);break;default:if(e.code==="KeyA"&&n){var r=this.visibleOptions.filter(function(l){return i.isValidOption(l)}).map(function(l){return i.getOptionValue(l)});this.updateModel(e,r),e.preventDefault();break}!n&&Ce(e.key)&&(!this.overlayVisible&&this.show(),this.searchOptions(e),e.preventDefault());break}this.clicked=!1},onContainerClick:function(e){this.disabled||this.loading||e.target.tagName==="INPUT"||e.target.getAttribute("data-pc-section")==="clearicon"||e.target.closest('[data-pc-section="clearicon"]')||((!this.overlay||!this.overlay.contains(e.target))&&(this.overlayVisible?this.hide(!0):this.show(!0)),this.clicked=!0)},onClearClick:function(e){this.updateModel(e,null),this.resetFilterOnClear&&(this.filterValue=null)},onFirstHiddenFocus:function(e){var i=e.relatedTarget===this.$refs.focusInput?we(this.overlay,':not([data-p-hidden-focusable="true"])'):this.$refs.focusInput;V(i)},onLastHiddenFocus:function(e){var i=e.relatedTarget===this.$refs.focusInput?xe(this.overlay,':not([data-p-hidden-focusable="true"])'):this.$refs.focusInput;V(i)},onOptionSelect:function(e,i){var n=this,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:-1,l=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!(this.disabled||this.isOptionDisabled(i))){var d=this.isSelected(i),p=null,f=this.getOptionValue(i)!==""?this.getOptionValue(i):this.getOptionLabel(i);d?p=this.d_value.filter(function(A){return!G(A,f,n.equalityKey)}):p=[].concat(Y(this.d_value||[]),[f]),this.updateModel(e,p),r!==-1&&(this.focusedOptionIndex=r),l&&V(this.$refs.focusInput)}},onOptionMouseMove:function(e,i){this.focusOnHover&&this.changeFocusedOptionIndex(e,i)},onOptionSelectRange:function(e){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:-1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:-1;if(n===-1&&(n=this.findNearestSelectedOptionIndex(r,!0)),r===-1&&(r=this.findNearestSelectedOptionIndex(n)),n!==-1&&r!==-1){var l=Math.min(n,r),d=Math.max(n,r),p=this.visibleOptions.slice(l,d+1).filter(function(f){return i.isValidOption(f)}).map(function(f){return i.getOptionValue(f)});this.updateModel(e,p)}},onFilterChange:function(e){var i=e.target.value;this.filterValue=i,this.focusedOptionIndex=-1,this.$emit("filter",{originalEvent:e,value:i}),!this.virtualScrollerDisabled&&this.virtualScroller.scrollToIndex(0)},onFilterKeyDown:function(e){switch(e.code){case"ArrowDown":this.onArrowDownKey(e);break;case"ArrowUp":this.onArrowUpKey(e,!0);break;case"ArrowLeft":case"ArrowRight":this.onArrowLeftKey(e,!0);break;case"Home":this.onHomeKey(e,!0);break;case"End":this.onEndKey(e,!0);break;case"Enter":case"NumpadEnter":this.onEnterKey(e);break;case"Escape":this.onEscapeKey(e);break;case"Tab":this.onTabKey(e,!0);break}},onFilterBlur:function(){this.focusedOptionIndex=-1},onFilterUpdated:function(){this.overlayVisible&&this.alignOverlay()},onOverlayClick:function(e){De.emit("overlay-click",{originalEvent:e,target:this.$el})},onOverlayKeyDown:function(e){switch(e.code){case"Escape":this.onEscapeKey(e);break}},onArrowDownKey:function(e){if(!this.overlayVisible)this.show();else{var i=this.focusedOptionIndex!==-1?this.findNextOptionIndex(this.focusedOptionIndex):this.clicked?this.findFirstOptionIndex():this.findFirstFocusedOptionIndex();e.shiftKey&&this.onOptionSelectRange(e,this.startRangeIndex,i),this.changeFocusedOptionIndex(e,i)}e.preventDefault()},onArrowUpKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(e.altKey&&!i)this.focusedOptionIndex!==-1&&this.onOptionSelect(e,this.visibleOptions[this.focusedOptionIndex]),this.overlayVisible&&this.hide(),e.preventDefault();else{var n=this.focusedOptionIndex!==-1?this.findPrevOptionIndex(this.focusedOptionIndex):this.clicked?this.findLastOptionIndex():this.findLastFocusedOptionIndex();e.shiftKey&&this.onOptionSelectRange(e,n,this.startRangeIndex),this.changeFocusedOptionIndex(e,n),!this.overlayVisible&&this.show(),e.preventDefault()}},onArrowLeftKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;i&&(this.focusedOptionIndex=-1)},onHomeKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(i){var n=e.currentTarget;e.shiftKey?n.setSelectionRange(0,e.target.selectionStart):(n.setSelectionRange(0,0),this.focusedOptionIndex=-1)}else{var r=e.metaKey||e.ctrlKey,l=this.findFirstOptionIndex();e.shiftKey&&r&&this.onOptionSelectRange(e,l,this.startRangeIndex),this.changeFocusedOptionIndex(e,l),!this.overlayVisible&&this.show()}e.preventDefault()},onEndKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(i){var n=e.currentTarget;if(e.shiftKey)n.setSelectionRange(e.target.selectionStart,n.value.length);else{var r=n.value.length;n.setSelectionRange(r,r),this.focusedOptionIndex=-1}}else{var l=e.metaKey||e.ctrlKey,d=this.findLastOptionIndex();e.shiftKey&&l&&this.onOptionSelectRange(e,this.startRangeIndex,d),this.changeFocusedOptionIndex(e,d),!this.overlayVisible&&this.show()}e.preventDefault()},onPageUpKey:function(e){this.scrollInView(0),e.preventDefault()},onPageDownKey:function(e){this.scrollInView(this.visibleOptions.length-1),e.preventDefault()},onEnterKey:function(e){this.overlayVisible?this.focusedOptionIndex!==-1&&(e.shiftKey?this.onOptionSelectRange(e,this.focusedOptionIndex):this.onOptionSelect(e,this.visibleOptions[this.focusedOptionIndex])):(this.focusedOptionIndex=-1,this.onArrowDownKey(e)),e.preventDefault()},onEscapeKey:function(e){this.overlayVisible&&(this.hide(!0),e.stopPropagation()),e.preventDefault()},onTabKey:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;i||(this.overlayVisible&&this.hasFocusableElements()?(V(e.shiftKey?this.$refs.lastHiddenFocusableElementOnOverlay:this.$refs.firstHiddenFocusableElementOnOverlay),e.preventDefault()):(this.focusedOptionIndex!==-1&&this.onOptionSelect(e,this.visibleOptions[this.focusedOptionIndex]),this.overlayVisible&&this.hide(this.filter)))},onShiftKey:function(){this.startRangeIndex=this.focusedOptionIndex},onOverlayEnter:function(e){N.set("overlay",e,this.$primevue.config.zIndex.overlay),Se(e,{position:"absolute",top:"0"}),this.alignOverlay(),this.scrollInView(),this.autoFilterFocus&&V(this.$refs.filterInput.$el),this.autoUpdateModel(),this.$attrSelector&&e.setAttribute(this.$attrSelector,"")},onOverlayAfterEnter:function(){this.bindOutsideClickListener(),this.bindScrollListener(),this.bindResizeListener(),this.$emit("show")},onOverlayLeave:function(){this.unbindOutsideClickListener(),this.unbindScrollListener(),this.unbindResizeListener(),this.$emit("hide"),this.overlay=null},onOverlayAfterLeave:function(e){N.clear(e)},alignOverlay:function(){this.appendTo==="self"?Oe(this.overlay,this.$el):(this.overlay.style.minWidth=Ie(this.$el)+"px",ke(this.overlay,this.$el))},bindOutsideClickListener:function(){var e=this;this.outsideClickListener||(this.outsideClickListener=function(i){e.overlayVisible&&e.isOutsideClicked(i)&&e.hide()},document.addEventListener("click",this.outsideClickListener,!0))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener,!0),this.outsideClickListener=null)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new Fe(this.$refs.container,function(){e.overlayVisible&&e.hide()})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(){e.overlayVisible&&!ve()&&e.hide()},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},isOutsideClicked:function(e){return!(this.$el.isSameNode(e.target)||this.$el.contains(e.target)||this.overlay&&this.overlay.contains(e.target))},getLabelByValue:function(e){var i=this,n=this.optionGroupLabel?this.flatOptions(this.options):this.options||[],r=n.find(function(l){return!i.isOptionGroup(l)&&G(i.getOptionValue(l)!==""?i.getOptionValue(l):i.getOptionLabel(l),e,i.equalityKey)});return r?this.getOptionLabel(r):null},getSelectedItemsLabel:function(){var e=/{(.*?)}/,i=this.selectedItemsLabel||this.$primevue.config.locale.selectionMessage;return e.test(i)?i.replace(i.match(e)[0],this.d_value.length+""):i},onToggleAll:function(e){var i=this;if(this.selectAll!==null)this.$emit("selectall-change",{originalEvent:e,checked:!this.allSelected});else{var n=this.allSelected?[]:this.visibleOptions.filter(function(r){return i.isValidOption(r)}).map(function(r){return i.getOptionValue(r)});this.updateModel(e,n)}},removeOption:function(e,i){var n=this;e.stopPropagation();var r=this.d_value.filter(function(l){return!G(l,i,n.equalityKey)});this.updateModel(e,r)},clearFilter:function(){this.filterValue=null},hasFocusableElements:function(){return ye(this.overlay,':not([data-p-hidden-focusable="true"])').length>0},isOptionMatched:function(e){var i;return this.isValidOption(e)&&typeof this.getOptionLabel(e)=="string"&&((i=this.getOptionLabel(e))===null||i===void 0?void 0:i.toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)))},isValidOption:function(e){return F(e)&&!(this.isOptionDisabled(e)||this.isOptionGroup(e))},isValidSelectedOption:function(e){return this.isValidOption(e)&&this.isSelected(e)},isEquals:function(e,i){return G(e,i,this.equalityKey)},isSelected:function(e){var i=this,n=this.getOptionValue(e)!==""?this.getOptionValue(e):this.getOptionLabel(e);return(this.d_value||[]).some(function(r){return i.isEquals(r,n)})},findFirstOptionIndex:function(){var e=this;return this.visibleOptions.findIndex(function(i){return e.isValidOption(i)})},findLastOptionIndex:function(){var e=this;return B(this.visibleOptions,function(i){return e.isValidOption(i)})},findNextOptionIndex:function(e){var i=this,n=e<this.visibleOptions.length-1?this.visibleOptions.slice(e+1).findIndex(function(r){return i.isValidOption(r)}):-1;return n>-1?n+e+1:e},findPrevOptionIndex:function(e){var i=this,n=e>0?B(this.visibleOptions.slice(0,e),function(r){return i.isValidOption(r)}):-1;return n>-1?n:e},findSelectedOptionIndex:function(){var e=this;if(this.$filled){for(var i=function(){var d=e.d_value[r],p=e.visibleOptions.findIndex(function(f){return e.isValidSelectedOption(f)&&e.isEquals(d,e.getOptionValue(f))});if(p>-1)return{v:p}},n,r=this.d_value.length-1;r>=0;r--)if(n=i(),n)return n.v}return-1},findFirstSelectedOptionIndex:function(){var e=this;return this.$filled?this.visibleOptions.findIndex(function(i){return e.isValidSelectedOption(i)}):-1},findLastSelectedOptionIndex:function(){var e=this;return this.$filled?B(this.visibleOptions,function(i){return e.isValidSelectedOption(i)}):-1},findNextSelectedOptionIndex:function(e){var i=this,n=this.$filled&&e<this.visibleOptions.length-1?this.visibleOptions.slice(e+1).findIndex(function(r){return i.isValidSelectedOption(r)}):-1;return n>-1?n+e+1:-1},findPrevSelectedOptionIndex:function(e){var i=this,n=this.$filled&&e>0?B(this.visibleOptions.slice(0,e),function(r){return i.isValidSelectedOption(r)}):-1;return n>-1?n:-1},findNearestSelectedOptionIndex:function(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=-1;return this.$filled&&(i?(n=this.findPrevSelectedOptionIndex(e),n=n===-1?this.findNextSelectedOptionIndex(e):n):(n=this.findNextSelectedOptionIndex(e),n=n===-1?this.findPrevSelectedOptionIndex(e):n)),n>-1?n:e},findFirstFocusedOptionIndex:function(){var e=this.findFirstSelectedOptionIndex();return e<0?this.findFirstOptionIndex():e},findLastFocusedOptionIndex:function(){var e=this.findSelectedOptionIndex();return e<0?this.findLastOptionIndex():e},searchOptions:function(e){var i=this;this.searchValue=(this.searchValue||"")+e.key;var n=-1;F(this.searchValue)&&(this.focusedOptionIndex!==-1?(n=this.visibleOptions.slice(this.focusedOptionIndex).findIndex(function(r){return i.isOptionMatched(r)}),n=n===-1?this.visibleOptions.slice(0,this.focusedOptionIndex).findIndex(function(r){return i.isOptionMatched(r)}):n+this.focusedOptionIndex):n=this.visibleOptions.findIndex(function(r){return i.isOptionMatched(r)}),n===-1&&this.focusedOptionIndex===-1&&(n=this.findFirstFocusedOptionIndex()),n!==-1&&this.changeFocusedOptionIndex(e,n)),this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout(function(){i.searchValue="",i.searchTimeout=null},500)},changeFocusedOptionIndex:function(e,i){this.focusedOptionIndex!==i&&(this.focusedOptionIndex=i,this.scrollInView(),this.selectOnFocus&&this.onOptionSelect(e,this.visibleOptions[i]))},scrollInView:function(){var e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:-1;this.$nextTick(function(){var n=i!==-1?"".concat(e.$id,"_").concat(i):e.focusedOptionId,r=ge(e.list,'li[id="'.concat(n,'"]'));r?r.scrollIntoView&&r.scrollIntoView({block:"nearest",inline:"nearest"}):e.virtualScrollerDisabled||e.virtualScroller&&e.virtualScroller.scrollToIndex(i!==-1?i:e.focusedOptionIndex)})},autoUpdateModel:function(){if(this.autoOptionFocus&&(this.focusedOptionIndex=this.findFirstFocusedOptionIndex()),this.selectOnFocus&&this.autoOptionFocus&&!this.$filled){var e=this.getOptionValue(this.visibleOptions[this.focusedOptionIndex]);this.updateModel(null,[e])}},updateModel:function(e,i){this.writeValue(i,e),this.$emit("change",{originalEvent:e,value:i})},flatOptions:function(e){var i=this;return(e||[]).reduce(function(n,r,l){n.push({optionGroup:r,group:!0,index:l});var d=i.getOptionGroupChildren(r);return d&&d.forEach(function(p){return n.push(p)}),n},[])},overlayRef:function(e){this.overlay=e},listRef:function(e,i){this.list=e,i&&i(e)},virtualScrollerRef:function(e){this.virtualScroller=e}},computed:{visibleOptions:function(){var e=this,i=this.optionGroupLabel?this.flatOptions(this.options):this.options||[];if(this.filterValue){var n=Le.filter(i,this.searchFields,this.filterValue,this.filterMatchMode,this.filterLocale);if(this.optionGroupLabel){var r=this.options||[],l=[];return r.forEach(function(d){var p=e.getOptionGroupChildren(d),f=p.filter(function(A){return n.includes(A)});f.length>0&&l.push(_(_({},d),{},w({},typeof e.optionGroupChildren=="string"?e.optionGroupChildren:"items",Y(f))))}),this.flatOptions(l)}return n}return i},label:function(){var e;if(this.d_value&&this.d_value.length){if(F(this.maxSelectedLabels)&&this.d_value.length>this.maxSelectedLabels)return this.getSelectedItemsLabel();e="";for(var i=0;i<this.d_value.length;i++)i!==0&&(e+=", "),e+=this.getLabelByValue(this.d_value[i])}else e=this.placeholder;return e},chipSelectedItems:function(){return F(this.maxSelectedLabels)&&this.d_value&&this.d_value.length>this.maxSelectedLabels},allSelected:function(){var e=this;return this.selectAll!==null?this.selectAll:F(this.visibleOptions)&&this.visibleOptions.every(function(i){return e.isOptionGroup(i)||e.isOptionDisabled(i)||e.isSelected(i)})},hasSelectedOption:function(){return this.$filled},equalityKey:function(){return this.optionValue?null:this.dataKey},searchFields:function(){return this.filterFields||[this.optionLabel]},maxSelectionLimitReached:function(){return this.selectionLimit&&this.d_value&&this.d_value.length===this.selectionLimit},filterResultMessageText:function(){return F(this.visibleOptions)?this.filterMessageText.replaceAll("{0}",this.visibleOptions.length):this.emptyFilterMessageText},filterMessageText:function(){return this.filterMessage||this.$primevue.config.locale.searchMessage||""},emptyFilterMessageText:function(){return this.emptyFilterMessage||this.$primevue.config.locale.emptySearchMessage||this.$primevue.config.locale.emptyFilterMessage||""},emptyMessageText:function(){return this.emptyMessage||this.$primevue.config.locale.emptyMessage||""},selectionMessageText:function(){return this.selectionMessage||this.$primevue.config.locale.selectionMessage||""},emptySelectionMessageText:function(){return this.emptySelectionMessage||this.$primevue.config.locale.emptySelectionMessage||""},selectedMessageText:function(){return this.$filled?this.selectionMessageText.replaceAll("{0}",this.d_value.length):this.emptySelectionMessageText},focusedOptionId:function(){return this.focusedOptionIndex!==-1?"".concat(this.$id,"_").concat(this.focusedOptionIndex):null},ariaSetSize:function(){var e=this;return this.visibleOptions.filter(function(i){return!e.isOptionGroup(i)}).length},toggleAllAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria[this.allSelected?"selectAll":"unselectAll"]:void 0},listAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.listLabel:void 0},virtualScrollerDisabled:function(){return!this.virtualScrollerOptions},hasFluid:function(){return be(this.fluid)?!!this.$pcFluid:this.fluid},isClearIconVisible:function(){return this.showClear&&this.d_value&&this.d_value.length&&this.d_value!=null&&F(this.options)},containerDataP:function(){return j(w({invalid:this.$invalid,disabled:this.disabled,focus:this.focused,fluid:this.$fluid,filled:this.$variant==="filled"},this.size,this.size))},labelDataP:function(){return j(w(w(w({placeholder:this.label===this.placeholder,clearable:this.showClear,disabled:this.disabled},this.size,this.size),"has-chip",this.display==="chip"&&this.d_value&&this.d_value.length&&(this.maxSelectedLabels?this.d_value.length<=this.maxSelectedLabels:!0)),"empty",!this.placeholder&&!this.$filled))},dropdownIconDataP:function(){return j(w({},this.size,this.size))},overlayDataP:function(){return j(w({},"portal-"+this.appendTo,"portal-"+this.appendTo))}},directives:{ripple:Ee},components:{InputText:Te,Checkbox:Ve,VirtualScroller:fe,Portal:Ae,Chip:Me,IconField:pe,InputIcon:ce,TimesIcon:ee,SearchIcon:X,ChevronDownIcon:Q,SpinnerIcon:Z,CheckIcon:he}};function R(t){"@babel/helpers - typeof";return R=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},R(t)}function J(t,e,i){return(e=tt(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function tt(t){var e=it(t,"string");return R(e)=="symbol"?e:e+""}function it(t,e){if(R(t)!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var n=i.call(t,e);if(R(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var nt=["data-p"],lt=["id","disabled","placeholder","tabindex","aria-label","aria-labelledby","aria-expanded","aria-controls","aria-activedescendant","aria-invalid"],rt=["data-p"],st={key:0},ot=["data-p"],at=["id","aria-label"],dt=["id"],ut=["id","aria-label","aria-selected","aria-disabled","aria-setsize","aria-posinset","onClick","onMousemove","data-p-selected","data-p-focused","data-p-disabled"];function ct(t,e,i,n,r,l){var d=S("Chip"),p=S("SpinnerIcon"),f=S("Checkbox"),A=S("InputText"),le=S("SearchIcon"),re=S("InputIcon"),se=S("IconField"),oe=S("VirtualScroller"),ae=S("Portal"),de=Re("ripple");return o(),c("div",s({ref:"container",class:t.cx("root"),style:t.sx("root"),onClick:e[7]||(e[7]=function(){return l.onContainerClick&&l.onContainerClick.apply(l,arguments)}),"data-p":l.containerDataP},t.ptmi("root")),[v("div",s({class:"p-hidden-accessible"},t.ptm("hiddenInputContainer"),{"data-p-hidden-accessible":!0}),[v("input",s({ref:"focusInput",id:t.inputId,type:"text",readonly:"",disabled:t.disabled,placeholder:t.placeholder,tabindex:t.disabled?-1:t.tabindex,role:"combobox","aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledby,"aria-haspopup":"listbox","aria-expanded":r.overlayVisible,"aria-controls":t.$id+"_list","aria-activedescendant":r.focused?l.focusedOptionId:void 0,"aria-invalid":t.invalid||void 0,onFocus:e[0]||(e[0]=function(){return l.onFocus&&l.onFocus.apply(l,arguments)}),onBlur:e[1]||(e[1]=function(){return l.onBlur&&l.onBlur.apply(l,arguments)}),onKeydown:e[2]||(e[2]=function(){return l.onKeyDown&&l.onKeyDown.apply(l,arguments)})},t.ptm("hiddenInput")),null,16,lt)],16),v("div",s({class:t.cx("labelContainer")},t.ptm("labelContainer")),[v("div",s({class:t.cx("label"),"data-p":l.labelDataP},t.ptm("label")),[m(t.$slots,"value",{value:t.d_value,placeholder:t.placeholder},function(){return[t.display==="comma"?(o(),c(K,{key:0},[E(k(l.label||"empty"),1)],64)):t.display==="chip"?(o(),c(K,{key:1},[l.chipSelectedItems?(o(),c("span",st,k(l.label),1)):(o(!0),c(K,{key:1},$(t.d_value,function(a){return o(),c("span",s({key:l.getLabelByValue(a),class:t.cx("chipItem")},{ref_for:!0},t.ptm("chipItem")),[m(t.$slots,"chip",{value:a,removeCallback:function(C){return l.removeOption(C,a)}},function(){return[I(d,{class:x(t.cx("pcChip")),label:l.getLabelByValue(a),removeIcon:t.chipIcon||t.removeTokenIcon,removable:"",unstyled:t.unstyled,onRemove:function(C){return l.removeOption(C,a)},pt:t.ptm("pcChip")},{removeicon:b(function(){return[m(t.$slots,t.$slots.chipicon?"chipicon":"removetokenicon",{class:x(t.cx("chipIcon")),item:a,removeCallback:function(C){return l.removeOption(C,a)}})]}),_:2},1032,["class","label","removeIcon","unstyled","onRemove","pt"])]})],16)}),128)),!t.d_value||t.d_value.length===0?(o(),c(K,{key:2},[E(k(t.placeholder||"empty"),1)],64)):y("",!0)],64)):y("",!0)]})],16,rt)],16),l.isClearIconVisible?m(t.$slots,"clearicon",{key:0,class:x(t.cx("clearIcon")),clearCallback:l.onClearClick},function(){return[(o(),O(D(t.clearIcon?"i":"TimesIcon"),s({ref:"clearIcon",class:[t.cx("clearIcon"),t.clearIcon],onClick:l.onClearClick},t.ptm("clearIcon"),{"data-pc-section":"clearicon"}),null,16,["class","onClick"]))]}):y("",!0),v("div",s({class:t.cx("dropdown")},t.ptm("dropdown")),[t.loading?m(t.$slots,"loadingicon",{key:0,class:x(t.cx("loadingIcon"))},function(){return[t.loadingIcon?(o(),c("span",s({key:0,class:[t.cx("loadingIcon"),"pi-spin",t.loadingIcon],"aria-hidden":"true"},t.ptm("loadingIcon")),null,16)):(o(),O(p,s({key:1,class:t.cx("loadingIcon"),spin:"","aria-hidden":"true"},t.ptm("loadingIcon")),null,16,["class"]))]}):m(t.$slots,"dropdownicon",{key:1,class:x(t.cx("dropdownIcon"))},function(){return[(o(),O(D(t.dropdownIcon?"span":"ChevronDownIcon"),s({class:[t.cx("dropdownIcon"),t.dropdownIcon],"aria-hidden":"true","data-p":l.dropdownIconDataP},t.ptm("dropdownIcon")),null,16,["class","data-p"]))]})],16),I(ae,{appendTo:t.appendTo},{default:b(function(){return[I(Pe,s({name:"p-connected-overlay",onEnter:l.onOverlayEnter,onAfterEnter:l.onOverlayAfterEnter,onLeave:l.onOverlayLeave,onAfterLeave:l.onOverlayAfterLeave},t.ptm("transition")),{default:b(function(){return[r.overlayVisible?(o(),c("div",s({key:0,ref:l.overlayRef,style:[t.panelStyle,t.overlayStyle],class:[t.cx("overlay"),t.panelClass,t.overlayClass],onClick:e[5]||(e[5]=function(){return l.onOverlayClick&&l.onOverlayClick.apply(l,arguments)}),onKeydown:e[6]||(e[6]=function(){return l.onOverlayKeyDown&&l.onOverlayKeyDown.apply(l,arguments)}),"data-p":l.overlayDataP},t.ptm("overlay")),[v("span",s({ref:"firstHiddenFocusableElementOnOverlay",role:"presentation","aria-hidden":"true",class:"p-hidden-accessible p-hidden-focusable",tabindex:0,onFocus:e[3]||(e[3]=function(){return l.onFirstHiddenFocus&&l.onFirstHiddenFocus.apply(l,arguments)})},t.ptm("hiddenFirstFocusableEl"),{"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0}),null,16),m(t.$slots,"header",{value:t.d_value,options:l.visibleOptions}),t.showToggleAll&&t.selectionLimit==null||t.filter?(o(),c("div",s({key:0,class:t.cx("header")},t.ptm("header")),[t.showToggleAll&&t.selectionLimit==null?(o(),O(f,{key:0,modelValue:l.allSelected,binary:!0,disabled:t.disabled,variant:t.variant,"aria-label":l.toggleAllAriaLabel,onChange:l.onToggleAll,unstyled:t.unstyled,pt:l.getHeaderCheckboxPTOptions("pcHeaderCheckbox"),formControl:{novalidate:!0}},{icon:b(function(a){return[t.$slots.headercheckboxicon?(o(),O(D(t.$slots.headercheckboxicon),{key:0,checked:a.checked,class:x(a.class)},null,8,["checked","class"])):a.checked?(o(),O(D(t.checkboxIcon?"span":"CheckIcon"),s({key:1,class:[a.class,J({},t.checkboxIcon,a.checked)]},l.getHeaderCheckboxPTOptions("pcHeaderCheckbox.icon")),null,16,["class"])):y("",!0)]}),_:1},8,["modelValue","disabled","variant","aria-label","onChange","unstyled","pt"])):y("",!0),t.filter?(o(),O(se,{key:1,class:x(t.cx("pcFilterContainer")),unstyled:t.unstyled,pt:t.ptm("pcFilterContainer")},{default:b(function(){return[I(A,{ref:"filterInput",value:r.filterValue,onVnodeMounted:l.onFilterUpdated,onVnodeUpdated:l.onFilterUpdated,class:x(t.cx("pcFilter")),placeholder:t.filterPlaceholder,disabled:t.disabled,variant:t.variant,unstyled:t.unstyled,role:"searchbox",autocomplete:"off","aria-owns":t.$id+"_list","aria-activedescendant":l.focusedOptionId,onKeydown:l.onFilterKeyDown,onBlur:l.onFilterBlur,onInput:l.onFilterChange,pt:t.ptm("pcFilter"),formControl:{novalidate:!0}},null,8,["value","onVnodeMounted","onVnodeUpdated","class","placeholder","disabled","variant","unstyled","aria-owns","aria-activedescendant","onKeydown","onBlur","onInput","pt"]),I(re,{unstyled:t.unstyled,pt:t.ptm("pcFilterIconContainer")},{default:b(function(){return[m(t.$slots,"filtericon",{},function(){return[t.filterIcon?(o(),c("span",s({key:0,class:t.filterIcon},t.ptm("filterIcon")),null,16)):(o(),O(le,ie(s({key:1},t.ptm("filterIcon"))),null,16))]})]}),_:3},8,["unstyled","pt"])]}),_:3},8,["class","unstyled","pt"])):y("",!0),t.filter?(o(),c("span",s({key:2,role:"status","aria-live":"polite",class:"p-hidden-accessible"},t.ptm("hiddenFilterResult"),{"data-p-hidden-accessible":!0}),k(l.filterResultMessageText),17)):y("",!0)],16)):y("",!0),v("div",s({class:t.cx("listContainer"),style:{"max-height":l.virtualScrollerDisabled?t.scrollHeight:""}},t.ptm("listContainer")),[I(oe,s({ref:l.virtualScrollerRef},t.virtualScrollerOptions,{items:l.visibleOptions,style:{height:t.scrollHeight},tabindex:-1,disabled:l.virtualScrollerDisabled,pt:t.ptm("virtualScroller")}),te({content:b(function(a){var M=a.styleClass,C=a.contentRef,z=a.items,g=a.getItemOptions,ue=a.contentStyle,H=a.itemSize;return[v("ul",s({ref:function(h){return l.listRef(h,C)},id:t.$id+"_list",class:[t.cx("list"),M],style:ue,role:"listbox","aria-multiselectable":"true","aria-label":l.listAriaLabel},t.ptm("list")),[(o(!0),c(K,null,$(z,function(u,h){return o(),c(K,{key:l.getOptionRenderKey(u,l.getOptionIndex(h,g))},[l.isOptionGroup(u)?(o(),c("li",s({key:0,id:t.$id+"_"+l.getOptionIndex(h,g),style:{height:H?H+"px":void 0},class:t.cx("optionGroup"),role:"option"},{ref_for:!0},t.ptm("optionGroup")),[m(t.$slots,"optiongroup",{option:u.optionGroup,index:l.getOptionIndex(h,g)},function(){return[E(k(l.getOptionGroupLabel(u.optionGroup)),1)]})],16,dt)):ze((o(),c("li",s({key:1,id:t.$id+"_"+l.getOptionIndex(h,g),style:{height:H?H+"px":void 0},class:t.cx("option",{option:u,index:h,getItemOptions:g}),role:"option","aria-label":l.getOptionLabel(u),"aria-selected":l.isSelected(u),"aria-disabled":l.isOptionDisabled(u),"aria-setsize":l.ariaSetSize,"aria-posinset":l.getAriaPosInset(l.getOptionIndex(h,g)),onClick:function(U){return l.onOptionSelect(U,u,l.getOptionIndex(h,g),!0)},onMousemove:function(U){return l.onOptionMouseMove(U,l.getOptionIndex(h,g))}},{ref_for:!0},l.getCheckboxPTOptions(u,g,h,"option"),{"data-p-selected":l.isSelected(u),"data-p-focused":r.focusedOptionIndex===l.getOptionIndex(h,g),"data-p-disabled":l.isOptionDisabled(u)}),[I(f,{defaultValue:l.isSelected(u),binary:!0,tabindex:-1,variant:t.variant,unstyled:t.unstyled,pt:l.getCheckboxPTOptions(u,g,h,"pcOptionCheckbox"),formControl:{novalidate:!0}},{icon:b(function(L){return[t.$slots.optioncheckboxicon||t.$slots.itemcheckboxicon?(o(),O(D(t.$slots.optioncheckboxicon||t.$slots.itemcheckboxicon),{key:0,checked:L.checked,class:x(L.class)},null,8,["checked","class"])):L.checked?(o(),O(D(t.checkboxIcon?"span":"CheckIcon"),s({key:1,class:[L.class,J({},t.checkboxIcon,L.checked)]},{ref_for:!0},l.getCheckboxPTOptions(u,g,h,"pcOptionCheckbox.icon")),null,16,["class"])):y("",!0)]}),_:2},1032,["defaultValue","variant","unstyled","pt"]),m(t.$slots,"option",{option:u,selected:l.isSelected(u),index:l.getOptionIndex(h,g)},function(){return[v("span",s({ref_for:!0},t.ptm("optionLabel")),k(l.getOptionLabel(u)),17)]})],16,ut)),[[de]])],64)}),128)),r.filterValue&&(!z||z&&z.length===0)?(o(),c("li",s({key:0,class:t.cx("emptyMessage"),role:"option"},t.ptm("emptyMessage")),[m(t.$slots,"emptyfilter",{},function(){return[E(k(l.emptyFilterMessageText),1)]})],16)):!t.options||t.options&&t.options.length===0?(o(),c("li",s({key:1,class:t.cx("emptyMessage"),role:"option"},t.ptm("emptyMessage")),[m(t.$slots,"empty",{},function(){return[E(k(l.emptyMessageText),1)]})],16)):y("",!0)],16,at)]}),_:2},[t.$slots.loader?{name:"loader",fn:b(function(a){var M=a.options;return[m(t.$slots,"loader",{options:M})]}),key:"0"}:void 0]),1040,["items","style","disabled","pt"])],16),m(t.$slots,"footer",{value:t.d_value,options:l.visibleOptions}),!t.options||t.options&&t.options.length===0?(o(),c("span",s({key:1,role:"status","aria-live":"polite",class:"p-hidden-accessible"},t.ptm("hiddenEmptyMessage"),{"data-p-hidden-accessible":!0}),k(l.emptyMessageText),17)):y("",!0),v("span",s({role:"status","aria-live":"polite",class:"p-hidden-accessible"},t.ptm("hiddenSelectedMessage"),{"data-p-hidden-accessible":!0}),k(l.selectedMessageText),17),v("span",s({ref:"lastHiddenFocusableElementOnOverlay",role:"presentation","aria-hidden":"true",class:"p-hidden-accessible p-hidden-focusable",tabindex:0,onFocus:e[4]||(e[4]=function(){return l.onLastHiddenFocus&&l.onLastHiddenFocus.apply(l,arguments)})},t.ptm("hiddenLastFocusableEl"),{"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0}),null,16)],16,ot)):y("",!0)]}),_:3},16,["onEnter","onAfterEnter","onLeave","onAfterLeave"])]}),_:3},8,["appendTo"])],16,nt)}ne.render=ct;const pt=He({__name:"MultiSelect",setup(t,{expose:e}){e();const n={theme:Ge({root:`inline-flex cursor-pointer relative select-none rounded-md p-fluid:flex
        bg-surface-0 dark:bg-surface-950
        border border-surface-300 hover:border-surface-400 dark:border-surface-600 dark:hover:border-surface-700
        p-focus:border-primary
        p-filled:bg-surface-50 dark:p-filled:bg-surface-800
        p-invalid:border-red-400 dark:p-invalid:border-red-300
        p-disabled:bg-surface-200 p-disabled:text-surface-500 dark:p-disabled:bg-surface-700 dark:p-disabled:text-surface-400 p-disabled:pointer-events-none
        shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        transition-colors duration-200`,labelContainer:"overflow-hidden flex-auto",label:`flex items-center gap-1 whitespace-nowrap overflow-hidden text-ellipsis px-3 py-2 p-has-chip:py-1 p-has-chip:px-[0.375rem]
        text-surface-700 dark:text-surface-0 
        p-placeholder:text-surface-500 dark:p-placeholder:text-surface-400
        p-disabled:text-surface-500 dark:p-disabled:text-surface-400
        p-empty:overflow-hidden p-empty:opacity-0
        p-small:text-sm p-small:px-[0.625rem] p-small:py-[0.375rem]
        p-large:text-lg p-large:px-[0.875rem] p-large:py-[0.625rem]`,chipItem:"",pcChip:{root:`inline-flex items-center gap-2 px-3 py-1 rounded-sm
            bg-surface-100 dark:bg-surface-800
            text-surface-800 dark:text-surface-0
            has-[img]:pt-1 has-[img]:pb-1
            p-removable:pe-2`,removeIcon:"cursor-pointer text-base w-4 h-4 rounded-full text-surface-800 dark:text-surface-0"},dropdown:`flex items-center justify-center shrink-0 bg-transparent
        text-surface-400 w-10 rounded-e-md`,overlay:`absolute top-0 left-0 rounded-md p-portal-self:min-w-full 
        bg-surface-0 dark:bg-surface-900
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]`,header:"flex items-center pt-2 pb-1 px-4 gap-2",pcHeaderCheckbox:{root:"relative inline-flex select-none w-5 h-5 align-bottom",input:`peer cursor-pointer disabled:cursor-default appearance-none 
            absolute start-0 top-0 w-full h-full m-0 p-0 opacity-0 z-10
            border border-transparent rounded-xs`,box:`flex justify-center items-center rounded-sm w-5 h-5
            border border-surface-300 dark:border-surface-700
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            peer-enabled:peer-hover:border-surface-400 dark:peer-enabled:peer-hover:border-surface-600
            p-checked:border-primary p-checked:bg-primary p-checked:text-primary-contrast
            peer-enabled:peer-hover:p-checked:bg-primary-emphasis peer-enabled:peer-hover:p-checked:border-primary-emphasis
            peer-focus-visible:outline-1 peer-focus-visible:outline-offset-2 peer-focus-visible:outline-primary peer-focus-visible:outline 
            p-disabled:bg-surface-200 dark:p-disabled:bg-surface-400 p-disabled:border-surface-300 dark:p-disabled:border-surface-700 p-disabled:text-surface-700 dark:p-disabled:text-surface-400
            shadow-[0_1px_2px_0_rgba(18,18,23,0.05)] transition-colors duration-200`,icon:"text-sm w-[0.875rem] h-[0.875rem] transition-none"},pcFilterContainer:{root:"relative flex-auto"},pcFilter:{root:`w-full appearance-none rounded-md outline-hidden
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            placeholder:text-surface-500 dark:placeholder:text-surface-400
            border border-surface-300 dark:border-surface-700
            enabled:hover:border-surface-400 dark:enabled:hover:border-surface-600
            enabled:focus:border-primary
            disabled:bg-surface-200 disabled:text-surface-500
            dark:disabled:bg-surface-700 dark:disabled:text-surface-400
            ps-3 pe-10 py-2 p-fluid:w-full
            transition-colors duration-200 shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]`},pcFilterIconContainer:{root:"absolute top-1/2 -mt-2 leading-none end-3 z-1"},listContainer:"overflow-auto",virtualScroller:"",list:"m-0 p-1 list-none gap-[2px] flex flex-col",optionGroup:"m-0 px-3 py-2 bg-transparent text-surface-500 dark:text-surface-400 font-semibold",option:`cursor-pointer font-normal whitespace-nowrap relative overflow-hidden flex items-center gap-2 px-3 py-2
        rounded-sm text-surface-700 dark:text-surface-0 bg-transparent border-none
        p-focus:bg-surface-100 dark:p-focus:bg-surface-800 p-focus:text-surface-800 dark:p-focus:text-surface-0
        transition-colors duration-200`,optionLabel:"",pcOptionCheckbox:{root:"relative inline-flex select-none w-5 h-5 align-bottom",input:`peer cursor-pointer disabled:cursor-default appearance-none 
            absolute start-0 top-0 w-full h-full m-0 p-0 opacity-0 z-10
            border border-transparent rounded-xs`,box:`flex justify-center items-center rounded-sm w-5 h-5
            border border-surface-300 dark:border-surface-700
            bg-surface-0 dark:bg-surface-950
            text-surface-700 dark:text-surface-0
            peer-enabled:peer-hover:border-surface-400 dark:peer-enabled:peer-hover:border-surface-600
            p-checked:border-primary p-checked:bg-primary p-checked:text-primary-contrast
            peer-enabled:peer-hover:p-checked:bg-primary-emphasis peer-enabled:peer-hover:p-checked:border-primary-emphasis
            peer-focus-visible:outline-1 peer-focus-visible:outline-offset-2 peer-focus-visible:outline-primary peer-focus-visible:outline 
            p-disabled:bg-surface-200 dark:p-disabled:bg-surface-400 p-disabled:border-surface-300 dark:p-disabled:border-surface-700 p-disabled:text-surface-700 dark:p-disabled:text-surface-400
            shadow-[0_1px_2px_0_rgba(18,18,23,0.05)] transition-colors duration-200`,icon:"text-sm w-[0.875rem] h-[0.875rem] transition-none"},emptyMessage:"px-3 py-2",transition:{enterFromClass:"opacity-0 scale-y-75",enterActiveClass:"transition duration-120 ease-[cubic-bezier(0,0,0.2,1)]",leaveActiveClass:"transition-opacity duration-100 ease-linear",leaveToClass:"opacity-0"}}),get ChevronDownIcon(){return Q},get SearchIcon(){return X},get SpinnerIcon(){return Z},get TimesIcon(){return ee},get MultiSelect(){return ne},get ptViewMerge(){return je}};return Object.defineProperty(n,"__isScriptSetup",{enumerable:!1,value:!0}),n}});function ft(t,e,i,n,r,l){return o(),O(n.MultiSelect,{unstyled:"",pt:n.theme,ptOptions:{mergeProps:n.ptViewMerge}},te({dropdownicon:b(()=>[I(n.ChevronDownIcon)]),loadingicon:b(()=>[I(n.SpinnerIcon,{class:"animate-spin"})]),filtericon:b(()=>[I(n.SearchIcon,{class:"text-surface-400"})]),clearicon:b(({clearCallback:d})=>[I(n.TimesIcon,{onClick:d,class:"text-surface-400 absolute top-1/2 -mt-2 end-10"},null,8,["onClick"])]),_:2},[$(t.$slots,(d,p)=>({name:p,fn:b(f=>[m(t.$slots,p,ie(Be(f??{})))])}))]),1032,["pt","ptOptions"])}const Kt=Ue(pt,[["render",ft]]);export{Kt as M};

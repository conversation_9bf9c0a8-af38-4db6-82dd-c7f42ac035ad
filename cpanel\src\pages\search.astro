---
import Layout from '@/layouts/Layout.astro';
import SearchPageBoundary from '@/components/search/SearchPageBoundary.vue';
import { trpc } from '@/lib/trpc';

// Готовим initialData для поиска
const query = Astro.url.searchParams.get('q') || '';

// Единообразные константы запросов (как в каталоге)
const categoriesQuery = { where: { level: 0 }, orderBy: { name: 'asc' } };
const brandsQuery = { orderBy: { name: 'asc' } };

// Входные параметры для поиска
const filters = {
  name: query,
  limit: 50,
  offset: 0,
  orderBy: 'name',
  orderDir: 'asc'
};

// Загружаем начальные данные только если есть поисковый запрос
let initialSearchResults, initialBrands, initialCategories, initialAttributeTemplates;

try {
  const promises = [
    trpc.crud.brand.findMany.query(brandsQuery),
    trpc.crud.partCategory.findMany.query(categoriesQuery),
    trpc.crud.attributeTemplate.findMany.query({ take: 100 }),
  ];

  // Добавляем поиск только если есть запрос
  if (query) {
    promises.unshift(trpc.search.searchParts.query(filters));
  }

  const results = await Promise.all(promises);
  
  if (query) {
    [initialSearchResults, initialBrands, initialCategories, initialAttributeTemplates] = results;
  } else {
    initialSearchResults = null;
    [initialBrands, initialCategories, initialAttributeTemplates] = results;
  }
} catch (error) {
  console.error('Error loading search initial data:', error);
  initialSearchResults = null;
  initialBrands = [];
  initialCategories = [];
  initialAttributeTemplates = [];
}
---

<Layout title={query ? `Поиск: ${query}` : 'Поиск запчастей'}>
  <main class="container mx-auto px-4 py-8">
    <SearchPageBoundary
      client:load
      initialQuery={query}
      initialSearchResults={initialSearchResults}
      initialBrands={initialBrands}
      initialCategories={initialCategories}
      initialAttributeTemplates={initialAttributeTemplates}
      categoriesQuery={categoriesQuery}
      brandsQuery={brandsQuery}
    />
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>

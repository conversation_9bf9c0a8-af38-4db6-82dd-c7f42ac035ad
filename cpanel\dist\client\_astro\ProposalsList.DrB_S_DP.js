import G from"./Button.DrThv2lH.js";import R from"./Card.C4y0_bWr.js";import{S as U}from"./Select.CQBzSu6y.js";import{T as X}from"./Tag.DTFTku6q.js";import{V as Y}from"./ToggleSwitch.9ueDJKWv.js";import{M as H,u as J}from"./MatchingDetailsGrid.BT5Doi8q.js";import{u as W}from"./useTrpc.spLZjt2f.js";import{I as q}from"./Icon.By8t0-Wj.js";import{_ as K}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as Q,c as d,a,e as s,w as p,h as C,j as w,i as Z,k as $,o as r,F as ee,r as te,b as ae,l as A,g as B,f as ue}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as i,r as g}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./index.6ykohhwZ.js";import"./index.BaVCXmir.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.By2TJOuX.js";import"./index.COq_zjeV.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./InputText.DOJMNEP-.js";import"./trpc.BpyaUO08.js";import"./useErrorHandler.DVDazL16.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";/* empty css                       */const se=Q({__name:"ProposalsList",setup(S,{expose:u}){u();const{matching:m,loading:e}=W(),{getAccuracyLabel:V,getAccuracySeverity:b}=J(),v=g("PENDING"),t=[{label:"Ожидают",value:"PENDING"},{label:"Подтверждено",value:"APPROVED"},{label:"Отклонено",value:"REJECTED"},{label:"Инвалидировано",value:"INVALIDATED"}],x=g([]),f=g(0),c=g(20),F=[{label:"10",value:10},{label:"20",value:20},{label:"50",value:50}],l=g(0),T=g(!1),I=C(()=>f.value===0?0:l.value+1),z=C(()=>Math.min(l.value+c.value,f.value)),n=async()=>{const o=await m.listProposals({status:v.value,skip:l.value,take:c.value});o&&(x.value=o.items,f.value=o.total)};w(v,async()=>{l.value=0,await n()}),w(c,async()=>{l.value=0,await n()});const L=async()=>{l.value+c.value<f.value&&(l.value+=c.value,await n())},N=async()=>{l.value>0&&(l.value=Math.max(0,l.value-c.value),await n())},P=async(o,_,y)=>{await m.approveProposal({id:o,override:{accuracy:_,notes:y}})&&await n()},j=async o=>{await m.rejectProposal({id:o})&&await n()},O=async()=>{await m.generateProposals({take:50})&&await n()};Z(n);const h={matching:m,loading:e,getAccuracyLabel:V,getAccuracySeverity:b,status:v,statusOptions:t,items:x,total:f,take:c,takeOptions:F,skip:l,expandAll:T,pageStart:I,pageEnd:z,load:n,next:L,prev:N,approve:P,reject:j,generate:O,summarizeDetails:(o=[])=>{let _=0,y=0,D=0,k=0;for(const M of o){const E=String(M?.kind||"");E.includes("EXACT")&&_++,E.includes("NEAR")&&y++,E.includes("LEGACY")&&D++,E.includes("WITHIN_TOLERANCE")&&k++}return{total:o.length,exact:_,near:y,legacy:D,tol:k}},VButton:G,VCard:R,VSelect:U,VTag:X,VToggleSwitch:Y,MatchingDetailsGrid:H,Icon:q};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}}),le={class:"space-y-4"},oe={class:"flex flex-col gap-3 md:flex-row md:items-center md:justify-between"},ie={class:"flex items-center gap-2"},ne={class:"hidden md:block text-sm text-surface-500"},re={class:"flex items-center gap-3"},ce={class:"flex items-center gap-2"},de={class:"flex items-center gap-2"},me={key:0,class:"py-10 text-center text-surface-500"},ve={key:1,class:"py-10 text-center text-surface-500"},ge={key:2,class:"divide-y divide-surface-border"},fe={class:"md:col-span-1"},_e={class:"font-mono font-semibold"},pe=["href"],xe={class:"md:col-span-1"},ye={key:0,class:"text-xs text-surface-500 mt-1"},Ee={class:"md:col-span-1 flex items-center justify-end gap-2"},De={class:"md:col-span-3"},Ve=["open"],be={class:"text-xs text-surface-500 cursor-pointer flex items-center gap-2 select-none"},he={class:"text-surface-400"},ke={class:"hidden md:inline-flex items-center gap-1"},Ce={class:"mt-2"},we={class:"flex items-center justify-between gap-2"},Ae={class:"text-sm text-surface-500"},Be={class:"flex justify-end gap-2"};function Se(S,u,m,e,V,b){const v=$("tooltip");return r(),d("div",le,[a("div",oe,[a("div",ie,[s(e.VSelect,{modelValue:e.status,"onUpdate:modelValue":u[0]||(u[0]=t=>e.status=t),options:e.statusOptions,optionLabel:"label",optionValue:"value",class:"w-56"},null,8,["modelValue"]),s(e.VButton,{loading:e.loading,label:"Обновить",onClick:e.load},{icon:p(()=>[s(e.Icon,{name:"pi pi-refresh",class:"w-5 h-5"})]),_:1},8,["loading"]),a("div",ne," Показано "+i(e.pageStart)+"–"+i(e.pageEnd)+" из "+i(e.total),1)]),a("div",re,[a("div",ce,[u[3]||(u[3]=a("span",{class:"text-xs text-surface-500"},"На странице",-1)),s(e.VSelect,{modelValue:e.take,"onUpdate:modelValue":u[1]||(u[1]=t=>e.take=t),options:e.takeOptions,optionLabel:"label",optionValue:"value",class:"w-28"},null,8,["modelValue"])]),a("div",de,[u[4]||(u[4]=a("span",{class:"text-xs text-surface-500"},"Раскрыть детали",-1)),s(e.VToggleSwitch,{modelValue:e.expandAll,"onUpdate:modelValue":u[2]||(u[2]=t=>e.expandAll=t)},null,8,["modelValue"])]),s(e.VButton,{label:"Сгенерировать",severity:"secondary",onClick:e.generate},{icon:p(()=>[s(e.Icon,{name:"pi pi-cog",class:"w-5 h-5"})]),_:1})])]),s(e.VCard,null,{content:p(()=>[e.loading?(r(),d("div",me,"Загрузка...")):e.items.length===0?(r(),d("div",ve,"Нет предложений")):(r(),d("div",ge,[(r(!0),d(ee,null,te(e.items,t=>(r(),d("div",{key:t.id,class:"py-3 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"},[a("div",fe,[u[5]||(u[5]=a("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),a("div",_e,i(t.catalogItem.sku)+" — "+i(t.catalogItem.brand?.name),1),a("a",{href:`/admin/parts/${t.part.id}`,class:"text-xs text-surface-500"},"Группа: "+i(t.part.name||"#"+t.part.id),9,pe)]),a("div",xe,[u[6]||(u[6]=a("div",{class:"text-sm text-surface-500 mb-1"},"Предложение",-1)),s(e.VTag,{value:e.getAccuracyLabel(t.accuracySuggestion),severity:e.getAccuracySeverity(t.accuracySuggestion)},null,8,["value","severity"]),t.notesSuggestion?(r(),d("div",ye,i(t.notesSuggestion),1)):ae("",!0)]),a("div",Ee,[A((r(),B(e.VButton,{size:"small",label:"Отклонить",severity:"danger",outlined:"",onClick:x=>e.reject(t.id)},{icon:p(()=>[s(e.Icon,{name:"pi pi-times",class:"w-5 h-5"})]),_:2},1032,["onClick"])),[[v,"Отклонить предложение"]]),A((r(),B(e.VButton,{size:"small",label:"Подтвердить",onClick:x=>e.approve(t.id,t.accuracySuggestion,t.notesSuggestion)},{icon:p(()=>[s(e.Icon,{name:"pi pi-check",class:"w-5 h-5"})]),_:2},1032,["onClick"])),[[v,"Подтвердить и применить"]])]),a("div",De,[a("details",{open:e.expandAll},[a("summary",be,[u[7]||(u[7]=ue(" Детали сопоставления ")),a("span",he,"("+i(e.summarizeDetails(t.details).total)+")",1),a("span",ke,[s(e.VTag,{size:"small",value:`EXACT ${e.summarizeDetails(t.details).exact}`,severity:"success"},null,8,["value"]),s(e.VTag,{size:"small",value:`NEAR ${e.summarizeDetails(t.details).near}`,severity:"info"},null,8,["value"]),s(e.VTag,{size:"small",value:`TOL ${e.summarizeDetails(t.details).tol}`,severity:"info"},null,8,["value"]),s(e.VTag,{size:"small",value:`LEGACY ${e.summarizeDetails(t.details).legacy}`,severity:"warning"},null,8,["value"])])]),a("div",Ce,[s(e.MatchingDetailsGrid,{details:t.details||[]},null,8,["details"])])],8,Ve)])]))),128))]))]),_:1}),a("div",we,[a("div",Ae,"Показано "+i(e.pageStart)+"–"+i(e.pageEnd)+" из "+i(e.total),1),a("div",Be,[s(e.VButton,{disabled:e.skip===0,label:"Назад",severity:"secondary",outlined:"",onClick:e.prev},null,8,["disabled"]),s(e.VButton,{disabled:e.skip+e.take>=e.total,label:"Вперёд",severity:"secondary",outlined:"",onClick:e.next},null,8,["disabled"])])])])}const ct=K(se,[["render",Se]]);export{ct as default};

{"name": "server", "scripts": {"dev": "bun run --hot index.ts"}, "dependencies": {"@hono/trpc-server": "^0.4.0", "@prisma/client": "6.13.0", "@trpc/server": "11.4.4", "@zenstackhq/runtime": "2.18.0", "@zenstackhq/server": "^2.18.0", "better-auth": "^1.3.4", "hono": "^4.9.0", "superjson": "^2.2.2", "xlsx": "^0.18.5", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "22.17.0", "@zenstackhq/trpc": "^2.18.0", "prisma": "6.13.0", "typescript": "^5.9.2", "zenstack": "2.18.0", "@types/bun": "^1.2.19"}, "trustedDependencies": ["@prisma/client", "@prisma/engines", "prisma", "zenstack"]}
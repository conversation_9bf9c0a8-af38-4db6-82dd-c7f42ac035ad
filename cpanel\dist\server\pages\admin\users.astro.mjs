import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { T as Toast, M as Menu, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { defineComponent, useSSRContext, ref, reactive, watch, computed, mergeProps, withCtx, createTextVNode, toDisplayString, createVNode, createBlock, createCommentVNode, openBlock } from 'vue';
import { t as trpc } from '../../chunks/trpc_DApR3DD7.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import { M as MultiSelect } from '../../chunks/MultiSelect_uAHCsge7.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { D as DataTable, s as script } from '../../chunks/index_CxapvcaX.mjs';
import { D as Dialog } from '../../chunks/Dialog_DqmfICId.mjs';
import { P as Password } from '../../chunks/Password_BQyeKHib.mjs';
import { T as Tag } from '../../chunks/Tag_B6nH2bAR.mjs';
import { u as useToast, _ as _export_sfc } from '../../chunks/ClientRouter_avhRMbqw.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "UsersManager",
  setup(__props, { expose: __expose }) {
    __expose();
    const toast = useToast();
    const page = ref(1);
    const pageSize = ref(20);
    const total = ref(0);
    const isLoading = ref(false);
    const query = reactive({
      search: "",
      sortBy: "createdAt",
      sortDirection: "desc"
    });
    const sortOptions = [
      { label: "\u0421\u043E\u0437\u0434\u0430\u043D", value: "createdAt" },
      { label: "Email", value: "email" },
      { label: "\u0418\u043C\u044F", value: "name" }
    ];
    const directionOptions = [
      { label: "\u041F\u043E \u0443\u0431\u044B\u0432\u0430\u043D\u0438\u044E", value: "desc" },
      { label: "\u041F\u043E \u0432\u043E\u0437\u0440\u0430\u0441\u0442\u0430\u043D\u0438\u044E", value: "asc" }
    ];
    const users = ref([]);
    const roleEdits = reactive({});
    const roleOptions = [
      { label: "USER", value: "USER" },
      { label: "SHOP", value: "SHOP" },
      { label: "ADMIN", value: "ADMIN" }
    ];
    function formatDate(value) {
      return value ? new Date(value).toLocaleString() : "";
    }
    async function fetchUsers() {
      isLoading.value = true;
      try {
        const result = await trpc.admin.listUsers.query({
          search: query.search || void 0,
          sortBy: query.sortBy,
          sortDirection: query.sortDirection,
          limit: pageSize.value,
          offset: (page.value - 1) * pageSize.value
        });
        users.value = result.users || result.data || [];
        total.value = result.total || result.count || users.value.length;
        roleEditsReset();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0435\u0439");
      } finally {
        isLoading.value = false;
      }
    }
    function roleEditsReset() {
      for (const u of users.value) {
        roleEdits[u.id] = u.role;
      }
    }
    async function saveRole(user) {
      const newRole = roleEdits[user.id];
      if (newRole === user.role) return;
      try {
        await trpc.admin.setUserRole.mutate({ userId: user.id, role: newRole });
        toast.success("\u0420\u043E\u043B\u044C \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0430");
        await fetchUsers();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0440\u043E\u043B\u044C");
        roleEdits[user.id] = user.role;
      }
    }
    async function toggleBan(user) {
      try {
        if (user.isBanned) {
          await trpc.admin.unbanUser.mutate({ userId: user.id });
          toast.success("\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C \u0440\u0430\u0437\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u043D");
        } else {
          await trpc.admin.banUser.mutate({ userId: user.id });
          toast.success("\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C \u0437\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u043D");
        }
        await fetchUsers();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0441\u0442\u0430\u0442\u0443\u0441 \u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u043A\u0438");
      }
    }
    async function impersonate(user) {
      try {
        await trpc.admin.impersonateUser.mutate({ userId: user.id });
        toast.success("\u0417\u0430\u043F\u0443\u0449\u0435\u043D\u0430 \u0438\u043C\u043F\u0435\u0440\u0441\u043E\u043D\u0430\u0446\u0438\u044F \u2014 \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u0435 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0443");
        window.location.href = "/admin";
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0438\u043C\u043F\u0435\u0440\u0441\u043E\u043D\u0430\u0446\u0438\u044E");
      }
    }
    async function resetPassword(user) {
      try {
        const newPassword = prompt("\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u043E\u0432\u044B\u0439 \u043F\u0430\u0440\u043E\u043B\u044C (\u043C\u0438\u043D. 6 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432)");
        if (!newPassword) return;
        await trpc.admin.resetUserPassword.mutate({ userId: user.id, newPassword });
        toast.success("\u041F\u0430\u0440\u043E\u043B\u044C \u0441\u0431\u0440\u043E\u0448\u0435\u043D");
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u043F\u0430\u0440\u043E\u043B\u044C");
      }
    }
    async function removeUser(user) {
      if (!confirm("\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F \u0431\u0435\u0437\u0432\u043E\u0437\u0432\u0440\u0430\u0442\u043D\u043E?")) return;
      try {
        await trpc.admin.removeUser.mutate({ userId: user.id });
        toast.success("\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C \u0443\u0434\u0430\u043B\u0451\u043D");
        await fetchUsers();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F");
      }
    }
    function onPage(event) {
      page.value = Math.floor(event.first / event.rows) + 1;
      pageSize.value = event.rows;
      fetchUsers();
    }
    function refetch() {
      page.value = 1;
      fetchUsers();
    }
    const createVisible = ref(false);
    const actionLoading = ref(false);
    const createForm = reactive({ name: "", email: "", password: "", role: "USER" });
    function openCreate() {
      createVisible.value = true;
    }
    async function createUser() {
      if (!createForm.email || !createForm.password) {
        toast.error("Email \u0438 \u043F\u0430\u0440\u043E\u043B\u044C \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B");
        return;
      }
      actionLoading.value = true;
      try {
        await trpc.admin.createUser.mutate({ ...createForm, emailVerified: true });
        toast.success("\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C \u0441\u043E\u0437\u0434\u0430\u043D");
        createVisible.value = false;
        createForm.name = "";
        createForm.email = "";
        createForm.password = "";
        createForm.role = "USER";
        await fetchUsers();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F");
      } finally {
        actionLoading.value = false;
      }
    }
    watch(() => [page.value, pageSize.value], fetchUsers);
    fetchUsers();
    loadAccessCatalog();
    const sessionsVisible = ref(false);
    const sessionsLoading = ref(false);
    const sessionsUser = ref(null);
    const sessions = ref([]);
    async function openSessions(user) {
      sessionsUser.value = user;
      sessionsVisible.value = true;
      sessionsLoading.value = true;
      try {
        const res = await trpc.admin.listUserSessions.query({ userId: user.id });
        sessions.value = res?.sessions || res?.data || [];
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0441\u0435\u0441\u0441\u0438\u0438");
      } finally {
        sessionsLoading.value = false;
      }
    }
    const aclPanelVisible = ref(false);
    const aclForm = reactive({ json: "" });
    const aclResult = ref(null);
    const aclSelectedUser = ref(null);
    const aclPermissions = reactive({});
    const aclSelectedResource = ref(null);
    const aclSelectedActions = ref([]);
    const resourceOptions = ref([]);
    const actionMap = ref({});
    const availableActions = computed(() => {
      const res = aclSelectedResource.value;
      if (!res) return [];
      return (actionMap.value[res] || []).map((a) => ({ label: a, value: a }));
    });
    async function loadAccessCatalog() {
      try {
        const catalog = await trpc.access.list.query();
        actionMap.value = catalog;
        resourceOptions.value = Object.keys(actionMap.value).map((k) => ({ label: k, value: k }));
      } catch (e) {
      }
    }
    const canCheckAcl = computed(() => {
      if (Object.keys(aclPermissions).length > 0) return true;
      try {
        const obj = JSON.parse(aclForm.json || "{}");
        return obj && typeof obj === "object" && Object.keys(obj).length > 0;
      } catch {
        return false;
      }
    });
    async function checkPermissions() {
      try {
        let permissions;
        if (Object.keys(aclPermissions).length > 0) {
          permissions = aclPermissions;
        } else {
          const obj = JSON.parse(aclForm.json || "{}");
          if (!obj || Object.keys(obj).length === 0) {
            toast.warn("\u0414\u043E\u0431\u0430\u0432\u044C\u0442\u0435 \u0445\u043E\u0442\u044F \u0431\u044B \u043E\u0434\u0438\u043D permission");
            return;
          }
          permissions = obj;
        }
        const res = await trpc.admin.hasPermission.query({
          permissions,
          userId: aclForm.userId || void 0,
          role: aclForm.role || void 0
        });
        aclResult.value = res;
        toast.success("\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430 \u0432\u044B\u043F\u043E\u043B\u043D\u0435\u043D\u0430");
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435\u043A\u043E\u0440\u0440\u0435\u043A\u0442\u043D\u044B\u0439 JSON \u0438\u043B\u0438 \u043E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u043E\u0432\u0435\u0440\u043A\u0438 \u043F\u0440\u0430\u0432");
      }
    }
    function openAclPanel(user) {
      aclPanelVisible.value = true;
      aclSelectedUser.value = user;
      aclForm.userId = user.id;
      aclForm.role = user.role;
    }
    function aclAddPermission() {
      const res = aclSelectedResource.value;
      const actions = aclSelectedActions.value;
      if (!res || actions.length === 0) return;
      aclPermissions[res] = [...new Set(actions)];
      aclSelectedResource.value = null;
      aclSelectedActions.value = [];
    }
    function aclClearPermissions() {
      for (const k of Object.keys(aclPermissions)) delete aclPermissions[k];
    }
    const rowMenus = ref({});
    function setRowMenuRef(id) {
      return (el) => {
        if (el) rowMenus.value[id] = el;
      };
    }
    function toggleRowMenu(event, id) {
      rowMenus.value[id]?.toggle(event);
    }
    function getRowMenuItems(user) {
      return [
        { label: "\u0418\u043C\u043F\u0435\u0440\u0441\u043E\u043D\u0430\u0446\u0438\u044F", icon: "pi pi-user", command: () => impersonate(user) },
        {
          label: user.banned ? "\u0420\u0430\u0437\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C" : "\u0417\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
          icon: user.banned ? "pi pi-unlock" : "pi pi-lock",
          command: () => toggleBan(user)
        },
        { label: "\u0421\u0431\u0440\u043E\u0441 \u043F\u0430\u0440\u043E\u043B\u044F", icon: "pi pi-key", command: () => resetPassword(user) },
        { label: "\u0421\u0435\u0441\u0441\u0438\u0438", icon: "pi pi-clock", command: () => openSessions(user) },
        { label: "\u041F\u0440\u0430\u0432\u0430", icon: "pi pi-shield", command: () => openAclPanel(user) },
        { separator: true },
        { label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C", icon: "pi pi-trash", command: () => removeUser(user) }
      ];
    }
    async function revokeSession(session) {
      try {
        await trpc.admin.revokeUserSession.mutate({ sessionToken: session.token });
        toast.success("\u0421\u0435\u0441\u0441\u0438\u044F \u043E\u0442\u043E\u0437\u0432\u0430\u043D\u0430");
        if (sessionsUser.value) await openSessions(sessionsUser.value);
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043E\u0442\u043E\u0437\u0432\u0430\u0442\u044C \u0441\u0435\u0441\u0441\u0438\u044E");
      }
    }
    async function revokeAllSessions() {
      if (!sessionsUser.value) return;
      try {
        await trpc.admin.revokeAllUserSessions.mutate({ userId: sessionsUser.value.id });
        toast.success("\u0412\u0441\u0435 \u0441\u0435\u0441\u0441\u0438\u0438 \u043E\u0442\u043E\u0437\u0432\u0430\u043D\u044B");
        await openSessions(sessionsUser.value);
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043E\u0442\u043E\u0437\u0432\u0430\u0442\u044C \u0432\u0441\u0435 \u0441\u0435\u0441\u0441\u0438\u0438");
      }
    }
    const __returned__ = { toast, page, pageSize, total, isLoading, query, sortOptions, directionOptions, users, roleEdits, roleOptions, formatDate, fetchUsers, roleEditsReset, saveRole, toggleBan, impersonate, resetPassword, removeUser, onPage, refetch, createVisible, actionLoading, createForm, openCreate, createUser, sessionsVisible, sessionsLoading, sessionsUser, sessions, openSessions, aclPanelVisible, aclForm, aclResult, aclSelectedUser, aclPermissions, aclSelectedResource, aclSelectedActions, resourceOptions, actionMap, availableActions, loadAccessCatalog, canCheckAcl, checkPermissions, openAclPanel, aclAddPermission, aclClearPermissions, rowMenus, setRowMenuRef, toggleRowMenu, getRowMenuItems, revokeSession, revokeAllSessions, InputText, Select, MultiSelect, Button, Menu, DataTable, get Column() {
      return script;
    }, Dialog, Password, Tag, Toast };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full max-w-6xl" }, _attrs))}><div class="flex items-center justify-between mb-4"><h2 class="text-xl font-semibold">\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438</h2><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["InputText"], {
    modelValue: $setup.query.search,
    "onUpdate:modelValue": ($event) => $setup.query.search = $event,
    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E email/\u0438\u043C\u0435\u043D\u0438",
    class: "w-64"
  }, null, _parent));
  _push(ssrRenderComponent($setup["Select"], {
    modelValue: $setup.query.sortBy,
    "onUpdate:modelValue": ($event) => $setup.query.sortBy = $event,
    options: $setup.sortOptions,
    optionLabel: "label",
    optionValue: "value",
    class: "w-44"
  }, null, _parent));
  _push(ssrRenderComponent($setup["Select"], {
    modelValue: $setup.query.sortDirection,
    "onUpdate:modelValue": ($event) => $setup.query.sortDirection = $event,
    options: $setup.directionOptions,
    optionLabel: "label",
    optionValue: "value",
    class: "w-36"
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",
    onClick: ($event) => $setup.refetch()
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
    icon: "pi pi-plus",
    severity: "success",
    onClick: ($event) => $setup.openCreate()
  }, null, _parent));
  _push(ssrRenderComponent($setup["Button"], {
    label: "\u041F\u0440\u043E\u0432\u0435\u0440\u043A\u0430 \u043F\u0440\u0430\u0432",
    severity: "secondary",
    onClick: ($event) => _ctx.aclVisible = true
  }, null, _parent));
  _push(`</div></div>`);
  _push(ssrRenderComponent($setup["DataTable"], {
    value: $setup.users,
    loading: $setup.isLoading,
    dataKey: "id",
    class: "w-full",
    rows: $setup.pageSize,
    paginator: true,
    totalRecords: $setup.total,
    onPage: $setup.onPage
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          field: "createdAt",
          header: "\u0421\u043E\u0437\u0434\u0430\u043D",
          sortable: false
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.formatDate(data.createdAt))}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.formatDate(data.createdAt)), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "email",
          header: "Email"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "name",
          header: "\u0418\u043C\u044F"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "role",
          header: "\u0420\u043E\u043B\u044C"
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["Select"], {
                modelValue: $setup.roleEdits[data.id],
                "onUpdate:modelValue": ($event) => $setup.roleEdits[data.id] = $event,
                options: $setup.roleOptions,
                class: "w-36",
                onChange: ($event) => $setup.saveRole(data)
              }, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["Select"], {
                  modelValue: $setup.roleEdits[data.id],
                  "onUpdate:modelValue": ($event) => $setup.roleEdits[data.id] = $event,
                  options: $setup.roleOptions,
                  class: "w-36",
                  onChange: ($event) => $setup.saveRole(data)
                }, null, 8, ["modelValue", "onUpdate:modelValue", "onChange"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0421\u0442\u0430\u0442\u0443\u0441" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["Tag"], {
                value: data.banned ? "\u0417\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u043D" : "\u0410\u043A\u0442\u0438\u0432\u0435\u043D",
                severity: data.banned ? "danger" : "success"
              }, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["Tag"], {
                  value: data.banned ? "\u0417\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u043D" : "\u0410\u043A\u0442\u0438\u0432\u0435\u043D",
                  severity: data.banned ? "danger" : "success"
                }, null, 8, ["value", "severity"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="relative inline-flex"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Button"], {
                size: "small",
                label: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                icon: "pi pi-ellipsis-v",
                onClick: ($event) => $setup.toggleRowMenu($event, data.id)
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Menu"], {
                model: $setup.getRowMenuItems(data),
                ref: $setup.setRowMenuRef(data.id),
                popup: true
              }, null, _parent3, _scopeId2));
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "relative inline-flex" }, [
                  createVNode($setup["Button"], {
                    size: "small",
                    label: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                    icon: "pi pi-ellipsis-v",
                    onClick: ($event) => $setup.toggleRowMenu($event, data.id)
                  }, null, 8, ["onClick"]),
                  createVNode($setup["Menu"], {
                    model: $setup.getRowMenuItems(data),
                    ref: $setup.setRowMenuRef(data.id),
                    popup: true
                  }, null, 8, ["model"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            field: "createdAt",
            header: "\u0421\u043E\u0437\u0434\u0430\u043D",
            sortable: false
          }, {
            body: withCtx(({ data }) => [
              createTextVNode(toDisplayString($setup.formatDate(data.createdAt)), 1)
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "email",
            header: "Email"
          }),
          createVNode($setup["Column"], {
            field: "name",
            header: "\u0418\u043C\u044F"
          }),
          createVNode($setup["Column"], {
            field: "role",
            header: "\u0420\u043E\u043B\u044C"
          }, {
            body: withCtx(({ data }) => [
              createVNode($setup["Select"], {
                modelValue: $setup.roleEdits[data.id],
                "onUpdate:modelValue": ($event) => $setup.roleEdits[data.id] = $event,
                options: $setup.roleOptions,
                class: "w-36",
                onChange: ($event) => $setup.saveRole(data)
              }, null, 8, ["modelValue", "onUpdate:modelValue", "onChange"])
            ]),
            _: 1
          }),
          createVNode($setup["Column"], { header: "\u0421\u0442\u0430\u0442\u0443\u0441" }, {
            body: withCtx(({ data }) => [
              createVNode($setup["Tag"], {
                value: data.banned ? "\u0417\u0430\u0431\u043B\u043E\u043A\u0438\u0440\u043E\u0432\u0430\u043D" : "\u0410\u043A\u0442\u0438\u0432\u0435\u043D",
                severity: data.banned ? "danger" : "success"
              }, null, 8, ["value", "severity"])
            ]),
            _: 1
          }),
          createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "relative inline-flex" }, [
                createVNode($setup["Button"], {
                  size: "small",
                  label: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                  icon: "pi pi-ellipsis-v",
                  onClick: ($event) => $setup.toggleRowMenu($event, data.id)
                }, null, 8, ["onClick"]),
                createVNode($setup["Menu"], {
                  model: $setup.getRowMenuItems(data),
                  ref: $setup.setRowMenuRef(data.id),
                  popup: true
                }, null, 8, ["model"])
              ])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Dialog"], {
    visible: $setup.sessionsVisible,
    "onUpdate:visible": ($event) => $setup.sessionsVisible = $event,
    header: "\u0421\u0435\u0441\u0441\u0438\u0438 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F",
    modal: true,
    style: { width: "720px" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.sessionsUser) {
          _push2(`<div class="mb-3 text-sm text-surface-600"${_scopeId}>${ssrInterpolate($setup.sessionsUser.email)}</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(ssrRenderComponent($setup["DataTable"], {
          value: $setup.sessions,
          loading: $setup.sessionsLoading,
          dataKey: "token",
          rows: 10,
          paginator: true
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["Column"], {
                field: "createdAt",
                header: "\u0421\u043E\u0437\u0434\u0430\u043D\u0430"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`${ssrInterpolate($setup.formatDate(data.createdAt))}`);
                  } else {
                    return [
                      createTextVNode(toDisplayString($setup.formatDate(data.createdAt)), 1)
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], {
                field: "expiresAt",
                header: "\u0418\u0441\u0442\u0435\u043A\u0430\u0435\u0442"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`${ssrInterpolate($setup.formatDate(data.expiresAt))}`);
                  } else {
                    return [
                      createTextVNode(toDisplayString($setup.formatDate(data.expiresAt)), 1)
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], {
                field: "ipAddress",
                header: "IP"
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], {
                field: "userAgent",
                header: "UA"
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["Button"], {
                      size: "small",
                      label: "\u041E\u0442\u043E\u0437\u0432\u0430\u0442\u044C",
                      severity: "warning",
                      onClick: ($event) => $setup.revokeSession(data)
                    }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["Button"], {
                        size: "small",
                        label: "\u041E\u0442\u043E\u0437\u0432\u0430\u0442\u044C",
                        severity: "warning",
                        onClick: ($event) => $setup.revokeSession(data)
                      }, null, 8, ["onClick"])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["Column"], {
                  field: "createdAt",
                  header: "\u0421\u043E\u0437\u0434\u0430\u043D\u0430"
                }, {
                  body: withCtx(({ data }) => [
                    createTextVNode(toDisplayString($setup.formatDate(data.createdAt)), 1)
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  field: "expiresAt",
                  header: "\u0418\u0441\u0442\u0435\u043A\u0430\u0435\u0442"
                }, {
                  body: withCtx(({ data }) => [
                    createTextVNode(toDisplayString($setup.formatDate(data.expiresAt)), 1)
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  field: "ipAddress",
                  header: "IP"
                }),
                createVNode($setup["Column"], {
                  field: "userAgent",
                  header: "UA"
                }),
                createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                  body: withCtx(({ data }) => [
                    createVNode($setup["Button"], {
                      size: "small",
                      label: "\u041E\u0442\u043E\u0437\u0432\u0430\u0442\u044C",
                      severity: "warning",
                      onClick: ($event) => $setup.revokeSession(data)
                    }, null, 8, ["onClick"])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`<div class="flex justify-end gap-2 mt-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u041E\u0442\u043E\u0437\u0432\u0430\u0442\u044C \u0432\u0441\u0435",
          severity: "warning",
          onClick: ($event) => $setup.revokeAllSessions()
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C",
          severity: "secondary",
          onClick: ($event) => $setup.sessionsVisible = false
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          $setup.sessionsUser ? (openBlock(), createBlock("div", {
            key: 0,
            class: "mb-3 text-sm text-surface-600"
          }, toDisplayString($setup.sessionsUser.email), 1)) : createCommentVNode("", true),
          createVNode($setup["DataTable"], {
            value: $setup.sessions,
            loading: $setup.sessionsLoading,
            dataKey: "token",
            rows: 10,
            paginator: true
          }, {
            default: withCtx(() => [
              createVNode($setup["Column"], {
                field: "createdAt",
                header: "\u0421\u043E\u0437\u0434\u0430\u043D\u0430"
              }, {
                body: withCtx(({ data }) => [
                  createTextVNode(toDisplayString($setup.formatDate(data.createdAt)), 1)
                ]),
                _: 1
              }),
              createVNode($setup["Column"], {
                field: "expiresAt",
                header: "\u0418\u0441\u0442\u0435\u043A\u0430\u0435\u0442"
              }, {
                body: withCtx(({ data }) => [
                  createTextVNode(toDisplayString($setup.formatDate(data.expiresAt)), 1)
                ]),
                _: 1
              }),
              createVNode($setup["Column"], {
                field: "ipAddress",
                header: "IP"
              }),
              createVNode($setup["Column"], {
                field: "userAgent",
                header: "UA"
              }),
              createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
                body: withCtx(({ data }) => [
                  createVNode($setup["Button"], {
                    size: "small",
                    label: "\u041E\u0442\u043E\u0437\u0432\u0430\u0442\u044C",
                    severity: "warning",
                    onClick: ($event) => $setup.revokeSession(data)
                  }, null, 8, ["onClick"])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, ["value", "loading"]),
          createVNode("div", { class: "flex justify-end gap-2 mt-3" }, [
            createVNode($setup["Button"], {
              label: "\u041E\u0442\u043E\u0437\u0432\u0430\u0442\u044C \u0432\u0441\u0435",
              severity: "warning",
              onClick: ($event) => $setup.revokeAllSessions()
            }, null, 8, ["onClick"]),
            createVNode($setup["Button"], {
              label: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C",
              severity: "secondary",
              onClick: ($event) => $setup.sessionsVisible = false
            }, null, 8, ["onClick"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Dialog"], {
    visible: $setup.aclPanelVisible,
    "onUpdate:visible": ($event) => $setup.aclPanelVisible = $event,
    header: "\u041A\u043E\u043D\u0442\u0440\u043E\u043B\u044C \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    modal: true,
    style: { width: "720px" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.aclSelectedUser) {
          _push2(`<div class="text-sm mb-3"${_scopeId}> \u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C: <b${_scopeId}>${ssrInterpolate($setup.aclSelectedUser.email)}</b> (${ssrInterpolate($setup.aclSelectedUser.name || "\u0431\u0435\u0437 \u0438\u043C\u0435\u043D\u0438")}) </div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div class="grid grid-cols-2 gap-3 mb-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.aclForm.userId,
          "onUpdate:modelValue": ($event) => $setup.aclForm.userId = $event,
          placeholder: "User ID (\u043E\u043F\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E)"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.aclForm.role,
          "onUpdate:modelValue": ($event) => $setup.aclForm.role = $event,
          placeholder: "Role (\u043E\u043F\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E)"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="grid grid-cols-2 gap-3 mb-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Select"], {
          modelValue: $setup.aclSelectedResource,
          "onUpdate:modelValue": ($event) => $setup.aclSelectedResource = $event,
          options: $setup.resourceOptions,
          optionLabel: "label",
          optionValue: "value",
          placeholder: "\u0420\u0435\u0441\u0443\u0440\u0441",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["MultiSelect"], {
          modelValue: $setup.aclSelectedActions,
          "onUpdate:modelValue": ($event) => $setup.aclSelectedActions = $event,
          options: $setup.availableActions,
          optionLabel: "label",
          optionValue: "value",
          placeholder: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex gap-2 mb-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C permission",
          onClick: ($event) => $setup.aclAddPermission()
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",
          severity: "secondary",
          onClick: ($event) => $setup.aclClearPermissions()
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
        if (Object.keys($setup.aclPermissions).length) {
          _push2(`<div class="font-mono text-xs bg-[--color-muted]/10 p-3 rounded mb-3"${_scopeId}>${ssrInterpolate($setup.aclPermissions)}</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div class="flex justify-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C",
          onClick: ($event) => $setup.checkPermissions(),
          disabled: !$setup.canCheckAcl
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C",
          severity: "secondary",
          onClick: ($event) => $setup.aclPanelVisible = false
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
        if ($setup.aclResult) {
          _push2(`<div class="mt-4 text-sm"${_scopeId}><pre${_scopeId}>${ssrInterpolate(JSON.stringify($setup.aclResult, null, 2))}</pre></div>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $setup.aclSelectedUser ? (openBlock(), createBlock("div", {
            key: 0,
            class: "text-sm mb-3"
          }, [
            createTextVNode(" \u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C: "),
            createVNode("b", null, toDisplayString($setup.aclSelectedUser.email), 1),
            createTextVNode(" (" + toDisplayString($setup.aclSelectedUser.name || "\u0431\u0435\u0437 \u0438\u043C\u0435\u043D\u0438") + ") ", 1)
          ])) : createCommentVNode("", true),
          createVNode("div", { class: "grid grid-cols-2 gap-3 mb-3" }, [
            createVNode($setup["InputText"], {
              modelValue: $setup.aclForm.userId,
              "onUpdate:modelValue": ($event) => $setup.aclForm.userId = $event,
              placeholder: "User ID (\u043E\u043F\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E)"
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode($setup["InputText"], {
              modelValue: $setup.aclForm.role,
              "onUpdate:modelValue": ($event) => $setup.aclForm.role = $event,
              placeholder: "Role (\u043E\u043F\u0446\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u043E)"
            }, null, 8, ["modelValue", "onUpdate:modelValue"])
          ]),
          createVNode("div", { class: "grid grid-cols-2 gap-3 mb-3" }, [
            createVNode($setup["Select"], {
              modelValue: $setup.aclSelectedResource,
              "onUpdate:modelValue": ($event) => $setup.aclSelectedResource = $event,
              options: $setup.resourceOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "\u0420\u0435\u0441\u0443\u0440\u0441",
              class: "w-full"
            }, null, 8, ["modelValue", "onUpdate:modelValue", "options"]),
            createVNode($setup["MultiSelect"], {
              modelValue: $setup.aclSelectedActions,
              "onUpdate:modelValue": ($event) => $setup.aclSelectedActions = $event,
              options: $setup.availableActions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
              class: "w-full"
            }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
          ]),
          createVNode("div", { class: "flex gap-2 mb-3" }, [
            createVNode($setup["Button"], {
              label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C permission",
              onClick: ($event) => $setup.aclAddPermission()
            }, null, 8, ["onClick"]),
            createVNode($setup["Button"], {
              label: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",
              severity: "secondary",
              onClick: ($event) => $setup.aclClearPermissions()
            }, null, 8, ["onClick"])
          ]),
          Object.keys($setup.aclPermissions).length ? (openBlock(), createBlock("div", {
            key: 1,
            class: "font-mono text-xs bg-[--color-muted]/10 p-3 rounded mb-3"
          }, toDisplayString($setup.aclPermissions), 1)) : createCommentVNode("", true),
          createVNode("div", { class: "flex justify-end gap-2" }, [
            createVNode($setup["Button"], {
              label: "\u041F\u0440\u043E\u0432\u0435\u0440\u0438\u0442\u044C",
              onClick: ($event) => $setup.checkPermissions(),
              disabled: !$setup.canCheckAcl
            }, null, 8, ["onClick", "disabled"]),
            createVNode($setup["Button"], {
              label: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C",
              severity: "secondary",
              onClick: ($event) => $setup.aclPanelVisible = false
            }, null, 8, ["onClick"])
          ]),
          $setup.aclResult ? (openBlock(), createBlock("div", {
            key: 2,
            class: "mt-4 text-sm"
          }, [
            createVNode("pre", null, toDisplayString(JSON.stringify($setup.aclResult, null, 2)), 1)
          ])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Dialog"], {
    visible: $setup.createVisible,
    "onUpdate:visible": ($event) => $setup.createVisible = $event,
    header: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F",
    modal: true,
    style: { width: "480px" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex flex-col gap-3"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.createForm.name,
          "onUpdate:modelValue": ($event) => $setup.createForm.name = $event,
          placeholder: "\u0418\u043C\u044F"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.createForm.email,
          "onUpdate:modelValue": ($event) => $setup.createForm.email = $event,
          placeholder: "Email"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Password"], {
          modelValue: $setup.createForm.password,
          "onUpdate:modelValue": ($event) => $setup.createForm.password = $event,
          placeholder: "\u041F\u0430\u0440\u043E\u043B\u044C",
          toggleMask: ""
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Select"], {
          modelValue: $setup.createForm.role,
          "onUpdate:modelValue": ($event) => $setup.createForm.role = $event,
          options: $setup.roleOptions,
          class: "w-36"
        }, null, _parent2, _scopeId));
        _push2(`<div class="flex justify-end gap-2 mt-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: ($event) => $setup.createVisible = false
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
          severity: "success",
          onClick: ($event) => $setup.createUser(),
          loading: $setup.actionLoading
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "flex flex-col gap-3" }, [
            createVNode($setup["InputText"], {
              modelValue: $setup.createForm.name,
              "onUpdate:modelValue": ($event) => $setup.createForm.name = $event,
              placeholder: "\u0418\u043C\u044F"
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode($setup["InputText"], {
              modelValue: $setup.createForm.email,
              "onUpdate:modelValue": ($event) => $setup.createForm.email = $event,
              placeholder: "Email"
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode($setup["Password"], {
              modelValue: $setup.createForm.password,
              "onUpdate:modelValue": ($event) => $setup.createForm.password = $event,
              placeholder: "\u041F\u0430\u0440\u043E\u043B\u044C",
              toggleMask: ""
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode($setup["Select"], {
              modelValue: $setup.createForm.role,
              "onUpdate:modelValue": ($event) => $setup.createForm.role = $event,
              options: $setup.roleOptions,
              class: "w-36"
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode("div", { class: "flex justify-end gap-2 mt-2" }, [
              createVNode($setup["Button"], {
                label: "\u041E\u0442\u043C\u0435\u043D\u0430",
                severity: "secondary",
                onClick: ($event) => $setup.createVisible = false
              }, null, 8, ["onClick"]),
              createVNode($setup["Button"], {
                label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
                severity: "success",
                onClick: ($event) => $setup.createUser(),
                loading: $setup.actionLoading
              }, null, 8, ["onClick", "loading"])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Toast"], null, null, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/users/UsersManager.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const UsersManager = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "UsersManager", UsersManager, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/admin/users/UsersManager.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/users/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/users/index.astro";
const $$url = "/admin/users";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

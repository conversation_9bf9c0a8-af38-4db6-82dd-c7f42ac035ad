<template>
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <svg
      class="absolute inset-0 h-full w-full"
      :class="className"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          :id="id"
          :width="width"
          :height="height"
          patternUnits="userSpaceOnUse"
        >
          <path
            :d="`M.5 ${height}V.5H${width}`"
            fill="none"
            :stroke="stroke"
            :stroke-width="strokeWidth"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" :fill="`url(#${id})`" />
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  width?: number
  height?: number
  x?: number
  y?: number
  stroke?: string
  strokeWidth?: number
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: 40,
  height: 40,
  x: -1,
  y: -1,
  stroke: 'currentColor',
  strokeWidth: 1,
  className: ''
})

const id = computed(() => `grid-pattern-${Math.random().toString(36).substr(2, 9)}`)
</script>
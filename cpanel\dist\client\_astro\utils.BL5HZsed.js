import{t as f}from"./bundle-mjs.D6B6e0vX.js";function i(t){var r,n,e="";if(typeof t=="string"||typeof t=="number")e+=t;else if(typeof t=="object")if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=i(t[r]))&&(e&&(e+=" "),e+=n)}else for(n in t)t[n]&&(e&&(e+=" "),e+=n);return e}function c(){for(var t,r,n=0,e="",o=arguments.length;n<o;n++)(t=arguments[n])&&(r=i(t))&&(e&&(e+=" "),e+=r);return e}var a={};function l(...t){return f(c(t))}function p(t){return new Promise((r,n)=>{const e=new FileReader;e.onload=()=>r(String(e.result)),e.onerror=o=>n(o),e.readAsDataURL(t)})}function d(t){if(!t)return"";const r={а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"e",ж:"zh",з:"z",и:"i",й:"y",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"h",ц:"ts",ч:"ch",ш:"sh",щ:"sch",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya"},n=t.trim().toLowerCase();let e="";for(const o of n){const s=o.charCodeAt(0);if(s>=97&&s<=122||s>=48&&s<=57){e+=o;continue}if(r[o]!==void 0){e+=r[o];continue}if(/\s|[_]+/.test(o)){e+="-";continue}}return e.replace(/[^a-z0-9-]/g,"-").replace(/-{2,}/g,"-").replace(/^-+|-+$/g,"")}function h(t,r){const n=[2,0,1,1,1,2];return r[t%100>4&&t%100<20?2:n[Math.min(t%10,5)]]}function g(t){if(!t)return;if(/^https?:\/\//i.test(t))return t;const r=typeof window<"u"?"http://localhost:3000":a.API_URL?String(a.API_URL).replace(/\/$/,""):"http://localhost:3000";return t.startsWith("/")?`${r}${t}`:`${r}/${t}`}export{l as c,p as f,h as p,g as r,d as s};

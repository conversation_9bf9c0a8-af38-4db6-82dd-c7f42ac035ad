import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { defineComponent, useSSRContext, ref, reactive, computed, mergeProps, withCtx, createTextVNode, toDisplayString, createVNode, withModifiers, createBlock, createCommentVNode, openBlock } from 'vue';
import { u as useAuth, n as navigate, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { P as Password } from '../../chunks/Password_BQyeKHib.mjs';
import { M as Message } from '../../chunks/Message_acgdACvd.mjs';
import { C as Checkbox } from '../../chunks/Checkbox_Ca7GoCvq.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
/* empty css                                    */
import { _ as _export_sfc } from '../../chunks/ClientRouter_avhRMbqw.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "LoginForm",
  setup(__props, { expose: __expose }) {
    __expose();
    const { signIn, LoginFormSchema } = useAuth();
    const isSubmitting = ref(false);
    const generalError = ref("");
    const formData = reactive({
      email: "",
      password: "",
      rememberMe: false
    });
    const fieldErrors = reactive({});
    const isFormValid = computed(() => {
      return formData.email && formData.password && Object.keys(fieldErrors).length === 0;
    });
    const validateField = (field) => {
      try {
        const fieldSchema = LoginFormSchema.shape[field];
        const result = fieldSchema.safeParse(formData[field]);
        if (!result.success) {
          fieldErrors[field] = result.error.issues[0]?.message || "\u041E\u0448\u0438\u0431\u043A\u0430 \u0432\u0430\u043B\u0438\u0434\u0430\u0446\u0438\u0438";
        } else {
          delete fieldErrors[field];
        }
      } catch (error) {
        console.error("Validation error for field:", field, error);
        delete fieldErrors[field];
      }
    };
    const clearFieldError = (field) => {
      if (fieldErrors[field]) {
        delete fieldErrors[field];
      }
      if (generalError.value) {
        generalError.value = "";
      }
    };
    const handleSubmit = async () => {
      generalError.value = "";
      Object.keys(fieldErrors).forEach((key) => delete fieldErrors[key]);
      const validationResult = LoginFormSchema.safeParse(formData);
      if (!validationResult.success) {
        validationResult.error.issues.forEach((issue) => {
          if (issue.path[0]) {
            fieldErrors[issue.path[0]] = issue.message;
          }
        });
        return;
      }
      isSubmitting.value = true;
      try {
        const result = await signIn(formData);
        if (result.error) {
          if (result.error.message.includes("Invalid email or password")) {
            generalError.value = "\u041D\u0435\u0432\u0435\u0440\u043D\u044B\u0439 email \u0438\u043B\u0438 \u043F\u0430\u0440\u043E\u043B\u044C";
          } else if (result.error.message.includes("Too many requests")) {
            generalError.value = "\u0421\u043B\u0438\u0448\u043A\u043E\u043C \u043C\u043D\u043E\u0433\u043E \u043F\u043E\u043F\u044B\u0442\u043E\u043A \u0432\u0445\u043E\u0434\u0430. \u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u043F\u043E\u0437\u0436\u0435.";
          } else {
            generalError.value = result.error.message || "\u041E\u0448\u0438\u0431\u043A\u0430 \u0432\u0445\u043E\u0434\u0430 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443";
          }
        } else {
          console.log("\u2705 \u0423\u0441\u043F\u0435\u0448\u043D\u044B\u0439 \u0432\u0445\u043E\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443");
          setTimeout(() => {
            navigate("/admin");
          }, 100);
        }
      } catch (error) {
        console.error("Login error:", error);
        generalError.value = "\u041F\u0440\u043E\u0438\u0437\u043E\u0448\u043B\u0430 \u043D\u0435\u043E\u0436\u0438\u0434\u0430\u043D\u043D\u0430\u044F \u043E\u0448\u0438\u0431\u043A\u0430. \u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0435\u0449\u0435 \u0440\u0430\u0437.";
      } finally {
        isSubmitting.value = false;
      }
    };
    const __returned__ = { signIn, LoginFormSchema, isSubmitting, generalError, formData, fieldErrors, isFormValid, validateField, clearFieldError, handleSubmit, Button, Card, InputText, Password, Message, Checkbox };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8" }, _attrs))} data-v-e3f15633><div class="max-w-md w-full space-y-8" data-v-e3f15633><div data-v-e3f15633><div class="mx-auto h-12 w-12 flex items-center justify-center" data-v-e3f15633><img class="h-12 w-12" src="/favicon.svg" alt="PartTec" data-v-e3f15633></div><h2 class="mt-6 text-center text-3xl font-extrabold text-surface-900" data-v-e3f15633> \u0412\u0445\u043E\u0434 \u0432 \u0430\u0434\u043C\u0438\u043D \u043F\u0430\u043D\u0435\u043B\u044C </h2><p class="mt-2 text-center text-sm text-surface-600" data-v-e3f15633> \u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u0443\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u043E\u043C PartTec </p></div>`);
  _push(ssrRenderComponent($setup["Card"], { class: "mt-8" }, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<form class="space-y-6" data-v-e3f15633${_scopeId}>`);
        if ($setup.generalError) {
          _push2(ssrRenderComponent($setup["Message"], {
            severity: "error",
            closable: false
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`${ssrInterpolate($setup.generalError)}`);
              } else {
                return [
                  createTextVNode(toDisplayString($setup.generalError), 1)
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div data-v-e3f15633${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "email",
          modelValue: $setup.formData.email,
          "onUpdate:modelValue": ($event) => $setup.formData.email = $event,
          type: "email",
          autocomplete: "email",
          class: "w-full",
          invalid: !!$setup.fieldErrors.email,
          onBlur: ($event) => $setup.validateField("email"),
          onInput: ($event) => $setup.clearFieldError("email")
        }, null, _parent2, _scopeId));
        _push2(`<label for="email" data-v-e3f15633${_scopeId}>Email \u0430\u0434\u0440\u0435\u0441</label></div>`);
        if ($setup.fieldErrors.email) {
          _push2(`<small class="text-red-500" data-v-e3f15633${_scopeId}>${ssrInterpolate($setup.fieldErrors.email)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div data-v-e3f15633${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Password"], {
          id: "password",
          modelValue: $setup.formData.password,
          "onUpdate:modelValue": ($event) => $setup.formData.password = $event,
          autocomplete: "current-password",
          class: "w-full",
          invalid: !!$setup.fieldErrors.password,
          feedback: false,
          toggleMask: "",
          onBlur: ($event) => $setup.validateField("password"),
          onInput: ($event) => $setup.clearFieldError("password")
        }, null, _parent2, _scopeId));
        _push2(`<label for="password" data-v-e3f15633${_scopeId}>\u041F\u0430\u0440\u043E\u043B\u044C</label></div>`);
        if ($setup.fieldErrors.password) {
          _push2(`<small class="text-red-500" data-v-e3f15633${_scopeId}>${ssrInterpolate($setup.fieldErrors.password)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div class="flex items-center justify-between" data-v-e3f15633${_scopeId}><div class="flex items-center" data-v-e3f15633${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Checkbox"], {
          id: "remember-me",
          modelValue: $setup.formData.rememberMe,
          "onUpdate:modelValue": ($event) => $setup.formData.rememberMe = $event,
          binary: true
        }, null, _parent2, _scopeId));
        _push2(`<label for="remember-me" class="ml-2 block text-sm text-surface-700" data-v-e3f15633${_scopeId}> \u0417\u0430\u043F\u043E\u043C\u043D\u0438\u0442\u044C \u043C\u0435\u043D\u044F </label></div><div class="text-sm" data-v-e3f15633${_scopeId}><a href="#" class="font-medium text-primary-600 hover:text-primary-500" data-v-e3f15633${_scopeId}> \u0417\u0430\u0431\u044B\u043B\u0438 \u043F\u0430\u0440\u043E\u043B\u044C? </a></div></div><div data-v-e3f15633${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          type: "submit",
          disabled: $setup.isSubmitting || !$setup.isFormValid,
          loading: $setup.isSubmitting,
          label: "\u0412\u043E\u0439\u0442\u0438",
          class: "w-full",
          size: "large"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="text-center" data-v-e3f15633${_scopeId}><p class="text-sm text-surface-600" data-v-e3f15633${_scopeId}> \u041D\u0435\u0442 \u0430\u043A\u043A\u0430\u0443\u043D\u0442\u0430? <a href="/admin/register" class="font-medium text-primary-600 hover:text-primary-500" data-v-e3f15633${_scopeId}> \u0417\u0430\u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C\u0441\u044F </a></p></div></form>`);
      } else {
        return [
          createVNode("form", {
            class: "space-y-6",
            onSubmit: withModifiers($setup.handleSubmit, ["prevent"])
          }, [
            $setup.generalError ? (openBlock(), createBlock($setup["Message"], {
              key: 0,
              severity: "error",
              closable: false
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString($setup.generalError), 1)
              ]),
              _: 1
            })) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["InputText"], {
                id: "email",
                modelValue: $setup.formData.email,
                "onUpdate:modelValue": ($event) => $setup.formData.email = $event,
                type: "email",
                autocomplete: "email",
                class: "w-full",
                invalid: !!$setup.fieldErrors.email,
                onBlur: ($event) => $setup.validateField("email"),
                onInput: ($event) => $setup.clearFieldError("email")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onBlur", "onInput"]),
              createVNode("label", { for: "email" }, "Email \u0430\u0434\u0440\u0435\u0441")
            ]),
            $setup.fieldErrors.email ? (openBlock(), createBlock("small", {
              key: 1,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.email), 1)) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["Password"], {
                id: "password",
                modelValue: $setup.formData.password,
                "onUpdate:modelValue": ($event) => $setup.formData.password = $event,
                autocomplete: "current-password",
                class: "w-full",
                invalid: !!$setup.fieldErrors.password,
                feedback: false,
                toggleMask: "",
                onBlur: ($event) => $setup.validateField("password"),
                onInput: ($event) => $setup.clearFieldError("password")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onBlur", "onInput"]),
              createVNode("label", { for: "password" }, "\u041F\u0430\u0440\u043E\u043B\u044C")
            ]),
            $setup.fieldErrors.password ? (openBlock(), createBlock("small", {
              key: 2,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.password), 1)) : createCommentVNode("", true),
            createVNode("div", { class: "flex items-center justify-between" }, [
              createVNode("div", { class: "flex items-center" }, [
                createVNode($setup["Checkbox"], {
                  id: "remember-me",
                  modelValue: $setup.formData.rememberMe,
                  "onUpdate:modelValue": ($event) => $setup.formData.rememberMe = $event,
                  binary: true
                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                createVNode("label", {
                  for: "remember-me",
                  class: "ml-2 block text-sm text-surface-700"
                }, " \u0417\u0430\u043F\u043E\u043C\u043D\u0438\u0442\u044C \u043C\u0435\u043D\u044F ")
              ]),
              createVNode("div", { class: "text-sm" }, [
                createVNode("a", {
                  href: "#",
                  class: "font-medium text-primary-600 hover:text-primary-500"
                }, " \u0417\u0430\u0431\u044B\u043B\u0438 \u043F\u0430\u0440\u043E\u043B\u044C? ")
              ])
            ]),
            createVNode("div", null, [
              createVNode($setup["Button"], {
                type: "submit",
                disabled: $setup.isSubmitting || !$setup.isFormValid,
                loading: $setup.isSubmitting,
                label: "\u0412\u043E\u0439\u0442\u0438",
                class: "w-full",
                size: "large"
              }, null, 8, ["disabled", "loading"])
            ]),
            createVNode("div", { class: "text-center" }, [
              createVNode("p", { class: "text-sm text-surface-600" }, [
                createTextVNode(" \u041D\u0435\u0442 \u0430\u043A\u043A\u0430\u0443\u043D\u0442\u0430? "),
                createVNode("a", {
                  href: "/admin/register",
                  class: "font-medium text-primary-600 hover:text-primary-500"
                }, " \u0417\u0430\u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C\u0441\u044F ")
              ])
            ])
          ], 32)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/auth/LoginForm.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const LoginForm = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender], ["__scopeId", "data-v-e3f15633"]]);

const $$Login = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "LoginForm", LoginForm, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/auth/LoginForm.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/login.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/login.astro";
const $$url = "/admin/login";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Login,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

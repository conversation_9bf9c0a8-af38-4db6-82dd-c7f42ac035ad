import{t as W}from"./trpc.BpyaUO08.js";import ve from"./Button.DrThv2lH.js";import{D as Ue,s as ze}from"./index.BWD5ZO4k.js";import{D as Se}from"./Dialog.Ct7C9BO5.js";import{I as Ve}from"./InputText.DOJMNEP-.js";import{D as ut}from"./Dropdown.Cj1958l9.js";import{S as at}from"./SecondaryButton.DkELYl7Q.js";import{_ as be}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as ge,g as k,o as i,w as d,a,e as r,K as Le,L as lt,j as me,i as qe,c as s,F,r as j,b as x,f as h,h as T,P as rt,n as nt}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as E,t as b,n as ce}from"./reactivity.esm-bundler.BQ12LWmY.js";import{T as Ce}from"./Tag.DTFTku6q.js";import it from"./Card.C4y0_bWr.js";import{V as st}from"./AutoComplete.rPzROuMW.js";import{A as je}from"./AttributeValueInput.BnZ_HprM.js";import{o as P,n as I,s as M,r as z,u as q,d as Z,e as He,c as Ee,a as $,b as Je}from"./types.C07aSKae.js";import{o as We,s as pe,u as Ke,n as fe,b as Me,d as Ne,r as ot,a as Ge,c as dt,e as ct}from"./types.FgRm47Sn.js";import{I as Fe}from"./Icon.By8t0-Wj.js";/* empty css                       */import{c as ee}from"./createLucideIcon.NtN1-Ts2.js";import{C as mt}from"./check.B3pubfVf.js";import{T as Qe,P as Xe}from"./trash.4HbnIIsp.js";import{S as pt}from"./Select.CQBzSu6y.js";import{M as ft}from"./Message.BY1UiDHQ.js";import{u as Ye}from"./useTrpc.spLZjt2f.js";import{u as vt}from"./useToast.pIbuf2bs.js";import{I as bt}from"./info.B6miOEHp.js";import{S as gt}from"./search.DFOrFhbU.js";import{P as Ze}from"./plus.CiWMw0wk.js";import{n as Ie}from"./router.DKcY2uv6.js";import"./index.6ykohhwZ.js";import"./index.BaVCXmir.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.By2TJOuX.js";import"./index.COq_zjeV.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";import"./index.D4QD70nN.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./InputNumber.vgPO18dj.js";import"./Checkbox.C7bFmmzc.js";import"./Textarea.BLEHJ3ym.js";/* empty css                       */import"./useErrorHandler.DVDazL16.js";import"./index.PhWaFJhe.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=ee("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=ee("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xt=ee("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=ee("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Et=ee("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=ee("tags",[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=ee("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),At=ge({__name:"EditEquipmentDialog",props:Le({equipment:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:Le(["save","cancel"],["update:isVisible"]),setup(p,{expose:u,emit:c}){u();const e=E([]),f=lt(p,"isVisible"),m=p,t=c,l=E({}),o=E("Создать модель техники");me(f,C=>{C&&(l.value={...m.equipment||{}},o.value=m.equipment?.id?`Редактировать: ${m.equipment.name}`:"Создать модель техники")});function A(){f.value=!1,t("cancel")}function D(){t("save",{...l.value}),f.value=!1}async function w(){const C=await W.crud.brand.findMany.query({select:{id:!0,name:!0},orderBy:{name:"asc"}});e.value=C}qe(()=>{w()});const B={brandOptions:e,isVisible:f,props:m,emit:t,localEquipment:l,dialogTitle:o,handleCancel:A,handleSave:D,loadBrands:w,Button:ve,Dialog:Se,InputText:Ve,Dropdown:ut,SecondaryButton:at};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),Ct={class:"flex flex-col gap-4 py-4"},Dt={class:"flex flex-col"},Tt={class:"flex flex-col"},wt={class:"flex justify-end gap-2"};function It(p,u,c,e,f,m){return i(),k(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[2]||(u[2]=t=>e.isVisible=t),modal:"",header:e.dialogTitle,class:"sm:w-100 w-9/10"},{default:d(()=>[a("div",Ct,[a("div",Dt,[u[3]||(u[3]=a("label",{for:"name"},"Наименование модели",-1)),r(e.InputText,{id:"name",modelValue:e.localEquipment.name,"onUpdate:modelValue":u[0]||(u[0]=t=>e.localEquipment.name=t),placeholder:"Например: Экскаватор CAT 320D"},null,8,["modelValue"])]),a("div",Tt,[u[4]||(u[4]=a("label",{for:"brand"},"Бренд",-1)),r(e.Dropdown,{id:"brand",modelValue:e.localEquipment.brandId,"onUpdate:modelValue":u[1]||(u[1]=t=>e.localEquipment.brandId=t),options:e.brandOptions,optionLabel:"name",optionValue:"id",placeholder:"Выберите бренд",showClear:""},null,8,["modelValue","options"])])]),a("div",wt,[r(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),r(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const Bt=be(At,[["render",It]]),De=P({id:I(),value:M(),numericValue:I().nullish()}).strict(),St=P({equipmentModel:z(q()),template:z(q())}),Re=P({equipmentModelId:M(),templateId:I()}),Vt=De;Vt.merge(Re).merge(St.partial());De.partial().passthrough();P({id:Z([I(),z(q())]),value:M(),numericValue:Z([I().nullish(),z(q())])}).partial().passthrough();const qt=De.partial({id:!0});qt.merge(Re);const Mt=De.partial();Mt.merge(Re.partial());const et=He(["STRING","NUMBER","BOOLEAN","DATE","JSON"]),tt=He(["MM","INCH","FT","G","KG","T","LB","ML","L","GAL","SEC","MIN","H","PCS","SET","PAIR","BAR","PSI","KW","HP","NM","RPM","C","F","PERCENT"]),Te=P({id:I(),name:M(),title:M(),description:M().nullish(),dataType:et,unit:tt.nullish(),isRequired:Je().default(!1),minValue:I().nullish(),maxValue:I().nullish(),allowedValues:$(M()),tolerance:I().default(0).nullish(),createdAt:Ee.date().default(()=>new Date),updatedAt:Ee.date()}).strict(),Nt=P({group:z(q()).optional(),partAttributes:$(q()).optional(),catalogItemAttributes:$(q()).optional(),equipmentAttributes:$(q()).optional(),synonymGroups:$(q()).optional()}),Oe=P({groupId:I().nullish()}),Ft=Te;Ft.merge(Oe).merge(Nt.partial());Te.partial().passthrough();P({id:Z([I(),z(q())]),name:M(),title:M(),description:M().nullish(),dataType:et,unit:tt.nullish(),isRequired:Je().default(!1),minValue:Z([I().nullish(),z(q())]),maxValue:Z([I().nullish(),z(q())]),allowedValues:$(M()),tolerance:Z([I().default(0).nullish(),z(q())]),createdAt:Ee.date().default(()=>new Date),updatedAt:Ee.date()}).partial().passthrough();const Rt=Te.partial({id:!0,dataType:!0,isRequired:!0,allowedValues:!0,tolerance:!0,createdAt:!0,updatedAt:!0});Rt.merge(Oe);const Ot=Te.partial();Ot.merge(Oe.partial());const ye=P({id:I(),name:M(),description:M().nullish()}).strict(),Lt=P({templates:$(q()).optional()}),Gt=ye;Gt.merge(Lt.partial());ye.partial().passthrough();P({id:Z([I(),z(q())]),name:M(),description:M().nullish()}).partial().passthrough();ye.partial({id:!0});ye.partial({id:!0});ye.partial();const Pt=p=>{switch(p.dataType){case"STRING":let u=pe().min(1,"Значение не может быть пустым");return p.allowedValues&&p.allowedValues.length>0?ct(p.allowedValues):u;case"NUMBER":let c=fe({required_error:"Значение обязательно",invalid_type_error:"Значение должно быть числом"});return p.minValue!==null&&p.minValue!==void 0&&(c=c.min(p.minValue,`Минимальное значение: ${p.minValue}`)),p.maxValue!==null&&p.maxValue!==void 0&&(c=c.max(p.maxValue,`Максимальное значение: ${p.maxValue}`)),c;case"BOOLEAN":return Me();case"DATE":return Ne({required_error:"Дата обязательна",invalid_type_error:"Неверный формат даты"});case"JSON":return ot(Ge()).or(dt(Ge()));default:return pe()}};We({templateId:fe().min(1,"Выберите шаблон атрибута"),value:Ke([pe(),fe(),Me(),Ne()]),equipmentModelId:pe().min(1,"ID модели техники обязателен")});We({id:fe().min(1,"ID атрибута обязателен"),value:Ke([pe(),fe(),Me(),Ne()])});function Be(p){const{value:u,template:c}=p,{dataType:e,unit:f}=c;let m,t;try{switch(e){case"STRING":t=u,m=u;break;case"NUMBER":t=parseFloat(u),m=isNaN(t)?u:t.toLocaleString("ru-RU"),f&&(m+=` ${ke(f)}`);break;case"BOOLEAN":t=u.toLowerCase()==="true",m=t?"Да":"Нет";break;case"DATE":t=new Date(u),m=isNaN(t.getTime())?u:t.toLocaleDateString("ru-RU");break;case"JSON":try{t=JSON.parse(u),m=JSON.stringify(t,null,2)}catch{t=u,m=u}break;default:t=u,m=u}}catch{t=u,m=u}return{displayValue:m,rawValue:t,unit:f,dataType:e}}function _e(p){const u={};return p.forEach(c=>{const e=c.template.group?.name||"Общие";u[e]||(u[e]=[]),u[e].push(c)}),Object.keys(u).forEach(c=>{u[c].sort((e,f)=>e.template.title.localeCompare(f.template.title,"ru"))}),u}function Ut(p,u,c){const e=new Set(u.map(m=>m.templateId));let f=p.filter(m=>!e.has(m.id));if(c){if(c.excludeTemplateIds){const m=new Set(c.excludeTemplateIds);f=f.filter(t=>!m.has(t.id))}if(c.groupId&&(f=f.filter(m=>m.groupId===c.groupId)),c.dataType&&(f=f.filter(m=>m.dataType===c.dataType)),c.searchQuery){const m=c.searchQuery.toLowerCase();f=f.filter(t=>t.title.toLowerCase().includes(m)||t.name.toLowerCase().includes(m)||t.description&&t.description.toLowerCase().includes(m))}}return f.sort((m,t)=>m.title.localeCompare(t.title,"ru"))}function ke(p){return p?{MM:"мм",INCH:"дюйм",FT:"фт",G:"г",KG:"кг",T:"т",LB:"фунт",ML:"мл",L:"л",GAL:"гал",SEC:"сек",MIN:"мин",H:"ч",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"psi",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[p]||p:""}function Ae(p){return{STRING:"Текст",NUMBER:"Число",BOOLEAN:"Да/Нет",DATE:"Дата",JSON:"JSON"}[p]||p}function Pe(p,u){switch(u){case"NUMBER":const c=parseFloat(p);return isNaN(c)?0:c;case"BOOLEAN":return p.toLowerCase()==="true"||p==="1"||p==="да";case"DATE":const e=new Date(p);return isNaN(e.getTime())?new Date:e;case"JSON":try{return JSON.parse(p)}catch{return p}default:return p}}const zt=ge({__name:"EquipmentAttributesList",props:{attributes:{},readonly:{type:Boolean,default:!1},showGroupColumn:{type:Boolean,default:!0},showDataTypeColumn:{type:Boolean,default:!1},compact:{type:Boolean,default:!1},groupByTemplate:{type:Boolean,default:!1}},emits:["edit-attribute","delete-attribute","update-value"],setup(p,{expose:u,emit:c}){u();const e=p,f=c,m=E(null),t=E(null),l=T(()=>e.attributes.map(g=>{const N=Be(g);return{id:g.id,name:g.template.title,value:N.displayValue,rawValue:N.rawValue,unit:N.unit,dataType:g.template.dataType,dataTypeDisplay:Ae(g.template.dataType),group:g.template.group?.name||"Общие",description:g.template.description,isRequired:g.template.isRequired,template:g.template,attribute:g}})),o=T(()=>e.groupByTemplate?_e(e.attributes):{});function A(g){f("edit-attribute",g)}function D(g){f("delete-attribute",g)}function w(g){switch(g){case"NUMBER":return"info";case"BOOLEAN":return"success";case"DATE":return"warning";case"JSON":return"secondary";default:return}}function B(g){if(g.dataType==="BOOLEAN")return g.rawValue?"✓":"✗";if(g.dataType==="NUMBER"&&g.unit){const N=ke(g.unit);return`${g.rawValue.toLocaleString("ru-RU")} ${N}`}return g.value}function C(g){m.value=g.id,t.value=g.rawValue}function L(){m.value=null,t.value=null}function R(g){t.value!==null&&t.value!==void 0&&f("update-value",g,t.value),L()}function K(g){return{STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"}[g]||"pi pi-question"}const H={props:e,emit:f,editingAttributeId:m,editingValue:t,tableData:l,groupedAttributes:o,handleEditAttribute:A,handleDeleteAttribute:D,getValueSeverity:w,formatValueForDisplay:B,startEditing:C,cancelEditing:L,saveEditing:R,getDataTypeIcon:K,get PencilIcon(){return Xe},get TrashIcon(){return Qe},get CheckIcon(){return mt},get XIcon(){return $e},Button:ve,Tag:Ce,DataTable:Ue,get Column(){return ze},AttributeValueInput:je,get formatAttributeValue(){return Be},get getUnitDisplayName(){return ke},Icon:Fe};return Object.defineProperty(H,"__isScriptSetup",{enumerable:!1,value:!0}),H}}),jt={class:"equipment-attributes-list"},Ht={key:0,class:"space-y-6"},Jt={class:"flex items-center gap-2 mb-3 pb-2 border-b border-surface-200 dark:border-surface-700"},Wt={class:"font-medium text-surface-900 dark:text-surface-0"},Kt={class:"space-y-3"},Qt={class:"flex-shrink-0 relative"},Xt=["title"],Yt={class:"flex-shrink-0 w-48"},Zt={class:"font-medium text-surface-900 dark:text-surface-0 text-sm"},$t={key:0,class:"text-xs text-surface-500 dark:text-surface-400 mt-1"},eu={class:"flex-1"},tu={key:0,class:"flex items-center gap-2"},uu={class:"flex gap-1"},au={key:1,class:"flex items-center gap-2 group"},lu={class:"text-surface-900 dark:text-surface-0"},ru={class:"flex-shrink-0 w-16 text-center"},nu={key:0,class:"text-sm text-surface-500 font-medium"},iu={key:0,class:"flex-shrink-0 flex gap-2"},su={class:"flex items-start gap-2"},ou={class:"flex-1 min-w-0"},du={class:"flex items-center gap-2 mb-1"},cu={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},mu=["title"],pu={key:0,class:"flex items-center gap-2"},fu={class:"flex gap-1"},vu={key:1,class:"flex items-center gap-2 group"},bu=["title"],gu={class:"flex items-center gap-1"};function yu(p,u,c,e,f,m){return i(),s("div",jt,[c.groupByTemplate&&Object.keys(e.groupedAttributes).length>0?(i(),s("div",Ht,[(i(!0),s(F,null,j(e.groupedAttributes,(t,l)=>(i(),s("div",{key:l,class:"attribute-group"},[a("div",Jt,[u[2]||(u[2]=a("i",{class:"pi pi-folder text-blue-600"},null,-1)),a("h4",Wt,b(l==="undefined"?"Без группы":l),1),r(e.Tag,{value:`${t.length} атр.`,severity:"secondary",size:"small"},null,8,["value"])]),a("div",Kt,[(i(!0),s(F,null,j(t,o=>(i(),s("div",{key:o.id,class:"attribute-item"},[a("div",{class:ce(["flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm",[o.value&&String(o.value).trim()?"border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10":"border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"]])},[a("div",Qt,[r(e.Icon,{name:e.getDataTypeIcon(o.template.dataType),class:"text-lg text-primary w-5 h-5"},null,8,["name"]),a("div",{class:ce(["absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900",o.value&&String(o.value).trim()?"bg-green-500":"bg-orange-500"]),title:o.value&&String(o.value).trim()?"Заполнено":"Не заполнено"},null,10,Xt)]),a("div",Yt,[a("div",Zt,[h(b(o.template.title)+" ",1),o.template.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs ml-1"},{default:d(()=>u[3]||(u[3]=[h("*")])),_:1,__:[3]})):x("",!0)]),o.template.description?(i(),s("div",$t,b(o.template.description),1)):x("",!0)]),a("div",eu,[e.editingAttributeId===o.id?(i(),s("div",tu,[r(e.AttributeValueInput,{modelValue:e.editingValue,"onUpdate:modelValue":u[0]||(u[0]=A=>e.editingValue=A),template:o.template,class:"flex-1",size:"small"},null,8,["modelValue","template"]),a("div",uu,[r(e.Button,{onClick:A=>e.saveEditing(o.id),size:"small",class:"p-1"},{default:d(()=>[r(e.CheckIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:e.cancelEditing,severity:"secondary",size:"small",class:"p-1"},{default:d(()=>[r(e.XIcon,{class:"w-3 h-3"})]),_:1})])])):(i(),s("div",au,[a("span",lu,b(e.formatAttributeValue(o).displayValue||"Не указано"),1),c.readonly?x("",!0):(i(),k(e.Button,{key:0,onClick:A=>e.startEditing(o),text:"",size:"small",class:"p-1 opacity-0 group-hover:opacity-100 transition-opacity"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]))]))]),a("div",ru,[o.template.unit?(i(),s("span",nu,b(e.getUnitDisplayName(o.template.unit)),1)):x("",!0)]),c.readonly?x("",!0):(i(),s("div",iu,[r(e.Button,{onClick:A=>e.handleEditAttribute(o.id),severity:"secondary",size:"small",outlined:"",class:"p-1"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:A=>e.handleDeleteAttribute(o.id),severity:"danger",size:"small",outlined:"",class:"p-1",disabled:o.template.isRequired},{default:d(()=>[r(e.TrashIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick","disabled"])]))],2)]))),128))])]))),128))])):(i(),k(e.DataTable,{key:1,value:e.tableData,paginator:!c.compact&&e.tableData.length>10,rows:c.compact?5:10,rowsPerPageOptions:[5,10,25,50],sortMode:"multiple",removableSort:"",loading:!1,dataKey:"id",size:c.compact?"small":"normal",class:ce(["p-datatable-sm",{"compact-table":c.compact}])},{empty:d(()=>u[6]||(u[6]=[a("div",{class:"text-center py-6 text-surface-500 dark:text-surface-400"},[a("i",{class:"pi pi-info-circle text-2xl mb-2 block"}),a("p",{class:"text-sm"},"Нет атрибутов для отображения")],-1)])),default:d(()=>[r(e.Column,{field:"name",header:"Атрибут",sortable:"",style:{minWidth:"200px"}},{body:d(({data:t})=>[a("div",su,[a("div",ou,[a("div",du,[a("span",cu,b(t.name),1),t.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs flex-shrink-0"},{default:d(()=>u[4]||(u[4]=[h(" Обязательный ")])),_:1,__:[4]})):x("",!0)]),t.description&&!c.compact?(i(),s("div",{key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2",title:t.description},b(t.description),9,mu)):x("",!0)])])]),_:1}),r(e.Column,{field:"value",header:"Значение",sortable:"",style:{minWidth:"150px"}},{body:d(({data:t})=>[e.editingAttributeId===t.id?(i(),s("div",pu,[r(e.AttributeValueInput,{modelValue:e.editingValue,"onUpdate:modelValue":u[1]||(u[1]=l=>e.editingValue=l),template:t.template,class:"w-full",size:"small"},null,8,["modelValue","template"]),a("div",fu,[r(e.Button,{onClick:l=>e.saveEditing(t.id),size:"small",class:"p-1"},{default:d(()=>[r(e.CheckIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:e.cancelEditing,severity:"secondary",size:"small",class:"p-1"},{default:d(()=>[r(e.XIcon,{class:"w-3 h-3"})]),_:1})])])):(i(),s("div",vu,[r(e.Tag,{severity:e.getValueSeverity(t.dataType),class:ce(["font-mono text-sm",{"text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20":t.dataType==="BOOLEAN"&&t.rawValue,"text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20":t.dataType==="BOOLEAN"&&!t.rawValue}])},{default:d(()=>[h(b(e.formatValueForDisplay(t)),1)]),_:2},1032,["severity","class"]),c.readonly?x("",!0):(i(),k(e.Button,{key:0,onClick:l=>e.startEditing(t),text:"",size:"small",class:"p-1 opacity-0 group-hover:opacity-100 transition-opacity"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3"})]),_:2},1032,["onClick"])),c.compact&&!c.showDataTypeColumn?(i(),s("span",{key:1,class:"text-xs text-surface-400 dark:text-surface-500",title:`Тип: ${t.dataTypeDisplay}`},b(t.dataType),9,bu)):x("",!0)]))]),_:1}),c.showGroupColumn?(i(),k(e.Column,{key:0,field:"group",header:"Группа",sortable:"",style:{minWidth:"120px"}},{body:d(({data:t})=>[r(e.Tag,{severity:"secondary",class:"text-xs"},{default:d(()=>[u[5]||(u[5]=a("i",{class:"pi pi-folder text-xs mr-1"},null,-1)),h(" "+b(t.group),1)]),_:2,__:[5]},1024)]),_:1})):x("",!0),c.showDataTypeColumn?(i(),k(e.Column,{key:1,field:"dataTypeDisplay",header:"Тип",sortable:"",style:{minWidth:"100px"}},{body:d(({data:t})=>[r(e.Tag,{severity:e.getValueSeverity(t.dataType),class:"text-xs"},{default:d(()=>[h(b(t.dataTypeDisplay),1)]),_:2},1032,["severity"])]),_:1})):x("",!0),c.readonly?x("",!0):(i(),k(e.Column,{key:2,field:"actions",header:"Действия",sortable:!1,style:{minWidth:"120px",width:"120px"}},{body:d(({data:t})=>[a("div",gu,[r(e.Button,{onClick:l=>e.handleEditAttribute(t.id),text:"",size:"small",class:"p-2",title:`Редактировать ${t.name}`},{default:d(()=>[r(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick","title"]),r(e.Button,{onClick:l=>e.handleDeleteAttribute(t.id),text:"",severity:"danger",size:"small",class:"p-2",title:`Удалить ${t.name}`,disabled:t.isRequired},{default:d(()=>[r(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick","title","disabled"])])]),_:1}))]),_:1},8,["value","paginator","rows","size","class"]))])}const hu=be(zt,[["render",yu],["__scopeId","data-v-b449289d"]]),xu=ge({__name:"AddEquipmentAttributeDialog",props:{visible:{type:Boolean},equipmentId:{},existingAttributes:{default:()=>[]}},emits:["update:visible","save","cancel"],setup(p,{expose:u,emit:c}){u();const e=p,f=c,{$trpc:m}=Ye(),t=vt(),l=E({templateId:0,value:"",template:void 0}),o=E(!1),A=E(!1),D=E(""),w=E(null),B=E(null),C=E([]),L=E([]),R=E([]),K=T({get:()=>e.visible,set:v=>f("update:visible",v)}),H=T(()=>{const v={searchQuery:D.value||void 0,groupId:w.value||void 0,dataType:B.value||void 0,excludeTemplateIds:e.existingAttributes.map(n=>n.templateId)};return Ut(L.value,e.existingAttributes,v)}),g=T(()=>L.value.find(v=>v.id===l.value.templateId)||null),N=T(()=>l.value.templateId>0&&l.value.value!==null&&l.value.value!==""&&C.value.length===0),U=T(()=>["STRING","NUMBER","BOOLEAN","DATE","JSON"].map(n=>({label:Ae(n),value:n}))),re=T(()=>[{label:"Все группы",value:null},...R.value.map(v=>({label:v.name,value:v.id}))]);async function Q(){o.value=!0;try{const v=await m.crud.attributeTemplate.findMany.query({include:{group:!0},orderBy:[{group:{name:"asc"}},{title:"asc"}]});L.value=v}catch(v){t.error("Не удалось загрузить шаблоны атрибутов"),console.error("Failed to load templates:",v)}finally{o.value=!1}}async function X(){try{const v=await m.crud.attributeGroup.findMany.query({orderBy:{name:"asc"}});R.value=v}catch(v){console.error("Failed to load attribute groups:",v)}}function ne(v){l.value.templateId=v.id,l.value.template=v,te(),D.value=""}function te(){const v=g.value;if(!v){l.value.value="";return}switch(v.dataType){case"STRING":l.value.value="";break;case"NUMBER":l.value.value=v.minValue||0;break;case"BOOLEAN":l.value.value=!1;break;case"DATE":l.value.value=new Date;break;case"JSON":l.value.value="";break;default:l.value.value=""}J()}function J(){C.value=[];const v=g.value;if(!(!v||l.value.value===null||l.value.value===""))try{const n=Pt(v),y=Pe(String(l.value.value),v.dataType);n.parse(y)}catch(n){n.errors?C.value=n.errors.map(y=>y.message):C.value=["Неверное значение атрибута"]}}async function ue(){if(N.value){A.value=!0;try{const v=g.value,n=Pe(String(l.value.value),v.dataType),y={templateId:l.value.templateId,value:n,template:v};f("save",y),ae()}catch(v){t.error("Ошибка при сохранении атрибута"),console.error("Save error:",v)}finally{A.value=!1}}}function ae(){l.value={templateId:0,value:"",template:void 0},D.value="",w.value=null,B.value=null,C.value=[],f("cancel")}function ie(){D.value="",w.value=null,B.value=null}function se(v){const n=[];return v.group&&n.push(v.group.name),n.push(Ae(v.dataType)),v.unit&&n.push(ke(v.unit)),v.isRequired&&n.push("Обязательный"),n.join(" • ")}me(()=>e.visible,v=>{v&&(Q(),X())}),me(()=>l.value.value,()=>{J()},{deep:!0}),me(()=>l.value.templateId,()=>{J()}),qe(()=>{e.visible&&(Q(),X())});const le={props:e,emit:f,$trpc:m,toast:t,formData:l,loading:o,saving:A,searchQuery:D,selectedGroupId:w,selectedDataType:B,validationErrors:C,allTemplates:L,attributeGroups:R,isVisible:K,availableTemplates:H,selectedTemplate:g,isFormValid:N,dataTypeOptions:U,groupOptions:re,loadTemplates:Q,loadAttributeGroups:X,selectTemplate:ne,resetValue:te,validateValue:J,handleSave:ue,handleCancel:ae,clearFilters:ie,getTemplateDisplayInfo:se,get SearchIcon(){return gt},get XIcon(){return $e},get InfoIcon(){return bt},Dialog:Se,Button:ve,InputText:Ve,Select:pt,Tag:Ce,Message:ft,AttributeValueInput:je};return Object.defineProperty(le,"__isScriptSetup",{enumerable:!1,value:!0}),le}}),_u={class:"space-y-6"},Eu={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-surface-50 dark:bg-surface-800 rounded-lg"},ku={class:"relative"},Au={class:"flex justify-between items-center mb-4"},Cu={class:"text-sm text-surface-600 dark:text-surface-400"},Du={class:"max-h-64 overflow-y-auto border border-surface-200 dark:border-surface-700 rounded-lg"},Tu={key:0,class:"p-4 text-center text-surface-500"},wu={key:1,class:"p-4 text-center text-surface-500"},Iu={key:2,class:"divide-y divide-surface-200 dark:divide-surface-700"},Bu=["onClick"],Su={class:"flex items-start justify-between"},Vu={class:"flex-1"},qu={class:"flex items-center gap-2 mb-1"},Mu={class:"font-medium text-surface-900 dark:text-surface-0"},Nu={class:"text-sm text-surface-600 dark:text-surface-400 mb-2"},Fu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2"},Ru={key:0,class:"ml-4"},Ou={key:0},Lu={class:"text-sm font-medium text-surface-900 dark:text-surface-0 mb-3"},Gu={class:"p-3 bg-surface-50 dark:bg-surface-800 rounded-lg mb-4"},Pu={class:"flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"},Uu={key:0,class:"text-xs text-surface-500 dark:text-surface-400 mt-1"},zu={class:"space-y-4"},ju={key:0,class:"mt-4"},Hu={class:"flex justify-end gap-2"};function Ju(p,u,c,e,f,m){const t=rt("Icon");return i(),k(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[4]||(u[4]=l=>e.isVisible=l),modal:"",closable:!0,draggable:!1,class:"w-full max-w-4xl",header:"Добавить атрибут"},{footer:d(()=>[a("div",Hu,[r(e.Button,{onClick:e.handleCancel,outlined:"",disabled:e.saving},{default:d(()=>u[13]||(u[13]=[h(" Отмена ")])),_:1,__:[13]},8,["disabled"]),r(e.Button,{onClick:e.handleSave,disabled:!e.isFormValid||e.saving,loading:e.saving},{default:d(()=>[h(b(e.saving?"Сохранение...":"Добавить атрибут"),1)]),_:1},8,["disabled","loading"])])]),default:d(()=>[a("div",_u,[a("div",null,[u[11]||(u[11]=a("h6",{class:"text-sm font-medium text-surface-900 dark:text-surface-0 mb-3"}," Выберите шаблон атрибута ",-1)),a("div",Eu,[a("div",ku,[r(e.InputText,{modelValue:e.searchQuery,"onUpdate:modelValue":u[0]||(u[0]=l=>e.searchQuery=l),placeholder:"Поиск по названию...",class:"w-full pl-10"},null,8,["modelValue"]),r(e.SearchIcon,{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-surface-400"})]),r(e.Select,{modelValue:e.selectedGroupId,"onUpdate:modelValue":u[1]||(u[1]=l=>e.selectedGroupId=l),options:e.groupOptions,"option-label":"label","option-value":"value",placeholder:"Выберите группу",class:"w-full","show-clear":!0},null,8,["modelValue","options"]),r(e.Select,{modelValue:e.selectedDataType,"onUpdate:modelValue":u[2]||(u[2]=l=>e.selectedDataType=l),options:e.dataTypeOptions,"option-label":"label","option-value":"value",placeholder:"Тип данных",class:"w-full","show-clear":!0},null,8,["modelValue","options"])]),a("div",Au,[a("span",Cu," Найдено шаблонов: "+b(e.availableTemplates.length),1),r(e.Button,{onClick:e.clearFilters,text:"",size:"small",class:"text-sm"},{default:d(()=>[r(e.XIcon,{class:"w-4 h-4 mr-1"}),u[5]||(u[5]=h(" Очистить фильтры "))]),_:1,__:[5]})]),a("div",Du,[e.loading?(i(),s("div",Tu,[r(t,{name:"pi pi-spinner pi-spin",class:"mr-2 inline-block"}),u[6]||(u[6]=h(" Загрузка шаблонов... "))])):e.availableTemplates.length===0?(i(),s("div",wu,[r(e.InfoIcon,{class:"w-5 h-5 mx-auto mb-2"}),u[7]||(u[7]=a("p",{class:"text-sm"},"Нет доступных шаблонов",-1)),u[8]||(u[8]=a("p",{class:"text-xs mt-1"},"Попробуйте изменить фильтры или все шаблоны уже используются",-1))])):(i(),s("div",Iu,[(i(!0),s(F,null,j(e.availableTemplates,l=>(i(),s("div",{key:l.id,class:ce(["p-4 hover:bg-surface-50 dark:hover:bg-surface-800 cursor-pointer transition-colors",{"bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary":e.formData.templateId===l.id}]),onClick:o=>e.selectTemplate(l)},[a("div",Su,[a("div",Vu,[a("div",qu,[a("h6",Mu,b(l.title),1),l.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs"},{default:d(()=>u[9]||(u[9]=[h(" Обязательный ")])),_:1,__:[9]})):x("",!0)]),a("p",Nu,b(e.getTemplateDisplayInfo(l)),1),l.description?(i(),s("p",Fu,b(l.description),1)):x("",!0)]),e.formData.templateId===l.id?(i(),s("div",Ru,u[10]||(u[10]=[a("i",{class:"pi pi-check text-primary text-lg"},null,-1)]))):x("",!0)])],10,Bu))),128))]))])]),e.selectedTemplate?(i(),s("div",Ou,[a("h6",Lu,' Введите значение для "'+b(e.selectedTemplate.title)+'" ',1),a("div",Gu,[a("div",Pu,[r(e.InfoIcon,{class:"w-4 h-4"}),a("span",null,b(e.getTemplateDisplayInfo(e.selectedTemplate)),1)]),e.selectedTemplate.description?(i(),s("p",Uu,b(e.selectedTemplate.description),1)):x("",!0)]),a("div",zu,[u[12]||(u[12]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Значение ",-1)),r(e.AttributeValueInput,{modelValue:e.formData.value,"onUpdate:modelValue":u[3]||(u[3]=l=>e.formData.value=l),template:e.selectedTemplate,placeholder:"Введите значение атрибута",class:"w-full"},null,8,["modelValue","template"])]),e.validationErrors.length>0?(i(),s("div",ju,[(i(!0),s(F,null,j(e.validationErrors,l=>(i(),k(e.Message,{key:l,severity:"error",closable:!1,class:"mb-2"},{default:d(()=>[h(b(l),1)]),_:2},1024))),128))])):x("",!0)])):x("",!0)])]),_:1},8,["visible"])}const Wu=be(xu,[["render",Ju],["__scopeId","data-v-cb27c7ef"]]),de=6,Ku=ge({__name:"EquipmentAttributesSection",props:{equipmentId:{},attributes:{},readonly:{type:Boolean,default:!1}},emits:["add-attribute","edit-attribute","delete-attribute"],setup(p,{expose:u,emit:c}){u();const e=p,f=c,{client:m}=Ye(),t=E(!1),l=E("grid"),o=E(!1),A=E(!1),D=E(null),w=E(null),B=E([]),C=E([]),L=E([]),R=E(!1),K=T(()=>e.attributes?_e(e.attributes):{}),H=T(()=>e.attributes&&e.attributes.length>0),g=T(()=>e.attributes?.length||0),N=T(()=>g.value>de),U=T(()=>e.attributes?t.value||g.value<=de?e.attributes:e.attributes.slice(0,de):[]),re=T(()=>U.value.length?_e(U.value):{}),Q=T(()=>Math.max(0,g.value-de)),X=T(()=>e.attributes?.filter(_=>_.value&&String(_.value).trim()).length||0);function ne(){o.value=!0}function te(_){f("add-attribute",_),o.value=!1}function J(){o.value=!1}function ue(_){f("edit-attribute",_)}function ae(_){f("delete-attribute",_)}function ie(){t.value=!t.value}function se(){l.value=l.value==="grid"?"table":"grid"}function le(_){return Be(_)}const v=T(()=>!e.readonly);async function n(_){const S=_.query.toLowerCase();try{const V=await m.crud.attributeGroup.findMany.query({where:{name:{contains:S,mode:"insensitive"}},take:10});B.value=V||[]}catch(V){console.error("Error filtering groups:",V),B.value=[]}}async function y(_){const S=_.query.toLowerCase();try{const V=await m.crud.attributeTemplate.findMany.query({where:{OR:[{title:{contains:S,mode:"insensitive"}},{name:{contains:S,mode:"insensitive"}}]},include:{group:!0},take:10});C.value=V||[]}catch(V){console.error("Error filtering templates:",V),C.value=[]}}async function O(){if(D.value){R.value=!0;try{const _=D.value.id||D.value,S=await m.crud.attributeTemplate.findMany.query({where:{groupId:_},include:{group:!0}});if(S)for(const V of S){const we={templateId:V.id,value:Y(V.dataType),template:V};f("add-attribute",we)}D.value=null}catch(_){console.error("Error loading group templates:",_)}finally{R.value=!1}}}function G(){if(!w.value)return;const _=w.value,S={templateId:_.id,value:Y(_.dataType),template:_};f("add-attribute",S),w.value=null}async function he(_){R.value=!0;try{const S=await m.crud.attributeTemplate.findMany.query({where:{groupId:_},include:{group:!0}});if(S)for(const V of S){const we={templateId:V.id,value:Y(V.dataType),template:V};f("add-attribute",we)}A.value=!1}catch(S){console.error("Error loading templates by group:",S)}finally{R.value=!1}}function Y(_){switch(_){case"STRING":return"";case"NUMBER":return 0;case"BOOLEAN":return!1;case"DATE":return new Date;case"JSON":return"";default:return""}}function xe(_){return{STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"}[_]||"pi pi-question"}qe(async()=>{try{const _=await m.crud.attributeGroup.findMany.query({include:{_count:{select:{templates:!0}}},orderBy:{name:"asc"}});L.value=_||[]}catch(_){console.error("Error loading template groups:",_)}});const oe={props:e,emit:f,client:m,showAllAttributes:t,COMPACT_LIMIT:de,viewMode:l,showAddDialog:o,showGroupDialog:A,selectedTemplateGroup:D,selectedTemplate:w,groupSuggestions:B,templateSuggestions:C,templateGroups:L,loadingTemplates:R,groupedAttributes:K,hasAttributes:H,totalAttributesCount:g,shouldShowExpandButton:N,visibleAttributes:U,visibleGroupedAttributes:re,hiddenAttributesCount:Q,filledAttributesCount:X,handleAddAttribute:ne,handleSaveAttribute:te,handleCancelAddAttribute:J,handleEditAttribute:ue,handleDeleteAttribute:ae,toggleShowAll:ie,toggleViewMode:se,formatAttribute:le,canManageAttributes:v,filterGroups:n,filterTemplates:y,loadSelectedGroupTemplates:O,addSingleTemplate:G,loadTemplatesByGroupId:he,getDefaultValueForType:Y,getDataTypeIcon:xe,get PlusIcon(){return Ze},get ChevronDownIcon(){return yt},get ChevronUpIcon(){return ht},get TableIcon(){return Et},get GridIcon(){return _t},get TagsIcon(){return kt},Button:ve,Tag:Ce,Card:it,AutoComplete:st,Dialog:Se,EquipmentAttributesList:hu,AddEquipmentAttributeDialog:Wu,get groupAttributes(){return _e},get getDataTypeDisplayName(){return Ae},Icon:Fe};return Object.defineProperty(oe,"__isScriptSetup",{enumerable:!1,value:!0}),oe}}),Qu={class:"equipment-attributes-section"},Xu={class:"flex items-center justify-between mb-4"},Yu={class:"flex items-center gap-3"},Zu={class:"font-semibold flex items-center gap-2 text-surface-900 dark:text-surface-0"},$u={class:"flex items-center gap-2"},ea={class:"p-4"},ta={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ua={class:"flex items-center gap-2"},aa={class:"flex-1"},la={class:"font-medium"},ra={class:"text-sm text-surface-600"},na={class:"flex items-end gap-2"},ia={key:1},sa={key:0},oa={key:0,class:"text-center pt-4"},da={key:1},ca={class:"mb-3 pb-2 border-b border-surface-200 dark:border-surface-700"},ma={class:"text-sm font-medium text-surface-700 dark:text-surface-300 flex items-center gap-2"},pa={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"},fa={class:"flex items-start justify-between"},va={class:"flex-1 min-w-0"},ba={class:"flex items-center gap-2 mb-1"},ga={class:"text-sm font-medium text-surface-900 dark:text-surface-0 truncate"},ya={class:"text-sm text-surface-600 dark:text-surface-400 mb-2 break-words"},ha=["title"],xa={key:0,class:"ml-2 flex flex-col gap-1 flex-shrink-0"},_a={key:0,class:"text-center pt-2"},Ea={key:2,class:"text-center py-6 text-surface-500 dark:text-surface-400"},ka={class:"space-y-4"},Aa={class:"flex items-center justify-between"},Ca={class:"font-medium text-surface-900 dark:text-surface-0"},Da={class:"text-sm text-surface-600 dark:text-surface-400"},Ta={class:"text-surface-500"};function wa(p,u,c,e,f,m){return i(),s("div",Qu,[a("div",Xu,[a("div",Yu,[a("h5",Zu,[r(e.Icon,{name:"pi pi-list",class:"text-green-600 w-4 h-4"}),u[5]||(u[5]=h(" Атрибуты модели "))]),e.totalAttributesCount>0?(i(),k(e.Tag,{key:0,value:`${e.filledAttributesCount}/${e.totalAttributesCount} заполнено`,severity:e.filledAttributesCount===e.totalAttributesCount?"success":"warn",size:"small"},null,8,["value","severity"])):x("",!0)]),a("div",$u,[e.hasAttributes?(i(),k(e.Button,{key:0,onClick:e.toggleViewMode,text:"",size:"small",title:e.viewMode==="grid"?"Переключить на табличный вид":"Переключить на сеточный вид"},{default:d(()=>[e.viewMode==="grid"?(i(),k(e.TableIcon,{key:0,class:"w-4 h-4"})):(i(),k(e.GridIcon,{key:1,class:"w-4 h-4"}))]),_:1},8,["title"])):x("",!0),e.canManageAttributes?(i(),k(e.Button,{key:1,onClick:u[0]||(u[0]=t=>e.showGroupDialog=!0),outlined:"",severity:"secondary",size:"small",class:"text-sm"},{default:d(()=>[r(e.TagsIcon,{class:"w-4 h-4 mr-1"}),u[6]||(u[6]=h(" Добавить группу "))]),_:1,__:[6]})):x("",!0),e.canManageAttributes?(i(),k(e.Button,{key:2,onClick:e.handleAddAttribute,size:"small",class:"text-sm"},{default:d(()=>[r(e.PlusIcon,{class:"w-4 h-4 mr-1"}),u[7]||(u[7]=h(" Добавить атрибут "))]),_:1,__:[7]})):x("",!0)])]),e.canManageAttributes?(i(),k(e.Card,{key:0,class:"mb-4"},{content:d(()=>[a("div",ea,[a("div",ta,[a("div",null,[u[8]||(u[8]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа шаблонов ",-1)),r(e.AutoComplete,{modelValue:e.selectedTemplateGroup,"onUpdate:modelValue":u[1]||(u[1]=t=>e.selectedTemplateGroup=t),suggestions:e.groupSuggestions,onComplete:e.filterGroups,"option-label":"name",placeholder:"Поиск группы...",class:"w-full",dropdown:""},null,8,["modelValue","suggestions"])]),a("div",null,[u[9]||(u[9]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Или отдельный шаблон ",-1)),r(e.AutoComplete,{modelValue:e.selectedTemplate,"onUpdate:modelValue":u[2]||(u[2]=t=>e.selectedTemplate=t),suggestions:e.templateSuggestions,onComplete:e.filterTemplates,"option-label":"title",placeholder:"Поиск шаблона...",class:"w-full",dropdown:""},{option:d(({option:t})=>[a("div",ua,[r(e.Icon,{name:e.getDataTypeIcon(t.dataType),class:"text-primary w-4 h-4"},null,8,["name"]),a("div",aa,[a("div",la,b(t.title),1),a("div",ra,b(t.group?.name)+" • "+b(e.getDataTypeDisplayName(t.dataType)),1)])])]),_:1},8,["modelValue","suggestions"])]),a("div",na,[r(e.Button,{onClick:e.loadSelectedGroupTemplates,size:"small",outlined:"",disabled:!e.selectedTemplateGroup||e.loadingTemplates,loading:e.loadingTemplates,label:"Добавить группу",class:"flex-1"},null,8,["disabled","loading"]),r(e.Button,{onClick:e.addSingleTemplate,size:"small",outlined:"",disabled:!e.selectedTemplate,label:"Добавить",class:"flex-1"},null,8,["disabled"])])])])]),_:1})):x("",!0),e.hasAttributes?(i(),s("div",ia,[e.viewMode==="table"?(i(),s("div",sa,[r(e.EquipmentAttributesList,{attributes:e.visibleAttributes,readonly:c.readonly,compact:!e.showAllAttributes&&e.shouldShowExpandButton,onEditAttribute:e.handleEditAttribute,onDeleteAttribute:e.handleDeleteAttribute},null,8,["attributes","readonly","compact"]),e.shouldShowExpandButton?(i(),s("div",oa,[r(e.Button,{onClick:e.toggleShowAll,text:"",size:"small",class:"text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"},{default:d(()=>[e.showAllAttributes?(i(),s(F,{key:1},[r(e.ChevronUpIcon,{class:"w-4 h-4 mr-1"}),u[10]||(u[10]=h(" Свернуть "))],64)):(i(),s(F,{key:0},[r(e.ChevronDownIcon,{class:"w-4 h-4 mr-1"}),h(" Показать все (еще "+b(e.hiddenAttributesCount)+") ",1)],64))]),_:1})])):x("",!0)])):(i(),s("div",da,[(i(!0),s(F,null,j(e.visibleGroupedAttributes,(t,l)=>(i(),s("div",{key:l,class:"attribute-group mb-4"},[a("div",ca,[a("h6",ma,[r(e.Icon,{name:"pi pi-folder",class:"w-3 h-3"}),h(" "+b(l)+" ",1),r(e.Tag,{severity:"info",value:t.length.toString(),class:"text-xs"},null,8,["value"])])]),a("div",pa,[(i(!0),s(F,null,j(t,o=>(i(),s("div",{key:o.id,class:"attribute-card p-3 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"},[a("div",fa,[a("div",va,[a("div",ba,[a("span",ga,b(o.template.title),1),o.template.isRequired?(i(),k(e.Tag,{key:0,severity:"danger",class:"text-xs flex-shrink-0"},{default:d(()=>u[11]||(u[11]=[h(" Обязательный ")])),_:1,__:[11]})):x("",!0)]),a("div",ya,b(e.formatAttribute(o).displayValue),1),o.template.description?(i(),s("div",{key:0,class:"text-xs text-surface-500 dark:text-surface-400 line-clamp-2",title:o.template.description},b(o.template.description),9,ha)):x("",!0)]),e.canManageAttributes?(i(),s("div",xa,[r(e.Button,{onClick:A=>e.handleEditAttribute(o.id),text:"",size:"small",class:"p-1 text-xs",title:`Редактировать ${o.template.title}`},{default:d(()=>[r(e.Icon,{name:"pi pi-pencil",class:"w-3 h-3"})]),_:2},1032,["onClick","title"]),r(e.Button,{onClick:A=>e.handleDeleteAttribute(o.id),text:"",severity:"danger",size:"small",class:"p-1 text-xs",title:`Удалить ${o.template.title}`,disabled:o.template.isRequired},{default:d(()=>[r(e.Icon,{name:"pi pi-trash",class:"w-3 h-3"})]),_:2},1032,["onClick","title","disabled"])])):x("",!0)])]))),128))])]))),128)),e.shouldShowExpandButton?(i(),s("div",_a,[r(e.Button,{onClick:e.toggleShowAll,text:"",size:"small",class:"text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"},{default:d(()=>[e.showAllAttributes?(i(),s(F,{key:1},[r(e.ChevronUpIcon,{class:"w-4 h-4 mr-1"}),u[12]||(u[12]=h(" Свернуть "))],64)):(i(),s(F,{key:0},[r(e.ChevronDownIcon,{class:"w-4 h-4 mr-1"}),h(" Показать все (еще "+b(e.hiddenAttributesCount)+") ",1)],64))]),_:1})])):x("",!0)]))])):(i(),s("div",Ea,[r(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[14]||(u[14]=a("p",{class:"text-sm mb-3"},"У данной модели техники нет атрибутов",-1)),e.canManageAttributes?(i(),k(e.Button,{key:0,onClick:e.handleAddAttribute,outlined:"",size:"small"},{default:d(()=>[r(e.PlusIcon,{class:"w-4 h-4 mr-1"}),u[13]||(u[13]=h(" Добавить первый атрибут "))]),_:1,__:[13]})):x("",!0)])),r(e.AddEquipmentAttributeDialog,{visible:e.showAddDialog,"onUpdate:visible":u[3]||(u[3]=t=>e.showAddDialog=t),"equipment-id":c.equipmentId,"existing-attributes":c.attributes||[],onSave:e.handleSaveAttribute,onCancel:e.handleCancelAddAttribute},null,8,["visible","equipment-id","existing-attributes"]),r(e.Dialog,{visible:e.showGroupDialog,"onUpdate:visible":u[4]||(u[4]=t=>e.showGroupDialog=t),modal:"",header:"Выбор группы шаблонов",style:{width:"40rem"}},{default:d(()=>[a("div",ka,[(i(!0),s(F,null,j(e.templateGroups,t=>(i(),s("div",{key:t.id,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4"},[a("div",Aa,[a("div",null,[a("h4",Ca,b(t.name),1),a("p",Da,b(t.description),1),a("small",Ta,b(t._count?.templates||0)+" шаблонов",1)]),r(e.Button,{onClick:l=>e.loadTemplatesByGroupId(t.id),size:"small",loading:e.loadingTemplates},{default:d(()=>u[15]||(u[15]=[h(" Добавить все ")])),_:2,__:[15]},1032,["onClick","loading"])])]))),128))])]),_:1},8,["visible"])])}const Ia=be(Ku,[["render",wa],["__scopeId","data-v-2cbb67a9"]]),Ba=ge({__name:"EquipmentList",props:{initialData:{}},setup(p,{expose:u}){u();const c=E(""),e=E(!1),f=E(null),m=E([]),t=E({}),l=E({}),o=p,A=E(o.initialData),D={id:"ID",name:"Наименование",brandId:"Бренд ID",createdAt:"Создано",updatedAt:"Обновлено"},w=["id","name","createdAt"];function B(){f.value={},e.value=!0}function C(n){f.value={...n},e.value=!0}async function L(n){if(n)try{if(n.id){const{id:y,...O}=n;await W.crud.equipmentModel.update.mutate({where:{id:y},data:O})}else if(n.name)await W.crud.equipmentModel.create.mutate({data:{name:n.name,brandId:n.brandId||null}});else{console.error("Name is required to create an equipment model.");return}Ie(window.location.href)}catch(y){console.error("Failed to save equipment model:",y)}finally{e.value=!1}}function R(){e.value=!1,f.value=null}async function K(n){e.value=!1,await W.crud.equipmentModel.delete.mutate({where:{id:n.id}}),Ie(window.location.href)}me(c,n=>{H(n)});async function H(n=""){console.log("value",n),A.value=await W.crud.equipmentModel.findMany.query({where:{OR:[{name:{contains:n}},{brand:{name:{contains:n}}}]},include:{brand:{select:{name:!0}},_count:{select:{partApplicabilities:!0,attributes:!0}}}})}function g(n){return new Date(n).toLocaleDateString("ru-RU",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}async function N(n){if(t.value[n])return t.value[n];try{const y=await W.crud.equipmentApplicability.findMany.query({where:{equipmentModelId:n},include:{part:{include:{partCategory:!0,applicabilities:{include:{catalogItem:{include:{brand:!0}}}},_count:{select:{attributes:!0,applicabilities:!0}}}}},orderBy:{part:{name:"asc"}}});return t.value[n]=y||[],y||[]}catch(y){return console.error("Failed to load equipment parts:",y),[]}}async function U(n){if(l.value[n])return l.value[n];try{const O=(await W.crud.equipmentModelAttribute.findMany.query({where:{equipmentModelId:n},include:{template:{include:{group:!0}}},orderBy:[{template:{group:{name:"asc"}}},{template:{title:"asc"}}]})).map(G=>({...G,template:{...G.template,group:G.template.group}}));return l.value[n]=O,O}catch(y){return console.error("Failed to load equipment attributes:",y),[]}}async function re(n){n.data._count.partApplicabilities>0&&await N(n.data.id),n.data._count.attributes>0&&await U(n.data.id)}function Q(n){return{EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"}[n]||n}function X(n){return{EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"}[n]||"secondary"}function ne(n){console.log("Edit part:",n)}function te(n){const y={};return n.forEach(O=>{const G=O.template.group?.name||"Общие";y[G]||(y[G]=[]),y[G].push(O)}),y}function J(n){const{value:y,template:O}=n,{dataType:G,unit:he}=O;switch(G){case"STRING":return y;case"NUMBER":const Y=parseFloat(y),xe=isNaN(Y)?y:Y.toLocaleString("ru-RU");return he?`${xe} ${ue(he)}`:xe;case"BOOLEAN":return y.toLowerCase()==="true"?"Да":"Нет";case"DATE":const oe=new Date(y);return isNaN(oe.getTime())?y:oe.toLocaleDateString("ru-RU");case"JSON":try{return JSON.stringify(JSON.parse(y),null,2)}catch{return y}default:return y}}function ue(n){return n?{MM:"мм",INCH:"дюйм",FT:"фт",G:"г",KG:"кг",T:"т",LB:"фунт",ML:"мл",L:"л",GAL:"гал",SEC:"сек",MIN:"мин",H:"ч",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"psi",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[n]||n:""}async function ae(n){m.value.some(O=>O.id===n.id)||(m.value.push(n),n._count.attributes>0&&!l.value[n.id]&&await U(n.id))}async function ie(n){try{await W.crud.equipmentModelAttribute.create.mutate({data:{equipmentModelId:n.equipmentId||n.equipmentModelId,templateId:n.templateId,value:String(n.value)}}),n.equipmentId&&(delete l.value[n.equipmentId],await U(n.equipmentId)),console.log("Attribute added successfully")}catch(y){console.error("Failed to add attribute:",y)}}function se(n){console.log("Edit attribute:",n)}function le(n){console.log("Delete attribute:",n)}const v={searchValue:c,dialogVisible:e,editingEquipment:f,expandedRows:m,equipmentPartsCache:t,equipmentAttributesCache:l,props:o,items:A,keyMapping:D,columnKeys:w,createEquipment:B,editEquipment:C,handleSave:L,handleCancel:R,deleteEquipment:K,debouncedSearch:H,formatDate:g,loadEquipmentParts:N,loadEquipmentAttributes:U,onRowExpand:re,getAccuracyLabel:Q,getAccuracySeverity:X,editPart:ne,groupAttributesByGroup:te,formatAttributeValue:J,getUnitDisplayName:ue,expandRowForAttributes:ae,handleAddAttribute:ie,handleEditAttribute:se,handleDeleteAttribute:le,Button:ve,DataTable:Ue,get PencilIcon(){return Xe},get TrashIcon(){return Qe},get PlusIcon(){return Ze},get ExternalLinkIcon(){return xt},get Column(){return ze},EditEquipmentDialog:Bt,EquipmentAttributesSection:Ia,get navigate(){return Ie},InputText:Ve,Tag:Ce,Icon:Fe};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}}),Sa={class:"flex justify-between items-center mb-4"},Va={class:"flex justify-end"},qa={class:"font-mono text-sm"},Ma={key:1,class:"text-surface-500"},Na={key:1,class:"text-surface-500"},Fa={class:"flex gap-2"},Ra={class:"p-4 bg-surface-50 dark:bg-surface-800"},Oa={key:0,class:"mb-6"},La={key:1},Ga={class:"mb-3 font-semibold flex items-center gap-2"},Pa={key:0},Ua={class:"grid gap-3"},za={class:"flex items-start justify-between"},ja={class:"flex-1"},Ha={class:"flex items-center gap-2 mb-2"},Ja={class:"font-medium text-surface-900 dark:text-surface-0"},Wa={class:"flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2"},Ka={class:"flex items-center gap-1"},Qa={class:"flex items-center gap-1"},Xa={key:0,class:"mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400"},Ya={key:1,class:"mt-3"},Za={class:"flex flex-wrap gap-2"},$a={class:"font-medium"},e0={class:"text-surface-500"},t0={key:0,class:"px-2 py-1 bg-surface-200 dark:bg-surface-700 rounded text-xs text-surface-600 dark:text-surface-400"},u0={class:"ml-4 flex flex-col gap-2"},a0={key:1,class:"text-center py-6 text-surface-500 dark:text-surface-400"},l0={key:2,class:"text-center py-6 text-surface-500 dark:text-surface-400"};function r0(p,u,c,e,f,m){return i(),s("div",null,[a("div",Sa,[u[4]||(u[4]=a("h1",{class:"text-2xl font-bold"},"Модели техники",-1)),r(e.Button,{onClick:e.createEquipment},{default:d(()=>[r(e.PlusIcon,{class:"w-5 h-5 mr-2"}),u[3]||(u[3]=h(" Создать модель "))]),_:1,__:[3]})]),r(e.DataTable,{"show-headers":"",value:e.items,expandedRows:e.expandedRows,"onUpdate:expandedRows":u[1]||(u[1]=t=>e.expandedRows=t),onRowExpand:e.onRowExpand,rowHover:!0},{header:d(()=>[a("div",Va,[r(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":u[0]||(u[0]=t=>e.searchValue=t),placeholder:"Поиск"},null,8,["modelValue"])])]),expansion:d(({data:t})=>[a("div",Ra,[(t._count.attributes>0,i(),s("div",Oa,[r(e.EquipmentAttributesSection,{"equipment-id":t.id,attributes:e.equipmentAttributesCache[t.id],onAddAttribute:l=>e.handleAddAttribute({...l,equipmentId:t.id}),onEditAttribute:e.handleEditAttribute,onDeleteAttribute:e.handleDeleteAttribute},null,8,["equipment-id","attributes","onAddAttribute"])])),t._count.partApplicabilities>0?(i(),s("div",La,[a("h5",Ga,[r(e.Icon,{name:"pi pi-wrench",class:"text-blue-600 w-4 h-4"}),h(" Запчасти для: "+b(t.name),1)]),e.equipmentPartsCache[t.id]&&e.equipmentPartsCache[t.id].length>0?(i(),s("div",Pa,[a("div",Ua,[(i(!0),s(F,null,j(e.equipmentPartsCache[t.id],l=>(i(),s("div",{key:l.id,class:"p-4 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"},[a("div",za,[a("div",ja,[a("div",Ha,[a("h6",Ja,b(l.part?.name||"Без названия"),1),l.part?.partCategory?(i(),k(e.Tag,{key:0,severity:"info",class:"text-xs"},{default:d(()=>[h(b(l.part.partCategory.name),1)]),_:2},1024)):x("",!0)]),a("div",Wa,[a("span",Ka,[r(e.Icon,{name:"pi pi-list",class:"w-3 h-3"}),h(" "+b(l.part?._count?.attributes||0)+" атрибутов ",1)]),a("span",Qa,[r(e.Icon,{name:"pi pi-box",class:"w-3 h-3"}),h(" "+b(l.part?._count?.applicabilities||0)+" каталожных позиций ",1)])]),l.notes?(i(),s("div",Xa,[r(e.Icon,{name:"pi pi-info-circle",class:"mr-1 w-4 h-4 inline-block"}),h(" "+b(l.notes),1)])):x("",!0),l.part?.applicabilities?.length>0?(i(),s("div",Ya,[u[5]||(u[5]=a("div",{class:"text-xs font-medium text-surface-700 dark:text-surface-300 mb-2"}," Каталожные позиции: ",-1)),a("div",Za,[(i(!0),s(F,null,j(l.part.applicabilities.slice(0,3),o=>(i(),s("div",{key:o.id,class:"flex items-center gap-1 px-2 py-1 bg-surface-100 dark:bg-surface-800 rounded text-xs"},[a("span",$a,b(o.catalogItem?.sku),1),a("span",e0,b(o.catalogItem?.brand?.name),1),r(e.Tag,{severity:e.getAccuracySeverity(o.accuracy),class:"text-xs"},{default:d(()=>[h(b(e.getAccuracyLabel(o.accuracy)),1)]),_:2},1032,["severity"])]))),128)),l.part.applicabilities.length>3?(i(),s("div",t0," +"+b(l.part.applicabilities.length-3)+" еще ",1)):x("",!0)])])):x("",!0)]),a("div",u0,[r(e.Button,{onClick:o=>e.editPart(l.part.id),outlined:"",size:"small",class:"text-xs"},{default:d(()=>[r(e.PencilIcon,{class:"w-3 h-3 mr-1"}),u[6]||(u[6]=h(" Редактировать "))]),_:2,__:[6]},1032,["onClick"]),r(e.Button,{onClick:()=>e.navigate(`/admin/parts/${l.part.id}`),outlined:"",severity:"secondary",size:"small",class:"text-xs"},{default:d(()=>[r(e.ExternalLinkIcon,{class:"w-3 h-3 mr-1"}),u[7]||(u[7]=h(" Подробнее "))]),_:2,__:[7]},1032,["onClick"])])])]))),128))])])):(i(),s("div",a0,[r(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[8]||(u[8]=h(" Запчасти для данной модели техники не найдены "))]))])):x("",!0),t._count.attributes===0&&t._count.partApplicabilities===0?(i(),s("div",l0,[r(e.Icon,{name:"pi pi-info-circle",class:"text-2xl mb-2 inline-block"}),u[9]||(u[9]=h(" Для данной модели техники нет дополнительной информации "))])):x("",!0)])]),default:d(()=>[r(e.Column,{expander:!0,headerStyle:"width: 3rem"}),(i(),s(F,null,j(e.columnKeys,t=>r(e.Column,{key:t,field:t,header:e.keyMapping[t]||t},nt({_:2},[t==="createdAt"?{name:"body",fn:d(({data:l})=>[h(b(e.formatDate(l[t])),1)]),key:"0"}:t==="id"?{name:"body",fn:d(({data:l})=>[a("div",qa,b(l[t].substring(0,8))+"... ",1)]),key:"1"}:void 0]),1032,["field","header"])),64)),r(e.Column,{field:"brand.name",header:"Бренд"},{body:d(({data:t})=>[h(b(t.brand?.name||"-"),1)]),_:1}),r(e.Column,{field:"_count.partApplicabilities",header:"Применимость деталей"},{body:d(({data:t})=>[t._count.partApplicabilities>0?(i(),k(e.Tag,{key:0,severity:"info",value:t._count.partApplicabilities.toString()},null,8,["value"])):(i(),s("span",Ma,"0"))]),_:1}),r(e.Column,{field:"_count.attributes",header:"Атрибуты"},{body:d(({data:t})=>[t._count.attributes>0?(i(),k(e.Tag,{key:0,severity:"secondary",value:t._count.attributes.toString(),class:"cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors",onClick:l=>e.expandRowForAttributes(t)},null,8,["value","onClick"])):(i(),s("span",Na,"0"))]),_:1}),r(e.Column,{header:"Действия"},{body:d(({data:t})=>[a("div",Fa,[r(e.Button,{onClick:l=>e.editEquipment(t),outlined:"",size:"small"},{default:d(()=>[r(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),r(e.Button,{onClick:l=>e.deleteEquipment(t),outlined:"",severity:"danger",size:"small"},{default:d(()=>[r(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","expandedRows"]),r(e.EditEquipmentDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":u[2]||(u[2]=t=>e.dialogVisible=t),equipment:e.editingEquipment,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","equipment"])])}const i4=be(Ba,[["render",r0]]);export{i4 as default};

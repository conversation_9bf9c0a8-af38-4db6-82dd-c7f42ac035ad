<template>
  <div class="part-wizard">
    <VCard>
      <template #header>
        <div
          class="flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700"
        >
          <h2
            class="text-xl font-semibold text-surface-900 dark:text-surface-0"
          >
            Мастер создания запчасти
          </h2>
          <div
            class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"
          >
            Шаг {{ currentStep }} из {{ totalSteps }}
          </div>
        </div>
      </template>

      <template #content>
        <div class="p-6">
          <!-- Вкладки шагов -->
          <div class="mb-4">
            <VTabs :value="activeStepKey" @update:value="onTabChange">
              <VTabList>
                <VTab
                  v-for="(step, index) in steps"
                  :key="step.key"
                  :value="step.key"
                >
                  <span class="text-sm">
                    {{ index + 1 }}. {{ step.title }}
                  </span>
                </VTab>
              </VTabList>
            </VTabs>
          </div>

          <!-- Содержимое шагов -->
          <div class="min-h-96">
            <!-- Шаг 1: Основная информация -->
            <div v-if="currentStep === 1" class="space-y-6">
              <h3
                class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"
              >
                Основная информация о запчасти
              </h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    Название запчасти *
                  </label>
                  <VInputText
                    v-model="formData.name"
                    placeholder="Например: Сальник коленвала передний"
                    class="w-full p-3"
                  />
                </div>

                <div>
                  <label
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    Категория *
                  </label>
                  <div class="flex gap-2">
                    <VAutoComplete
                      v-model="selectedCategory"
                      :suggestions="categorySuggestions"
                      @complete="searchCategories"
                      option-label="name"
                      placeholder="Поиск категории..."
                      class="flex-1"
                      dropdown
                    />
                    <VButton
                      @click="showCreateCategory = true"
                      severity="secondary"
                      outlined
                      size="small"
                      label="Создать новую категорию"
                    >
                      +
                    </VButton>
                  </div>
                </div>
              </div>

              <!-- Изображение (только в режиме редактирования) -->
              <div v-if="mode === 'edit' && props.part" class="mt-4">
                <h4 class="text-surface-900 dark:text-surface-0 mb-2 font-medium">Изображение запчасти</h4>
                <div class="flex items-center gap-4">
                  <div class="w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden">
                    <img
                      v-if="partImageUrl"
                      :src="resolveMediaUrl(partImageUrl)"
                      alt="Изображение запчасти"
                      class="object-cover w-full h-full"
                    />
                    <span v-else class="text-surface-500 text-sm">Нет изображения</span>
                  </div>
                  <div class="flex flex-col gap-2">
                    <input type="file" accept="image/*" @change="onSelectPartImage" />
                    <div class="flex gap-2">
                      <VButton size="small" :disabled="!selectedFile || uploading" @click="uploadPartImage">Загрузить</VButton>
                      <VButton size="small" severity="danger" outlined :disabled="!partImageUrl || uploading" @click="removePartImage">Удалить</VButton>
                    </div>
                    <div v-if="uploading" class="text-surface-500 text-xs">Загрузка...</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Шаг 2: Атрибуты -->
            <div v-if="currentStep === 2" class="space-y-6">
              <AttributeManager v-model="formData.attributes" />
            </div>

            <!-- Шаг 3: Каталожные позиции -->
            <div v-if="currentStep === 3" class="space-y-6">
              <CatalogItemWizardEditor v-model="formData.catalogItems" />
            </div>

            <!-- Шаг 4: Применимость к технике -->
            <div v-if="currentStep === 4" class="space-y-6">
              <EquipmentSelector v-model="formData.equipmentApplicabilities" />
            </div>

            <!-- Шаг 5: Подтверждение -->
            <div v-if="currentStep === 5" class="space-y-6">
              <h3
                class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"
              >
                Подтверждение создания
              </h3>

              <div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6">
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Основная информация:
                </h4>
                <dl class="space-y-2">
                  <div class="flex">
                    <dt class="w-32 text-surface-600 dark:text-surface-400">
                      Название:
                    </dt>
                    <dd class="text-surface-900 dark:text-surface-0">
                      {{ formData.name }}
                    </dd>
                  </div>
                  <div class="flex">
                    <dt class="w-32 text-surface-600 dark:text-surface-400">
                      Категория:
                    </dt>
                    <dd class="text-surface-900 dark:text-surface-0">
                      {{ selectedCategory?.name }}
                    </dd>
                  </div>
                </dl>
              </div>

              <!-- Атрибуты -->
              <div
                v-if="formData.attributes.length > 0"
                class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"
              >
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Атрибуты ({{ formData.attributes.length }}):
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="(attribute, index) in formData.attributes"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"
                  >
                    <div class="flex-1">
                      <div
                        class="font-medium text-surface-900 dark:text-surface-0"
                      >
                        {{
                          attribute.template?.title || attribute.templateTitle
                        }}
                        <span
                          v-if="attribute.template?.isRequired"
                          class="text-red-500 ml-1"
                          >*</span
                        >
                      </div>
                      <div
                        class="text-sm text-surface-600 dark:text-surface-400"
                      >
                        {{ attribute.value
                        }}{{
                          attribute.template?.unit || attribute.templateUnit
                            ? `
                        ${getUnitLabel(
                          attribute.template?.unit || attribute.templateUnit
                        )}`
                            : ""
                        }}
                        <span
                          v-if="
                            attribute.template?.group?.name ||
                            attribute.templateGroup
                          "
                          class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"
                        >
                          {{
                            attribute.template?.group?.name ||
                            attribute.templateGroup
                          }}
                        </span>
                      </div>
                    </div>
                    <div class="text-xs text-surface-500 dark:text-surface-400">
                      {{
                        getDataTypeLabel(
                          attribute.template?.dataType ||
                            attribute.templateDataType
                        )
                      }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6">
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Каталожные позиции ({{ formData.catalogItems.length }}):
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="(item, index) in formData.catalogItems"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"
                  >
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">
                          {{
                            item.isExisting
                              ? item.existingCatalogItem?.sku
                              : item.sku
                          }}
                        </span>
                        <span class="text-surface-600 dark:text-surface-400">
                          ({{
                            item.isExisting
                              ? item.existingCatalogItem?.brand?.name
                              : item.selectedBrand?.name
                          }})
                        </span>
                        <span
                          v-if="item.isExisting"
                          class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"
                        >
                          Существующая
                        </span>
                        <span
                          v-else
                          class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                        >
                          Новая
                        </span>
                      </div>
                      <div
                        class="text-sm text-surface-600 dark:text-surface-400 mt-1"
                      >
                        Точность: {{ getAccuracyLabel(item.accuracy) }}
                        <span v-if="item.notes" class="ml-2"
                          >• {{ item.notes }}</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Применимость к технике -->
              <div
                v-if="formData.equipmentApplicabilities.length > 0"
                class="bg-surface-50 dark:bg-surface-900 rounded-lg p-6"
              >
                <h4
                  class="font-medium text-surface-900 dark:text-surface-0 mb-4"
                >
                  Применимость к технике ({{
                    formData.equipmentApplicabilities.length
                  }}):
                </h4>
                <div class="space-y-3">
                  <div
                    v-for="(
                      equipment, index
                    ) in formData.equipmentApplicabilities"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"
                  >
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">
                          {{
                            equipment.isExisting
                              ? equipment.existingEquipmentModel?.name
                              : equipment.name
                          }}
                        </span>
                        <span
                          v-if="
                            equipment.selectedBrand ||
                            equipment.existingEquipmentModel?.brand
                          "
                          class="text-surface-600 dark:text-surface-400"
                        >
                          ({{
                            equipment.selectedBrand?.name ||
                            equipment.existingEquipmentModel?.brand?.name
                          }})
                        </span>
                        <span
                          v-if="equipment.isExisting"
                          class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"
                        >
                          Существующая
                        </span>
                        <span
                          v-else
                          class="px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"
                        >
                          Новая
                        </span>
                      </div>
                      <div
                        v-if="equipment.notes"
                        class="text-sm text-surface-600 dark:text-surface-400 mt-1"
                      >
                        {{ equipment.notes }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Кнопки навигации -->
          <div
            class="flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700"
          >
            <VButton
              v-if="currentStep > 1"
              @click="previousStep"
              severity="secondary"
              outlined
            >
              Назад
            </VButton>
            <div v-else></div>

            <div class="flex gap-3">
              <VButton
                v-if="currentStep < totalSteps"
                @click="nextStep"
                :disabled="!canProceed"
                label="Далее"
                outlined
              />
              <VButton
                :label="
                  mode === 'edit' ? 'Сохранить изменения' : 'Создать запчасть'
                "
                v-else
                @click="savePart"
                :loading="loading || saving"
                :disabled="!canFinish || saving"
              ></VButton>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Сообщение об ошибке -->
    <VMessage v-if="error" severity="error" class="mt-4">
      {{ error }}
    </VMessage>
    <VMessage v-if="saveError" severity="error" class="mt-4">
      {{ saveError }}
    </VMessage>

    <!-- Диалоги быстрого создания -->
    <QuickCreateCategory
      v-model:visible="showCreateCategory"
      @created="onCategoryCreated"
    />

    <QuickCreateBrand
      v-model:visible="showCreateBrand"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import VTabs from "@/volt/Tabs.vue";
import VTabList from "@/volt/TabList.vue";
import VTab from "@/volt/Tab.vue";
import { useTrpc } from "@/composables/useTrpc";
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VInputText from "@/volt/InputText.vue";
import VAutoComplete from "@/volt/AutoComplete.vue";
import VMessage from "@/volt/Message.vue";
import QuickCreateCategory from "./QuickCreateCategory.vue";
import QuickCreateBrand from "./QuickCreateBrand.vue";
import AttributeManager from "@/components/admin/attributes/AttributeManager.vue";
import EquipmentSelector from "./EquipmentSelector.vue";
import CatalogItemWizardEditor from "../catalogitems/CatalogItemWizardEditor.vue";
import { fileToBase64, resolveMediaUrl } from '@/lib/utils'

// Props для поддержки редактирования
interface Props {
  part?: any; // Запчасть для редактирования (если null - создание новой)
  mode?: "create" | "edit";
}

const props = withDefaults(defineProps<Props>(), {
  part: null,
  mode: "create",
});

// Emits
interface Emits {
  (e: "created", part: any): void;
  (e: "updated", part: any): void;
}

const emit = defineEmits<Emits>();

// Интерфейсы
interface CatalogItemForm {
  // Для создания новой позиции
  sku: string;
  brandId: number | "";
  selectedBrand: any;
  description: string;

  // Для поиска существующей позиции
  isExisting?: boolean; // true если выбрана существующая позиция
  existingCatalogItem?: any; // выбранная существующая позиция

  // Уровень точности применимости
  accuracy:
    | "EXACT_MATCH"
    | "MATCH_WITH_NOTES"
    | "REQUIRES_MODIFICATION"
    | "PARTIAL_MATCH";
  notes?: string; // примечания для accuracy
}

interface AttributeForm {
  id?: number; // ID существующего атрибута (для редактирования)
  templateId: number; // ID шаблона
  value: string; // Значение атрибута
  isRequired?: boolean; // Добавлено

  // Информация из шаблона (для отображения)
  template?: {
    id: number;
    name: string;
    title: string;
    description?: string;
    dataType: string;
    unit?: string;
    isRequired?: boolean; // Добавлено
    group?: {
      id: number;
      name: string;
    };
  };

  // Альтернативные поля для новых атрибутов (до сохранения)
  templateTitle?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

interface EquipmentApplicabilityForm {
  // Для создания новой модели
  name: string;
  selectedBrand: any;

  // Для поиска существующей модели
  isExisting?: boolean; // true если выбрана существующая модель
  existingEquipmentModel?: any; // выбранная существующая модель

  // Примечания
  notes?: string;
}

interface PartForm {
  name: string;
  attributes: AttributeForm[];
  catalogItems: CatalogItemForm[];
  equipmentApplicabilities: EquipmentApplicabilityForm[];
}

// Шаги мастера
const steps = [
  { title: "Основная информация", key: "basic" },
  { title: "Атрибуты", key: "attributes" },
  { title: "Каталожные позиции", key: "catalog" },
  { title: "Применимость к технике", key: "equipment" },
  { title: "Подтверждение", key: "confirm" },
];

const totalSteps = steps.length;
const currentStep = ref(1);
const activeStepKey = computed({
  get: () => steps[currentStep.value - 1]?.key,
  set: (val: string) => {
    const index = steps.findIndex((s) => s.key === val);
    if (index >= 0) currentStep.value = index + 1;
  },
});

// Данные формы
const formData = ref<PartForm>({
  name: "",
  attributes: [],
  catalogItems: [],
  equipmentApplicabilities: [],
});

// tRPC клиент
const {
  loading,
  error,
  clearError,
  partCategories,
  parts,
  catalogItems,
  equipmentModels,
  partAttributes,
  client,
  partApplicability,
  equipmentApplicability,
  media,
} = useTrpc();

// Удалены опции точности - теперь в CatalogItemEditor

// Удалены опции для типа каталожной позиции - теперь в CatalogItemEditor

// Удалены методы фильтрации точности - теперь в CatalogItemEditor

// Данные для автокомплита
const selectedCategory = ref<any>(null);
const categorySuggestions = ref<any[]>([]);

// Данные для автокомплита точности применимости (объявляется после accuracyOptions)

// Диалоги быстрого создания
const showCreateCategory = ref(false);
const showCreateBrand = ref(false);

// Локальные состояния процесса сохранения
const saving = ref(false);
const saveError = ref<string | null>(null);

// Изображение запчасти (режим редактирования)
const partImageUrl = computed(() => props.part?.image?.url || null)
const selectedFile = ref<File | null>(null)
const uploading = ref(false)

const onSelectPartImage = (e: Event) => {
  const input = e.target as HTMLInputElement
  if (input.files && input.files[0]) {
    selectedFile.value = input.files[0]
  }
}

const uploadPartImage = async () => {
  if (!props.part?.id || !selectedFile.value) return
  try {
    uploading.value = true
    const dataUrl = await fileToBase64(selectedFile.value)
    await media.uploadPartImage({
      partId: props.part.id,
      fileName: selectedFile.value.name,
      fileData: dataUrl,
      mimeType: selectedFile.value.type || 'image/png',
    })
    // обновим props.part через повторную загрузку данных
    await loadPartData()
    selectedFile.value = null
  } finally {
    uploading.value = false
  }
}

const removePartImage = async () => {
  if (!props.part?.id) return
  try {
    uploading.value = true
    await media.deletePartImage({ partId: props.part.id })
    await loadPartData()
  } finally {
    uploading.value = false
  }
}

// Обработчики быстрого создания
const onCategoryCreated = (category: any) => {
  selectedCategory.value = category;
  categorySuggestions.value = [category, ...categorySuggestions.value];
};

// Удален метод onBrandCreated - теперь в CatalogItemEditor

// Поиск категорий
const searchCategories = async (event: any) => {
  const query = event.query.toLowerCase();
  const categories = await partCategories.findMany({
    where: {
      name: {
        contains: query,
        mode: "insensitive",
      },
    },
    take: 10,
  });

  if (categories && Array.isArray(categories)) {
    categorySuggestions.value = categories;
  }
};

// Удалены методы поиска - теперь в CatalogItemEditor

// Вычисляемые свойства
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1:
      return formData.value.name.trim() && selectedCategory.value;
    case 2:
      // Требуем заполнения обязательных атрибутов
      return formData.value.attributes.every((attribute) => {
        const isRequired = Boolean(attribute.template?.isRequired || attribute.isRequired);
        if (!isRequired) return true;
        const dataType = attribute.template?.dataType || attribute.templateDataType;
        const value = attribute.value;
        if (dataType === 'BOOLEAN') {
          return value !== null && value !== undefined;
        }
        return String(value ?? '').trim().length > 0;
      });
    case 3:
      // Каталожные позиции необязательны: если ничего не добавлено, можно продолжать.
      // Если добавлены, валидируем каждую позицию.
      return formData.value.catalogItems.every((item) => {
        if (item.isExisting) {
          return item.existingCatalogItem && item.accuracy;
        } else {
          return item.sku.trim() && item.selectedBrand && item.accuracy;
        }
      });
    case 4:
      // Техника не обязательна, можно пропустить
      return formData.value.equipmentApplicabilities.every((equipment) => {
        if (equipment.isExisting) {
          return equipment.existingEquipmentModel;
        } else {
          return equipment.name.trim();
        }
      });
    case 5:
      return true;
    default:
      return false;
  }
});

// Отдельная проверка для финального шага (кнопка создания/сохранения)
const canFinish = computed(() => canProceed.value && currentStep.value === totalSteps);

// Методы навигации
const nextStep = () => {
  if (currentStep.value < totalSteps && canProceed.value) {
    currentStep.value++;
    clearError();
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
    clearError();
  }
};

// Переключение вкладок с учётом валидации предыдущих шагов
const onTabChange = (nextKey: string) => {
  const nextIndex = steps.findIndex((s) => s.key === nextKey) + 1;
  if (nextIndex === currentStep.value) return;

  // Запрещаем переход вперёд, если текущий шаг не валиден
  if (nextIndex > currentStep.value && !canProceed.value) return;

  // Разрешаем свободную навигацию назад
  currentStep.value = nextIndex;
  clearError();
};

// Удалены методы работы с каталожными позициями - теперь в CatalogItemEditor

// Создание или обновление запчасти
const savePart = async () => {
  if (!canProceed.value) return;

  try {
    saving.value = true;
    saveError.value = null;
    clearError();

    const partData = {
      name: formData.value.name,
      partCategoryId: typeof selectedCategory.value === 'object'
        ? selectedCategory.value.id
        : selectedCategory.value,
      level: props.part?.level || 0,
      path: props.part?.path || "/",
    } as any;

    let partResult: any = null;
    if (props.mode === "edit" && props.part) {
      partResult = await parts.update({ where: { id: props.part.id }, data: partData });
    } else {
      partResult = await parts.create({ data: partData });
    }

    if (!partResult || !partResult.id) {
      throw new Error(`Не удалось ${props.mode === 'edit' ? 'обновить' : 'создать'} запчасть`);
    }
    const partId = partResult.id as number;

    // Атрибуты: дедупликация и батчи
    const attributeByTemplate = new Map<number, any>();
    for (const attribute of formData.value.attributes) {
      if (!attribute.templateId) continue;
      attributeByTemplate.set(attribute.templateId, attribute);
    }
    const attributesToCreate: Array<{ templateId: number; value: string }> = [];
    const attributesToUpdate: Array<{ id: number; value: string }> = [];
    attributeByTemplate.forEach((attribute) => {
      const raw = attribute.value;
      const hasValue = !(raw === null || raw === undefined || String(raw).trim() === '');
      if (!hasValue) return;
      if (attribute.id) {
        attributesToUpdate.push({ id: attribute.id, value: String(raw).trim() });
      } else {
        attributesToCreate.push({ templateId: attribute.templateId, value: String(raw).trim() });
      }
    });
    const attributeOps: Promise<any>[] = [];
    if (attributesToCreate.length > 0) {
      attributeOps.push(partAttributes.bulkCreate({ partId, attributes: attributesToCreate }) as any);
    }
    if (attributesToUpdate.length > 0) {
      attributeOps.push(Promise.all(attributesToUpdate.map((a) => partAttributes.update({ id: a.id, value: a.value }))) as any);
    }
    if (attributeOps.length > 0) {
      await Promise.all(attributeOps);
    }

    // Каталожные позиции: собрать id
    const catalogItemIds: Array<{ id: number; accuracy: CatalogItemForm['accuracy']; notes?: string }> = [];
    const createCatalogErrors: string[] = [];
    for (const item of formData.value.catalogItems) {
      if (item.isExisting && item.existingCatalogItem) {
        catalogItemIds.push({ id: item.existingCatalogItem.id, accuracy: item.accuracy, notes: item.notes || undefined });
      }
    }
    const newItems = formData.value.catalogItems.filter((i) => !i.isExisting);
    const newCreates = newItems.map(async (item) => {
      let brandId: number | null = null;
      if (typeof item.selectedBrand === 'object' && item.selectedBrand?.id) brandId = item.selectedBrand.id;
      else if (typeof item.selectedBrand === 'number') brandId = item.selectedBrand;
      else if (typeof item.brandId === 'number') brandId = item.brandId;
      if (!brandId) {
        createCatalogErrors.push(`Не выбран бренд для каталожной позиции ${item.sku}`);
        return null;
      }
      const catalogResult: any = await catalogItems.create({
        data: {
          sku: item.sku.toUpperCase().trim(),
          brandId,
          description: item.description || undefined,
          isPublic: true,
        },
      });
      if (!catalogResult || !catalogResult.id) {
        createCatalogErrors.push(`Не удалось создать каталожную позицию ${item.sku}`);
        return null;
      }
      return { id: catalogResult.id as number, accuracy: item.accuracy, notes: item.notes || undefined };
    });
    const createdCatalog = await Promise.all(newCreates);
    for (const c of createdCatalog) { if (c) catalogItemIds.push(c); }

    // Upsert применимости Part ↔ CatalogItem
    const applicabilityErrors: string[] = [];
    const applicabilityOps = catalogItemIds.map((c) =>
      partApplicability.upsert({
        where: { partId_catalogItemId: { partId, catalogItemId: c.id } },
        update: { accuracy: c.accuracy, notes: c.notes },
        create: { partId, catalogItemId: c.id, accuracy: c.accuracy, notes: c.notes },
      })
    );
    const applicabilityResults = await Promise.all(applicabilityOps);
    applicabilityResults.forEach((res, idx) => { if (!res) applicabilityErrors.push(`Связь с каталожной позицией #${catalogItemIds[idx].id} не сохранена`); });

    // Модели техники: собрать id
    const equipmentIds: Array<{ id: string; notes?: string }> = [];
    const createEquipmentErrors: string[] = [];
    for (const eq of formData.value.equipmentApplicabilities) {
      if (eq.isExisting && eq.existingEquipmentModel) {
        equipmentIds.push({ id: eq.existingEquipmentModel.id, notes: eq.notes || undefined });
      }
    }
    const newEquipments = formData.value.equipmentApplicabilities.filter((e) => !e.isExisting);
    const newEquipCreates = newEquipments.map(async (e) => {
      const data: any = { name: e.name.trim() };
      if (e.selectedBrand) data.brandId = (typeof e.selectedBrand === 'object') ? e.selectedBrand.id : e.selectedBrand;
      const equipmentResult: any = await equipmentModels.create({ data });
      if (!equipmentResult || !equipmentResult.id) {
        createEquipmentErrors.push(`Не удалось создать модель техники ${e.name}`);
        return null;
      }
      return { id: equipmentResult.id as string, notes: e.notes || undefined };
    });
    const createdEquip = await Promise.all(newEquipCreates);
    for (const e of createdEquip) { if (e) equipmentIds.push(e); }

    // Upsert применимости Part ↔ EquipmentModel
    const equipApplicabilityErrors: string[] = [];
    const equipApplicabilityOps = equipmentIds.map((e) =>
      equipmentApplicability.upsert({
        where: { partId_equipmentModelId: { partId, equipmentModelId: e.id } },
        update: { notes: e.notes },
        create: { partId, equipmentModelId: e.id, notes: e.notes },
      })
    );
    const equipApplicabilityResults = await Promise.all(equipApplicabilityOps);
    equipApplicabilityResults.forEach((res, idx) => { if (!res) equipApplicabilityErrors.push(`Связь с техникой #${equipmentIds[idx].id} не сохранена`); });

    // Агрегация ошибок
    const allErrors = [
      ...createCatalogErrors,
      ...applicabilityErrors,
      ...createEquipmentErrors,
      ...equipApplicabilityErrors,
    ].filter(Boolean);
    if (allErrors.length > 0) {
      saveError.value = allErrors.join('; ');
    }

    if (props.mode === "edit") emit("updated", partResult);
    else emit("created", partResult);

    if (props.mode === "create") resetForm();
  } catch (err: any) {
    console.error(`Ошибка ${props.mode === 'edit' ? 'обновления' : 'создания'} запчасти:`, err);
    saveError.value = err?.message || 'Произошла ошибка при сохранении запчасти';
  } finally {
    saving.value = false;
  }
};

// Сброс формы
const resetForm = () => {
  formData.value = {
    name: "",
    attributes: [],
    catalogItems: [],
    equipmentApplicabilities: [],
  };
  selectedCategory.value = null;
  currentStep.value = 1;
};

// Вспомогательные функции для отображения
const getDataTypeLabel = (dataType: string | undefined) => {
  if (!dataType) return "";
  const labels: Record<string, string> = {
    STRING: "Строка",
    NUMBER: "Число",
    BOOLEAN: "Логическое",
    DATE: "Дата",
    JSON: "JSON",
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string | undefined) => {
  if (!unit) return "";
  const labels: Record<string, string> = {
    MM: "мм",
    INCH: "дюймы",
    FT: "футы",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунты",
    ML: "мл",
    L: "л",
    GAL: "галлоны",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "PSI",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%",
  };
  return labels[unit] || unit;
};

const getAccuracyLabel = (accuracy: string) => {
  const labels: Record<string, string> = {
    EXACT_MATCH: "Точное совпадение",
    MATCH_WITH_NOTES: "С примечаниями",
    REQUIRES_MODIFICATION: "Требует доработки",
    PARTIAL_MATCH: "Частичное совпадение",
  };
  return labels[accuracy] || accuracy;
};

// Загрузка существующих атрибутов запчасти
const loadExistingAttributes = async (partId: number) => {
  try {
    const attributes = await partAttributes.findByPartId({ partId });
    if (attributes && Array.isArray(attributes)) {
      formData.value.attributes = attributes.map((attr: any) => ({
        id: attr.id,
        templateId: attr.templateId,
        value: attr.value,
        template: attr.template,
        // Для отображения
        templateTitle: attr.template?.title,
        templateDataType: attr.template?.dataType,
        templateUnit: attr.template?.unit,
        templateGroup: attr.template?.group?.name,
        templateDescription: attr.template?.description,
      }));
    }
  } catch (error) {
    console.warn("Не удалось загрузить атрибуты запчасти:", error);
  }
};

// Загрузка существующих каталожных позиций
const loadExistingCatalogItems = async (partId: number) => {
  try {
    const applicabilities = await client.crud.partApplicability.findMany.query({
      where: { partId },
      include: {
        catalogItem: {
          include: {
            brand: true
          }
        }
      }
    });

    if (applicabilities) {
      formData.value.catalogItems = applicabilities.map((app: any) => ({
        isExisting: true,
        existingCatalogItem: app.catalogItem,
        accuracy: app.accuracy,
        notes: app.notes || "",
        // Поля для новых позиций (пустые)
        sku: "",
        brandId: "",
        selectedBrand: null,
        description: "",
      }));
    }
  } catch (error) {
    console.warn("Не удалось загрузить каталожные позиции:", error);
  }
};

// Загрузка существующей применимости к технике
const loadExistingEquipmentApplicabilities = async (partId: number) => {
  try {
    const applicabilities = await client.crud.equipmentApplicability.findMany.query({
      where: { partId },
      include: {
        equipmentModel: {
          include: {
            brand: true
          }
        }
      }
    });

    if (applicabilities) {
      formData.value.equipmentApplicabilities = applicabilities.map((app: any) => ({
        isExisting: true,
        existingEquipmentModel: app.equipmentModel,
        notes: app.notes || "",
        // Поля для новых моделей (пустые)
        name: "",
        selectedBrand: null,
      }));
    }
  } catch (error) {
    console.warn("Не удалось загрузить применимость к технике:", error);
  }
};

// Загрузка данных для редактирования
const loadPartData = async () => {
  if (props.part && props.mode === "edit") {
    formData.value.name = props.part.name || "";

    // Устанавливаем категорию
    if (props.part.partCategory) {
      selectedCategory.value = props.part.partCategory;
    }

    // Загружаем все связанные данные
    if (props.part.id) {
      await Promise.all([
        loadExistingAttributes(props.part.id),
        loadExistingCatalogItems(props.part.id),
        loadExistingEquipmentApplicabilities(props.part.id)
      ]);
    }
  }
};

// Watchers
watch(
  () => props.part,
  async () => {
    if (props.mode === "edit") {
      await loadPartData();
    }
  },
  { immediate: true }
);

// Инициализация
onMounted(async () => {
  if (props.mode === "edit" && props.part) {
    await loadPartData();
  }
});
</script>

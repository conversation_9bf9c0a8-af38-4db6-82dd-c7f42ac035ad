import { createElementBlock, openBlock, mergeProps, createCommentVNode, createElementVNode, renderSlot, defineComponent, useSSRContext, ref, createSlots, renderList, withCtx } from 'vue';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/card';
import BaseStyle from '@primevue/core/base/style';
import { p as ptViewMerge } from './SecondaryButton_B0hmlm1n.mjs';
import { ssrRenderComponent, ssrRenderSlot } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_avhRMbqw.mjs';

var classes = {
  root: 'p-card p-component',
  header: 'p-card-header',
  body: 'p-card-body',
  caption: 'p-card-caption',
  title: 'p-card-title',
  subtitle: 'p-card-subtitle',
  content: 'p-card-content',
  footer: 'p-card-footer'
};
var CardStyle = BaseStyle.extend({
  name: 'card',
  style: style,
  classes: classes
});

var script$1 = {
  name: 'BaseCard',
  "extends": BaseComponent,
  style: CardStyle,
  provide: function provide() {
    return {
      $pcCard: this,
      $parentInstance: this
    };
  }
};

var script = {
  name: 'Card',
  "extends": script$1,
  inheritAttrs: false
};

function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root')
  }, _ctx.ptmi('root')), [_ctx.$slots.header ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx('header')
  }, _ctx.ptm('header')), [renderSlot(_ctx.$slots, "header")], 16)) : createCommentVNode("", true), createElementVNode("div", mergeProps({
    "class": _ctx.cx('body')
  }, _ctx.ptm('body')), [_ctx.$slots.title || _ctx.$slots.subtitle ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx('caption')
  }, _ctx.ptm('caption')), [_ctx.$slots.title ? (openBlock(), createElementBlock("div", mergeProps({
    key: 0,
    "class": _ctx.cx('title')
  }, _ctx.ptm('title')), [renderSlot(_ctx.$slots, "title")], 16)) : createCommentVNode("", true), _ctx.$slots.subtitle ? (openBlock(), createElementBlock("div", mergeProps({
    key: 1,
    "class": _ctx.cx('subtitle')
  }, _ctx.ptm('subtitle')), [renderSlot(_ctx.$slots, "subtitle")], 16)) : createCommentVNode("", true)], 16)) : createCommentVNode("", true), createElementVNode("div", mergeProps({
    "class": _ctx.cx('content')
  }, _ctx.ptm('content')), [renderSlot(_ctx.$slots, "content")], 16), _ctx.$slots.footer ? (openBlock(), createElementBlock("div", mergeProps({
    key: 1,
    "class": _ctx.cx('footer')
  }, _ctx.ptm('footer')), [renderSlot(_ctx.$slots, "footer")], 16)) : createCommentVNode("", true)], 16)], 16);
}

script.render = render;

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Card",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `flex flex-col rounded-xl
        bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0
        shadow-md`,
      header: ``,
      body: `p-5 flex flex-col gap-2`,
      caption: `flex flex-col gap-2`,
      title: `font-medium text-xl`,
      subtitle: `text-surface-500 dark:text-surface-400`,
      content: ``,
      footer: ``
    });
    const __returned__ = { theme, get Card() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Card"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Card.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Card = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { Card as C };

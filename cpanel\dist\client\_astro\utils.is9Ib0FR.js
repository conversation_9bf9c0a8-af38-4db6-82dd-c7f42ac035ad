import{J as q,a7 as I}from"./reactivity.esm-bundler.BQ12LWmY.js";var D=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},p=typeof window>"u"||"Deno"in globalThis;function E(){}function ht(t,e){return typeof t=="function"?t(e):t}function k(t){return typeof t=="number"&&t>=0&&t!==1/0}function Q(t,e){return Math.max(t+(e||0)-Date.now(),0)}function G(t,e){return typeof t=="function"?t(e):t}function N(t,e){return typeof t=="function"?t(e):t}function lt(t,e){const{type:s="all",exact:r,fetchStatus:n,predicate:o,queryKey:a,stale:i}=t;if(a){if(r){if(e.queryHash!==H(a,e.options))return!1}else if(!C(e.queryKey,a))return!1}if(s!=="all"){const u=e.isActive();if(s==="active"&&!u||s==="inactive"&&u)return!1}return!(typeof i=="boolean"&&e.isStale()!==i||n&&n!==e.state.fetchStatus||o&&!o(e))}function ft(t,e){const{exact:s,status:r,predicate:n,mutationKey:o}=t;if(o){if(!e.options.mutationKey)return!1;if(s){if(g(e.options.mutationKey)!==g(o))return!1}else if(!C(e.options.mutationKey,o))return!1}return!(r&&e.state.status!==r||n&&!n(e))}function H(t,e){return(e?.queryKeyHashFn||g)(t)}function g(t){return JSON.stringify(t,(e,s)=>w(s)?Object.keys(s).sort().reduce((r,n)=>(r[n]=s[n],r),{}):s)}function C(t,e){return t===e?!0:typeof t!=typeof e?!1:t&&e&&typeof t=="object"&&typeof e=="object"?Object.keys(e).every(s=>C(t[s],e[s])):!1}function T(t,e){if(t===e)return t;const s=U(t)&&U(e);if(s||w(t)&&w(e)){const r=s?t:Object.keys(t),n=r.length,o=s?e:Object.keys(e),a=o.length,i=s?[]:{},u=new Set(r);let f=0;for(let l=0;l<a;l++){const c=s?l:o[l];(!s&&u.has(c)||s)&&t[c]===void 0&&e[c]===void 0?(i[c]=void 0,f++):(i[c]=T(t[c],e[c]),i[c]===t[c]&&t[c]!==void 0&&f++)}return n===a&&f===n?t:i}return e}function dt(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function U(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function w(t){if(!R(t))return!1;const e=t.constructor;if(e===void 0)return!0;const s=e.prototype;return!(!R(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function R(t){return Object.prototype.toString.call(t)==="[object Object]"}function _(t){return new Promise(e=>{setTimeout(e,t)})}function $(t,e,s){return typeof s.structuralSharing=="function"?s.structuralSharing(t,e):s.structuralSharing!==!1?T(t,e):e}function yt(t,e,s=0){const r=[...t,e];return s&&r.length>s?r.slice(1):r}function pt(t,e,s=0){const r=[e,...t];return s&&r.length>s?r.slice(0,-1):r}var P=Symbol();function B(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:!t.queryFn||t.queryFn===P?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}function bt(t,e){return typeof t=="function"?t(...e):!!t}var V=class extends D{#t;#s;#e;constructor(){super(),this.#e=t=>{if(!p&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#s||this.setEventListener(this.#e)}onUnsubscribe(){this.hasListeners()||(this.#s?.(),this.#s=void 0)}setEventListener(t){this.#e=t,this.#s?.(),this.#s=t(e=>{typeof e=="boolean"?this.setFocused(e):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},z=new V,J=class extends D{#t=!0;#s;#e;constructor(){super(),this.#e=t=>{if(!p&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#s||this.setEventListener(this.#e)}onUnsubscribe(){this.hasListeners()||(this.#s?.(),this.#s=void 0)}setEventListener(t){this.#e=t,this.#s?.(),this.#s=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(s=>{s(t)}))}isOnline(){return this.#t}},A=new J;function Y(){let t,e;const s=new Promise((n,o)=>{t=n,e=o});s.status="pending",s.catch(()=>{});function r(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{r({status:"fulfilled",value:n}),t(n)},s.reject=n=>{r({status:"rejected",reason:n}),e(n)},s}function W(t){return Math.min(1e3*2**t,3e4)}function M(t){return(t??"online")==="online"?A.isOnline():!0}var K=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function X(t){let e=!1,s=0,r;const n=Y(),o=()=>n.status!=="pending",a=h=>{o()||(b(new K(h)),t.abort?.())},i=()=>{e=!0},u=()=>{e=!1},f=()=>z.isFocused()&&(t.networkMode==="always"||A.isOnline())&&t.canRun(),l=()=>M(t.networkMode)&&t.canRun(),c=h=>{o()||(r?.(),n.resolve(h))},b=h=>{o()||(r?.(),n.reject(h))},j=()=>new Promise(h=>{r=m=>{(o()||f())&&h(m)},t.onPause?.()}).then(()=>{r=void 0,o()||t.onContinue?.()}),v=()=>{if(o())return;let h;const m=s===0?t.initialPromise:void 0;try{h=m??t.fn()}catch(d){h=Promise.reject(d)}Promise.resolve(h).then(c).catch(d=>{if(o())return;const y=t.retry??(p?0:3),S=t.retryDelay??W,x=typeof S=="function"?S(s,d):S,L=y===!0||typeof y=="number"&&s<y||typeof y=="function"&&y(s,d);if(e||!L){b(d);return}s++,t.onFail?.(s,d),_(x).then(()=>f()?void 0:j()).then(()=>{e?b(d):v()})})};return{promise:n,status:()=>n.status,cancel:a,continue:()=>(r?.(),n),cancelRetry:i,continueRetry:u,canStart:l,start:()=>(l()?v():j().then(v),n)}}var Z=t=>setTimeout(t,0);function tt(){let t=[],e=0,s=i=>{i()},r=i=>{i()},n=Z;const o=i=>{e?t.push(i):n(()=>{s(i)})},a=()=>{const i=t;t=[],i.length&&n(()=>{r(()=>{i.forEach(u=>{s(u)})})})};return{batch:i=>{let u;e++;try{u=i()}finally{e--,e||a()}return u},batchCalls:i=>(...u)=>{o(()=>{i(...u)})},schedule:o,setNotifyFunction:i=>{s=i},setBatchNotifyFunction:i=>{r=i},setScheduler:i=>{n=i}}}var et=tt(),st=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),k(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(p?1/0:300*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},vt=class extends st{#t;#s;#e;#r;#i;#a;#o;constructor(t){super(),this.#o=!1,this.#a=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#r=t.client,this.#e=this.#r.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#t=nt(this.options),this.state=t.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#i?.promise}setOptions(t){this.options={...this.#a,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#e.remove(this)}setData(t,e){const s=$(this.state.data,t,this.options);return this.#n({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#n({type:"setState",state:t,setStateOptions:e})}cancel(t){const e=this.#i?.promise;return this.#i?.cancel(t),e?e.then(E).catch(E):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(t=>N(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===P||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>G(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Q(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(e=>e.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#i?.continue()}onOnline(){this.observers.find(e=>e.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#i?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#i&&(this.#o?this.#i.cancel({revert:!0}):this.#i.cancelRetry()),this.scheduleGc()),this.#e.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#n({type:"invalidate"})}async fetch(t,e){if(this.state.fetchStatus!=="idle"&&this.#i?.status()!=="rejected"){if(this.state.data!==void 0&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#i)return this.#i.continueRetry(),this.#i.promise}if(t&&this.setOptions(t),!this.options.queryFn){const i=this.observers.find(u=>u.options.queryFn);i&&this.setOptions(i.options)}const s=new AbortController,r=i=>{Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(this.#o=!0,s.signal)})},n=()=>{const i=B(this.options,e),f=(()=>{const l={client:this.#r,queryKey:this.queryKey,meta:this.meta};return r(l),l})();return this.#o=!1,this.options.persister?this.options.persister(i,f,this):i(f)},a=(()=>{const i={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#r,state:this.state,fetchFn:n};return r(i),i})();this.options.behavior?.onFetch(a,this),this.#s=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#n({type:"fetch",meta:a.fetchOptions?.meta}),this.#i=X({initialPromise:e?.initialPromise,fn:a.fetchFn,abort:s.abort.bind(s),onFail:(i,u)=>{this.#n({type:"failed",failureCount:i,error:u})},onPause:()=>{this.#n({type:"pause"})},onContinue:()=>{this.#n({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0});try{const i=await this.#i.start();if(i===void 0)throw new Error(`${this.queryHash} data is undefined`);return this.setData(i),this.#e.config.onSuccess?.(i,this),this.#e.config.onSettled?.(i,this.state.error,this),i}catch(i){if(i instanceof K){if(i.silent)return this.#i.promise;if(i.revert)return this.setState({...this.#s,fetchStatus:"idle"}),this.state.data}throw this.#n({type:"error",error:i}),this.#e.config.onError?.(i,this),this.#e.config.onSettled?.(this.state.data,i,this),i}finally{this.scheduleGc()}}#n(t){const e=s=>{switch(t.type){case"failed":return{...s,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...s,fetchStatus:"paused"};case"continue":return{...s,fetchStatus:"fetching"};case"fetch":return{...s,...it(s.data,this.options),fetchMeta:t.meta??null};case"success":const r={...s,data:t.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return this.#s=t.manual?r:void 0,r;case"error":const n=t.error;return{...s,error:n,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...s,isInvalidated:!0};case"setState":return{...s,...t.state}}};this.state=e(this.state),et.batch(()=>{this.observers.forEach(s=>{s.onQueryUpdate()}),this.#e.notify({query:this,type:"updated",action:t})})}};function it(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:M(e.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function nt(t){const e=typeof t.initialData=="function"?t.initialData():t.initialData,s=e!==void 0,r=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var rt="VUE_QUERY_CLIENT";function mt(t){const e=t?`:${t}`:"";return`${rt}${e}`}function St(t,e){Object.keys(t).forEach(s=>{t[s]=e[s]})}function O(t,e,s="",r=0){if(e){const n=e(t,s,r);if(n===void 0&&q(t)||n!==void 0)return n}if(Array.isArray(t))return t.map((n,o)=>O(n,e,String(o),r+1));if(typeof t=="object"&&at(t)){const n=Object.entries(t).map(([o,a])=>[o,O(a,e,o,r+1)]);return Object.fromEntries(n)}return t}function ot(t,e){return O(t,e)}function F(t,e=!1){return ot(t,(s,r,n)=>{if(n===1&&r==="queryKey")return F(s,!0);if(e&&ut(s))return F(s(),e);if(q(s))return F(I(s),e)})}function at(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;const e=Object.getPrototypeOf(t);return e===null||e===Object.prototype}function ut(t){return typeof t=="function"}export{C as A,P as B,vt as Q,st as R,D as S,G as a,k as b,it as c,$ as d,et as e,z as f,mt as g,F as h,p as i,bt as j,H as k,X as l,lt as m,E as n,ft as o,Y as p,B as q,N as r,dt as s,Q as t,St as u,pt as v,yt as w,A as x,ht as y,g as z};

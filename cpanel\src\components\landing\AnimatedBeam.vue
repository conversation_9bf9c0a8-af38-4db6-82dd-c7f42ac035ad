<template>
  <svg
    ref="svgRoot"
    class="pointer-events-none absolute left-0 top-0 w-full h-full transform-gpu z-20"
    :width="svgDimensions.width"
    :height="svgDimensions.height"
    xmlns="http://www.w3.org/2000/svg"
    :viewBox="`0 0 ${svgDimensions.width} ${svgDimensions.height}`"
  >
    <defs>
      <linearGradient :id="gradientId" gradientUnits="userSpaceOnUse" :x1="gradient.x1" :y1="gradient.y1" :x2="gradient.x2" :y2="gradient.y2">
        <stop :stop-color="gradientStartColor" stop-opacity="0" />
        <stop :stop-color="gradientStartColor" />
        <stop offset="32.5%" :stop-color="gradientStopColor" />
        <stop offset="100%" :stop-color="gradientStopColor" stop-opacity="0" />
        <animate attributeName="x1" :values="gradientAnim.x1" :dur="`${duration}s`" repeatCount="indefinite" :begin="`${delay}s`" />
        <animate attributeName="x2" :values="gradientAnim.x2" :dur="`${duration}s`" repeatCount="indefinite" :begin="`${delay}s`" />
      </linearGradient>
    </defs>
    <!-- Base stroke -->
    <path v-if="pathD" :d="pathD" :stroke="pathColor" :stroke-width="pathWidth" :stroke-opacity="pathOpacity" stroke-linecap="round" fill="none" />
    <!-- Gradient stroke overlay -->
    <path v-if="pathD" :d="pathD" :stroke-width="pathWidth" :stroke="gradientUrl" stroke-linecap="round" fill="none" />
  </svg>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue'

interface Props {
  className?: string
  // Важно: при передаче из шаблона Vue разворачивает Ref => здесь приходят уже HTMLElement | null
  containerRef?: HTMLElement | null
  fromRef?: HTMLElement | null
  toRef?: HTMLElement | null
  curvature?: number
  reverse?: boolean
  pathColor?: string
  pathWidth?: number
  pathOpacity?: number
  gradientStartColor?: string
  gradientStopColor?: string
  delay?: number
  duration?: number
  startXOffset?: number
  startYOffset?: number
  endXOffset?: number
  endYOffset?: number
}

const props = withDefaults(defineProps<Props>(), {
  curvature: 0,
  reverse: false,
  duration: 6,
  delay: 0,
  pathColor: 'rgba(96,165,250,0.15)',
  pathWidth: 2,
  pathOpacity: 0.2,
  gradientStartColor: '#60a5fa',
  gradientStopColor: '#a78bfa',
  startXOffset: 0,
  startYOffset: 0,
  endXOffset: 0,
  endYOffset: 0,
})

const pathD = ref('')
const svgDimensions = ref({ width: 0, height: 0 })
const svgRoot = ref<SVGSVGElement | null>(null)

function resolveElement(target: unknown): HTMLElement | null {
  if (!target) return null
  // Direct HTMLElement
  if (typeof (target as any).getBoundingClientRect === 'function') {
    return target as HTMLElement
  }
  // Vue component instance with $el
  if ((target as any).$el && typeof (target as any).$el.getBoundingClientRect === 'function') {
    return (target as any).$el as HTMLElement
  }
  // Ref wrapper
  if ((target as any).value !== undefined) {
    return resolveElement((target as any).value)
  }
  return null
}
const gradientId = `beam-gradient-${Math.random().toString(36).slice(2)}`
const gradientUrl = computed(() => `url(#${gradientId})`)

const gradient = { x1: '0%', y1: '0%', x2: '0%', y2: '0%' }
const gradientAnim = computed(() => {
  return props.reverse
    ? { x1: '90%;-10%', x2: '100%;0%' }
    : { x1: '10%;110%', x2: '0%;100%' }
})

let resizeObserver: ResizeObserver | null = null

const updatePath = () => {
  const container = resolveElement(props.containerRef) || (svgRoot.value?.parentElement as HTMLElement | null)
  const from = resolveElement(props.fromRef)
  const to = resolveElement(props.toRef)
  if (!container) return

  const containerRect = container.getBoundingClientRect()
  const svgWidth = Math.max(1, Math.round(containerRect.width))
  const svgHeight = Math.max(1, Math.round(containerRect.height))
  svgDimensions.value = { width: svgWidth, height: svgHeight }

  if (!from || !to) return

  const rectA = from.getBoundingClientRect()
  const rectB = to.getBoundingClientRect()

  const startX = rectA.left + rectA.width / 2 - containerRect.left + (props.startXOffset || 0)
  const startY = rectA.top + rectA.height / 2 - containerRect.top + (props.startYOffset || 0)
  const endX = rectB.left + rectB.width / 2 - containerRect.left + (props.endXOffset || 0)
  const endY = rectB.top + rectB.height / 2 - containerRect.top + (props.endYOffset || 0)

  const controlY = startY - (props.curvature || 0)
  pathD.value = `M ${startX},${startY} Q ${(startX + endX) / 2},${controlY} ${endX},${endY}`
}

onMounted(() => {
  updatePath()
  const container = resolveElement(props.containerRef) || (svgRoot.value?.parentElement as HTMLElement | null)
  if (container) {
    resizeObserver = new ResizeObserver(() => updatePath())
    resizeObserver.observe(container)
  }
  // also observe endpoints if they resize/move layout
  const from = resolveElement(props.fromRef)
  const to = resolveElement(props.toRef)
  if (from) {
    try { new ResizeObserver(() => updatePath()).observe(from) } catch {}
  }
  if (to) {
    try { new ResizeObserver(() => updatePath()).observe(to) } catch {}
  }
  window.addEventListener('resize', updatePath)
  window.addEventListener('scroll', updatePath, true)
  // ensure second pass after children mount
  nextTick(() => updatePath())
  // try a few frames until refs are ready
  let attemptsRemaining = 20
  const tryRAF = () => {
    updatePath()
    if (!pathD.value && attemptsRemaining-- > 0) {
      requestAnimationFrame(tryRAF)
    }
  }
  requestAnimationFrame(tryRAF)
  // delayed retries in case of late layout
  setTimeout(updatePath, 150)
  setTimeout(updatePath, 350)
  setTimeout(updatePath, 700)
})

onBeforeUnmount(() => {
  if (resizeObserver) resizeObserver.disconnect()
  window.removeEventListener('resize', updatePath)
  window.removeEventListener('scroll', updatePath, true)
})

// react to late ref bindings
watch(() => [resolveElement(props.fromRef), resolveElement(props.toRef)], () => updatePath(), { flush: 'post' })

// react to late container ref
watch(() => resolveElement(props.containerRef), () => updatePath(), { flush: 'post' })
</script>
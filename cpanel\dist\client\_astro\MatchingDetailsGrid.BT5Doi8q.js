import{T as H}from"./Tag.DTFTku6q.js";import{V as Y}from"./ToggleSwitch.9ueDJKWv.js";import{S as X}from"./Select.CQBzSu6y.js";import{I as U}from"./InputText.DOJMNEP-.js";import{I as W}from"./Icon.By8t0-Wj.js";import{_ as B}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as F,c as r,o as i,b as d,a,e as c,F as g,r as w,f as p,h as A}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{n as v,t as o,r as T}from"./reactivity.esm-bundler.BQ12LWmY.js";const K=()=>({getAccuracyLabel:u=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[u]||u,getAccuracySeverity:u=>({EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"})[u]||"secondary",getDetailSeverity:u=>u.includes("EXACT")?"success":u.includes("WITHIN_TOLERANCE")||u.includes("NEAR")?"info":u.includes("LEGACY")?"warning":"secondary",getKindLabel:u=>({NUMBER_EXACT:"Число: точное",NUMBER_WITHIN_TOLERANCE:"Число: в допуске",STRING_EXACT:"Строка: точное",STRING_SYNONYM_EXACT:"Строка: группа EXACT",STRING_SYNONYM_NEAR:"Строка: группа NEAR",STRING_SYNONYM_LEGACY:"Строка: группа LEGACY",EXACT_STRING:"Точное совпадение"})[u]||u}),z=F({__name:"MatchingDetailsGrid",props:{details:{},controls:{type:Boolean,default:!0}},setup(E,{expose:n}){n();const f=E,{getKindLabel:t,getDetailSeverity:u}=K(),x=T(!1),e=T(!0),C=T(""),N=T("importance"),h=[{label:"По важности",value:"importance"},{label:"По атрибуту",value:"name"}],I=l=>String(l?.kind||"").startsWith("NUMBER"),O=l=>String(l?.kind||"").startsWith("STRING_SYNONYM"),k=l=>String(l?.kind||"").includes("EXACT")&&!String(l?.kind||"").includes("WITHIN_TOLERANCE"),S=l=>{const s=String(l?.kind||"");return l?.notes||s.includes("NEAR")||s.includes("LEGACY")||s.includes("WITHIN_TOLERANCE")||I(l)&&(l.delta!==void 0||l.toleranceUsed!==void 0)?!0:!k(l)},V=A(()=>{let l=Array.isArray(f.details)?f.details:[];x.value&&(l=l.filter(S));const s=C.value.trim().toLowerCase();return s&&(l=l.filter(m=>String(m?.templateTitle||m?.templateId||"").toLowerCase().includes(s))),l}),_=l=>{const s=String(l?.kind||"");return s.includes("LEGACY")?0:s.includes("NEAR")||s.includes("WITHIN_TOLERANCE")?1:s.includes("EXACT")?2:3},M=A(()=>{const l=[...V.value];return N.value==="name"?l.sort((s,m)=>String(s?.templateTitle||s?.templateId).localeCompare(String(m?.templateTitle||m?.templateId))):l.sort((s,m)=>_(s)-_(m)),l}),D=A(()=>{let l=0,s=0,m=0,b=0;const R=Array.isArray(f.details)?f.details:[];for(const G of R){const y=String(G?.kind||"");y.includes("EXACT")&&l++,y.includes("NEAR")&&s++,y.includes("LEGACY")&&m++,y.includes("WITHIN_TOLERANCE")&&b++}return{total:R.length,exact:l,near:s,legacy:m,tol:b}}),L={props:f,getKindLabel:t,getDetailSeverity:u,showOnlyImportant:x,showNotes:e,search:C,sortMode:N,sortOptions:h,isNumeric:I,isStringSynonym:O,isExact:k,isImportant:S,filteredDetails:V,importanceRank:_,preparedDetails:M,summary:D,getKindIcon:l=>{const s=String(l||"");return s.includes("EXACT")&&!s.includes("WITHIN_TOLERANCE")?"pi pi-check-circle":s.includes("WITHIN_TOLERANCE")||s.includes("NEAR")?"pi pi-info-circle":s.includes("LEGACY")?"pi pi-exclamation-triangle":"pi pi-circle"},accentClass:l=>{const s=String(l?.kind||"");return s.includes("LEGACY")?"border-yellow-400/60 dark:border-yellow-500/60":s.includes("WITHIN_TOLERANCE")||s.includes("NEAR")?"border-sky-400/60 dark:border-sky-500/60":s.includes("EXACT")?"border-emerald-400/60 dark:border-emerald-500/60":"border-surface-200 dark:border-surface-600"},valueClass:l=>{const s=String(l?.kind||"");return s.includes("LEGACY")?"text-yellow-700 dark:text-yellow-300":s.includes("WITHIN_TOLERANCE")||s.includes("NEAR")?"text-sky-700 dark:text-sky-300":s.includes("EXACT")?"text-emerald-700 dark:text-emerald-300":"text-surface-800 dark:text-surface-200"},VTag:H,VToggleSwitch:Y,VSelect:X,VInputText:U,Icon:W};return Object.defineProperty(L,"__isScriptSetup",{enumerable:!1,value:!0}),L}}),j={class:"space-y-2"},P={key:0,class:"flex flex-wrap items-center justify-between gap-2"},Q={class:"flex items-center gap-3"},q={class:"inline-flex items-center gap-2 text-xs text-surface-600"},J={class:"inline-flex items-center gap-2 text-xs text-surface-600"},Z={class:"hidden md:flex items-center gap-2"},$={class:"flex items-center gap-2 max-w-xs w-full md:max-w-sm"},ee={class:"hidden md:flex items-center gap-1 text-xs text-surface-500"},te={class:"hidden md:block"},se={class:"grid grid-cols-12 px-2 py-2 text-[11px] uppercase tracking-wide text-surface-500"},le={key:0,class:"col-span-1"},ne={class:"divide-y divide-surface-border rounded border"},ae={class:"col-span-3 flex items-center gap-2 min-w-0"},re=["title"],ie={key:0,class:"ml-1 text-xs text-surface-500"},oe={key:0,class:"ml-1 text-xs text-surface-500"},ue={key:1,class:"ml-1 text-xs text-surface-500"},ce={class:"col-span-2"},de={key:0,class:"col-span-1 text-xs text-surface-500"},me={key:0},fe={class:"md:hidden grid grid-cols-1 gap-2"},ve={class:"flex items-center justify-between"},pe={class:"text-xs text-surface-700 dark:text-surface-300 flex items-center gap-2"},ye={class:"font-medium"},ge={class:"text-xs text-surface-500 mt-1"},Te={key:0},Ee={key:1},xe={key:0},_e={key:0,class:"text-xs text-surface-500 mt-1"};function Ae(E,n,f,t,u,x){return i(),r("div",j,[f.controls?(i(),r("div",P,[a("div",Q,[a("label",q,[c(t.VToggleSwitch,{modelValue:t.showOnlyImportant,"onUpdate:modelValue":n[0]||(n[0]=e=>t.showOnlyImportant=e)},null,8,["modelValue"]),n[4]||(n[4]=a("span",null,"Только важное",-1))]),a("label",J,[c(t.VToggleSwitch,{modelValue:t.showNotes,"onUpdate:modelValue":n[1]||(n[1]=e=>t.showNotes=e)},null,8,["modelValue"]),n[5]||(n[5]=a("span",null,"Показывать заметки",-1))]),a("div",Z,[n[6]||(n[6]=a("span",{class:"text-xs text-surface-500"},"Сортировка",-1)),c(t.VSelect,{modelValue:t.sortMode,"onUpdate:modelValue":n[2]||(n[2]=e=>t.sortMode=e),options:t.sortOptions,optionLabel:"label",optionValue:"value",class:"w-40"},null,8,["modelValue"])])]),a("div",$,[c(t.VInputText,{modelValue:t.search,"onUpdate:modelValue":n[3]||(n[3]=e=>t.search=e),placeholder:"Поиск по атрибуту",class:"w-full"},null,8,["modelValue"])]),a("div",ee,[c(t.VTag,{size:"small",value:`Всего ${t.summary.total}`,severity:"secondary"},null,8,["value"]),c(t.VTag,{size:"small",value:`EXACT ${t.summary.exact}`,severity:"success"},null,8,["value"]),c(t.VTag,{size:"small",value:`NEAR ${t.summary.near}`,severity:"info"},null,8,["value"]),c(t.VTag,{size:"small",value:`TOL ${t.summary.tol}`,severity:"info"},null,8,["value"]),c(t.VTag,{size:"small",value:`LEGACY ${t.summary.legacy}`,severity:"warning"},null,8,["value"])])])):d("",!0),a("div",te,[a("div",se,[n[7]||(n[7]=a("div",{class:"col-span-3"},"Атрибут",-1)),n[8]||(n[8]=a("div",{class:"col-span-3"},"Значение в item",-1)),n[9]||(n[9]=a("div",{class:"col-span-3"},"Значение в part",-1)),n[10]||(n[10]=a("div",{class:"col-span-2"},"Результат",-1)),t.showNotes?(i(),r("div",le,"Заметки")):d("",!0)]),a("div",ne,[(i(!0),r(g,null,w(t.preparedDetails,e=>(i(),r("div",{key:e.templateId+":"+e.kind+":"+e.itemValue+":"+e.partValue,class:v(["grid grid-cols-12 items-center px-2 py-2 text-sm border-l-2",t.accentClass(e)])},[a("div",ae,[c(t.Icon,{name:t.getKindIcon(e.kind),class:"w-4 h-4 text-surface-400"},null,8,["name"]),a("span",{class:"truncate",title:e.templateTitle||"template #"+e.templateId},o(e.templateTitle||"template #"+e.templateId),9,re)]),a("div",{class:v(["col-span-3 font-mono break-words",t.valueClass(e)])},[p(o(e.itemValue)+" ",1),t.isNumeric(e)&&e.delta!==void 0?(i(),r("span",ie,"Δ="+o(e.delta),1)):d("",!0)],2),a("div",{class:v(["col-span-3 font-mono break-words",t.valueClass(e)])},[p(o(e.partValue)+" ",1),t.isNumeric(e)&&e.toleranceUsed!==void 0?(i(),r("span",oe,"tol="+o(e.toleranceUsed),1)):d("",!0),t.isStringSynonym(e)&&e.synonymLevel?(i(),r("span",ue,"level="+o(e.synonymLevel),1)):d("",!0)],2),a("div",ce,[c(t.VTag,{size:"small",value:t.getKindLabel(e.kind),severity:t.getDetailSeverity(e.kind)},null,8,["value","severity"])]),t.showNotes?(i(),r("div",de,[e.notes?(i(),r("span",me,o(e.notes),1)):d("",!0)])):d("",!0)],2))),128))])]),a("div",fe,[(i(!0),r(g,null,w(t.preparedDetails,e=>(i(),r("div",{key:e.templateId+":"+e.kind+":"+e.itemValue+":"+e.partValue,class:v(["p-2 bg-surface-50 dark:bg-surface-900 rounded border border-l-2",t.accentClass(e)])},[a("div",ve,[a("div",pe,[c(t.Icon,{name:t.getKindIcon(e.kind),class:"w-4 h-4 text-surface-400"},null,8,["name"]),a("span",ye,o(e.templateTitle||"template #"+e.templateId),1)]),c(t.VTag,{value:t.getKindLabel(e.kind),severity:t.getDetailSeverity(e.kind),size:"small"},null,8,["value","severity"])]),a("div",ge,[t.isNumeric(e)?(i(),r(g,{key:0},[n[11]||(n[11]=p(" item: ")),a("span",{class:v(["font-mono",t.valueClass(e)])},o(e.itemValue),3),n[12]||(n[12]=p(" → part: ")),a("span",{class:v(["font-mono",t.valueClass(e)])},o(e.partValue),3),e.delta!==void 0?(i(),r("span",Te," | Δ="+o(e.delta),1)):d("",!0),e.toleranceUsed!==void 0?(i(),r("span",Ee," | tol="+o(e.toleranceUsed),1)):d("",!0)],64)):(i(),r(g,{key:1},[n[13]||(n[13]=p(" item: ")),a("span",{class:v(["font-mono",t.valueClass(e)])},o(e.itemValue),3),n[14]||(n[14]=p(" → part: ")),a("span",{class:v(["font-mono",t.valueClass(e)])},o(e.partValue),3),e.synonymLevel?(i(),r("span",xe," | level="+o(e.synonymLevel),1)):d("",!0)],64))]),t.showNotes&&e.notes?(i(),r("div",_e,o(e.notes),1)):d("",!0)],2))),128))])])}const Oe=B(z,[["render",Ae]]);export{Oe as M,K as u};

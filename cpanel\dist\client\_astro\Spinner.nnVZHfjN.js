import{r as E,t as g,n as h}from"./reactivity.esm-bundler.BQ12LWmY.js";import{j as V,d as w,c as y,o as f,e as u,w as c,a as r,b as I,F as j,r as N,g as B,f as M,h as P}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import O from"./Card.C4y0_bWr.js";import{S as L}from"./SecondaryButton.DkELYl7Q.js";import{S as A}from"./Select.CQBzSu6y.js";/* empty css                         */import{_ as F}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{M as U}from"./index.CBuVmUry.js";function ge(l,a=300){const t=E(l.value);let e=null;return V(l,d=>{e&&clearTimeout(e),e=setTimeout(()=>{t.value=d},a)},{immediate:!1}),t}const q=w({__name:"CatalogPagination",props:{currentPage:{},totalItems:{},itemsPerPage:{},enableVirtualScroll:{type:Boolean,default:!1},hasMoreItems:{type:Boolean,default:!1},loadingMore:{type:Boolean,default:!1}},emits:["pageChange","itemsPerPageChange","loadMore"],setup(l,{expose:a,emit:t}){a();const e=()=>"⏮",d=()=>"◀",_=()=>"▶",s=()=>"⏭",o=l,i=t,C=[{label:"10",value:10},{label:"20",value:20},{label:"50",value:50},{label:"100",value:100}],k=P(()=>Math.ceil(o.totalItems/o.itemsPerPage)),D=P(()=>o.totalItems===0?0:(o.currentPage-1)*o.itemsPerPage+1),p=P(()=>{const n=o.currentPage*o.itemsPerPage;return Math.min(n,o.totalItems)}),T=P(()=>{const n=[],x=k.value,v=o.currentPage;if(x<=7)for(let m=1;m<=x;m++)n.push(m);else{n.push(1),v>3&&n.push(-1);const m=Math.max(2,v-1),z=Math.min(x-1,v+1);for(let b=m;b<=z;b++)n.includes(b)||n.push(b);v<x-2&&n.push(-2),n.includes(x)||n.push(x)}return n.filter(m=>m>0)}),S={FirstPageIcon:e,PrevPageIcon:d,NextPageIcon:_,LastPageIcon:s,props:o,emit:i,itemsPerPageOptions:C,totalPages:k,startItem:D,endItem:p,visiblePages:T,goToPage:n=>{n>=1&&n<=k.value&&n!==o.currentPage&&i("pageChange",n)},changeItemsPerPage:n=>{i("itemsPerPageChange",n)},loadMore:()=>{i("loadMore")},Card:O,SecondaryButton:L,Select:A};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}}),G={class:"catalog-pagination"},H={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},J={class:"text-sm text-surface-600 dark:text-surface-300"},K={class:"flex items-center gap-2"},Q={class:"hidden sm:flex items-center gap-1"},R={class:"sm:hidden flex items-center gap-2"},W={class:"text-sm text-surface-600 dark:text-surface-300"},X={class:"flex items-center gap-2 text-sm"},Y={key:0,class:"mt-4"},Z={class:"flex items-center justify-center"};function $(l,a,t,e,d,_){return f(),y("div",G,[u(e.Card,null,{content:c(()=>[r("div",H,[r("div",J," Показано "+g(e.startItem)+"-"+g(e.endItem)+" из "+g(t.totalItems)+" записей ",1),r("div",K,[u(e.SecondaryButton,{disabled:t.currentPage===1,onClick:a[0]||(a[0]=s=>e.goToPage(1)),"icon-only":"",title:"Первая страница"},{icon:c(()=>[u(e.FirstPageIcon)]),_:1},8,["disabled"]),u(e.SecondaryButton,{disabled:t.currentPage===1,onClick:a[1]||(a[1]=s=>e.goToPage(t.currentPage-1)),"icon-only":"",title:"Предыдущая страница"},{icon:c(()=>[u(e.PrevPageIcon)]),_:1},8,["disabled"]),r("div",Q,[(f(!0),y(j,null,N(e.visiblePages,s=>(f(),B(e.SecondaryButton,{key:s,class:h({"bg-primary text-primary-contrast":s===t.currentPage,"text-primary":s!==t.currentPage}),onClick:o=>e.goToPage(s),size:"small"},{default:c(()=>[M(g(s),1)]),_:2},1032,["class","onClick"]))),128))]),r("div",R,[r("span",W," Страница "+g(t.currentPage)+" из "+g(e.totalPages),1)]),u(e.SecondaryButton,{disabled:t.currentPage===e.totalPages,onClick:a[2]||(a[2]=s=>e.goToPage(t.currentPage+1)),"icon-only":"",title:"Следующая страница"},{icon:c(()=>[u(e.NextPageIcon)]),_:1},8,["disabled"]),u(e.SecondaryButton,{disabled:t.currentPage===e.totalPages,onClick:a[3]||(a[3]=s=>e.goToPage(e.totalPages)),"icon-only":"",title:"Последняя страница"},{icon:c(()=>[u(e.LastPageIcon)]),_:1},8,["disabled"])]),r("div",X,[a[4]||(a[4]=r("span",{class:"text-surface-600 dark:text-surface-300"},"На странице:",-1)),u(e.Select,{"model-value":t.itemsPerPage,options:e.itemsPerPageOptions,"onUpdate:modelValue":e.changeItemsPerPage,class:"w-20"},null,8,["model-value"])])]),t.enableVirtualScroll?(f(),y("div",Y,[r("div",Z,[t.hasMoreItems?(f(),B(e.SecondaryButton,{key:0,loading:t.loadingMore,onClick:e.loadMore,outlined:""},{default:c(()=>a[5]||(a[5]=[M(" Загрузить еще ")])),_:1,__:[5]},8,["loading"])):I("",!0)])])):I("",!0)]),_:1})])}const fe=F(q,[["render",$],["__scopeId","data-v-ff69da72"]]),ee=w({__name:"Spinner",props:{size:{default:"md"},variant:{default:"default"},label:{},centered:{type:Boolean,default:!1}},setup(l,{expose:a}){a();const t=l,e=P(()=>{const o="flex items-center gap-2",i=t.centered?"justify-center":"",C=t.label?"flex-col":"";return`${o} ${i} ${C}`.trim()}),d=P(()=>{const o={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"},i={default:"text-[--color-foreground]",primary:"text-[--color-primary]",secondary:"text-[--color-secondary]",success:"text-[--color-success]",warning:"text-[--color-warning]",danger:"text-[--color-danger]"};return`${o[t.size]} ${i[t.variant]}`}),_=P(()=>`${{xs:"text-xs",sm:"text-sm",md:"text-sm",lg:"text-base",xl:"text-lg"}[t.size]} text-[--color-muted] font-medium`),s={props:t,containerClasses:e,spinnerClasses:d,labelClasses:_,get Motion(){return U}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}});function te(l,a,t,e,d,_){return f(),y("div",{class:h(e.containerClasses)},[u(e.Motion,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},class:h(e.spinnerClasses)},{default:c(()=>a[0]||(a[0]=[r("svg",{viewBox:"0 0 24 24",fill:"none"},[r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-dasharray":"60 40",class:"opacity-25"}),r("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-dasharray":"15 85",class:"opacity-75"})],-1)])),_:1,__:[0]},8,["class"]),t.label?(f(),y("div",{key:0,class:h(e.labelClasses)},g(t.label),3)):I("",!0)],2)}const Pe=F(ee,[["render",te]]);export{fe as C,Pe as S,ge as u};

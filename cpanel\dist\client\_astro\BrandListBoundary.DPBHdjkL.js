import{E as M}from"./ErrorBoundary.B0AhELGe.js";import{t as g}from"./trpc.BpyaUO08.js";import O from"./Button.DrThv2lH.js";import{D as P,s as A}from"./index.BWD5ZO4k.js";import{D as L}from"./Dialog.Ct7C9BO5.js";import{I}from"./InputText.DOJMNEP-.js";import{S as U}from"./SecondaryButton.DkELYl7Q.js";import{V as j}from"./AutoComplete.rPzROuMW.js";import{_ as E}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{V as q}from"./ToggleSwitch.9ueDJKWv.js";import{d as h,g as x,o as B,w as m,a as t,e as a,K as D,L as R,j as k,i as N,c as S,f as K,F as z,r as G}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as d}from"./reactivity.esm-bundler.BQ12LWmY.js";import{n as T}from"./router.DKcY2uv6.js";import H from"./Toast.DmmKUJB6.js";import{P as J}from"./plus.CiWMw0wk.js";import{T as Q,P as W}from"./trash.4HbnIIsp.js";import"./createLucideIcon.NtN1-Ts2.js";import"./triangle-alert.CP-lXbmj.js";import"./index.6ykohhwZ.js";import"./index.BaVCXmir.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.By2TJOuX.js";import"./index.COq_zjeV.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";import"./index.D4QD70nN.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./index.CmzoVUnM.js";import"./index.PhWaFJhe.js";const X=h({__name:"EditBrandDialog",props:D({brand:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:D(["save","cancel"],["update:isVisible"]),setup(s,{expose:o,emit:i}){o();const e=d([]),l=d([]),r=R(s,"isVisible"),u=s,f=i,p=d({}),_=d("Создать бренд"),V=c=>{const n=c.query.toLowerCase();n?e.value=l.value.filter(v=>v.toLowerCase().includes(n)):e.value=[...l.value]};k(r,c=>{c&&(p.value={...u.brand||{}},_.value=u.brand?.id?`Редактировать: ${u.brand.name}`:"Создать бренд")});function C(){r.value=!1,f("cancel")}function w(){f("save",{...p.value}),r.value=!1}async function b(){const c=await g.crud.brand.findMany.query({where:{country:{not:null}},select:{country:!0},distinct:["country"],orderBy:{country:"asc"}});l.value=c.map(n=>n.country),e.value=[...l.value]}N(()=>{b()});const y={countryOptions:e,allCountries:l,isVisible:r,props:u,emit:f,localBrand:p,dialogTitle:_,filterCountries:V,handleCancel:C,handleSave:w,loadCountries:b,Button:O,Dialog:L,InputText:I,SecondaryButton:U,AutoComplete:j,ToggleSwitch:q};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}}),Y={class:"flex flex-col gap-4 py-4"},Z={class:"flex flex-col"},$={class:"flex flex-col"},ee={class:"flex flex-col"},oe={class:"flex gap-3"},ue={class:"flex justify-end gap-2"};function ne(s,o,i,e,l,r){return B(),x(e.Dialog,{visible:e.isVisible,"onUpdate:visible":o[4]||(o[4]=u=>e.isVisible=u),modal:"",header:e.dialogTitle,class:"sm:w-100 w-9/10"},{default:m(()=>[t("div",Y,[t("div",Z,[o[5]||(o[5]=t("label",{for:"name"},"Наименование",-1)),a(e.InputText,{id:"name",modelValue:e.localBrand.name,"onUpdate:modelValue":o[0]||(o[0]=u=>e.localBrand.name=u)},null,8,["modelValue"])]),t("div",$,[o[6]||(o[6]=t("label",{for:"slug"},"URL слаг",-1)),a(e.InputText,{id:"slug",modelValue:e.localBrand.slug,"onUpdate:modelValue":o[1]||(o[1]=u=>e.localBrand.slug=u)},null,8,["modelValue"])]),t("div",ee,[o[7]||(o[7]=t("label",{for:"country"},"Страна",-1)),a(e.AutoComplete,{onComplete:e.filterCountries,id:"country",dropdownMode:"current",modelValue:e.localBrand.country,"onUpdate:modelValue":o[2]||(o[2]=u=>e.localBrand.country=u),dropdown:"",suggestions:e.countryOptions},null,8,["modelValue","suggestions"])]),t("div",oe,[o[8]||(o[8]=t("label",{for:"isOem"},"OEM",-1)),a(e.ToggleSwitch,{id:"isOem",modelValue:e.localBrand.isOem,"onUpdate:modelValue":o[3]||(o[3]=u=>e.localBrand.isOem=u)},null,8,["modelValue"])])]),t("div",ue,[a(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),a(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const ae=E(X,[["render",ne]]),le=h({__name:"BrandList",props:{initialData:{}},setup(s,{expose:o}){o();const i=d(""),e=d(!1),l=d(null),r=s,u=d(r.initialData),f={id:"ID",name:"Наименование",slug:"url слаг",country:"Страна",isOem:"OEM"},p=["id","name","slug","country","isOem"];function _(){l.value={},e.value=!0}function V(n){l.value={...n},e.value=!0}async function C(n){if(n)try{if(n.id){const{id:v,...F}=n;await g.crud.brand.update.mutate({where:{id:v},data:F})}else if(n.name&&n.slug)await g.crud.brand.create.mutate({data:{name:n.name,slug:n.slug,country:n.country,isOem:n.isOem||!1}});else{console.error("Name and slug are required to create a brand.");return}T(window.location.href)}catch(v){console.error("Failed to save brand:",v)}finally{e.value=!1}}function w(){e.value=!1,l.value=null}async function b(n){e.value=!1,await g.crud.brand.delete.mutate({where:{id:n.id}}),T(window.location.href)}k(i,n=>{y(n)});async function y(n=""){console.log("value",n),u.value=await g.crud.brand.findMany.query({where:{OR:[{name:{contains:n}},{slug:{contains:n}},{country:{contains:n}}]}})}const c={searchValue:i,dialogVisible:e,editingBrand:l,props:r,items:u,keyMapping:f,columnKeys:p,createBrand:_,editBrand:V,handleSave:C,handleCancel:w,deleteBrand:b,debouncedSearch:y,Button:O,DataTable:P,get PencilIcon(){return W},get TrashIcon(){return Q},get PlusIcon(){return J},get Column(){return A},EditBrandDialog:ae,InputText:I,Toast:H};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}}),te={class:"flex justify-between items-center mb-4"},re={class:"flex justify-end"},ie={class:"flex gap-2"};function se(s,o,i,e,l,r){return B(),S("div",null,[t("div",te,[o[3]||(o[3]=t("h1",{class:"text-2xl font-bold"},"Бренды",-1)),a(e.Button,{onClick:e.createBrand},{default:m(()=>[a(e.PlusIcon,{class:"w-5 h-5 mr-2"}),o[2]||(o[2]=K(" Создать бренд "))]),_:1,__:[2]})]),a(e.DataTable,{"show-headers":"",value:e.items},{header:m(()=>[t("div",re,[a(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":o[0]||(o[0]=u=>e.searchValue=u),placeholder:"Поиск"},null,8,["modelValue"])])]),default:m(()=>[(B(),S(z,null,G(e.columnKeys,u=>a(e.Column,{key:u,field:u,header:e.keyMapping[u]||u},null,8,["field","header"])),64)),a(e.Column,{field:"_count.catalogItems",header:"Кол-во кат.поз."}),a(e.Column,{header:"Действия"},{body:m(({data:u})=>[t("div",ie,[a(e.Button,{onClick:f=>e.editBrand(u),outlined:"",size:"small"},{default:m(()=>[a(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),a(e.Button,{onClick:f=>e.deleteBrand(u),outlined:"",severity:"danger",size:"small"},{default:m(()=>[a(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value"]),a(e.EditBrandDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":o[1]||(o[1]=u=>e.dialogVisible=u),brand:e.editingBrand,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","brand"]),a(e.Toast)])}const de=E(le,[["render",se]]),ce=h({__name:"BrandListBoundary",props:{initialData:{}},setup(s,{expose:o}){o();const i=s,e=d(0),r={props:i,key:e,onRetry:()=>{e.value++},ErrorBoundary:M,BrandList:de};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}});function me(s,o,i,e,l,r){return B(),x(e.ErrorBoundary,{variant:"minimal",title:"Ошибка брендов",message:"Список брендов не отрисовался. Повторите попытку.",onRetry:e.onRetry},{default:m(()=>[(B(),x(e.BrandList,{initialData:i.initialData,key:e.key},null,8,["initialData"]))]),_:1})}const $e=E(ce,[["render",me]]);export{$e as default};

import{u as F,C as v}from"./index.J-5Oa3io.js";import{s as z}from"./index.6ykohhwZ.js";import{s as T}from"./Dialog.Ct7C9BO5.js";import{s as O,p as M}from"./utils.BUKUcbtE.js";import{B as R}from"./index.BaVCXmir.js";import{P as S,g as j,o as m,n as C,w as u,p as y,e as f,m as h,a as d,c as L,b as w,F as x,M as D,d as V,f as B}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{n as I,t as g,r as E}from"./reactivity.esm-bundler.BQ12LWmY.js";import{_ as H}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{S as U}from"./SecondaryButton.DkELYl7Q.js";import{D as N}from"./DangerButton.Du4QYdLH.js";import{c as k}from"./createLucideIcon.NtN1-Ts2.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=k("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=k("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=k("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=k("undo-2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]]),he=()=>{const e=F(),t=n=>{e.require({message:n.message||"Вы уверены?",header:n.header||"Подтверждение",icon:n.icon||"pi pi-exclamation-triangle",acceptLabel:n.acceptLabel||"Да",rejectLabel:n.rejectLabel||"Отмена",acceptClass:n.acceptClass,rejectClass:n.rejectClass,accept:n.accept,reject:n.reject})};return{show:t,confirmDelete:(n="запись",a,s)=>{t({message:`Вы действительно хотите удалить эту ${n}? Это действие нельзя отменить.`,header:"Подтверждение удаления",icon:"pi pi-trash",acceptLabel:"Удалить",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:a,reject:s})},confirmSave:(n="Сохранить изменения?",a,s)=>{t({message:n,header:"Сохранение",icon:"pi pi-save",acceptLabel:"Сохранить",rejectLabel:"Отмена",acceptClass:"bg-primary-500 hover:bg-primary-600",accept:a,reject:s})},confirmCancel:(n="У вас есть несохраненные изменения. Вы действительно хотите выйти без сохранения?",a,s)=>{t({message:n,header:"Несохраненные изменения",icon:"pi pi-exclamation-triangle",acceptLabel:"Выйти без сохранения",rejectLabel:"Остаться",acceptClass:"bg-orange-500 hover:bg-orange-600",accept:a,reject:s})},confirmPublish:(n="запись",a,s)=>{t({message:`Опубликовать эту ${n}? После публикации она станет доступна всем пользователям.`,header:"Подтверждение публикации",icon:"pi pi-globe",acceptLabel:"Опубликовать",rejectLabel:"Отмена",acceptClass:"bg-green-500 hover:bg-green-600",accept:a,reject:s})},confirmArchive:(n="запись",a,s)=>{t({message:`Архивировать эту ${n}? Архивированные записи не отображаются в основном списке.`,header:"Подтверждение архивирования",icon:"pi pi-archive",acceptLabel:"Архивировать",rejectLabel:"Отмена",acceptClass:"bg-gray-500 hover:bg-gray-600",accept:a,reject:s})},confirmRestore:(n="запись",a,s)=>{t({message:`Восстановить эту ${n}? Она снова станет доступна в основном списке.`,header:"Подтверждение восстановления",icon:"pi pi-refresh",acceptLabel:"Восстановить",rejectLabel:"Отмена",acceptClass:"bg-blue-500 hover:bg-blue-600",accept:a,reject:s})},confirmBulkAction:(n,a,s,P)=>{t({message:`Выполнить действие "${n}" для ${a} записей?`,header:"Массовое действие",icon:"pi pi-list",acceptLabel:"Выполнить",rejectLabel:"Отмена",acceptClass:"bg-primary-500 hover:bg-primary-600",accept:s,reject:P})}}};var J=`
    .p-confirmdialog .p-dialog-content {
        display: flex;
        align-items: center;
        gap: dt('confirmdialog.content.gap');
    }

    .p-confirmdialog-icon {
        color: dt('confirmdialog.icon.color');
        font-size: dt('confirmdialog.icon.size');
        width: dt('confirmdialog.icon.size');
        height: dt('confirmdialog.icon.size');
    }
`,K={root:"p-confirmdialog",icon:"p-confirmdialog-icon",message:"p-confirmdialog-message",pcRejectButton:"p-confirmdialog-reject-button",pcAcceptButton:"p-confirmdialog-accept-button"},Q=R.extend({name:"confirmdialog",style:J,classes:K}),W={name:"BaseConfirmDialog",extends:O,props:{group:String,breakpoints:{type:Object,default:null},draggable:{type:Boolean,default:!0}},style:Q,provide:function(){return{$pcConfirmDialog:this,$parentInstance:this}}},A={name:"ConfirmDialog",extends:W,confirmListener:null,closeListener:null,data:function(){return{visible:!1,confirmation:null}},mounted:function(){var t=this;this.confirmListener=function(r){r&&r.group===t.group&&(t.confirmation=r,t.confirmation.onShow&&t.confirmation.onShow(),t.visible=!0)},this.closeListener=function(){t.visible=!1,t.confirmation=null},v.on("confirm",this.confirmListener),v.on("close",this.closeListener)},beforeUnmount:function(){v.off("confirm",this.confirmListener),v.off("close",this.closeListener)},methods:{accept:function(){this.confirmation.accept&&this.confirmation.accept(),this.visible=!1},reject:function(){this.confirmation.reject&&this.confirmation.reject(),this.visible=!1},onHide:function(){this.confirmation.onHide&&this.confirmation.onHide(),this.visible=!1}},computed:{appendTo:function(){return this.confirmation?this.confirmation.appendTo:"body"},target:function(){return this.confirmation?this.confirmation.target:null},modal:function(){return this.confirmation?this.confirmation.modal==null?!0:this.confirmation.modal:!0},header:function(){return this.confirmation?this.confirmation.header:null},message:function(){return this.confirmation?this.confirmation.message:null},blockScroll:function(){return this.confirmation?this.confirmation.blockScroll:!0},position:function(){return this.confirmation?this.confirmation.position:null},acceptLabel:function(){if(this.confirmation){var t,r=this.confirmation;return r.acceptLabel||((t=r.acceptProps)===null||t===void 0?void 0:t.label)||this.$primevue.config.locale.accept}return this.$primevue.config.locale.accept},rejectLabel:function(){if(this.confirmation){var t,r=this.confirmation;return r.rejectLabel||((t=r.rejectProps)===null||t===void 0?void 0:t.label)||this.$primevue.config.locale.reject}return this.$primevue.config.locale.reject},acceptIcon:function(){var t;return this.confirmation?this.confirmation.acceptIcon:(t=this.confirmation)!==null&&t!==void 0&&t.acceptProps?this.confirmation.acceptProps.icon:null},rejectIcon:function(){var t;return this.confirmation?this.confirmation.rejectIcon:(t=this.confirmation)!==null&&t!==void 0&&t.rejectProps?this.confirmation.rejectProps.icon:null},autoFocusAccept:function(){return this.confirmation.defaultFocus===void 0||this.confirmation.defaultFocus==="accept"},autoFocusReject:function(){return this.confirmation.defaultFocus==="reject"},closeOnEscape:function(){return this.confirmation?this.confirmation.closeOnEscape:!0}},components:{Dialog:T,Button:z}};function Y(e,t,r,c,i,o){var p=S("Button"),b=S("Dialog");return m(),j(b,{visible:i.visible,"onUpdate:visible":[t[2]||(t[2]=function(l){return i.visible=l}),o.onHide],role:"alertdialog",class:I(e.cx("root")),modal:o.modal,header:o.header,blockScroll:o.blockScroll,appendTo:o.appendTo,position:o.position,breakpoints:e.breakpoints,closeOnEscape:o.closeOnEscape,draggable:e.draggable,pt:e.pt,unstyled:e.unstyled},C({default:u(function(){return[e.$slots.container?w("",!0):(m(),L(x,{key:0},[e.$slots.message?(m(),j(D(e.$slots.message),{key:1,message:i.confirmation},null,8,["message"])):(m(),L(x,{key:0},[y(e.$slots,"icon",{},function(){return[e.$slots.icon?(m(),j(D(e.$slots.icon),{key:0,class:I(e.cx("icon"))},null,8,["class"])):i.confirmation.icon?(m(),L("span",h({key:1,class:[i.confirmation.icon,e.cx("icon")]},e.ptm("icon")),null,16)):w("",!0)]}),d("span",h({class:e.cx("message")},e.ptm("message")),g(o.message),17)],64))],64))]}),_:2},[e.$slots.container?{name:"container",fn:u(function(l){return[y(e.$slots,"container",{message:i.confirmation,closeCallback:l.onclose,acceptCallback:o.accept,rejectCallback:o.reject})]}),key:"0"}:void 0,e.$slots.container?void 0:{name:"footer",fn:u(function(){var l;return[f(p,h({class:[e.cx("pcRejectButton"),i.confirmation.rejectClass],autofocus:o.autoFocusReject,unstyled:e.unstyled,text:((l=i.confirmation.rejectProps)===null||l===void 0?void 0:l.text)||!1,onClick:t[0]||(t[0]=function(n){return o.reject()})},i.confirmation.rejectProps,{label:o.rejectLabel,pt:e.ptm("pcRejectButton")}),C({_:2},[o.rejectIcon||e.$slots.rejecticon?{name:"icon",fn:u(function(n){return[y(e.$slots,"rejecticon",{},function(){return[d("span",h({class:[o.rejectIcon,n.class]},e.ptm("pcRejectButton").icon,{"data-pc-section":"rejectbuttonicon"}),null,16)]})]}),key:"0"}:void 0]),1040,["class","autofocus","unstyled","text","label","pt"]),f(p,h({label:o.acceptLabel,class:[e.cx("pcAcceptButton"),i.confirmation.acceptClass],autofocus:o.autoFocusAccept,unstyled:e.unstyled,onClick:t[1]||(t[1]=function(n){return o.accept()})},i.confirmation.acceptProps,{pt:e.ptm("pcAcceptButton")}),C({_:2},[o.acceptIcon||e.$slots.accepticon?{name:"icon",fn:u(function(n){return[y(e.$slots,"accepticon",{},function(){return[d("span",h({class:[o.acceptIcon,n.class]},e.ptm("pcAcceptButton").icon,{"data-pc-section":"acceptbuttonicon"}),null,16)]})]}),key:"0"}:void 0]),1040,["label","class","autofocus","unstyled","pt"])]}),key:"1"}]),1032,["visible","class","modal","header","blockScroll","appendTo","position","breakpoints","closeOnEscape","draggable","onUpdate:visible","pt","unstyled"])}A.render=Y;const Z=V({__name:"ConfirmDialog",setup(e,{expose:t}){t();const c={theme:E({root:`max-h-[90%] max-w-screen rounded-xl
        border border-surface-200 dark:border-surface-700
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0 shadow-lg`,mask:"bg-black/50 fixed top-0 start-0 w-full h-full",transition:{enterFromClass:"opacity-0 scale-75",enterActiveClass:"transition-all duration-150 ease-[cubic-bezier(0,0,0.2,1)]",leaveActiveClass:"transition-all duration-150 ease-[cubic-bezier(0.4,0,0.2,1)]",leaveToClass:"opacity-0 scale-75"}}),get ConfirmDialog(){return A},SecondaryButton:U,get ptViewMerge(){return M},get AlertCircle(){return q},get XCircleIcon(){return X},DangerButton:N,get Trash2Icon(){return _},get Undo2Icon(){return G}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}}),$={class:"flex items-center justify-between shrink-0 p-5"},ee={class:"font-semibold text-xl"},te={class:"overflow-y-auto pt-0 px-5 pb-5 flex items-center gap-4"},ne={class:"pt-0 px-5 pb-5 flex justify-end gap-2"};function oe(e,t,r,c,i,o){return m(),j(c.ConfirmDialog,{unstyled:"",pt:c.theme,ptOptions:{mergeProps:c.ptViewMerge}},{container:u(({message:p,acceptCallback:b,rejectCallback:l})=>[d("div",$,[d("span",ee,g(p.header),1),f(c.SecondaryButton,{variant:"text",rounded:"",onClick:l,autofocus:""},{default:u(()=>[f(c.XCircleIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])]),d("div",te,[f(c.AlertCircle,{class:"w-4 h-4"}),B(" "+g(p.message),1)]),d("div",ne,[f(c.SecondaryButton,{onClick:l,size:"small"},{default:u(()=>[f(c.Undo2Icon,{class:"w-4 h-4"}),B(" "+g(p.rejectProps?.label),1)]),_:2},1032,["onClick"]),f(c.DangerButton,{onClick:b,size:"small"},{default:u(()=>[f(c.Trash2Icon,{class:"w-4 h-4"}),B(" "+g(p.acceptProps?.label),1)]),_:2},1032,["onClick"])])]),_:1},8,["pt","ptOptions"])}const ge=H(Z,[["render",oe]]);export{ge as C,he as u};

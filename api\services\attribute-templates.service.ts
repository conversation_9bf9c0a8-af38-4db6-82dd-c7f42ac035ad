import { TRPCError } from '@trpc/server'
import { getSystemDB } from '../db'

export class AttributeTemplatesService {
  static async findMany(input: any) {
    try {
      const db = getSystemDB()
      const where: any = {}

      if (input.groupId) where.groupId = input.groupId
      if (input.search) {
        where.OR = [
          { name: { contains: input.search, mode: 'insensitive' } },
          { title: { contains: input.search, mode: 'insensitive' } },
          { description: { contains: input.search, mode: 'insensitive' } },
        ]
      }
      if (input.dataType) where.dataType = input.dataType

      const [templates, total] = await Promise.all([
        db.attributeTemplate.findMany({
          where,
          include: {
            group: true,
            _count: { select: { partAttributes: true, catalogItemAttributes: true, equipmentAttributes: true } },
          },
          orderBy: [{ group: { name: 'asc' } }, { name: 'asc' }],
          take: input.limit,
          skip: input.offset,
        }),
        db.attributeTemplate.count({ where }),
      ])

      return { templates, total, hasMore: input.offset + input.limit < total }
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить шаблоны атрибутов' })
    }
  }

  static async findById(input: { id: number }) {
    try {
      const db = getSystemDB()
      const template = await db.attributeTemplate.findUnique({
        where: { id: input.id },
        include: {
          group: true,
          _count: { select: { partAttributes: true, catalogItemAttributes: true, equipmentAttributes: true } },
        },
      })
      if (!template) throw new TRPCError({ code: 'NOT_FOUND', message: 'Шаблон атрибута не найден' })
      return template
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить шаблон атрибута' })
    }
  }

  static async create(input: any) {
    try {
      const db = getSystemDB()
      const existingTemplate = await db.attributeTemplate.findUnique({ where: { name: input.name } })
      if (existingTemplate) throw new TRPCError({ code: 'CONFLICT', message: 'Шаблон с таким именем уже существует' })

      if (input.groupId) {
        const group = await db.attributeGroup.findUnique({ where: { id: input.groupId } })
        if (!group) throw new TRPCError({ code: 'NOT_FOUND', message: 'Группа атрибутов не найдена' })
      }

      const template = await db.attributeTemplate.create({
        data: {
          name: input.name,
          title: input.title,
          description: input.description,
          dataType: input.dataType,
          unit: input.unit,
          isRequired: input.isRequired,
          minValue: input.minValue,
          maxValue: input.maxValue,
          allowedValues: input.allowedValues,
          tolerance: input.tolerance ?? 0,
          groupId: input.groupId,
        },
        include: { group: true },
      })
      return template
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось создать шаблон атрибута' })
    }
  }

  static async update(input: any) {
    try {
      const db = getSystemDB()
      const existingTemplate = await db.attributeTemplate.findUnique({ where: { id: input.id } })
      if (!existingTemplate) throw new TRPCError({ code: 'NOT_FOUND', message: 'Шаблон атрибута не найден' })

      if (input.name && input.name !== existingTemplate.name) {
        const nameConflict = await db.attributeTemplate.findUnique({ where: { name: input.name } })
        if (nameConflict) throw new TRPCError({ code: 'CONFLICT', message: 'Шаблон с таким именем уже существует' })
      }

      if (input.groupId !== undefined && input.groupId !== null) {
        const group = await db.attributeGroup.findUnique({ where: { id: input.groupId } })
        if (!group) throw new TRPCError({ code: 'NOT_FOUND', message: 'Группа атрибутов не найдена' })
      }

      const { id, ...updateData } = input
      const updatedTemplate = await db.attributeTemplate.update({
        where: { id },
        data: updateData,
        include: { group: true },
      })
      return updatedTemplate
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось обновить шаблон атрибута' })
    }
  }

  static async delete(input: { id: number }) {
    try {
      const db = getSystemDB()
      const existingTemplate = await db.attributeTemplate.findUnique({
        where: { id: input.id },
        include: { _count: { select: { partAttributes: true, catalogItemAttributes: true, equipmentAttributes: true } } },
      })
      if (!existingTemplate) throw new TRPCError({ code: 'NOT_FOUND', message: 'Шаблон атрибута не найден' })
      const totalUsage =
        existingTemplate._count.partAttributes +
        existingTemplate._count.catalogItemAttributes +
        existingTemplate._count.equipmentAttributes
      if (totalUsage > 0) {
        throw new TRPCError({ code: 'CONFLICT', message: `Шаблон используется в ${totalUsage} атрибутах и не может быть удален` })
      }
      await db.attributeTemplate.delete({ where: { id: input.id } })
      return { success: true }
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось удалить шаблон атрибута' })
    }
  }

  static async findAllGroups() {
    try {
      const db = getSystemDB()
      const groups = await db.attributeGroup.findMany({
        include: { _count: { select: { templates: true } } },
        orderBy: { name: 'asc' },
      })
      return groups
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось получить группы атрибутов' })
    }
  }

  static async searchGroups(input: any) {
    try {
      const db = getSystemDB()
      const where: any = {}
      if (input.query && input.query.trim()) {
        where.OR = [
          { name: { contains: input.query.trim(), mode: 'insensitive' } },
          { description: { contains: input.query.trim(), mode: 'insensitive' } },
        ]
      }
      const groups = await db.attributeGroup.findMany({
        where,
        include: { _count: { select: { templates: true } } },
        orderBy: [{ name: 'asc' }],
        take: input.limit,
      })
      return groups
    } catch (error: any) {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось найти группы атрибутов' })
    }
  }

  static async createGroup(input: any) {
    try {
      const db = getSystemDB()
      const existingGroup = await db.attributeGroup.findFirst({
        where: { name: { equals: input.name, mode: 'insensitive' } },
      })
      if (existingGroup) throw new TRPCError({ code: 'CONFLICT', message: 'Группа с таким именем уже существует' })
      const group = await db.attributeGroup.create({ data: { name: input.name.trim(), description: input.description } })
      return group
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось создать группу атрибутов' })
    }
  }

  static async updateGroup(input: any) {
    try {
      const db = getSystemDB()
      const existingGroup = await db.attributeGroup.findUnique({ where: { id: input.id } })
      if (!existingGroup) throw new TRPCError({ code: 'NOT_FOUND', message: 'Группа атрибутов не найдена' })
      if (input.name && input.name !== existingGroup.name) {
        const nameConflict = await db.attributeGroup.findFirst({
          where: { name: { equals: input.name, mode: 'insensitive' }, id: { not: input.id } },
        })
        if (nameConflict) throw new TRPCError({ code: 'CONFLICT', message: 'Группа с таким именем уже существует' })
      }
      const { id, ...data } = input
      const updatedGroup = await db.attributeGroup.update({ where: { id }, data })
      return updatedGroup
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось обновить группу атрибутов' })
    }
  }

  static async deleteGroup(input: { id: number }) {
    try {
      const db = getSystemDB()
      const existingGroup = await db.attributeGroup.findUnique({
        where: { id: input.id },
        include: { _count: { select: { templates: true } } },
      })
      if (!existingGroup) throw new TRPCError({ code: 'NOT_FOUND', message: 'Группа атрибутов не найдена' })
      if (existingGroup._count.templates > 0) {
        throw new TRPCError({ code: 'CONFLICT', message: `В группе есть ${existingGroup._count.templates} шаблонов. Сначала удалите или переместите их.` })
      }
      await db.attributeGroup.delete({ where: { id: input.id } })
      return { success: true }
    } catch (error: any) {
      if (error instanceof TRPCError) throw error
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: error.message || 'Не удалось удалить группу атрибутов' })
    }
  }
}



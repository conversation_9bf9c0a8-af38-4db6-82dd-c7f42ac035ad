import{E as L4}from"./ErrorBoundary.B0AhELGe.js";import{u as t4}from"./useTrpc.spLZjt2f.js";import{a as R4}from"./useUrlParams.D0jSWJSf.js";import{u as z4,C as O4}from"./ConfirmDialog.J0sszorU.js";/* empty css                       */import{_ as H}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{u as d4}from"./useToast.pIbuf2bs.js";import Z from"./Card.C4y0_bWr.js";import{I as c4}from"./InputText.DOJMNEP-.js";import{V as m4}from"./AutoComplete.rPzROuMW.js";import J from"./Button.DrThv2lH.js";import{D as a4}from"./Dialog.Ct7C9BO5.js";import U4 from"./Toast.DmmKUJB6.js";import{D as N4,s as j4}from"./index.BWD5ZO4k.js";import{T as s4}from"./Tag.DTFTku6q.js";import{s as H4}from"./utils.BUKUcbtE.js";import{B as q4}from"./index.BaVCXmir.js";import{c as m,o,m as e4,a,d as q,k as $,g as b,w as l,e as s,b as _,F as Q,r as W,l as X,f as K,h as G4,j as f4,i as v4}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{I as r4}from"./Icon.By8t0-Wj.js";import{L as g4,M as Q4,a as W4,R as y4}from"./MatchingLoadingState.BeWu0CtS.js";import{T as X4,P as K4}from"./trash.4HbnIIsp.js";import{c as b4}from"./createLucideIcon.NtN1-Ts2.js";import{t as f,n as i4,r as y,a as J4}from"./reactivity.esm-bundler.BQ12LWmY.js";import{w as Y4}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{V as C4}from"./Textarea.BLEHJ3ym.js";import{C as Z4}from"./Checkbox.C7bFmmzc.js";import{Q as $4,A as u0}from"./QuickCreateBrand.C_-iK7cE.js";import{S as e0}from"./Select.CQBzSu6y.js";import{M as t0,u as a0}from"./MatchingDetailsGrid.BT5Doi8q.js";import{P as s0}from"./plus.CiWMw0wk.js";import{S as r0}from"./search.DFOrFhbU.js";import"./triangle-alert.CP-lXbmj.js";import"./trpc.BpyaUO08.js";import"./useErrorHandler.DVDazL16.js";import"./router.DKcY2uv6.js";import"./index.J-5Oa3io.js";import"./index.6ykohhwZ.js";import"./index.BH7IgUdp.js";import"./index.CDQpPXyE.js";import"./SecondaryButton.DkELYl7Q.js";import"./DangerButton.Du4QYdLH.js";import"./index.PhWaFJhe.js";import"./index.COq_zjeV.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.By2TJOuX.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./index.CUNrRq8E.js";import"./index.CmzoVUnM.js";import"./index.CS9OBiV4.js";import"./index.D4QD70nN.js";import"./bundle-mjs.D6B6e0vX.js";import"./AttributeValueInput.BnZ_HprM.js";import"./InputNumber.vgPO18dj.js";import"./Message.BY1UiDHQ.js";import"./ToggleSwitch.9ueDJKWv.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n0=b4("scan-eye",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["path",{d:"M18.944 12.33a1 1 0 0 0 0-.66 7.5 7.5 0 0 0-13.888 0 1 1 0 0 0 0 .66 7.5 7.5 0 0 0 13.888 0",key:"11ak4c"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o0=b4("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var l0=`
    .p-progressspinner {
        position: relative;
        margin: 0 auto;
        width: 100px;
        height: 100px;
        display: inline-block;
    }

    .p-progressspinner::before {
        content: '';
        display: block;
        padding-top: 100%;
    }

    .p-progressspinner-spin {
        height: 100%;
        transform-origin: center center;
        width: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        animation: p-progressspinner-rotate 2s linear infinite;
    }

    .p-progressspinner-circle {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: 0;
        stroke: dt('progressspinner.colorOne');
        animation:
            p-progressspinner-dash 1.5s ease-in-out infinite,
            p-progressspinner-color 6s ease-in-out infinite;
        stroke-linecap: round;
    }

    @keyframes p-progressspinner-rotate {
        100% {
            transform: rotate(360deg);
        }
    }
    @keyframes p-progressspinner-dash {
        0% {
            stroke-dasharray: 1, 200;
            stroke-dashoffset: 0;
        }
        50% {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: -35px;
        }
        100% {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: -124px;
        }
    }
    @keyframes p-progressspinner-color {
        100%,
        0% {
            stroke: dt('progressspinner.color.one');
        }
        40% {
            stroke: dt('progressspinner.color.two');
        }
        66% {
            stroke: dt('progressspinner.color.three');
        }
        80%,
        90% {
            stroke: dt('progressspinner.color.four');
        }
    }
`,i0={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},d0=q4.extend({name:"progressspinner",style:l0,classes:i0}),c0={name:"BaseProgressSpinner",extends:H4,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:d0,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},E4={name:"ProgressSpinner",extends:c0,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},m0=["fill","stroke-width"];function f0(d,t,r,u,C,k){return o(),m("div",e4({class:d.cx("root"),role:"progressbar"},d.ptmi("root")),[(o(),m("svg",e4({class:d.cx("spin"),viewBox:"25 25 50 50",style:k.svgStyle},d.ptm("spin")),[a("circle",e4({class:d.cx("circle"),cx:"50",cy:"50",r:"20",fill:d.fill,"stroke-width":d.strokeWidth,strokeMiterlimit:"10"},d.ptm("circle")),null,16,m0)],16))],16)}E4.render=f0;const v0=q({__name:"CatalogItemsTable",props:{items:{},loading:{type:Boolean},totalRecords:{},rows:{},first:{}},emits:["page","sort","edit","delete","view-details","match"],setup(d,{expose:t}){t();const u={formatDate:C=>{if(!C)return"";const k=new Date(C);return new Intl.DateTimeFormat("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(k)},VCard:Z,VDataTable:N4,get VColumn(){return j4},VButton:J,VTag:s4,get VProgressSpinner(){return E4},get PencilIcon(){return K4},get ScanEyeIcon(){return n0},get TrashIcon(){return X4},Icon:r4,get LinkIcon(){return g4}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}}),g0={class:"flex items-center gap-2"},y0={class:"font-mono font-medium"},b0={class:"flex items-center gap-2"},C0={class:"font-medium"},E0={class:"max-w-xs"},p0=["title"],h0={key:1,class:"text-surface-400 italic"},D0={class:"flex flex-wrap gap-1"},_0={class:"text-center"},k0={key:1,class:"text-surface-400 text-sm"},x0={key:0,class:"text-surface-600 dark:text-surface-400 text-sm"},F0={key:1,class:"text-surface-400 italic"},B0={class:"text-surface-600 dark:text-surface-400 font-mono text-sm"},w0={class:"flex gap-1"},I0={class:"py-8 text-center"},A0={class:"py-8 text-center"};function V0(d,t,r,u,C,k){const v=$("tooltip");return o(),b(u.VCard,null,{content:l(()=>[s(u.VDataTable,{value:r.items,loading:r.loading,paginator:!0,rows:r.rows,"total-records":r.totalRecords,first:r.first,lazy:!0,sortable:!0,"paginator-template":"FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown","current-page-report-template":"Показано {first} - {last} из {totalRecords} записей","rows-per-page-options":[10,20,50],onPage:t[0]||(t[0]=e=>d.$emit("page",e)),onSort:t[1]||(t[1]=e=>d.$emit("sort",e))},{empty:l(()=>[a("div",I0,[s(u.Icon,{name:"pi pi-inbox",class:"text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl"}),t[2]||(t[2]=a("p",{class:"text-surface-500 dark:text-surface-400 mb-2 text-lg"},"Каталожные позиции не найдены",-1)),t[3]||(t[3]=a("p",{class:"text-surface-400 dark:text-surface-500 text-sm"},"Попробуйте изменить параметры поиска или создайте новую позицию",-1))])]),loading:l(()=>[a("div",A0,[s(u.VProgressSpinner,{size:"50"}),t[4]||(t[4]=a("p",{class:"text-surface-500 dark:text-surface-400 mt-4"},"Загрузка каталожных позиций...",-1))])]),default:l(()=>[s(u.VColumn,{field:"sku",header:"Артикул",sortable:!0,class:"min-w-32"},{body:l(({data:e})=>[a("div",g0,[a("span",y0,f(e.sku),1),e.isPublic?_("",!0):(o(),b(u.VTag,{key:0,value:"Приватная",severity:"warning",size:"small"}))])]),_:1}),s(u.VColumn,{field:"brand.name",header:"Бренд",sortable:!0,class:"min-w-32"},{body:l(({data:e})=>[a("div",b0,[a("span",C0,f(e.brand?.name||"Не указан"),1),e.brand?.isOem?(o(),b(u.VTag,{key:0,value:"OEM",severity:"info",size:"small"})):_("",!0)])]),_:1}),s(u.VColumn,{field:"description",header:"Описание",class:"min-w-48"},{body:l(({data:e})=>[a("div",E0,[e.description?(o(),m("p",{key:0,class:"text-surface-600 dark:text-surface-400 truncate text-sm",title:e.description},f(e.description),9,p0)):(o(),m("span",h0,"Нет описания"))])]),_:1}),s(u.VColumn,{header:"Атрибуты",class:"min-w-32"},{body:l(({data:e})=>[a("div",D0,[(o(!0),m(Q,null,W(e.attributes?.slice(0,3),c=>(o(),b(u.VTag,{key:c.id,value:`${c.template?.title}: ${c.value}`,severity:"secondary",size:"small",class:"text-xs"},null,8,["value"]))),128)),e.attributes?.length>3?(o(),b(u.VTag,{key:0,value:`+${e.attributes.length-3}`,severity:"secondary",size:"small",class:"text-xs"},null,8,["value"])):_("",!0)])]),_:1}),s(u.VColumn,{header:"Применимость",class:"min-w-24"},{body:l(({data:e})=>[a("div",_0,[e.applicabilities?.length>0?(o(),b(u.VTag,{key:0,value:`${e.applicabilities.length} групп`,severity:"success",size:"small"},null,8,["value"])):(o(),m("span",k0,"Не назначена"))])]),_:1}),s(u.VColumn,{field:"source",header:"Источник",class:"min-w-32"},{body:l(({data:e})=>[e.source?(o(),m("span",x0,f(e.source),1)):(o(),m("span",F0,"Не указан"))]),_:1}),s(u.VColumn,{field:"id",header:"ID",sortable:!0,class:"min-w-20"},{body:l(({data:e})=>[a("span",B0," #"+f(e.id),1)]),_:1}),s(u.VColumn,{header:"Действия",class:"min-w-32"},{body:l(({data:e})=>[a("div",w0,[X((o(),b(u.VButton,{onClick:c=>d.$emit("match",e),severity:"secondary",size:"small",text:""},{default:l(()=>[s(u.LinkIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])),[[v,"Найти группу взаимозаменяемости"]]),s(u.VButton,{onClick:c=>d.$emit("view-details",e),severity:"secondary",size:"small",text:""},{default:l(()=>[s(u.ScanEyeIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),s(u.VButton,{onClick:c=>d.$emit("edit",e),severity:"secondary",size:"small",text:""},{default:l(()=>[s(u.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),s(u.VButton,{onClick:c=>d.$emit("delete",e),severity:"danger",size:"small",text:""},{default:l(()=>[s(u.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading","rows","total-records","first"])]),_:1})}const T0=H(v0,[["render",V0]]),S0=q({__name:"CatalogItemForm",props:{item:{}},emits:["save","cancel"],setup(d,{expose:t,emit:r}){t();const u=d,C=r,{brands:k,loading:v}=t4(),e=y({sku:"",selectedBrand:null,description:"",source:"",isPublic:!0,attributes:[]}),c=y({sku:"",brandId:""}),T=y(!1),x=y(!1),B=y([]),R=()=>(c.value.sku="",e.value.sku.trim()?e.value.sku.length<2?(c.value.sku="Артикул должен содержать минимум 2 символа",!1):e.value.sku.length>64?(c.value.sku="Артикул не может быть длиннее 64 символов",!1):!0:(c.value.sku="Артикул обязателен",!1)),z=()=>(c.value.brandId="",e.value.selectedBrand?!0:(c.value.brandId="Бренд обязателен",!1)),I=G4(()=>e.value.sku.trim()&&e.value.selectedBrand&&!c.value.sku&&!c.value.brandId),A=async g=>{try{const D=g.query.toLowerCase(),F=await k.findMany({where:{name:{contains:D,mode:"insensitive"}},take:10});F&&(B.value=F)}catch(D){console.error("Ошибка поиска брендов:",D)}},w=g=>{e.value.selectedBrand=g,B.value=[g,...B.value]},S=g=>{e.value.attributes.splice(g,1)},U=g=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[g]||g,p=()=>{const g=R(),D=z();if(!g||!D)return;const F={sku:e.value.sku.toUpperCase().trim(),brandId:e.value.selectedBrand.id,description:e.value.description.trim()||void 0,source:e.value.source.trim()||void 0,isPublic:e.value.isPublic},O=e.value.attributes?e.value.attributes.filter(E=>{const G=E.value&&String(E.value).trim()!=="",P=E.templateId||E.template?.id;return G&&P}):[];O.length>0?u.item?F.attributes={deleteMany:{},create:O.map(E=>({templateId:E.templateId||E.template?.id,value:E.value}))}:F.attributes={create:O.map(E=>({templateId:E.templateId||E.template?.id,value:E.value}))}:u.item&&(F.attributes={deleteMany:{}}),C("save",F)},h=()=>{u.item&&(e.value={sku:u.item.sku||"",selectedBrand:u.item.brand||null,description:u.item.description||"",source:u.item.source||"",isPublic:u.item.isPublic??!0,attributes:u.item.attributes||[]})};f4(()=>u.item,h,{immediate:!0}),v4(()=>{h()});const M={props:u,emit:C,brands:k,loading:v,formData:e,errors:c,showAttributeManager:T,showCreateBrand:x,brandSuggestions:B,validateSku:R,validateBrand:z,isFormValid:I,searchBrands:A,onBrandCreated:w,removeAttribute:S,getUnitLabel:U,onSubmit:p,loadItemData:h,VInputText:c4,VTextarea:C4,VAutoComplete:m4,VCheckbox:Z4,VButton:J,VDialog:a4,AttributeManager:u0,QuickCreateBrand:$4,Icon:r4};return Object.defineProperty(M,"__isScriptSetup",{enumerable:!1,value:!0}),M}}),M0={class:"catalog-item-form"},P0={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},L0={key:0,class:"p-error"},R0={class:"flex gap-2"},z0={key:0,class:"p-error"},O0={class:"flex items-center gap-3"},U0={class:"flex items-center justify-between mb-4"},N0={key:0,class:"space-y-3"},j0={class:"flex-1"},H0={class:"font-medium text-surface-900 dark:text-surface-0"},q0={key:0,class:"text-red-500 ml-1"},G0={class:"text-sm text-surface-600 dark:text-surface-400"},Q0={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},W0={key:1,class:"text-center py-6 text-surface-500"},X0={class:"flex justify-end gap-3 pt-4 border-t border-surface-200 dark:border-surface-700"};function K0(d,t,r,u,C,k){const v=$("tooltip");return o(),m("div",M0,[a("form",{onSubmit:Y4(u.onSubmit,["prevent"]),class:"space-y-6"},[a("div",P0,[a("div",null,[t[12]||(t[12]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Артикул (SKU) * ",-1)),s(u.VInputText,{modelValue:u.formData.sku,"onUpdate:modelValue":t[0]||(t[0]=e=>u.formData.sku=e),placeholder:"Например: 12345-ABC",class:i4(["w-full",{"p-invalid":u.errors.sku}]),onBlur:u.validateSku},null,8,["modelValue","class"]),u.errors.sku?(o(),m("small",L0,f(u.errors.sku),1)):_("",!0)]),a("div",null,[t[13]||(t[13]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд * ",-1)),a("div",R0,[s(u.VAutoComplete,{modelValue:u.formData.selectedBrand,"onUpdate:modelValue":t[1]||(t[1]=e=>u.formData.selectedBrand=e),suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:i4(["flex-1",{"p-invalid":u.errors.brandId}]),dropdown:""},null,8,["modelValue","suggestions","class"]),X((o(),b(u.VButton,{onClick:t[2]||(t[2]=e=>u.showCreateBrand=!0),severity:"secondary",outlined:"",size:"small"},{default:l(()=>[s(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})),[[v,"Создать новый бренд"]])]),u.errors.brandId?(o(),m("small",z0,f(u.errors.brandId),1)):_("",!0)])]),a("div",null,[t[14]||(t[14]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),s(u.VTextarea,{modelValue:u.formData.description,"onUpdate:modelValue":t[3]||(t[3]=e=>u.formData.description=e),placeholder:"Описание каталожной позиции...",rows:"3",class:"w-full"},null,8,["modelValue"])]),a("div",null,[t[15]||(t[15]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Источник информации ",-1)),s(u.VInputText,{modelValue:u.formData.source,"onUpdate:modelValue":t[4]||(t[4]=e=>u.formData.source=e),placeholder:"Например: Официальный каталог, Данные клиента",class:"w-full"},null,8,["modelValue"])]),a("div",O0,[s(u.VCheckbox,{modelValue:u.formData.isPublic,"onUpdate:modelValue":t[5]||(t[5]=e=>u.formData.isPublic=e),"input-id":"isPublic",binary:""},null,8,["modelValue"]),t[16]||(t[16]=a("label",{for:"isPublic",class:"text-sm font-medium text-surface-700 dark:text-surface-300"}," Публичная позиция (видна всем пользователям) ",-1))]),a("div",null,[a("div",U0,[t[17]||(t[17]=a("h4",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Атрибуты ",-1)),s(u.VButton,{onClick:t[6]||(t[6]=e=>u.showAttributeManager=!0),severity:"secondary",outlined:"",size:"small",label:"Добавить атрибут"},{icon:l(()=>[s(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})]),u.formData.attributes.length>0?(o(),m("div",N0,[(o(!0),m(Q,null,W(u.formData.attributes,(e,c)=>(o(),m("div",{key:c,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[a("div",j0,[a("div",H0,[K(f(e.template?.title||e.templateTitle)+" ",1),e.template?.isRequired?(o(),m("span",q0,"*")):_("",!0)]),a("div",G0,[K(f(e.value)+" "+f(e.template?.unit?u.getUnitLabel(e.template.unit):"")+" ",1),e.template?.group?.name?(o(),m("span",Q0,f(e.template.group.name),1)):_("",!0)])]),s(u.VButton,{onClick:T=>u.removeAttribute(c),severity:"danger",size:"small",text:""},{default:l(()=>[s(u.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick"])]))),128))])):(o(),m("div",W0," Атрибуты не добавлены "))]),a("div",X0,[s(u.VButton,{onClick:t[7]||(t[7]=e=>d.$emit("cancel")),severity:"secondary",outlined:"",label:"Отмена"}),s(u.VButton,{type:"submit",loading:u.loading,disabled:!u.isFormValid,label:r.item?"Сохранить изменения":"Создать позицию"},null,8,["loading","disabled","label"])])],32),s(u.VDialog,{visible:u.showAttributeManager,"onUpdate:visible":t[10]||(t[10]=e=>u.showAttributeManager=e),modal:"",header:"Управление атрибутами",class:"w-full max-w-2xl"},{default:l(()=>[s(u.AttributeManager,{modelValue:u.formData.attributes,"onUpdate:modelValue":t[8]||(t[8]=e=>u.formData.attributes=e),onClose:t[9]||(t[9]=e=>u.showAttributeManager=!1)},null,8,["modelValue"])]),_:1},8,["visible"]),s(u.QuickCreateBrand,{visible:u.showCreateBrand,"onUpdate:visible":t[11]||(t[11]=e=>u.showCreateBrand=e),onCreated:u.onBrandCreated},null,8,["visible"])])}const J0=H(S0,[["render",K0]]),Y0=q({__name:"CatalogItemCard",props:{item:{}},emits:["edit","close","match","unlink"],setup(d,{expose:t}){t();const v={getUnitLabel:e=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[e]||e,getDataTypeLabel:e=>e?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[e]||e:"",getAccuracyLabel:e=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[e]||e,getAccuracySeverity:e=>({EXACT_MATCH:"success",MATCH_WITH_NOTES:"info",REQUIRES_MODIFICATION:"warning",PARTIAL_MATCH:"secondary"})[e]||"secondary",VCard:Z,VButton:J,VTag:s4,Icon:r4};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}}),Z0={class:"catalog-item-card"},$0={class:"flex items-start justify-between mb-6"},uu={class:"flex items-center gap-4"},eu={class:"text-2xl font-bold text-surface-900 dark:text-surface-0 font-mono"},tu={class:"flex flex-wrap items-center gap-2 mt-1"},au={class:"flex gap-2"},su={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6"},ru={class:"space-y-4"},nu={class:"p-4"},ou={key:0,class:"text-surface-700 dark:text-surface-300"},lu={key:1,class:"text-surface-400 italic"},iu={class:"p-4 space-y-3"},du={class:"flex justify-between"},cu={class:"font-medium"},mu={class:"flex justify-between"},fu={class:"flex justify-between"},vu={class:"font-medium font-mono"},gu={class:"space-y-4"},yu={class:"p-4 space-y-3"},bu={class:"flex justify-between"},Cu={class:"font-medium"},Eu={class:"flex justify-between"},pu={key:0,class:"flex justify-between"},hu={class:"font-medium"},Du={class:"p-4 space-y-3"},_u={class:"flex justify-between"},ku={class:"flex justify-between"},xu={class:"p-4 border-b border-surface-200 dark:border-surface-700"},Fu={class:"font-semibold text-surface-900 dark:text-surface-0"},Bu={class:"p-4"},wu={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Iu={class:"font-medium text-surface-900 dark:text-surface-0 mb-1"},Au={key:0,class:"text-red-500 ml-1"},Vu={class:"text-lg font-semibold text-primary-600 dark:text-primary-400 mb-1"},Tu={class:"flex items-center gap-2"},Su={class:"text-xs text-surface-500"},Mu={class:"p-4 border-b border-surface-200 dark:border-surface-700"},Pu={class:"font-semibold text-surface-900 dark:text-surface-0"},Lu={class:"p-4"},Ru={class:"space-y-3"},zu={class:"flex-1 min-w-0"},Ou={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},Uu=["href"],Nu={key:1},ju={class:"flex flex-wrap items-center gap-2 mt-1"},Hu={key:0,class:"text-sm text-surface-600 dark:text-surface-400"},qu={class:"ml-3 shrink-0"};function Gu(d,t,r,u,C,k){const v=$("tooltip");return o(),m("div",Z0,[a("div",$0,[a("div",uu,[a("div",null,[a("h2",eu,f(r.item.sku),1),a("div",tu,[s(u.VTag,{value:r.item.brand?.name||"Бренд не указан",severity:r.item.brand?.isOem?"info":"secondary"},null,8,["value","severity"]),r.item.brand?.isOem?(o(),b(u.VTag,{key:0,value:"OEM",severity:"info",size:"small"})):_("",!0),r.item.isPublic?_("",!0):(o(),b(u.VTag,{key:1,value:"Приватная",severity:"warning",size:"small"})),s(u.VTag,{value:"#"+r.item.id,severity:"secondary",size:"small"},null,8,["value"]),r.item.source?(o(),b(u.VTag,{key:2,value:"Источник: "+r.item.source,severity:"secondary",size:"small"},null,8,["value"])):_("",!0),s(u.VTag,{value:"Атрибутов: "+(r.item.attributes?.length||0),severity:"secondary",size:"small"},null,8,["value"]),s(u.VTag,{value:"Применимостей: "+(r.item.applicabilities?.length||0),severity:(r.item.applicabilities?.length||0)>0?"success":"secondary",size:"small"},null,8,["value","severity"])])])]),a("div",au,[s(u.VButton,{onClick:t[0]||(t[0]=e=>d.$emit("edit")),severity:"secondary",outlined:"",size:"small",label:"Редактировать"},{icon:l(()=>[s(u.Icon,{name:"pi pi-pencil",class:"w-5 h-5"})]),_:1}),s(u.VButton,{onClick:t[1]||(t[1]=e=>d.$emit("match")),severity:"secondary",outlined:"",size:"small",label:"Подобрать"},{icon:l(()=>[s(u.Icon,{name:"pi pi-link",class:"w-5 h-5"})]),_:1}),s(u.VButton,{onClick:t[2]||(t[2]=e=>d.$emit("close")),severity:"secondary",text:"",size:"small"},{icon:l(()=>[s(u.Icon,{name:"pi pi-times",class:"w-5 h-5"})]),_:1})])]),a("div",su,[a("div",ru,[s(u.VCard,null,{header:l(()=>t[3]||(t[3]=[a("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[a("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Описание ")],-1)])),content:l(()=>[a("div",nu,[r.item.description?(o(),m("p",ou,f(r.item.description),1)):(o(),m("span",lu," Описание не указано "))])]),_:1}),s(u.VCard,null,{header:l(()=>t[4]||(t[4]=[a("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[a("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Метаданные ")],-1)])),content:l(()=>[a("div",iu,[a("div",du,[t[5]||(t[5]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Источник:",-1)),a("span",cu,f(r.item.source||"Не указан"),1)]),a("div",mu,[t[6]||(t[6]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Видимость:",-1)),s(u.VTag,{value:r.item.isPublic?"Публичная":"Приватная",severity:r.item.isPublic?"success":"warning",size:"small"},null,8,["value","severity"])]),a("div",fu,[t[7]||(t[7]=a("span",{class:"text-surface-600 dark:text-surface-400"},"ID:",-1)),a("span",vu," #"+f(r.item.id),1)])])]),_:1})]),a("div",gu,[r.item.brand?(o(),b(u.VCard,{key:0},{header:l(()=>t[8]||(t[8]=[a("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[a("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Информация о бренде ")],-1)])),content:l(()=>[a("div",yu,[a("div",bu,[t[9]||(t[9]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Название:",-1)),a("span",Cu,f(r.item.brand.name),1)]),a("div",Eu,[t[10]||(t[10]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Тип:",-1)),s(u.VTag,{value:r.item.brand.isOem?"OEM производитель":"Aftermarket",severity:r.item.brand.isOem?"info":"secondary",size:"small"},null,8,["value","severity"])]),r.item.brand.country?(o(),m("div",pu,[t[11]||(t[11]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Страна:",-1)),a("span",hu,f(r.item.brand.country),1)])):_("",!0)])]),_:1})):_("",!0),s(u.VCard,null,{header:l(()=>t[12]||(t[12]=[a("div",{class:"p-4 border-b border-surface-200 dark:border-surface-700"},[a("h3",{class:"font-semibold text-surface-900 dark:text-surface-0"}," Статистика ")],-1)])),content:l(()=>[a("div",Du,[a("div",_u,[t[13]||(t[13]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Атрибутов:",-1)),s(u.VTag,{value:r.item.attributes?.length||0,severity:"secondary",size:"small"},null,8,["value"])]),a("div",ku,[t[14]||(t[14]=a("span",{class:"text-surface-600 dark:text-surface-400"},"Групп применимости:",-1)),s(u.VTag,{value:r.item.applicabilities?.length||0,severity:(r.item.applicabilities?.length||0)>0?"success":"secondary",size:"small"},null,8,["value","severity"])])])]),_:1})])]),r.item.attributes?.length>0?(o(),b(u.VCard,{key:0,class:"mb-6"},{header:l(()=>[a("div",xu,[a("h3",Fu," Атрибуты ("+f(r.item.attributes.length)+") ",1)])]),content:l(()=>[a("div",Bu,[a("div",wu,[(o(!0),m(Q,null,W(r.item.attributes,e=>(o(),m("div",{key:e.id,class:"p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[a("div",Iu,[K(f(e.template?.title||"Неизвестный атрибут")+" ",1),e.template?.isRequired?(o(),m("span",Au,"*")):_("",!0)]),a("div",Vu,f(e.value)+" "+f(e.template?.unit?u.getUnitLabel(e.template.unit):""),1),a("div",Tu,[e.template?.group?.name?(o(),b(u.VTag,{key:0,value:e.template.group.name,severity:"secondary",size:"small"},null,8,["value"])):_("",!0),a("span",Su,f(u.getDataTypeLabel(e.template?.dataType)),1)])]))),128))])])]),_:1})):_("",!0),r.item.applicabilities?.length>0?(o(),b(u.VCard,{key:1},{header:l(()=>[a("div",Mu,[a("h3",Pu," Применимость ("+f(r.item.applicabilities.length)+") ",1)])]),content:l(()=>[a("div",Lu,[a("div",Ru,[(o(!0),m(Q,null,W(r.item.applicabilities,e=>(o(),m("div",{key:e.id,class:"flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"},[a("div",zu,[a("div",Ou,[e.part?.id?(o(),m("a",{key:0,class:"hover:text-primary-600 dark:hover:text-primary-400 underline-offset-2 hover:underline",href:`/admin/parts/${e.part.id}`,target:"_blank",rel:"noopener"},f(e.part?.name||`Группа #${e.part?.id}`),9,Uu)):(o(),m("span",Nu,f(e.part?.name||`Группа #${e.part?.id}`),1))]),a("div",ju,[s(u.VTag,{value:u.getAccuracyLabel(e.accuracy),severity:u.getAccuracySeverity(e.accuracy),size:"small"},null,8,["value","severity"]),e.notes?(o(),m("span",Hu,f(e.notes),1)):_("",!0)])]),a("div",qu,[X((o(),b(u.VButton,{severity:"danger",text:"",size:"small",onClick:c=>d.$emit("unlink",e)},{icon:l(()=>[s(u.Icon,{name:"pi pi-trash",class:"w-4 h-4"})]),_:2},1032,["onClick"])),[[v,"Отвязать"]])])]))),128))])])]),_:1})):_("",!0)])}const Qu=H(Y0,[["render",Gu]]),Wu=q({__name:"MatchingResults",props:{item:{},results:{},loading:{type:Boolean}},emits:["refresh","link"],setup(d,{expose:t,emit:r}){t();const u=d,C=r,{getAccuracyLabel:k,getAccuracySeverity:v}=a0(),e=y(!1),c=y(null),T=y(!1),x=J4({accuracy:"EXACT_MATCH",notes:""}),B=[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требуется модификация",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],R=p=>{c.value=p,x.accuracy=p.accuracySuggestion,x.notes="";const h=(p.details||[]).find(g=>String(g.kind).includes("NEAR")||String(g.kind).includes("LEGACY"));h?.notes&&(x.notes=h.notes),(p.details||[]).find(g=>g.kind==="NUMBER_WITHIN_TOLERANCE")&&!x.notes&&(x.notes="Совпадение по допуску"),e.value=!0},z=()=>{e.value=!1,c.value=null,x.accuracy="EXACT_MATCH",x.notes=""},I=()=>{c.value&&(T.value=!0,C("link",{partId:c.value.part.id,accuracy:x.accuracy,notes:x.notes||void 0}),T.value=!1,z())},{matching:A}=t4(),w=d4(),U={props:u,emit:C,getAccuracyLabel:k,getAccuracySeverity:v,showConfirmDialog:e,selectedCandidate:c,linking:T,confirmForm:x,accuracyOptions:B,openConfirmDialog:R,closeConfirmDialog:z,confirmLink:I,matching:A,toast:w,queueProposal:async p=>{try{const h={partId:p.part.id,accuracy:p.accuracySuggestion,notes:void 0},M=(p.details||[]).find(D=>String(D.kind).includes("NEAR")||String(D.kind).includes("LEGACY"));M?.notes&&(h.notes=M.notes),(p.details||[]).find(D=>D.kind==="NUMBER_WITHIN_TOLERANCE")&&!h.notes&&(h.notes="Совпадение по допуску"),await A.proposeLink({catalogItemId:u.item.id,partId:h.partId,accuracySuggestion:h.accuracy,notesSuggestion:h.notes,details:p.details})}catch{w.error("Ошибка","Не удалось добавить предложение")}},VButton:J,VCard:Z,VTag:s4,VDialog:a4,VSelect:e0,VTextarea:C4,MatchingDetailsGrid:t0,MatchingEmptyState:W4,get RefreshCcwIcon(){return y4},get LinkIcon(){return g4},get SendIcon(){return o0},MatchingLoadingState:Q4};return Object.defineProperty(U,"__isScriptSetup",{enumerable:!1,value:!0}),U}}),Xu={class:"p-4 space-y-4"},Ku={class:"flex items-center justify-between"},Ju={class:"text-lg font-mono font-semibold"},Yu={class:"flex gap-2"},Zu={key:2,class:"space-y-3"},$u={class:"p-4 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"},ue={class:"md:col-span-1"},ee={class:"font-semibold text-surface-900 dark:text-surface-0"},te={class:"mt-2"},ae={class:"mt-3"},se={class:"md:col-span-2"},re={key:0,class:"space-y-4"},ne={class:"grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface-50 dark:bg-surface-900 rounded"},oe={class:"font-semibold"},le={class:"text-sm"},ie={class:"font-semibold"},de={class:"space-y-3"},ce={class:"flex justify-between"};function me(d,t,r,u,C,k){const v=$("tooltip");return o(),m("div",Xu,[a("div",Ku,[a("div",null,[t[5]||(t[5]=a("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),a("div",Ju,[K(f(r.item.sku)+" ",1),t[4]||(t[4]=a("span",{class:"text-surface-500"},"—",-1)),K(" "+f(r.item.brand?.name),1)])]),a("div",Yu,[s(u.VButton,{severity:"secondary",outlined:"",size:"small",loading:r.loading,onClick:t[0]||(t[0]=e=>d.$emit("refresh"))},{default:l(()=>[s(u.RefreshCcwIcon)]),_:1},8,["loading"])])]),r.loading?(o(),b(u.MatchingLoadingState,{key:0})):!r.results||r.results.length===0?(o(),b(u.MatchingEmptyState,{key:1})):(o(),m("div",Zu,[(o(!0),m(Q,null,W(r.results,e=>(o(),b(u.VCard,{key:e.part.id,class:"border"},{content:l(()=>[a("div",$u,[a("div",ue,[a("div",ee,f(e.part.name||"Группа #"+e.part.id),1),a("div",te,[s(u.VTag,{value:u.getAccuracyLabel(e.accuracySuggestion),severity:u.getAccuracySeverity(e.accuracySuggestion)},null,8,["value","severity"])]),a("div",ae,[X((o(),b(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:c=>u.openConfirmDialog(e)},{default:l(()=>[s(u.LinkIcon)]),_:2},1032,["onClick"])),[[v,"Создать связь"]]),X((o(),b(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:c=>u.queueProposal(e),class:"ml-2"},{default:l(()=>[s(u.SendIcon)]),_:2},1032,["onClick"])),[[v,"В очередь предложений"]])])]),a("div",se,[t[6]||(t[6]=a("div",{class:"text-sm text-surface-500 mb-2"},"Детали сопоставления",-1)),s(u.MatchingDetailsGrid,{details:e.details,controls:!1},null,8,["details"])])])]),_:2},1024))),128))])),s(u.VDialog,{visible:u.showConfirmDialog,"onUpdate:visible":t[3]||(t[3]=e=>u.showConfirmDialog=e),modal:"",header:"Подтверждение связи",class:"w-full max-w-3xl"},{footer:l(()=>[a("div",ce,[s(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.closeConfirmDialog}),s(u.VButton,{label:"Создать связь",severity:"success",onClick:u.confirmLink,loading:u.linking},null,8,["loading"])])]),default:l(()=>[u.selectedCandidate?(o(),m("div",re,[a("div",ne,[a("div",null,[t[7]||(t[7]=a("div",{class:"text-sm text-surface-500"},"Каталожная позиция",-1)),a("div",oe,f(r.item.sku),1),a("div",le,f(r.item.brand?.name),1)]),a("div",null,[t[8]||(t[8]=a("div",{class:"text-sm text-surface-500"},"Группа взаимозаменяемости",-1)),a("div",ie,f(u.selectedCandidate.part.name||`Группа #${u.selectedCandidate.part.id}`),1)])]),a("div",null,[t[9]||(t[9]=a("h3",{class:"text-lg font-semibold mb-3"},"Детали сопоставления",-1)),s(u.MatchingDetailsGrid,{details:u.selectedCandidate.details,controls:!1},null,8,["details"])]),a("div",de,[a("div",null,[t[10]||(t[10]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Точность совпадения ",-1)),s(u.VSelect,{modelValue:u.confirmForm.accuracy,"onUpdate:modelValue":t[1]||(t[1]=e=>u.confirmForm.accuracy=e),options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue"])]),a("div",null,[t[11]||(t[11]=a("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),s(u.VTextarea,{modelValue:u.confirmForm.notes,"onUpdate:modelValue":t[2]||(t[2]=e=>u.confirmForm.notes=e),rows:"3",placeholder:"Дополнительная информация о совместимости...",class:"w-full"},null,8,["modelValue"]),t[12]||(t[12]=a("small",{class:"text-surface-500"}," Укажите особенности применения, ограничения или условия замены ",-1))])])])):_("",!0)]),_:1},8,["visible"])])}const fe=H(Wu,[["render",me]]),ve=q({__name:"CatalogItemsManager",setup(d,{expose:t}){t();const{catalogItems:r,brands:u,loading:C,error:k,matching:v,partApplicability:e,client:c}=t4(),T=c.crud.catalogItemAttribute,x=z4(),B=d4(),R=y([]),z=y(0),I=y(1),A=y(20),w=y(""),S=y(null),U=y([]),p=R4({q:"",brandId:void 0,page:1,rows:20,sortField:"",sortOrder:1},{prefix:"ci_",numberParams:["brandId","page","rows"],debounceMs:300}),h=y(!1),M=y(!1),g=y(null),D=y(null),F=y(null),O=y(!1),E=y(null),G=y(!1),P=y(""),N=y(1),V=async()=>{try{const n={skip:(I.value-1)*A.value,take:A.value,include:{brand:!0,attributes:{include:{template:!0}},applicabilities:{include:{part:!0}}}};if(w.value.trim()&&(n.where={OR:[{sku:{contains:w.value.trim(),mode:"insensitive"}},{description:{contains:w.value.trim(),mode:"insensitive"}},{brand:{name:{contains:w.value.trim(),mode:"insensitive"}}}]}),S.value){const j={brandId:S.value.id};n.where?n.where={AND:[n.where,j]}:n.where=j}P.value?n.orderBy={[P.value]:N.value===1?"asc":"desc"}:n.orderBy={id:"desc"};const i=await r.findMany(n);Array.isArray(i)?R.value=i:R.value=[];const L=await r.findMany({where:n.where,select:{id:!0}});z.value=Array.isArray(L)?L.length:0}catch(n){console.error("Ошибка загрузки каталожных позиций:",n),B.error("Ошибка","Не удалось загрузить каталожные позиции")}},p4=async n=>{try{const i=n.query.toLowerCase(),L=await u.findMany({where:{name:{contains:i,mode:"insensitive"}},take:10});U.value=Array.isArray(L)?L:[]}catch(i){console.error("Ошибка поиска брендов:",i)}};let Y;const h4=()=>{clearTimeout(Y),Y=setTimeout(()=>{I.value=1,p.updateFilters({q:w.value||void 0,page:1}),V()},300)},D4=()=>{I.value=1,p.updateFilters({brandId:S.value?.id,page:1}),V()},_4=n=>{I.value=Math.floor(n.first/n.rows)+1,A.value=n.rows,p.updateFilters({page:I.value,rows:A.value}),V()},k4=n=>{P.value=n.sortField,N.value=n.sortOrder,p.updateFilters({sortField:P.value,sortOrder:N.value}),V()},x4=()=>{V()},F4=n=>{g.value={...n},h.value=!0},B4=n=>{x.confirmDelete(`позицию "${n.sku}" (${n.brand?.name})`,async()=>{try{await r.delete({where:{id:n.id}}),B.success("Успешно","Позиция удалена"),V()}catch(i){console.error("Ошибка удаления:",i),B.error("Ошибка","Не удалось удалить позицию")}})},w4=n=>{D.value=n,M.value=!0},I4=n=>{F.value=n,O.value=!0,u4()},u4=async()=>{if(F.value){G.value=!0,E.value=null;try{const n=await v.findMatchingParts({catalogItemId:F.value.id});if(E.value=n?n.candidates||[]:[],E.value&&E.value.length===1&&E.value[0].accuracySuggestion==="EXACT_MATCH"){const i=E.value[0];await n4({partId:i.part.id,accuracy:"EXACT_MATCH"})}}catch(n){console.error("Ошибка подбора:",n),E.value=[]}finally{G.value=!1}}},n4=async n=>{if(F.value)try{await e.upsert({where:{partId_catalogItemId:{partId:n.partId,catalogItemId:F.value.id}},create:{partId:n.partId,catalogItemId:F.value.id,accuracy:n.accuracy,notes:n.notes},update:{accuracy:n.accuracy,notes:n.notes}}),O.value=!1,V()}catch(i){console.error("Ошибка привязки:",i),B.error("Ошибка","Не удалось привязать позицию")}},A4=async n=>{try{const{attributes:i,...L}=n;let j=null;if(g.value?(j=await r.update({where:{id:g.value.id},data:L}),i?.deleteMany!==void 0&&await T.deleteMany.mutate({where:{catalogItemId:g.value.id}})):j=await r.create({data:L}),i?.create&&i.create.length>0)for(const l4 of i.create)try{await T.create.mutate({data:{value:String(l4.value),catalogItem:{connect:{id:j.id}},template:{connect:{id:l4.templateId}}}})}catch(P4){console.error("Ошибка создания атрибута:",P4)}h.value=!1,g.value=null,V()}catch(i){console.error("❌ Ошибка сохранения:",i),B.error("Ошибка","Не удалось сохранить позицию")}},V4=()=>{h.value=!1,g.value=null},T4=()=>{g.value={...D.value},M.value=!1,h.value=!0},S4=()=>{F.value=D.value,O.value=!0,u4()},M4=async n=>{n?.id&&x.show({header:"Удалить связь?",message:`Отвязать позицию от группы #${n.part?.id||""}?`,icon:"pi pi-trash",acceptLabel:"Отвязать",rejectLabel:"Отмена",acceptClass:"bg-red-500 hover:bg-red-600",accept:async()=>{try{await e.delete({where:{id:n.id}}),D.value&&(D.value={...D.value,applicabilities:(D.value.applicabilities||[]).filter(i=>i.id!==n.id)}),V()}catch(i){console.error("Ошибка отвязки:",i),B.error("Ошибка","Не удалось отвязать позицию")}}})};v4(()=>{const n=p.filters.value;w.value=n.q||"",S.value=n.brandId?{id:n.brandId}:null,I.value=n.page||1,A.value=n.rows||20,P.value=n.sortField||"",N.value=n.sortOrder===-1?-1:1,V()}),f4(p.filters,n=>{const i=n,L=i.brandId?{id:i.brandId}:null;w.value!==(i.q||"")&&(w.value=i.q||""),(S.value?.id||null)!==(i.brandId??null)&&(S.value=L),I.value!==(i.page||1)&&(I.value=i.page||1),A.value!==(i.rows||20)&&(A.value=i.rows||20),P.value!==(i.sortField||"")&&(P.value=i.sortField||""),N.value!==(i.sortOrder===-1?-1:1)&&(N.value=i.sortOrder===-1?-1:1),V()});const o4={catalogItemsApi:r,brands:u,loading:C,error:k,matching:v,partApplicability:e,client:c,catalogItemAttributesApi:T,confirm:x,toast:B,catalogItems:R,totalRecords:z,currentPage:I,pageSize:A,searchQuery:w,selectedBrand:S,brandSuggestions:U,urlSync:p,showCreateDialog:h,showDetailsDialog:M,editingItem:g,selectedItem:D,matchingItem:F,showMatchingDialog:O,matchingResults:E,matchingLoading:G,sortField:P,sortOrder:N,loadCatalogItems:V,searchBrands:p4,get searchTimeout(){return Y},set searchTimeout(n){Y=n},debouncedSearch:h4,onBrandFilterChange:D4,onPageChange:_4,onSort:k4,refreshData:x4,onEdit:F4,onDelete:B4,onViewDetails:w4,onMatch:I4,runMatching:u4,linkToPart:n4,onSave:A4,onCancel:V4,onEditFromDetails:T4,onMatchFromCard:S4,onUnlink:M4,VCard:Z,VInputText:c4,VAutoComplete:m4,VButton:J,VDialog:a4,VConfirmDialog:O4,Toast:U4,CatalogItemsTable:T0,CatalogItemForm:J0,CatalogItemCard:Qu,MatchingResults:fe,get SearchIcon(){return r0},get PlusIcon(){return s0},get RefreshCcwIcon(){return y4}};return Object.defineProperty(o4,"__isScriptSetup",{enumerable:!1,value:!0}),o4}}),ge={class:"catalog-items-manager"},ye={class:"p-6"},be={class:"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between"},Ce={class:"flex flex-col sm:flex-row gap-4 flex-1"},Ee={class:"flex-1"},pe={class:"flex gap-2"};function he(d,t,r,u,C,k){return o(),m("div",ge,[s(u.VCard,{class:"mb-6"},{content:l(()=>[a("div",ye,[a("div",be,[a("div",Ce,[a("div",Ee,[s(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":t[0]||(t[0]=v=>u.searchQuery=v),placeholder:"Поиск по артикулу, описанию или бренду...",class:"w-full",onInput:u.debouncedSearch},{prefix:l(()=>[s(u.SearchIcon)]),_:1},8,["modelValue"])]),s(u.VAutoComplete,{modelValue:u.selectedBrand,"onUpdate:modelValue":t[1]||(t[1]=v=>u.selectedBrand=v),suggestions:u.brandSuggestions,onComplete:u.searchBrands,"option-label":"name",placeholder:"Фильтр по бренду",class:"w-full sm:w-64",onChange:u.onBrandFilterChange,dropdown:""},null,8,["modelValue","suggestions"])]),a("div",pe,[s(u.VButton,{onClick:t[2]||(t[2]=v=>u.showCreateDialog=!0),severity:"secondary",outlined:"",label:"Добавить позицию"},{default:l(()=>[s(u.PlusIcon)]),_:1}),s(u.VButton,{onClick:u.refreshData,severity:"secondary",outlined:"",loading:u.loading},{default:l(()=>[s(u.RefreshCcwIcon)]),_:1},8,["loading"])])])])]),_:1}),s(u.CatalogItemsTable,{items:u.catalogItems,loading:u.loading,"total-records":u.totalRecords,rows:u.pageSize,first:(u.currentPage-1)*u.pageSize,onPage:u.onPageChange,onSort:u.onSort,onEdit:u.onEdit,onDelete:u.onDelete,onViewDetails:u.onViewDetails,onMatch:u.onMatch},null,8,["items","loading","total-records","rows","first"]),s(u.VDialog,{visible:u.showCreateDialog,"onUpdate:visible":t[3]||(t[3]=v=>u.showCreateDialog=v),modal:"",header:u.editingItem?"Редактировать позицию":"Создать позицию",class:"w-full max-w-2xl"},{default:l(()=>[s(u.CatalogItemForm,{item:u.editingItem,onSave:u.onSave,onCancel:u.onCancel},null,8,["item"])]),_:1},8,["visible","header"]),s(u.VDialog,{visible:u.showDetailsDialog,"onUpdate:visible":t[5]||(t[5]=v=>u.showDetailsDialog=v),modal:"",header:"Детали позиции",class:"w-full max-w-3xl"},{default:l(()=>[u.selectedItem?(o(),b(u.CatalogItemCard,{key:0,item:u.selectedItem,onEdit:u.onEditFromDetails,onMatch:u.onMatchFromCard,onClose:t[4]||(t[4]=v=>u.showDetailsDialog=!1),onUnlink:u.onUnlink},null,8,["item"])):_("",!0)]),_:1},8,["visible"]),s(u.VDialog,{visible:u.showMatchingDialog,"onUpdate:visible":t[6]||(t[6]=v=>u.showMatchingDialog=v),modal:"",header:"Подбор взаимозаменяемых групп",class:"w-full max-w-4xl"},{default:l(()=>[u.matchingItem?(o(),b(u.MatchingResults,{key:0,item:u.matchingItem,results:u.matchingResults,loading:u.matchingLoading,onRefresh:u.runMatching,onLink:u.linkToPart},null,8,["item","results","loading"])):_("",!0)]),_:1},8,["visible"]),s(u.VConfirmDialog),s(u.Toast)])}const De=H(ve,[["render",he]]),_e=q({__name:"CatalogItemsManagerBoundary",setup(d,{expose:t}){t();const r=y(0),C={key:r,onRetry:()=>{r.value++},ErrorBoundary:L4,CatalogItemsManager:De};return Object.defineProperty(C,"__isScriptSetup",{enumerable:!1,value:!0}),C}});function ke(d,t,r,u,C,k){return o(),b(u.ErrorBoundary,{variant:"detailed",title:"Ошибка каталожных позиций",message:"Не удалось загрузить или отрисовать таблицу. Попробуйте повторить.",onRetry:u.onRetry},{default:l(()=>[(o(),b(u.CatalogItemsManager,{key:u.key}))]),_:1})}const Tt=H(_e,[["render",ke]]);export{Tt as default};

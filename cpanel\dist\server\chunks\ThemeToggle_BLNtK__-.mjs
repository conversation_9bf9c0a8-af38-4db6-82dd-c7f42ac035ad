import { ref, computed, onMounted, watch, defineComponent, useSSRContext, onUnmounted, mergeProps, createVNode, resolveDynamicComponent } from 'vue';
import './SecondaryButton_B0hmlm1n.mjs';
import { Monitor, Sun, Moon, Check } from 'lucide-vue-next';
import { ssrRenderAttrs, ssrRenderClass, ssrRenderAttr, ssrRenderVNode, ssrInterpolate, ssrRenderList, ssrRenderComponent } from 'vue/server-renderer';
/* empty css                                  */
import { _ as _export_sfc } from './ClientRouter_avhRMbqw.mjs';

const STORAGE_KEY = "parttec-theme";
const currentTheme = ref("system");
const systemPrefersDark = ref(false);
const activeTheme = computed(() => {
  if (currentTheme.value === "system") {
    return systemPrefersDark.value ? "dark" : "light";
  }
  return currentTheme.value;
});
const applyTheme = (theme) => {
  if (typeof document !== "undefined") {
    const root = document.documentElement;
    if (theme === "dark") {
      root.setAttribute("data-theme", "dark");
      root.classList.add("dark");
    } else {
      root.removeAttribute("data-theme");
      root.classList.remove("dark");
    }
  }
};
const updateSystemTheme = () => {
  if (typeof window !== "undefined" && window.matchMedia) {
    systemPrefersDark.value = window.matchMedia("(prefers-color-scheme: dark)").matches;
  }
};
const loadTheme = () => {
  if (typeof window !== "undefined") {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored && ["light", "dark", "system"].includes(stored)) {
      return stored;
    }
  }
  return "system";
};
const saveTheme = (theme) => {
  if (typeof window !== "undefined") {
    localStorage.setItem(STORAGE_KEY, theme);
  }
};
const useTheme = () => {
  onMounted(() => {
    currentTheme.value = loadTheme();
    updateSystemTheme();
    if (typeof window !== "undefined" && window.matchMedia) {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = (e) => {
        systemPrefersDark.value = e.matches;
      };
      mediaQuery.addEventListener("change", handleChange);
      return () => {
        mediaQuery.removeEventListener("change", handleChange);
      };
    }
  });
  watch(activeTheme, (newTheme) => {
    applyTheme(newTheme);
  }, { immediate: true });
  watch(currentTheme, (newTheme) => {
    saveTheme(newTheme);
  });
  const setTheme = (theme) => {
    currentTheme.value = theme;
  };
  const toggleTheme = () => {
    if (currentTheme.value === "system") {
      const newTheme = systemPrefersDark.value ? "light" : "dark";
      setTheme(newTheme);
    } else if (currentTheme.value === "light") {
      setTheme("dark");
    } else {
      setTheme("light");
    }
  };
  const resetToSystem = () => {
    setTheme("system");
  };
  const isDark = computed(() => activeTheme.value === "dark");
  const isLight = computed(() => activeTheme.value === "light");
  const isSystem = computed(() => currentTheme.value === "system");
  const themeIcon = computed(() => {
    switch (currentTheme.value) {
      case "light":
        return "Sun";
      case "dark":
        return "Moon";
      case "system":
        return "Monitor";
      default:
        return "Monitor";
    }
  });
  const themeName = computed(() => {
    switch (currentTheme.value) {
      case "light":
        return "Светлая";
      case "dark":
        return "Темная";
      case "system":
        return "Системная";
      default:
        return "Системная";
    }
  });
  return {
    // Состояние (только для чтения)
    currentTheme: computed(() => currentTheme.value),
    activeTheme,
    systemPrefersDark: computed(() => systemPrefersDark.value),
    // Вычисляемые значения
    isDark,
    isLight,
    isSystem,
    themeIcon,
    themeName,
    // Методы
    setTheme,
    toggleTheme,
    resetToSystem
  };
};

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ThemeToggle",
  props: {
    mode: { default: "toggle" },
    showLabel: { type: Boolean, default: false },
    buttonClass: { default: "" }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const {
      currentTheme,
      activeTheme,
      isDark,
      isLight,
      isSystem,
      themeIcon,
      themeName,
      systemPrefersDark,
      setTheme,
      toggleTheme
    } = useTheme();
    const showMenu = ref(false);
    const themeIconComponent = computed(() => {
      switch (currentTheme.value) {
        case "light":
          return Moon;
        // Показываем луну, так как следующая тема будет темная
        case "dark":
          return Sun;
        // Показываем солнце, так как следующая тема будет светлая
        case "system":
          return systemPrefersDark.value ? Sun : Moon;
        // Показываем противоположную системной
        default:
          return Monitor;
      }
    });
    const themes = [
      { value: "light", label: "\u0421\u0432\u0435\u0442\u043B\u0430\u044F", icon: Sun },
      { value: "dark", label: "\u0422\u0435\u043C\u043D\u0430\u044F", icon: Moon },
      { value: "system", label: "\u0421\u0438\u0441\u0442\u0435\u043C\u043D\u0430\u044F", icon: Monitor }
    ];
    const toggleMenu = () => {
      showMenu.value = !showMenu.value;
    };
    const selectTheme = (theme) => {
      setTheme(theme);
      showMenu.value = false;
    };
    const handleClickOutside = (event) => {
      const target = event.target;
      if (!target.closest(".theme-toggle")) {
        showMenu.value = false;
      }
    };
    onMounted(() => {
      document.addEventListener("click", handleClickOutside);
    });
    onUnmounted(() => {
      document.removeEventListener("click", handleClickOutside);
    });
    const __returned__ = { props, currentTheme, activeTheme, isDark, isLight, isSystem, themeIcon, themeName, systemPrefersDark, setTheme, toggleTheme, showMenu, themeIconComponent, themes, toggleMenu, selectTheme, handleClickOutside, get Check() {
      return Check;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "theme-toggle" }, _attrs))} data-v-94d58d16>`);
  if ($props.mode === "toggle") {
    _push(`<button class="${ssrRenderClass([$props.buttonClass, "p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"])}"${ssrRenderAttr("title", `\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0442\u0435\u043C\u0443 (\u0442\u0435\u043A\u0443\u0449\u0430\u044F: ${$setup.themeName})`)} data-v-94d58d16>`);
    ssrRenderVNode(_push, createVNode(resolveDynamicComponent($setup.themeIconComponent), { size: 20 }, null), _parent);
    if ($props.showLabel) {
      _push(`<span class="ml-2" data-v-94d58d16>${ssrInterpolate($setup.themeName)}</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</button>`);
  } else if ($props.mode === "menu") {
    _push(`<div class="relative" data-v-94d58d16><button class="${ssrRenderClass([$props.buttonClass, "p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors flex items-center"])}"${ssrRenderAttr("title", `\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u0442\u0435\u043C\u0443 (\u0442\u0435\u043A\u0443\u0449\u0430\u044F: ${$setup.themeName})`)} data-v-94d58d16>`);
    ssrRenderVNode(_push, createVNode(resolveDynamicComponent($setup.themeIconComponent), { size: 20 }, null), _parent);
    if ($props.showLabel) {
      _push(`<span class="ml-2" data-v-94d58d16>${ssrInterpolate($setup.themeName)}</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</button>`);
    if ($setup.showMenu) {
      _push(`<div class="absolute right-0 mt-2 w-48 bg-[--color-card] rounded-[--radius-md] [box-shadow:var(--shadow-lg)] border border-[--color-border] z-50" data-v-94d58d16><div class="py-1" data-v-94d58d16><!--[-->`);
      ssrRenderList($setup.themes, (theme) => {
        _push(`<button class="${ssrRenderClass([{
          "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.currentTheme === theme.value
        }, "flex items-center w-full px-4 py-2 text-sm text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"])}" data-v-94d58d16>`);
        ssrRenderVNode(_push, createVNode(resolveDynamicComponent(theme.icon), {
          size: 16,
          class: "mr-3"
        }, null), _parent);
        _push(`<span data-v-94d58d16>${ssrInterpolate(theme.label)}</span>`);
        if ($setup.currentTheme === theme.value) {
          _push(ssrRenderComponent($setup["Check"], {
            size: 16,
            class: "ml-auto text-[--p-primary-color]"
          }, null, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</button>`);
      });
      _push(`<!--]--></div></div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else if ($props.mode === "buttons") {
    _push(`<div class="flex rounded-[--radius-md] border border-[--color-border] overflow-hidden" data-v-94d58d16><!--[-->`);
    ssrRenderList($setup.themes, (theme) => {
      _push(`<button class="${ssrRenderClass([{
        "bg-[--color-primary] text-[--color-primary-foreground]": $setup.currentTheme === theme.value,
        "bg-[--color-card] text-[--color-foreground] hover:bg-[--p-content-hover-background]": $setup.currentTheme !== theme.value
      }, "flex items-center px-3 py-2 text-sm transition-colors border-r border-[--color-border] last:border-r-0"])}"${ssrRenderAttr("title", `\u0412\u044B\u0431\u0440\u0430\u0442\u044C ${theme.label.toLowerCase()} \u0442\u0435\u043C\u0443`)} data-v-94d58d16>`);
      ssrRenderVNode(_push, createVNode(resolveDynamicComponent(theme.icon), { size: 16 }, null), _parent);
      if ($props.showLabel) {
        _push(`<span class="ml-2" data-v-94d58d16>${ssrInterpolate(theme.label)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</button>`);
    });
    _push(`<!--]--></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/ui/ThemeToggle.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ThemeToggle = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender], ["__scopeId", "data-v-94d58d16"]]);

export { ThemeToggle as T, useTheme as u };

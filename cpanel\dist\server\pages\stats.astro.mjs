import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_AEsVEWgS.mjs';
import { defineComponent, useSSRContext, computed, mergeProps, withCtx, createVNode, toDisplayString, createTextVNode, ref, createBlock, openBlock } from 'vue';
import { E as ErrorBoundary } from '../chunks/ErrorBoundary_BKC82unE.mjs';
import { useQuery } from '@tanstack/vue-query';
import { f as fetchers, q as qk, u as useQueryToastErrors } from '../chunks/fetchers_CTjsTB2x.mjs';
import { C as Card } from '../chunks/Card_aE2_b9LT.mjs';
import { S as SecondaryButton } from '../chunks/SecondaryButton_B0hmlm1n.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
/* empty css                                 */
import { _ as _export_sfc } from '../chunks/ClientRouter_avhRMbqw.mjs';
import { t as trpc } from '../chunks/trpc_DApR3DD7.mjs';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "StatsPageComponent",
  props: {
    initialStats: { default: () => ({ parts: 0, brands: 0, categories: 0 }) }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const { data: partsCountData, error: partsError, refetch: refetchParts, isFetching: isPartsLoading } = useQuery({
      queryKey: qk.stats.parts(),
      queryFn: () => fetchers.stats.parts(),
      initialData: props.initialStats?.parts,
      staleTime: 6e4
      // 1 минута
    });
    useQueryToastErrors(partsError);
    const { data: brandsCountData, error: brandsError, refetch: refetchBrands, isFetching: isBrandsLoading } = useQuery({
      queryKey: qk.stats.brands(),
      queryFn: () => fetchers.stats.brands(),
      initialData: props.initialStats?.brands,
      staleTime: 6e4
    });
    useQueryToastErrors(brandsError);
    const { data: categoriesCountData, error: categoriesError, refetch: refetchCategories, isFetching: isCategoriesLoading } = useQuery({
      queryKey: qk.stats.categories(),
      queryFn: () => fetchers.stats.categories(),
      initialData: props.initialStats?.categories,
      staleTime: 6e4
    });
    useQueryToastErrors(categoriesError);
    const partsCount = computed(() => partsCountData.value ?? 0);
    const brandsCount = computed(() => brandsCountData.value ?? 0);
    const categoriesCount = computed(() => categoriesCountData.value ?? 0);
    const isLoading = computed(
      () => isPartsLoading.value || isBrandsLoading.value || isCategoriesLoading.value
    );
    const hasErrors = computed(
      () => partsError.value || brandsError.value || categoriesError.value
    );
    const refreshStats = async () => {
      await Promise.all([
        refetchParts(),
        refetchBrands(),
        refetchCategories()
      ]);
    };
    const __returned__ = { props, partsCountData, partsError, refetchParts, isPartsLoading, brandsCountData, brandsError, refetchBrands, isBrandsLoading, categoriesCountData, categoriesError, refetchCategories, isCategoriesLoading, partsCount, brandsCount, categoriesCount, isLoading, hasErrors, refreshStats, Card, SecondaryButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "stats-page" }, _attrs))} data-v-3e452373><div class="mb-8" data-v-3e452373><h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2" data-v-3e452373> \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 \u0441\u0438\u0441\u0442\u0435\u043C\u044B </h1><p class="text-surface-600 dark:text-surface-400" data-v-3e452373> \u041E\u0431\u0449\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0434\u0430\u043D\u043D\u044B\u0445 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0435 </p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8" data-v-3e452373>`);
  _push(ssrRenderComponent($setup["Card"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="text-center" data-v-3e452373${_scopeId}><div class="text-3xl font-bold text-primary mb-2" data-v-3e452373${_scopeId}>${ssrInterpolate($setup.isLoading ? "..." : $setup.partsCount)}</div><div class="text-surface-600 dark:text-surface-400" data-v-3e452373${_scopeId}> \u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 \u0432 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0435 </div></div>`);
      } else {
        return [
          createVNode("div", { class: "text-center" }, [
            createVNode("div", { class: "text-3xl font-bold text-primary mb-2" }, toDisplayString($setup.isLoading ? "..." : $setup.partsCount), 1),
            createVNode("div", { class: "text-surface-600 dark:text-surface-400" }, " \u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 \u0432 \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0435 ")
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Card"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="text-center" data-v-3e452373${_scopeId}><div class="text-3xl font-bold text-primary mb-2" data-v-3e452373${_scopeId}>${ssrInterpolate($setup.isLoading ? "..." : $setup.brandsCount)}</div><div class="text-surface-600 dark:text-surface-400" data-v-3e452373${_scopeId}> \u0411\u0440\u0435\u043D\u0434\u043E\u0432 </div></div>`);
      } else {
        return [
          createVNode("div", { class: "text-center" }, [
            createVNode("div", { class: "text-3xl font-bold text-primary mb-2" }, toDisplayString($setup.isLoading ? "..." : $setup.brandsCount), 1),
            createVNode("div", { class: "text-surface-600 dark:text-surface-400" }, " \u0411\u0440\u0435\u043D\u0434\u043E\u0432 ")
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Card"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="text-center" data-v-3e452373${_scopeId}><div class="text-3xl font-bold text-primary mb-2" data-v-3e452373${_scopeId}>${ssrInterpolate($setup.isLoading ? "..." : $setup.categoriesCount)}</div><div class="text-surface-600 dark:text-surface-400" data-v-3e452373${_scopeId}> \u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439 </div></div>`);
      } else {
        return [
          createVNode("div", { class: "text-center" }, [
            createVNode("div", { class: "text-3xl font-bold text-primary mb-2" }, toDisplayString($setup.isLoading ? "..." : $setup.categoriesCount), 1),
            createVNode("div", { class: "text-surface-600 dark:text-surface-400" }, " \u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439 ")
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div><div class="flex justify-center" data-v-3e452373>`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    onClick: $setup.refreshStats,
    disabled: $setup.isLoading
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`${ssrInterpolate($setup.isLoading ? "\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u0435..." : "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0441\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0443")}`);
      } else {
        return [
          createTextVNode(toDisplayString($setup.isLoading ? "\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u0435..." : "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0441\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0443"), 1)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.hasErrors) {
    _push(`<div class="mt-6" data-v-3e452373>`);
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="text-center text-red-500" data-v-3e452373${_scopeId}><p class="mb-4" data-v-3e452373${_scopeId}>\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0435 \u043D\u0435\u043A\u043E\u0442\u043E\u0440\u044B\u0445 \u0434\u0430\u043D\u043D\u044B\u0445</p>`);
          _push2(ssrRenderComponent($setup["SecondaryButton"], { onClick: $setup.refreshStats }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C `);
              } else {
                return [
                  createTextVNode(" \u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C ")
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "text-center text-red-500" }, [
              createVNode("p", { class: "mb-4" }, "\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0435 \u043D\u0435\u043A\u043E\u0442\u043E\u0440\u044B\u0445 \u0434\u0430\u043D\u043D\u044B\u0445"),
              createVNode($setup["SecondaryButton"], { onClick: $setup.refreshStats }, {
                default: withCtx(() => [
                  createTextVNode(" \u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C ")
                ]),
                _: 1
              })
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/stats/StatsPageComponent.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const StatsPageComponent = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1], ["__scopeId", "data-v-3e452373"]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "StatsPageBoundary",
  props: {
    initialStats: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const key = ref(0);
    const onRetry = () => {
      key.value++;
    };
    const __returned__ = { key, onRetry, ErrorBoundary, StatsPageComponent };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ErrorBoundary"], mergeProps({
    variant: "detailed",
    onRetry: $setup.onRetry
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["StatsPageComponent"], {
          key: $setup.key,
          "initial-stats": $props.initialStats
        }, null, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock($setup["StatsPageComponent"], {
            key: $setup.key,
            "initial-stats": $props.initialStats
          }, null, 8, ["initial-stats"]))
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/stats/StatsPageBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const StatsPageBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Stats = createComponent(async ($$result, $$props, $$slots) => {
  let initialStats;
  try {
    const [partsCount, brandsCount, categoriesCount] = await Promise.all([
      trpc.crud.part.count.query(),
      trpc.crud.brand.count.query(),
      trpc.crud.partCategory.count.query()
    ]);
    initialStats = {
      parts: partsCount,
      brands: brandsCount,
      categories: categoriesCount
    };
  } catch (error) {
    console.error("Error loading stats initial data:", error);
    initialStats = {
      parts: 0,
      brands: 0,
      categories: 0
    };
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "\u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 \u0441\u0438\u0441\u0442\u0435\u043C\u044B", "data-astro-cid-j3fvw3lo": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8" data-astro-cid-j3fvw3lo> ${renderComponent($$result2, "StatsPageBoundary", StatsPageBoundary, { "client:load": true, "initialStats": initialStats, "client:component-hydration": "load", "client:component-path": "@/components/stats/StatsPageBoundary.vue", "client:component-export": "default", "data-astro-cid-j3fvw3lo": true })} </main> ` })} `;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/stats.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/stats.astro";
const $$url = "/stats";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Stats,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

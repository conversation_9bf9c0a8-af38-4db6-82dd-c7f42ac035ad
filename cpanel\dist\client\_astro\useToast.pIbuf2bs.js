import{u as i}from"./index.PhWaFJhe.js";const m=()=>{const c=i(),t=o=>{c.add({severity:o.severity||"info",summary:o.summary,detail:o.detail,life:o.life||5e3,closable:o.closable!==!1,group:o.group})},a=(o,r,e)=>{t({severity:"success",summary:o,detail:r,life:e})},w=(o,r,e)=>{t({severity:"info",summary:o,detail:r,life:e})},n=(o,r,e)=>{t({severity:"warn",summary:o,detail:r,life:e})},s=(o,r,e)=>{t({severity:"error",summary:o,detail:r,life:e||8e3})};return{show:t,success:a,info:w,warn:n,error:s,clear:o=>{c.removeAllGroups()},remove:o=>{c.remove(o)},showSaveSuccess:(o="Запись")=>{a("Сохранено",`${o} успешно сохранена`)},showDeleteSuccess:(o="Запись")=>{a("Удалено",`${o} успешно удалена`)},showSaveError:(o="Запись",r)=>{s("Ошибка сохранения",r||`Не удалось сохранить ${o.toLowerCase()}`)},showDeleteError:(o="Запись",r)=>{s("Ошибка удаления",r||`Не удалось удалить ${o.toLowerCase()}`)},showLoadError:(o="Данные",r)=>{s("Ошибка загрузки",r||`Не удалось загрузить ${o.toLowerCase()}`)},showValidationError:(o="Проверьте правильность заполнения полей")=>{n("Ошибка валидации",o)},showNetworkError:()=>{s("Ошибка сети","Проверьте подключение к интернету и попробуйте снова")},showUnauthorizedError:()=>{s("Нет доступа","У вас недостаточно прав для выполнения этого действия")}}};export{m as u};

import{a8 as we,a9 as be,a6 as Re}from"./reactivity.esm-bundler.BQ12LWmY.js";import{e as Se,o as G,c as N,s as O,b as B,a as X,u as Y}from"./types.C07aSKae.js";var _e=Object.defineProperty,Oe=Object.defineProperties,Te=Object.getOwnPropertyDescriptors,Z=Object.getOwnPropertySymbols,Pe=Object.prototype.hasOwnProperty,Ee=Object.prototype.propertyIsEnumerable,K=(e,t,r)=>t in e?_e(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,S=(e,t)=>{for(var r in t||(t={}))Pe.call(t,r)&&K(e,r,t[r]);if(Z)for(var r of Z(t))Ee.call(t,r)&&K(e,r,t[r]);return e},_=(e,t)=>Oe(e,Te(t)),Ae=class extends Error{constructor(e,t,r){super(t||e.toString(),{cause:r}),this.status=e,this.statusText=t,this.error=r}},Ue=async(e,t)=>{var r,n,s,a,o,l;let c=t||{};const i={onRequest:[t?.onRequest],onResponse:[t?.onResponse],onSuccess:[t?.onSuccess],onError:[t?.onError],onRetry:[t?.onRetry]};if(!t||!t?.plugins)return{url:e,options:c,hooks:i};for(const u of t?.plugins||[]){if(u.init){const f=await((r=u.init)==null?void 0:r.call(u,e.toString(),t));c=f.options||c,e=f.url}i.onRequest.push((n=u.hooks)==null?void 0:n.onRequest),i.onResponse.push((s=u.hooks)==null?void 0:s.onResponse),i.onSuccess.push((a=u.hooks)==null?void 0:a.onSuccess),i.onError.push((o=u.hooks)==null?void 0:o.onError),i.onRetry.push((l=u.hooks)==null?void 0:l.onRetry)}return{url:e,options:c,hooks:i}},ee=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(){return this.options.delay}},Ie=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(e){return Math.min(this.options.maxDelay,this.options.baseDelay*2**e)}};function Le(e){if(typeof e=="number")return new ee({type:"linear",attempts:e,delay:1e3});switch(e.type){case"linear":return new ee(e);case"exponential":return new Ie(e);default:throw new Error("Invalid retry strategy")}}var Ne=async e=>{const t={},r=async n=>typeof n=="function"?await n():n;if(e?.auth){if(e.auth.type==="Bearer"){const n=await r(e.auth.token);if(!n)return t;t.authorization=`Bearer ${n}`}else if(e.auth.type==="Basic"){const n=r(e.auth.username),s=r(e.auth.password);if(!n||!s)return t;t.authorization=`Basic ${btoa(`${n}:${s}`)}`}else if(e.auth.type==="Custom"){const n=r(e.auth.value);if(!n)return t;t.authorization=`${r(e.auth.prefix)} ${n}`}}return t},xe=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function qe(e){const t=e.headers.get("content-type"),r=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!t)return"json";const n=t.split(";").shift()||"";return xe.test(n)?"json":r.has(n)||n.startsWith("text/")?"text":"blob"}function Ce(e){try{return JSON.parse(e),!0}catch{return!1}}function oe(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}function te(e){try{return JSON.parse(e)}catch{return e}}function re(e){return typeof e=="function"}function je(e){if(e?.customFetchImpl)return e.customFetchImpl;if(typeof globalThis<"u"&&re(globalThis.fetch))return globalThis.fetch;if(typeof window<"u"&&re(window.fetch))return window.fetch;throw new Error("No fetch implementation found")}async function ke(e){const t=new Headers(e?.headers),r=await Ne(e);for(const[n,s]of Object.entries(r||{}))t.set(n,s);if(!t.has("content-type")){const n=$e(e?.body);n&&t.set("content-type",n)}return t}function $e(e){return oe(e)?"application/json":null}function De(e){if(!e?.body)return null;const t=new Headers(e?.headers);if(oe(e.body)&&!t.has("content-type")){for(const[r,n]of Object.entries(e?.body))n instanceof Date&&(e.body[r]=n.toISOString());return JSON.stringify(e.body)}return e.body}function Me(e,t){var r;if(t?.method)return t.method.toUpperCase();if(e.startsWith("@")){const n=(r=e.split("@")[1])==null?void 0:r.split("/")[0];return ae.includes(n)?n.toUpperCase():t?.body?"POST":"GET"}return t?.body?"POST":"GET"}function Fe(e,t){let r;return!e?.signal&&e?.timeout&&(r=setTimeout(()=>t?.abort(),e?.timeout)),{abortTimeout:r,clearTimeout:()=>{r&&clearTimeout(r)}}}var Be=class ie extends Error{constructor(t,r){super(r||JSON.stringify(t,null,2)),this.issues=t,Object.setPrototypeOf(this,ie.prototype)}};async function M(e,t){let r=await e["~standard"].validate(t);if(r.issues)throw new Be(r.issues);return r.value}var ae=["get","post","put","patch","delete"],Ve=e=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(t,r){var n,s,a,o;const l=((s=(n=e.plugins)==null?void 0:n.find(c=>{var i;return(i=c.schema)!=null&&i.config?t.startsWith(c.schema.config.baseURL||"")||t.startsWith(c.schema.config.prefix||""):!1}))==null?void 0:s.schema)||e.schema;if(l){let c=t;(a=l.config)!=null&&a.prefix&&c.startsWith(l.config.prefix)&&(c=c.replace(l.config.prefix,""),l.config.baseURL&&(t=t.replace(l.config.prefix,l.config.baseURL))),(o=l.config)!=null&&o.baseURL&&c.startsWith(l.config.baseURL)&&(c=c.replace(l.config.baseURL,""));const i=l.schema[c];if(i){let u=_(S({},r),{method:i.method,output:i.output});return r?.disableValidation||(u=_(S({},u),{body:i.input?await M(i.input,r?.body):r?.body,params:i.params?await M(i.params,r?.params):r?.params,query:i.query?await M(i.query,r?.query):r?.query})),{url:t,options:u}}}return{url:t,options:r}}}),We=e=>{async function t(r,n){const s=_(S(S({},e),n),{plugins:[...e?.plugins||[],Ve(e||{})]});if(e?.catchAllError)try{return await J(r,s)}catch(a){return{data:null,error:{status:500,statusText:"Fetch Error",message:"Fetch related error. Captured by catchAllError option. See error property for more details.",error:a}}}return await J(r,s)}return t};function He(e,t){let{baseURL:r,params:n,query:s}=t||{query:{},params:{},baseURL:""},a=e.startsWith("http")?e.split("/").slice(0,3).join("/"):r||"";if(e.startsWith("@")){const f=e.toString().split("@")[1].split("/")[0];ae.includes(f)&&(e=e.replace(`@${f}/`,"/"))}a.endsWith("/")||(a+="/");let[o,l]=e.replace(a,"").split("?");const c=new URLSearchParams(l);for(const[f,h]of Object.entries(s||{}))h!=null&&c.set(f,String(h));if(n)if(Array.isArray(n)){const f=o.split("/").filter(h=>h.startsWith(":"));for(const[h,v]of f.entries()){const y=n[h];o=o.replace(v,y)}}else for(const[f,h]of Object.entries(n))o=o.replace(`:${f}`,String(h));o=o.split("/").map(encodeURIComponent).join("/"),o.startsWith("/")&&(o=o.slice(1));let i=c.toString();return i=i.length>0?`?${i}`.replace(/\+/g,"%20"):"",a.startsWith("http")?new URL(`${o}${i}`,a):`${a}${o}${i}`}var J=async(e,t)=>{var r,n,s,a,o,l,c,i;const{hooks:u,url:f,options:h}=await Ue(e,t),v=je(h),y=new AbortController,w=(r=h.signal)!=null?r:y.signal,T=He(f,h),L=De(h),A=await ke(h),d=Me(f,h);let p=_(S({},h),{url:T,headers:A,body:L,method:d,signal:w});for(const b of u.onRequest)if(b){const g=await b(p);g instanceof Object&&(p=g)}("pipeTo"in p&&typeof p.pipeTo=="function"||typeof((n=t?.body)==null?void 0:n.pipe)=="function")&&("duplex"in p||(p.duplex="half"));const{clearTimeout:C}=Fe(h,y);let m=await v(p.url,p);C();const z={response:m,request:p};for(const b of u.onResponse)if(b){const g=await b(_(S({},z),{response:(s=t?.hookOptions)!=null&&s.cloneResponse?m.clone():m}));g instanceof Response?m=g:g instanceof Object&&(m=g.response)}if(m.ok){if(!(p.method!=="HEAD"))return{data:"",error:null};const g=qe(m),P={data:"",response:m,request:p};if(g==="json"||g==="text"){const E=await m.text(),ve=await((a=p.jsonParser)!=null?a:te)(E);P.data=ve}else P.data=await m[g]();p?.output&&p.output&&!p.disableValidation&&(P.data=await M(p.output,P.data));for(const E of u.onSuccess)E&&await E(_(S({},P),{response:(o=t?.hookOptions)!=null&&o.cloneResponse?m.clone():m}));return t?.throw?P.data:{data:P.data,error:null}}const ye=(l=t?.jsonParser)!=null?l:te,j=await m.text(),Q=Ce(j),V=Q?await ye(j):null,ge={response:m,responseText:j,request:p,error:_(S({},V),{status:m.status,statusText:m.statusText})};for(const b of u.onError)b&&await b(_(S({},ge),{response:(c=t?.hookOptions)!=null&&c.cloneResponse?m.clone():m}));if(t?.retry){const b=Le(t.retry),g=(i=t.retryAttempt)!=null?i:0;if(await b.shouldAttemptRetry(g,m)){for(const E of u.onRetry)E&&await E(z);const P=b.getDelay(g);return await new Promise(E=>setTimeout(E,P)),await J(e,_(S({},t),{retryAttempt:g+1}))}}if(t?.throw)throw new Ae(m.status,m.statusText,Q?V:j);return{data:null,error:_(S({},V),{status:m.status,statusText:m.statusText})}},Je={},Ge={};const F=Object.create(null),x=e=>Je||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?F:globalThis),I=new Proxy(F,{get(e,t){return x()[t]??F[t]},has(e,t){const r=x();return t in r||t in F},set(e,t,r){const n=x(!0);return n[t]=r,!0},deleteProperty(e,t){if(!t)return!1;const r=x(!0);return delete r[t],!0},ownKeys(){const e=x(!0);return Object.keys(e)}});function ze(e){return e?e!=="false":!1}const Qe=typeof process<"u"&&Ge&&"production"||"";Qe==="test"||ze(I.TEST);class ue extends Error{constructor(t,r){super(t),this.name="BetterAuthError",this.message=t,this.cause=r,this.stack=""}}function Xe(e){try{return new URL(e).pathname!=="/"}catch{throw new ue(`Invalid base URL: ${e}. Please provide a valid base URL.`)}}function W(e,t="/api/auth"){return Xe(e)?e:(t=t.startsWith("/")?t:`/${t}`,`${e.replace(/\/+$/,"")}${t}`)}function Ye(e,t,r){if(e)return W(e,t);const n=I.BETTER_AUTH_URL||I.NEXT_PUBLIC_BETTER_AUTH_URL||I.PUBLIC_BETTER_AUTH_URL||I.NUXT_PUBLIC_BETTER_AUTH_URL||I.NUXT_PUBLIC_AUTH_URL||(I.BASE_URL!=="/"?I.BASE_URL:void 0);if(n)return W(n,t);if(typeof window<"u"&&window.location)return W(window.location.origin,t)}let R=[],U=0;const k=4;let le=e=>{let t=[],r={get(){return r.lc||r.listen(()=>{})(),r.value},lc:0,listen(n){return r.lc=t.push(n),()=>{for(let a=U+k;a<R.length;)R[a]===n?R.splice(a,k):a+=k;let s=t.indexOf(n);~s&&(t.splice(s,1),--r.lc||r.off())}},notify(n,s){let a=!R.length;for(let o of t)R.push(o,r.value,n,s);if(a){for(U=0;U<R.length;U+=k)R[U](R[U+1],R[U+2],R[U+3]);R.length=0}},off(){},set(n){let s=r.value;s!==n&&(r.value=n,r.notify(s))},subscribe(n){let s=r.listen(n);return n(r.value),s},value:e};return r};const Ze=5,$=6,D=10;let Ke=(e,t,r,n)=>(e.events=e.events||{},e.events[r+D]||(e.events[r+D]=n(s=>{e.events[r].reduceRight((a,o)=>(o(a),a),{shared:{},...s})})),e.events[r]=e.events[r]||[],e.events[r].push(t),()=>{let s=e.events[r],a=s.indexOf(t);s.splice(a,1),s.length||(delete e.events[r],e.events[r+D](),delete e.events[r+D])}),et=1e3,tt=(e,t)=>Ke(e,n=>{let s=t(n);s&&e.events[$].push(s)},Ze,n=>{let s=e.listen;e.listen=(...o)=>(!e.lc&&!e.active&&(e.active=!0,n()),s(...o));let a=e.off;return e.events[$]=[],e.off=()=>{a(),setTimeout(()=>{if(e.active&&!e.lc){e.active=!1;for(let o of e.events[$])o();e.events[$]=[]}},et)},()=>{e.listen=s,e.off=a}});const rt=typeof window>"u",nt=(e,t,r,n)=>{const s=le({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>a()}),a=()=>{const l=typeof n=="function"?n({data:s.get().data,error:s.get().error,isPending:s.get().isPending}):n;return r(t,{...l,async onSuccess(c){s.set({data:c.data,error:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await l?.onSuccess?.(c)},async onError(c){const{request:i}=c,u=typeof i.retry=="number"?i.retry:i.retry?.attempts,f=i.retryAttempt||0;u&&f<u||(s.set({error:c.error,data:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await l?.onError?.(c))},async onRequest(c){const i=s.get();s.set({isPending:i.data===null,data:i.data,error:null,isRefetching:!0,refetch:s.value.refetch}),await l?.onRequest?.(c)}})};e=Array.isArray(e)?e:[e];let o=!1;for(const l of e)l.subscribe(()=>{rt||(o?a():tt(s,()=>(setTimeout(()=>{a()},0),o=!0,()=>{s.off(),l.off()})))});return s},st={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},ot=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,ne={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},it=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/;function at(e){return e instanceof Date&&!isNaN(e.getTime())}function ut(e){const t=it.exec(e);if(!t)return null;const[,r,n,s,a,o,l,c,i,u,f]=t;let h=new Date(Date.UTC(parseInt(r,10),parseInt(n,10)-1,parseInt(s,10),parseInt(a,10),parseInt(o,10),parseInt(l,10),c?parseInt(c.padEnd(3,"0"),10):0));if(i){const v=(parseInt(u,10)*60+parseInt(f,10))*(i==="+"?-1:1);h.setUTCMinutes(h.getUTCMinutes()+v)}return at(h)?h:null}function lt(e,t={}){const{strict:r=!1,warnings:n=!1,reviver:s,parseDates:a=!0}=t;if(typeof e!="string")return e;const o=e.trim();if(o[0]==='"'&&o.endsWith('"')&&!o.slice(1,-1).includes('"'))return o.slice(1,-1);const l=o.toLowerCase();if(l.length<=9&&l in ne)return ne[l];if(!ot.test(o)){if(r)throw new SyntaxError("[better-json] Invalid JSON");return e}if(Object.entries(st).some(([i,u])=>{const f=u.test(o);return f&&n&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${i} pattern`),f})&&r)throw new Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(o,(u,f)=>{if(u==="__proto__"||u==="constructor"&&f&&typeof f=="object"&&"prototype"in f){n&&console.warn(`[better-json] Dropping "${u}" key to prevent prototype pollution`);return}if(a&&typeof f=="string"){const h=ut(f);if(h)return h}return s?s(u,f):f})}catch(i){if(r)throw i;return e}}function ct(e,t={strict:!0}){return lt(e,t)}const ft={id:"redirect",name:"Redirect",hooks:{onSuccess(e){if(e.data?.url&&e.data?.redirect&&typeof window<"u"&&window.location&&window.location)try{window.location.href=e.data.url}catch{}}}};function dt(e){const t=le(!1);return{session:nt(t,"/get-session",e,{method:"GET"}),$sessionSignal:t}}const ht=e=>{const t="credentials"in Request.prototype,r=Ye(e?.baseURL,e?.basePath),n=e?.plugins?.flatMap(d=>d.fetchPlugins).filter(d=>d!==void 0)||[],s={id:"lifecycle-hooks",name:"lifecycle-hooks",hooks:{onSuccess:e?.fetchOptions?.onSuccess,onError:e?.fetchOptions?.onError,onRequest:e?.fetchOptions?.onRequest,onResponse:e?.fetchOptions?.onResponse}},{onSuccess:a,onError:o,onRequest:l,onResponse:c,...i}=e?.fetchOptions||{},u=We({baseURL:r,...t?{credentials:"include"}:{},method:"GET",jsonParser(d){return d?ct(d,{strict:!1}):null},customFetchImpl:async(d,p)=>{try{return await fetch(d,p)}catch{return Response.error()}},...i,plugins:[s,...i.plugins||[],...e?.disableDefaultFetchPlugins?[]:[ft],...n]}),{$sessionSignal:f,session:h}=dt(u),v=e?.plugins||[];let y={},w={$sessionSignal:f,session:h},T={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"};const L=[{signal:"$sessionSignal",matcher(d){return d==="/sign-out"||d==="/update-user"||d.startsWith("/sign-in")||d.startsWith("/sign-up")||d==="/delete-user"||d==="/verify-email"}}];for(const d of v)d.getAtoms&&Object.assign(w,d.getAtoms?.(u)),d.pathMethods&&Object.assign(T,d.pathMethods),d.atomListeners&&L.push(...d.atomListeners);const A={notify:d=>{w[d].set(!w[d].get())},listen:(d,p)=>{w[d].subscribe(p)},atoms:w};for(const d of v)d.getActions&&Object.assign(y,d.getActions?.(u,A,e));return{pluginsActions:y,pluginsAtoms:w,pluginPathMethods:T,atomListeners:L,$fetch:u,$store:A}};function pt(e,t,r){const n=t[e],{fetchOptions:s,query:a,...o}=r||{};return n||(s?.method?s.method:o&&Object.keys(o).length>0?"POST":"GET")}function mt(e,t,r,n,s){function a(o=[]){return new Proxy(function(){},{get(l,c){const i=[...o,c];let u=e;for(const f of i)if(u&&typeof u=="object"&&f in u)u=u[f];else{u=void 0;break}return typeof u=="function"?u:a(i)},apply:async(l,c,i)=>{const u="/"+o.map(A=>A.replace(/[A-Z]/g,d=>`-${d.toLowerCase()}`)).join("/"),f=i[0]||{},h=i[1]||{},{query:v,fetchOptions:y,...w}=f,T={...h,...y},L=pt(u,r,f);return await t(u,{...T,body:L==="GET"?void 0:{...w,...T?.body||{}},query:v||T?.query,method:L,async onSuccess(A){await T?.onSuccess?.(A);const d=s?.find(m=>m.matcher(u));if(!d)return;const p=n[d.signal];if(!p)return;const C=p.get();setTimeout(()=>{p.set(!C)},10)}})}})}return a()}function yt(e){return e.charAt(0).toUpperCase()+e.slice(1)}function se(e){let t=we(),r=e.subscribe(n=>{t.value=n});return be()&&Re(r),t}function gt(e){return`use${yt(e)}`}function vt(e){const{pluginPathMethods:t,pluginsActions:r,pluginsAtoms:n,$fetch:s,$store:a,atomListeners:o}=ht(e);let l={};for(const[f,h]of Object.entries(n))l[gt(f)]=()=>se(h);function c(f){if(f){const h=se(n.$sessionSignal),v=e?.fetchOptions?.baseURL||e?.baseURL;let y=v?new URL(v).pathname:"/api/auth";return y=y==="/"?"/api/auth":y,y=y.endsWith("/")?y.slice(0,-1):y,f(`${y}/get-session`,{ref:h}).then(w=>({data:w.data,isPending:!1,error:w.error}))}return l.useSession()}const i={...r,...l,useSession:c,$fetch:s,$store:a};return mt(i,s,t,n,o)}function wt(e){return{authorize(t,r="AND"){let n=!1;for(const[s,a]of Object.entries(t)){const o=e[s];if(!o)return{success:!1,error:`You are not allowed to access resource: ${s}`};if(Array.isArray(a))n=a.every(l=>o.includes(l));else if(typeof a=="object"){const l=a;l.connector==="OR"?n=l.actions.some(c=>o.includes(c)):n=l.actions.every(c=>o.includes(c))}else throw new ue("Invalid access control request");if(n&&r==="OR")return{success:n};if(!n&&r==="AND")return{success:!1,error:`unauthorized to access resource "${s}"`}}return n?{success:n}:{success:!1,error:"Not authorized"}},statements:e}}function bt(e){return{newRole(t){return wt(t)},statements:e}}const Rt={user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]},ce=bt(Rt),fe=ce.newRole({user:["create","list","set-role","ban","impersonate","delete","set-password","update"],session:["list","revoke","delete"]}),de=ce.newRole({user:[],session:[]}),St={admin:fe,user:de},_t=e=>{if(e.userId&&e.options?.adminUserIds?.includes(e.userId))return!0;if(!e.permissions&&!e.permission)return!1;const t=(e.role||e.options?.defaultRole||"user").split(","),r=e.options?.roles||St;for(const n of t)if(r[n]?.authorize(e.permission??e.permissions)?.success)return!0;return!1},Ot=()=>({id:"username",$InferServerPlugin:{}}),Tt=()=>({id:"phoneNumber",$InferServerPlugin:{},atomListeners:[{matcher(e){return e==="/phone-number/update"||e==="/phone-number/verify"},signal:"$sessionSignal"}]}),Pt=()=>({id:"anonymous",$InferServerPlugin:{},pathMethods:{"/sign-in/anonymous":"POST"}}),Et=e=>{const t={admin:fe,user:de,...e?.roles};return{id:"admin-client",$InferServerPlugin:{},getActions:()=>({admin:{checkRolePermission:r=>_t({role:r.role,options:{ac:e?.ac,roles:t},permissions:r.permissions??r.permission})}}),pathMethods:{"/admin/list-users":"GET","/admin/stop-impersonating":"POST"}}},he=Se(["GUEST","USER","SHOP","ADMIN"]),q=G({id:O(),name:O().nullish(),email:O(),emailVerified:B().default(!1),image:O().nullish(),role:he,banned:B().default(!1),banReason:O().nullish(),banExpires:N.date().nullish(),createdAt:N.date().default(()=>new Date),updatedAt:N.date()}).strict(),At=G({accounts:X(Y()).optional(),sessions:X(Y()).optional()}),Ut=q;Ut.merge(At.partial());q.partial().passthrough();G({id:O(),name:O().nullish(),email:O(),emailVerified:B().default(!1),image:O().nullish(),role:he,banned:B().default(!1),banReason:O().nullish(),banExpires:N.date().nullish(),createdAt:N.date().default(()=>new Date),updatedAt:N.date()}).partial().passthrough();q.partial({id:!0,emailVerified:!0,role:!0,banned:!0,createdAt:!0,updatedAt:!0});const Ct=q.partial({id:!0,emailVerified:!0,role:!0,banned:!0,createdAt:!0,updatedAt:!0});q.partial();const H={baseURL:"http://localhost:3000",fetchOptions:{timeout:1e4,retries:3}},pe=vt({baseURL:H.baseURL,plugins:[Et(),Tt(),Ot(),Pt()],fetchOptions:{timeout:H.fetchOptions?.timeout,retry:H.fetchOptions?.retries,onRequest:e=>{},onResponse:e=>{},onError:e=>{console.error("❌ Auth error:",e.error),e.response?.status===401?(console.warn("🔒 Unauthorized - redirecting to login"),typeof window<"u"&&window.location.pathname.includes("/login")):e.response?.status===403?console.warn("🚫 Forbidden - insufficient permissions"):e.response?.status>=500&&console.error("🔥 Server error - check API status")}}}),{signIn:jt,signUp:kt,signOut:$t,useSession:Dt,getSession:Mt,$ERROR_CODES:Ft}=pe;pe.admin;const me=(e,t)=>e?.role===t,It=e=>me(e,"ADMIN"),Lt=e=>me(e,"SHOP"),Bt=e=>It(e)||Lt(e),Vt=(e,t="Произошла ошибка")=>typeof e=="string"?e:e?.message?e.message:e?.error?.message?e.error.message:t;export{Ct as U,Lt as a,pe as b,Bt as c,Vt as g,me as h,It as i,Dt as u};

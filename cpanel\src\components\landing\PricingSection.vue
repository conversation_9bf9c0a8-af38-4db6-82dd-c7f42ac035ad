<template>
  <div class="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
    <Motion
      v-for="(plan, index) in pricingPlansData"
      :key="index"
      :initial="{ opacity: 0, y: 60 }"
      :whileInView="{ opacity: 1, y: 0 }"
      :transition="{ duration: 0.8, delay: index * 0.1 }"
      :viewport="{ once: true }"
    >
      <div class="relative h-full">
        <Motion
          v-if="plan.popular"
          :initial="{ opacity: 0, y: -20 }"
          :animate="{ opacity: 1, y: 0 }"
          class="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10"
        >
          <div class="bg-blue-600 text-white px-6 py-3 rounded-full text-sm font-medium flex items-center gap-2">
            <Star class="w-4 h-4" />
            Рекомендуемый
            <Sparkles class="w-4 h-4 text-blue-400" />
          </div>
        </Motion>

        <Card variant="elevated" class="h-full flex flex-col p-6">
          <div class="text-center mb-8">
            <h3 class="text-3xl font-bold text-white mb-3">{{ plan.name }}</h3>
            <p class="text-gray-100 mb-6">{{ plan.description }}</p>

            <div class="text-center">
              <div class="text-5xl font-bold mb-2 text-blue-400">{{ plan.price }}₽</div>
              <div class="text-gray-500">за {{ plan.period }}</div>
            </div>
          </div>

          <div class="flex-1 space-y-6">
            <div>
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <CheckCircle class="w-5 h-5 text-green-400" />
                Включено в план
              </h4>
              <ul class="space-y-3">
                <Motion
                  v-for="(feature, i) in plan.features"
                  :key="i"
                  :initial="{ opacity: 0, x: -20 }"
                  :whileInView="{ opacity: 1, x: 0 }"
                  :transition="{ delay: i * 0.05 }"
                  :viewport="{ once: true }"
                  class="flex items-start gap-3 text-sm"
                >
                  <CheckCircle2 class="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span class="text-gray-100">{{ feature }}</span>
                </Motion>
              </ul>
            </div>

            <div v-if="plan.limitations.length > 0">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <AlertTriangle class="w-5 h-5 text-yellow-400" />
                Ограничения
              </h4>
              <ul class="space-y-3">
                <li
                  v-for="(limitation, i) in plan.limitations"
                  :key="i"
                  class="flex items-start gap-3 text-sm"
                >
                  <div class="w-4 h-4 border border-gray-500 rounded mt-0.5 flex-shrink-0" />
                  <span class="text-gray-300">{{ limitation }}</span>
                </li>
              </ul>
            </div>
          </div>

          <Button 
            size="lg" 
            :variant="plan.popular ? 'primary' : 'primary'"
            class="w-full flex items-center justify-center gap-2"
          >
            Выбрать план
            <ArrowRight class="w-4 h-4" />
          </Button>
        </Card>
      </div>
    </Motion>
  </div>
</template>

<script setup lang="ts">
import { Motion } from 'motion-v'
import { 
  Star, 
  Sparkles, 
  CheckCircle, 
  CheckCircle2, 
  AlertTriangle, 
  ArrowRight 
} from 'lucide-vue-next'
import Card from '../ui/Card.vue'
import Button from '../ui/Button.vue'

const pricingPlansData = [
  {
    name: "Базовый",
    price: "4,990",
    period: "мес",
    description: "Для небольших мастерских и сервисных центров",
    features: [
      "До 2,000 запросов в месяц",
      "Доступ к разделу «Сальники»",
      "Базовые технические характеристики",
      "Экспорт результатов в Excel/PDF",
      "Email поддержка (48 часов)",
      "Мобильное приложение",
    ],
    limitations: ["Ограниченный доступ к техническим чертежам", "Без API интеграции", "Стандартные отчеты"],
    popular: false,
  },
  {
    name: "Профессиональный",
    price: "12,990",
    period: "мес",
    description: "Для средних предприятий и дилерских центров",
    features: [
      "До 15,000 запросов в месяц",
      "Все активные разделы каталога",
      "Полные технические характеристики",
      "3D модели и чертежи деталей",
      "REST API для интеграции",
      "Приоритетная поддержка (24 часа)",
      "Расширенная аналитика и отчеты",
      "Персональный кабинет с историей",
      "Уведомления о новых аналогах",
    ],
    limitations: ["Ограничения по количеству API вызовов", "Стандартная интеграция"],
    popular: true,
  },
  {
    name: "Корпоративный",
    price: "29,990",
    period: "мес",
    description: "Для крупных производственных предприятий",
    features: [
      "Безлимитные запросы и API вызовы",
      "Все разделы + приоритетный доступ к новым",
      "Полная техническая документация",
      "CAD файлы и 3D модели",
      "Кастомная API интеграция",
      "Персональный технический менеджер",
      "SLA 99.9% доступности",
      "Белый лейбл решения",
      "Интеграция с ERP системами",
      "Обучение персонала",
      "Техническая поддержка 24/7",
    ],
    limitations: [],
    popular: false,
  },
]
</script>
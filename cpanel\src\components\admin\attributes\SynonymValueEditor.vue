<template>
  <div class="space-y-4">
    <div class="flex gap-2">
      <VInputText v-model="newValue" placeholder="Введите значение и нажмите Добавить" class="flex-1" @keyup.enter="addValue" />
      <VButton label="Добавить" @click="addValue" :disabled="!canAdd">
        <template #icon>
          <Icon name="pi pi-plus" class="w-5 h-5" />
        </template>
      </VButton>
    </div>
    <VDataTable :value="synonyms" :loading="loading" class="p-datatable-sm" table-style="min-width: 24rem" striped-rows>
      <Column field="value" header="Значение"></Column>
      <Column header="" style="width: 80px">
        <template #body="{ data }">
          <VButton size="small" severity="danger" outlined @click="removeValue(data)">
            <Icon name="pi pi-trash" class="w-5 h-5" />
          </VButton>
        </template>
      </Column>
    </VDataTable>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import VInputText from '@/volt/InputText.vue'
import VButton from '@/volt/Button.vue'
import VDataTable from '@/volt/DataTable.vue'
import Column from 'primevue/column'
import { useToast } from '@/composables/useToast'
import { useTrpc } from '@/composables/useTrpc'
import Icon from '@/components/ui/Icon.vue'

const props = defineProps<{ groupId: number }>()

const { attributeSynonyms } = useTrpc()
const toast = useToast()

const synonyms = ref<Array<{ id: number; value: string }>>([])
const loading = ref(false)
const newValue = ref('')

const canAdd = ref(false)
watch(newValue, (v) => {
  const trimmed = (v || '').trim()
  canAdd.value = trimmed.length > 0 && !synonyms.value.some(s => s.value.toLowerCase() === trimmed.toLowerCase())
})

const load = async () => {
  loading.value = true
  try {
    const result = await attributeSynonyms.synonyms.findMany({ groupId: props.groupId })
    if (Array.isArray(result)) synonyms.value = result as any
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось загрузить значения')
  } finally {
    loading.value = false
  }
}

const addValue = async () => {
  const value = newValue.value.trim()
  if (!value) return
  if (synonyms.value.some(s => s.value.toLowerCase() === value.toLowerCase())) {
    toast.error('Дубликаты не допускаются')
    return
  }
  try {
    const created = await attributeSynonyms.synonyms.create({ groupId: props.groupId, value })
    if (created && typeof created === 'object') {
      synonyms.value.push(created as any)
      newValue.value = ''
      // Успешный тост уже показывается глобально через useTrpc
    }
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось добавить значение')
  }
}

const removeValue = async (row: { id: number }) => {
  if (!confirm('Удалить значение?')) return
  try {
    await attributeSynonyms.synonyms.delete({ id: row.id })
    synonyms.value = synonyms.value.filter(s => s.id !== row.id)
    // Успешный тост уже показывается глобально через useTrpc
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось удалить значение')
  }
}

onMounted(load)
watch(() => props.groupId, load)
</script>



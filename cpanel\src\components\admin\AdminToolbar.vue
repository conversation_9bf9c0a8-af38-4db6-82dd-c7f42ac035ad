<template>
  <Toolbar class="bg-surface-0 dark:bg-surface-100 shadow-sm border-b border-surface-200 dark:border-surface-700 px-4 sm:px-6 lg:px-8">
    <template #start>
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <img class="h-8 w-8" src="/favicon.svg" alt="PartTec" />
        </div>
        <div class="ml-4">
          <h1 class="text-xl font-semibold text-surface-900 dark:text-surface-50">
            PartTec Admin
          </h1>
        </div>
      </div>
    </template>

    <template #center>
      <!-- Основная навигация -->
      <nav class="hidden md:flex space-x-2">
        <SecondaryButton 
          label="Главная" 
          text 
          @click="navigateTo('/admin')"
          :class="{ 'bg-primary-50 text-primary-600': isActive('/admin') }"
        />
        <SecondaryButton 
          label="Каталог" 
          text 
          @click="navigateTo('/admin/catalog')"
          :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/catalog') }"
        />
        <SecondaryButton 
          label="Пользователи" 
          text 
          @click="navigateTo('/admin/users')"
          :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/users') }"
        />
        <SecondaryButton 
          label="Настройки" 
          text 
          @click="navigateTo('/admin/settings')"
          :class="{ 'bg-primary-50 text-primary-600': isActive('/admin/settings') }"
        />
      </nav>
    </template>

    <template #end>
      <div class="flex items-center space-x-4">
        <!-- Переключатель тем -->
        <ThemeToggle mode="menu" class="hidden sm:block" />

        <!-- Мобильное меню -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors"
        >
          <Menu :size="20" />
        </button>

        <!-- Профиль пользователя -->
        <UserMenu />
      </div>
    </template>
  </Toolbar>

  <!-- Мобильное меню -->
  <div
    v-if="showMobileMenu"
    class="md:hidden bg-surface-0 dark:bg-surface-100 border-b border-surface-200 dark:border-surface-700 px-4 py-2"
  >
    <nav class="space-y-1">
      <SecondaryButton
        label="Главная"
        text
        class="w-full justify-start"
        @click="navigateTo('/admin')"
      />
      <SecondaryButton
        label="Каталог"
        text
        class="w-full justify-start"
        @click="navigateTo('/admin/catalog')"
      />
      <SecondaryButton
        label="Пользователи"
        text
        class="w-full justify-start"
        @click="navigateTo('/admin/users')"
      />
      <SecondaryButton
        label="Настройки"
        text
        class="w-full justify-start"
        @click="navigateTo('/admin/settings')"
      />

      <!-- Разделитель -->
      <div class="border-t border-surface-200 dark:border-surface-700 my-2"></div>

      <!-- Переключатель тем для мобильных -->
      <div class="px-2 py-1">
        <ThemeToggle mode="buttons" show-label class="w-full" />
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Toolbar from '@/volt/Toolbar.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import UserMenu from '@/components/auth/UserMenu.vue'
import ThemeToggle from '@/components/ui/ThemeToggle.vue'
import { Menu } from 'lucide-vue-next'
import { navigate } from 'astro:transitions/client'

// Локальное состояние
const showMobileMenu = ref(false)

// Получаем текущий путь
const currentPath = computed(() => {
  if (typeof window !== 'undefined') {
    return window.location.pathname
  }
  return ''
})

// Методы
const navigateTo = (path: string) => {
  // window.location.href = path
  navigate(path)

  showMobileMenu.value = false
}

const isActive = (path: string): boolean => {
  if (path === '/admin') {
    return currentPath.value === '/admin'
  }
  return currentPath.value.startsWith(path)
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}
</script>


import{s as E,p as P}from"./utils.BUKUcbtE.js";import{B as O}from"./index.BaVCXmir.js";import{c,o as l,a as n,p as g,m,d as w,g as B,n as $,r as j,w as f,q as I,b as M,M as H,e as o,h as y,F as K}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{_ as S}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{b as L,r as h,t as v,n as b}from"./reactivity.esm-bundler.BQ12LWmY.js";import{S as D}from"./SecondaryButton.DkELYl7Q.js";import{u as W}from"./useAuth.D4HmQrUw.js";import{f as J}from"./index.BH7IgUdp.js";import{M as Q}from"./Menu.D9nHHLIB.js";import{n as _}from"./router.DKcY2uv6.js";import{I as X}from"./Icon.By8t0-Wj.js";/* empty css                                */import{T as Y}from"./ThemeToggle.BiFs6AFw.js";import{M as Z}from"./menu.BH1dgvL-.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.6ykohhwZ.js";import"./auth-client.CMFsScx1.js";import"./types.C07aSKae.js";import"./types.FgRm47Sn.js";import"./index.CDQpPXyE.js";import"./index.CLs7nh7g.js";import"./index.S_9XL1GF.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";/* empty css                       */import"./useTheme.DwNY0gjL.js";/* empty css                                */import"./createLucideIcon.NtN1-Ts2.js";import"./check.B3pubfVf.js";var ee=`
    .p-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: dt('toolbar.padding');
        background: dt('toolbar.background');
        border: 1px solid dt('toolbar.border.color');
        color: dt('toolbar.color');
        border-radius: dt('toolbar.border.radius');
        gap: dt('toolbar.gap');
    }

    .p-toolbar-start,
    .p-toolbar-center,
    .p-toolbar-end {
        display: flex;
        align-items: center;
    }
`,ae={root:"p-toolbar p-component",start:"p-toolbar-start",center:"p-toolbar-center",end:"p-toolbar-end"},te=O.extend({name:"toolbar",style:ee,classes:ae}),re={name:"BaseToolbar",extends:E,props:{ariaLabelledby:{type:String,default:null}},style:te,provide:function(){return{$pcToolbar:this,$parentInstance:this}}},F={name:"Toolbar",extends:re,inheritAttrs:!1},ne=["aria-labelledby"];function oe(a,t,r,e,p,s){return l(),c("div",m({class:a.cx("root"),role:"toolbar","aria-labelledby":a.ariaLabelledby},a.ptmi("root")),[n("div",m({class:a.cx("start")},a.ptm("start")),[g(a.$slots,"start")],16),n("div",m({class:a.cx("center")},a.ptm("center")),[g(a.$slots,"center")],16),n("div",m({class:a.cx("end")},a.ptm("end")),[g(a.$slots,"end")],16)],16,ne)}F.render=oe;const se=w({__name:"Toolbar",setup(a,{expose:t}){t();const e={theme:h({root:`flex items-center justify-between flex-wrap p-3 gap-2
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0
        border border-surface-200 dark:border-surface-700 rounded-md`,start:"flex items-center",center:"flex items-center",end:"flex items-center"}),get Toolbar(){return F},get ptViewMerge(){return P}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function ie(a,t,r,e,p,s){return l(),B(e.Toolbar,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},$({_:2},[j(a.$slots,(i,u)=>({name:u,fn:f(d=>[g(a.$slots,u,L(I(d??{})))])}))]),1032,["pt","ptOptions"])}const le=S(se,[["render",ie]]);var ue=`
    .p-avatar {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: dt('avatar.width');
        height: dt('avatar.height');
        font-size: dt('avatar.font.size');
        background: dt('avatar.background');
        color: dt('avatar.color');
        border-radius: dt('avatar.border.radius');
    }

    .p-avatar-image {
        background: transparent;
    }

    .p-avatar-circle {
        border-radius: 50%;
    }

    .p-avatar-circle img {
        border-radius: 50%;
    }

    .p-avatar-icon {
        font-size: dt('avatar.icon.size');
        width: dt('avatar.icon.size');
        height: dt('avatar.icon.size');
    }

    .p-avatar img {
        width: 100%;
        height: 100%;
    }

    .p-avatar-lg {
        width: dt('avatar.lg.width');
        height: dt('avatar.lg.width');
        font-size: dt('avatar.lg.font.size');
    }

    .p-avatar-lg .p-avatar-icon {
        font-size: dt('avatar.lg.icon.size');
        width: dt('avatar.lg.icon.size');
        height: dt('avatar.lg.icon.size');
    }

    .p-avatar-xl {
        width: dt('avatar.xl.width');
        height: dt('avatar.xl.width');
        font-size: dt('avatar.xl.font.size');
    }

    .p-avatar-xl .p-avatar-icon {
        font-size: dt('avatar.xl.icon.size');
        width: dt('avatar.xl.icon.size');
        height: dt('avatar.xl.icon.size');
    }

    .p-avatar-group {
        display: flex;
        align-items: center;
    }

    .p-avatar-group .p-avatar + .p-avatar {
        margin-inline-start: dt('avatar.group.offset');
    }

    .p-avatar-group .p-avatar {
        border: 2px solid dt('avatar.group.border.color');
    }

    .p-avatar-group .p-avatar-lg + .p-avatar-lg {
        margin-inline-start: dt('avatar.lg.group.offset');
    }

    .p-avatar-group .p-avatar-xl + .p-avatar-xl {
        margin-inline-start: dt('avatar.xl.group.offset');
    }
`,de={root:function(t){var r=t.props;return["p-avatar p-component",{"p-avatar-image":r.image!=null,"p-avatar-circle":r.shape==="circle","p-avatar-lg":r.size==="large","p-avatar-xl":r.size==="xlarge"}]},label:"p-avatar-label",icon:"p-avatar-icon"},ce=O.extend({name:"avatar",style:ue,classes:de}),pe={name:"BaseAvatar",extends:E,props:{label:{type:String,default:null},icon:{type:String,default:null},image:{type:String,default:null},size:{type:String,default:"normal"},shape:{type:String,default:"square"},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:ce,provide:function(){return{$pcAvatar:this,$parentInstance:this}}};function x(a){"@babel/helpers - typeof";return x=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(a)}function C(a,t,r){return(t=me(t))in a?Object.defineProperty(a,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):a[t]=r,a}function me(a){var t=fe(a,"string");return x(t)=="symbol"?t:t+""}function fe(a,t){if(x(a)!="object"||!a)return a;var r=a[Symbol.toPrimitive];if(r!==void 0){var e=r.call(a,t);if(x(e)!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(a)}var V={name:"Avatar",extends:pe,inheritAttrs:!1,emits:["error"],methods:{onError:function(t){this.$emit("error",t)}},computed:{dataP:function(){return J(C(C({},this.shape,this.shape),this.size,this.size))}}},ve=["aria-labelledby","aria-label","data-p"],be=["data-p"],ge=["data-p"],ye=["src","alt","data-p"];function he(a,t,r,e,p,s){return l(),c("div",m({class:a.cx("root"),"aria-labelledby":a.ariaLabelledby,"aria-label":a.ariaLabel},a.ptmi("root"),{"data-p":s.dataP}),[g(a.$slots,"default",{},function(){return[a.label?(l(),c("span",m({key:0,class:a.cx("label")},a.ptm("label"),{"data-p":s.dataP}),v(a.label),17,be)):a.$slots.icon?(l(),B(H(a.$slots.icon),{key:1,class:b(a.cx("icon"))},null,8,["class"])):a.icon?(l(),c("span",m({key:2,class:[a.cx("icon"),a.icon]},a.ptm("icon"),{"data-p":s.dataP}),null,16,ge)):a.image?(l(),c("img",m({key:3,src:a.image,alt:a.ariaLabel,onError:t[0]||(t[0]=function(){return s.onError&&s.onError.apply(s,arguments)})},a.ptm("image"),{"data-p":s.dataP}),null,16,ye)):M("",!0)]})],16,ve)}V.render=he;const xe=w({__name:"Avatar",setup(a,{expose:t}){t();const e={theme:h({root:`inline-flex items-center justify-center
        w-8 h-8 text-base rounded-md
        bg-surface-200 dark:bg-surface-700
        has-[img]:bg-transparent
        p-circle:rounded-full
        p-large:w-12 p-large:h-12 p-large:text-2xl
        p-xlarge:w-16 p-xlarge:h-16 p-xlarge:text-[2rem]`,label:"",icon:"text-base p-large:text-2xl p-xlarge:text-[2rem]",image:"p-circle:rounded-full w-full h-full"}),get Avatar(){return V},get ptViewMerge(){return P}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function _e(a,t,r,e,p,s){return l(),B(e.Avatar,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},$({_:2},[j(a.$slots,(i,u)=>({name:u,fn:f(d=>[g(a.$slots,u,L(I(d??{})))])}))]),1032,["pt","ptOptions"])}const we=S(xe,[["render",_e]]),Se=w({__name:"UserMenu",setup(a,{expose:t}){t();const{user:r,displayName:e,userAvatar:p,userRole:s,signOut:i,loadingState:u}=W(),d=h(),U=h(!1),N=y(()=>e.value?e.value.split(" ").map(k=>k.charAt(0)).join("").toUpperCase().slice(0,2):"U"),R=y(()=>{switch(s.value){case"ADMIN":return"Администратор";case"SHOP":return"Владелец магазина";case"USER":return"Пользователь";case"GUEST":return"Гость";default:return"Пользователь"}}),T=y(()=>u.signOut),q=y(()=>[{label:"Профиль",icon:"pi pi-user",command:()=>{_("/admin/profile")}},{label:"Настройки",icon:"pi pi-cog",command:()=>{_("/admin/settings")}},{separator:!0},{label:T.value?"Выход...":"Выйти",icon:"pi pi-sign-out",class:"text-red-600",command:A}]),G=k=>{d.value.toggle(k)},A=async()=>{_("/admin/logout")},z={user:r,displayName:e,userAvatar:p,userRole:s,signOut:i,loadingState:u,menu:d,isMenuOpen:U,userInitials:N,roleLabel:R,isSigningOut:T,menuItems:q,toggleMenu:G,handleSignOut:A,SecondaryButton:D,Avatar:we,Menu:Q,Icon:X};return Object.defineProperty(z,"__isScriptSetup",{enumerable:!1,value:!0}),z}}),ke={class:"relative"},Be={class:"hidden md:block text-left"},Me={class:"text-sm font-medium text-surface-700"},Te={class:"text-xs text-surface-500"},Ae={class:"px-4 py-3 border-b border-surface-200"},ze={class:"text-sm font-medium text-surface-900"},Ce={class:"text-sm text-surface-500"},Ee={class:"text-xs text-surface-400 mt-1"};function Pe(a,t,r,e,p,s){return l(),c("div",ke,[o(e.SecondaryButton,{onClick:e.toggleMenu,text:"",class:"flex items-center space-x-3"},{default:f(()=>[o(e.Avatar,{image:e.userAvatar,label:e.userInitials,size:"normal",shape:"circle"},null,8,["image","label"]),n("div",Be,[n("p",Me,v(e.displayName),1),n("p",Te,v(e.roleLabel),1)]),o(e.Icon,{name:"pi pi-chevron-down",class:b(["text-surface-400 transition-transform duration-200",{"rotate-180":e.isMenuOpen}])},null,8,["class"])]),_:1}),o(e.Menu,{ref:"menu",model:e.menuItems,popup:!0,class:"w-56"},{start:f(()=>[n("div",Ae,[n("p",ze,v(e.displayName),1),n("p",Ce,v(e.user?.email),1),n("p",Ee,v(e.roleLabel),1)])]),_:1},8,["model"]),e.isMenuOpen?(l(),c("div",{key:0,class:"fixed inset-0 z-40",onClick:t[0]||(t[0]=(...i)=>a.closeMenu&&a.closeMenu(...i))})):M("",!0)])}const Oe=S(Se,[["render",Pe],["__scopeId","data-v-b7dcda07"]]),$e=w({__name:"AdminToolbar",setup(a,{expose:t}){t();const r=h(!1),e=y(()=>typeof window<"u"?window.location.pathname:""),u={showMobileMenu:r,currentPath:e,navigateTo:d=>{_(d),r.value=!1},isActive:d=>d==="/admin"?e.value==="/admin":e.value.startsWith(d),toggleMobileMenu:()=>{r.value=!r.value},Toolbar:le,SecondaryButton:D,UserMenu:Oe,ThemeToggle:Y,get Menu(){return Z}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}}),je={class:"hidden md:flex space-x-2"},Ie={class:"flex items-center space-x-4"},Le={key:0,class:"md:hidden bg-surface-0 dark:bg-surface-100 border-b border-surface-200 dark:border-surface-700 px-4 py-2"},De={class:"space-y-1"},Fe={class:"px-2 py-1"};function Ve(a,t,r,e,p,s){return l(),c(K,null,[o(e.Toolbar,{class:"bg-surface-0 dark:bg-surface-100 shadow-sm border-b border-surface-200 dark:border-surface-700 px-4 sm:px-6 lg:px-8"},{start:f(()=>t[8]||(t[8]=[n("div",{class:"flex items-center"},[n("div",{class:"flex-shrink-0"},[n("img",{class:"h-8 w-8",src:"/favicon.svg",alt:"PartTec"})]),n("div",{class:"ml-4"},[n("h1",{class:"text-xl font-semibold text-surface-900 dark:text-surface-50"}," PartTec Admin ")])],-1)])),center:f(()=>[n("nav",je,[o(e.SecondaryButton,{label:"Главная",text:"",onClick:t[0]||(t[0]=i=>e.navigateTo("/admin")),class:b({"bg-primary-50 text-primary-600":e.isActive("/admin")})},null,8,["class"]),o(e.SecondaryButton,{label:"Каталог",text:"",onClick:t[1]||(t[1]=i=>e.navigateTo("/admin/catalog")),class:b({"bg-primary-50 text-primary-600":e.isActive("/admin/catalog")})},null,8,["class"]),o(e.SecondaryButton,{label:"Пользователи",text:"",onClick:t[2]||(t[2]=i=>e.navigateTo("/admin/users")),class:b({"bg-primary-50 text-primary-600":e.isActive("/admin/users")})},null,8,["class"]),o(e.SecondaryButton,{label:"Настройки",text:"",onClick:t[3]||(t[3]=i=>e.navigateTo("/admin/settings")),class:b({"bg-primary-50 text-primary-600":e.isActive("/admin/settings")})},null,8,["class"])])]),end:f(()=>[n("div",Ie,[o(e.ThemeToggle,{mode:"menu",class:"hidden sm:block"}),n("button",{onClick:e.toggleMobileMenu,class:"md:hidden p-2 rounded-md text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-200 transition-colors"},[o(e.Menu,{size:20})]),o(e.UserMenu)])]),_:1}),e.showMobileMenu?(l(),c("div",Le,[n("nav",De,[o(e.SecondaryButton,{label:"Главная",text:"",class:"w-full justify-start",onClick:t[4]||(t[4]=i=>e.navigateTo("/admin"))}),o(e.SecondaryButton,{label:"Каталог",text:"",class:"w-full justify-start",onClick:t[5]||(t[5]=i=>e.navigateTo("/admin/catalog"))}),o(e.SecondaryButton,{label:"Пользователи",text:"",class:"w-full justify-start",onClick:t[6]||(t[6]=i=>e.navigateTo("/admin/users"))}),o(e.SecondaryButton,{label:"Настройки",text:"",class:"w-full justify-start",onClick:t[7]||(t[7]=i=>e.navigateTo("/admin/settings"))}),t[9]||(t[9]=n("div",{class:"border-t border-surface-200 dark:border-surface-700 my-2"},null,-1)),n("div",Fe,[o(e.ThemeToggle,{mode:"buttons","show-label":"",class:"w-full"})])])])):M("",!0)],64)}const ga=S($e,[["render",Ve]]);export{ga as default};

import { ref, computed, defineComponent, useSSRContext, onMounted, mergeProps } from 'vue';
import { a as useToast$1 } from './_@astro-renderers_CicWY1rm.mjs';
import { ssrRenderAttrs } from 'vue/server-renderer';
import { e as createComponent, f as createAstro, h as addAttribute, l as renderScript, r as renderTemplate } from './astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                                  */

const _export_sfc = (sfc, props) => {
  const target = sfc.__vccOpts || sfc;
  for (const [key, val] of props) {
    target[key] = val;
  }
  return target;
};

const useToast = () => {
  const toast = useToast$1();
  const show = (message) => {
    toast.add({
      severity: message.severity || "info",
      summary: message.summary,
      detail: message.detail,
      life: message.life || 5e3,
      closable: message.closable !== false,
      group: message.group
    });
  };
  const success = (summary, detail, life) => {
    show({
      severity: "success",
      summary,
      detail,
      life
    });
  };
  const info = (summary, detail, life) => {
    show({
      severity: "info",
      summary,
      detail,
      life
    });
  };
  const warn = (summary, detail, life) => {
    show({
      severity: "warn",
      summary,
      detail,
      life
    });
  };
  const error = (summary, detail, life) => {
    show({
      severity: "error",
      summary,
      detail,
      life: life || 8e3
      // Ошибки показываем дольше
    });
  };
  const clear = (group) => {
    toast.removeAllGroups();
  };
  const remove = (message) => {
    toast.remove(message);
  };
  const showSaveSuccess = (entityName = "Запись") => {
    success("Сохранено", `${entityName} успешно сохранена`);
  };
  const showDeleteSuccess = (entityName = "Запись") => {
    success("Удалено", `${entityName} успешно удалена`);
  };
  const showSaveError = (entityName = "Запись", errorMessage) => {
    error(
      "Ошибка сохранения",
      errorMessage || `Не удалось сохранить ${entityName.toLowerCase()}`
    );
  };
  const showDeleteError = (entityName = "Запись", errorMessage) => {
    error(
      "Ошибка удаления",
      errorMessage || `Не удалось удалить ${entityName.toLowerCase()}`
    );
  };
  const showLoadError = (entityName = "Данные", errorMessage) => {
    error(
      "Ошибка загрузки",
      errorMessage || `Не удалось загрузить ${entityName.toLowerCase()}`
    );
  };
  const showValidationError = (message = "Проверьте правильность заполнения полей") => {
    warn("Ошибка валидации", message);
  };
  const showNetworkError = () => {
    error(
      "Ошибка сети",
      "Проверьте подключение к интернету и попробуйте снова"
    );
  };
  const showUnauthorizedError = () => {
    error(
      "Нет доступа",
      "У вас недостаточно прав для выполнения этого действия"
    );
  };
  return {
    // Базовые методы
    show,
    success,
    info,
    warn,
    error,
    clear,
    remove,
    // Удобные методы
    showSaveSuccess,
    showDeleteSuccess,
    showSaveError,
    showDeleteError,
    showLoadError,
    showValidationError,
    showNetworkError,
    showUnauthorizedError
  };
};

const globalErrors = ref([]);
const maxErrors = 50;
const useErrorHandler = () => {
  const toast = useToast();
  const generateErrorId = () => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };
  const createError = (code, message, details, field) => {
    return {
      id: generateErrorId(),
      code,
      message,
      details,
      field,
      timestamp: /* @__PURE__ */ new Date()
    };
  };
  const addToGlobalErrors = (error) => {
    globalErrors.value.unshift(error);
    if (globalErrors.value.length > maxErrors) {
      globalErrors.value = globalErrors.value.slice(0, maxErrors);
    }
  };
  const handleTRPCError = (err, showToast = true) => {
    console.error("tRPC Error:", err);
    const code = err?.data?.code || err?.shape?.code || err?.name || "UNKNOWN_ERROR";
    const zodErrors = err?.data?.zodError?.fieldErrors || err?.zodError?.fieldErrors;
    let message = err?.message;
    if (!message) {
      switch (code) {
        case "UNAUTHORIZED":
          message = "Требуется авторизация";
          break;
        case "FORBIDDEN":
          message = "Недостаточно прав для выполнения операции";
          break;
        case "NOT_FOUND":
          message = "Ресурс не найден";
          break;
        case "BAD_REQUEST":
          message = "Некорректный запрос";
          break;
        case "CONFLICT":
          message = "Конфликт данных";
          break;
        case "PRECONDITION_FAILED":
          message = "Нарушены условия выполнения операции";
          break;
        case "INTERNAL_SERVER_ERROR":
          message = "Внутренняя ошибка сервера";
          break;
        case "TIMEOUT":
          message = "Превышено время ожидания";
          break;
        default:
          message = "Произошла ошибка при выполнении запроса";
      }
    }
    if (zodErrors && typeof zodErrors === "object") {
      const validationDetails = Object.entries(zodErrors).flatMap(
        ([field, issues]) => (Array.isArray(issues) ? issues : [issues]).filter(Boolean).map((issue) => `${field}: ${issue}`)
      ).slice(0, 5).join("\n");
      if (validationDetails) {
        message = `Ошибка валидации:
${validationDetails}`;
      }
    }
    const error = {
      ...createError(code, message, err),
      trpcCode: code,
      zodErrors
    };
    addToGlobalErrors(error);
    if (showToast) {
      toast.error("Ошибка", message);
    }
    return error;
  };
  const handleNetworkError = (err, url, showToast = true) => {
    console.error("Network Error:", err);
    const status = err?.status || err?.response?.status || 0;
    const message = err?.message || "Ошибка сети";
    const retryable = status >= 500 || status === 0 || status === 408 || status === 429;
    const error = {
      ...createError("NETWORK_ERROR", message, err),
      status,
      retryable,
      url
    };
    addToGlobalErrors(error);
    if (showToast) {
      if (status === 0) {
        toast.error("Ошибка сети", "Проверьте подключение к интернету");
      } else if (status >= 500) {
        toast.error("Ошибка сервера", "Попробуйте повторить запрос позже");
      } else {
        toast.error("Ошибка сети", message);
      }
    }
    return error;
  };
  const handleValidationError = (field, value, constraint, showToast = true) => {
    const message = `Поле "${field}": ${constraint}`;
    const error = {
      ...createError("VALIDATION_ERROR", message, { value, constraint }, field),
      field,
      value,
      constraint
    };
    addToGlobalErrors(error);
    if (showToast) {
      toast.warn("Ошибка валидации", message);
    }
    return error;
  };
  const handleGenericError = (err, context, showToast = true) => {
    console.error("Generic Error:", err);
    const message = err?.message || "Произошла неизвестная ошибка";
    const code = err?.code || err?.name || "GENERIC_ERROR";
    const error = createError(
      code,
      context ? `${context}: ${message}` : message,
      err
    );
    addToGlobalErrors(error);
    if (showToast) {
      toast.error("Ошибка", error.message);
    }
    return error;
  };
  const handleError = (err, options = {}) => {
    const { context, showToast = true, url } = options;
    if (err?.data?.code || err?.shape?.code) {
      return handleTRPCError(err, showToast);
    }
    if (err?.status || err?.response?.status) {
      return handleNetworkError(err, url, showToast);
    }
    return handleGenericError(err, context, showToast);
  };
  const clearErrors = () => {
    globalErrors.value = [];
  };
  const removeError = (errorId) => {
    const index = globalErrors.value.findIndex((err) => err.id === errorId);
    if (index !== -1) {
      globalErrors.value.splice(index, 1);
    }
  };
  const getErrorsByType = (code) => {
    return globalErrors.value.filter((err) => err.code === code);
  };
  const getRecentErrors = (count = 10) => {
    return globalErrors.value.slice(0, count);
  };
  const hasCriticalErrors = computed(() => {
    const criticalCodes = ["INTERNAL_SERVER_ERROR", "UNAUTHORIZED", "FORBIDDEN"];
    return globalErrors.value.some((err) => criticalCodes.includes(err.code));
  });
  const errorStats = computed(() => {
    const stats = {};
    globalErrors.value.forEach((err) => {
      stats[err.code] = (stats[err.code] || 0) + 1;
    });
    return stats;
  });
  const showSaveError = (entityName = "Запись", error) => {
    if (error) {
      handleError(error, { context: `Сохранение ${entityName.toLowerCase()}` });
    } else {
      toast.error("Ошибка сохранения", `Не удалось сохранить ${entityName.toLowerCase()}`);
    }
  };
  const showDeleteError = (entityName = "Запись", error) => {
    if (error) {
      handleError(error, { context: `Удаление ${entityName.toLowerCase()}` });
    } else {
      toast.error("Ошибка удаления", `Не удалось удалить ${entityName.toLowerCase()}`);
    }
  };
  const showLoadError = (entityName = "Данные", error) => {
    if (error) {
      handleError(error, { context: `Загрузка ${entityName.toLowerCase()}` });
    } else {
      toast.error("Ошибка загрузки", `Не удалось загрузить ${entityName.toLowerCase()}`);
    }
  };
  return {
    // Состояние
    errors: computed(() => globalErrors.value),
    hasCriticalErrors,
    errorStats,
    // Основные методы обработки
    handleError,
    handleTRPCError,
    handleNetworkError,
    handleValidationError,
    handleGenericError,
    // Управление ошибками
    clearErrors,
    removeError,
    getErrorsByType,
    getRecentErrors,
    // Удобные методы
    showSaveError,
    showDeleteError,
    showLoadError
  };
};
const setupGlobalErrorHandler = () => {
  if (typeof window === "undefined") return;
  const { handleError } = useErrorHandler();
  window.addEventListener("unhandledrejection", (event) => {
    console.error("Unhandled promise rejection:", event.reason);
    handleError(event.reason, { context: "Необработанная ошибка промиса" });
  });
  window.addEventListener("error", (event) => {
    console.error("JavaScript error:", event.error);
    handleError(event.error, { context: "JavaScript ошибка" });
  });
};

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "GlobalErrorHandlerInit",
  setup(__props, { expose: __expose }) {
    __expose();
    onMounted(() => {
      setupGlobalErrorHandler();
    });
    const __returned__ = {};
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<span${ssrRenderAttrs(mergeProps({ style: { "display": "none" } }, _attrs))}></span>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/system/GlobalErrorHandlerInit.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const GlobalErrorHandlerInit = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$ClientRouter = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ClientRouter;
  const { fallback = "animate" } = Astro2.props;
  return renderTemplate`<meta name="astro-view-transitions-enabled" content="true"><meta name="astro-view-transitions-fallback"${addAttribute(fallback, "content")}>${renderScript($$result, "D:/Dev/PARTTEC/parttec3/frontend/node_modules/astro/components/ClientRouter.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/node_modules/astro/components/ClientRouter.astro", void 0);

export { $$ClientRouter as $, GlobalErrorHandlerInit as G, _export_sfc as _, useErrorHandler as a, useToast as u };

import type { CSSProperties, ReactElement } from "react"

import { cn } from "@/lib/utils"

interface ShimmerProps {
  children: ReactElement
  background?: string
  className?: string
  duration?: string
}

export default function Shimmer({
  children,
  background = "linear-gradient(110deg, transparent 40%, rgba(255, 255, 255, 0.5) 50%, transparent 60%)",
  className,
  duration = "3s",
}: ShimmerProps) {
  return (
    <div
      className={cn("relative overflow-hidden rounded-lg bg-[length:250%_100%] bg-no-repeat", className)}
      style={
        {
          backgroundImage: background,
          animation: `shimmer ${duration} infinite`,
        } as CSSProperties
      }
    >
      {children}
    </div>
  )
}

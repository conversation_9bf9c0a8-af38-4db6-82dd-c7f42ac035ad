import { e as createComponent, k as renderComponent, l as renderScript, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Logout = createComponent(async ($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0412\u044B\u0445\u043E\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043C\u044B - PartTec", "showSidebar": false }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="min-h-[60vh] flex items-center justify-center"> <div class="max-w-md w-full text-center"> <!-- Иконка загрузки --> <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-primary-100 mb-6"> <svg class="animate-spin h-12 w-12 text-primary-600" fill="none" viewBox="0 0 24 24"> <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle> <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path> </svg> </div> <!-- Сообщение --> <h1 class="text-2xl font-bold text-surface-900 mb-4">
Выход из системы...
</h1> <p class="text-surface-600 mb-8">
Пожалуйста, подождите, мы завершаем вашу сессию.
</p> <!-- Ссылка на вход (на случай, если JS не работает) --> <a href="/admin/login" class="text-primary-600 hover:text-primary-500 text-sm">
Перейти на страницу входа
</a> </div> </div> ` })} ${renderScript($$result, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/logout.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/logout.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/logout.astro";
const $$url = "/admin/logout";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Logout,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

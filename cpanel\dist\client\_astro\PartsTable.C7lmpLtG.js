import{u as h0}from"./useTrpc.spLZjt2f.js";import{u as k0,C as E0}from"./ConfirmDialog.J0sszorU.js";import{u as D0}from"./useToast.pIbuf2bs.js";import{M as F0,u as p0}from"./MatchingDetailsGrid.BT5Doi8q.js";import{t as w0}from"./trpc.BpyaUO08.js";import A0 from"./Card.C4y0_bWr.js";import V0 from"./Button.DrThv2lH.js";import{I as I0}from"./InputText.DOJMNEP-.js";import{D as B0,s as T0}from"./index.BWD5ZO4k.js";import{T as M0}from"./Tag.DTFTku6q.js";import{M as P0}from"./Message.BY1UiDHQ.js";import{D as S0}from"./Dialog.Ct7C9BO5.js";import L0 from"./PartWizard.lhc8Ogic.js";import{V as R0}from"./AutoComplete.rPzROuMW.js";import{S as N0}from"./Select.CQBzSu6y.js";import{V as U0}from"./Textarea.BLEHJ3ym.js";import{I as z0}from"./Icon.By8t0-Wj.js";import{M as H0,a as O0,L as j0,R as G0}from"./MatchingLoadingState.BeWu0CtS.js";import{S as q0}from"./SecondaryButton.DkELYl7Q.js";import{D as W0}from"./DangerButton.Du4QYdLH.js";import Q0 from"./Toast.DmmKUJB6.js";import{_ as X0}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{P as K0,T as Y0}from"./trash.4HbnIIsp.js";import{P as J0}from"./plus.CiWMw0wk.js";import{d as Z0,c as d,a as e,e as o,g as b,b as x,w as l,i as $0,o as i,f,F as T,r as M}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as c,t as n}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./useErrorHandler.DVDazL16.js";import"./index.J-5Oa3io.js";import"./index.BaVCXmir.js";import"./index.6ykohhwZ.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./createLucideIcon.NtN1-Ts2.js";import"./index.PhWaFJhe.js";import"./ToggleSwitch.9ueDJKWv.js";import"./index.COq_zjeV.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.By2TJOuX.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";import"./index.D4QD70nN.js";import"./AttributeValueInput.BnZ_HprM.js";import"./InputNumber.vgPO18dj.js";import"./Checkbox.C7bFmmzc.js";import"./QuickCreateBrand.C_-iK7cE.js";import"./utils.BL5HZsed.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";/* empty css                       */import"./index.CmzoVUnM.js";const u4=Z0({__name:"PartsTable",setup(e0,{expose:t}){t();const{loading:G,error:u,clearError:P,parts:h,partCategories:a,matching:r,partApplicability:q}=h0(),W=k0(),p=D0(),{getAccuracyLabel:t0,getAccuracySeverity:a0}=p0(),Q=c([]),k=c([]),X=c(0),w=c(25),E=c(0),s0=c([]),S=c({}),L=c(""),D=c(null),A=c([]),R=c(!1),N=c(null),U=c(!1),C=c(null),z=c(!1),V=c(null),I=c(!1),_=c(null),H=c(!1),g=c({accuracy:"EXACT_MATCH",notes:""}),o0=[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требуется модификация",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],O=c("createdAt"),j=c(-1),v=async()=>{try{P();const s={skip:E.value*w.value,take:w.value,orderBy:{[O.value]:j.value===1?"asc":"desc"},include:{image:!0,partCategory:!0,attributes:!0,applicabilities:{include:{catalogItem:{include:{brand:!0}}}},equipmentApplicabilities:{include:{equipmentModel:{include:{brand:!0}}}}}};if(L.value.trim()&&(s.where={...s.where,name:{contains:L.value.trim(),mode:"insensitive"}}),D.value){const y=typeof D.value=="object"?D.value.id:D.value;s.where={...s.where,partCategoryId:y}}const m=await h.findMany(s);m&&(Q.value=m);const F=await h.findMany({where:s.where,select:{id:!0}});F&&(X.value=F.length)}catch(s){console.error("Ошибка загрузки запчастей:",s)}},K=async()=>{try{const s=await a.findMany({orderBy:{name:"asc"}});s&&(k.value=s)}catch(s){console.error("Ошибка загрузки категорий:",s)}},Y=async s=>{try{const m=await w0.partAttributes.findByPartId.query({partId:s});m&&(S.value[s]=m)}catch(m){console.error("Ошибка загрузки атрибутов запчасти:",m)}},r0=s=>{E.value=s.page,w.value=s.rows,v()},l0=s=>{O.value=s.sortField,j.value=s.sortOrder,v()},i0=s=>{const m=s.data.id;S.value[m]||Y(m)},n0=()=>{v()},d0=()=>{E.value=0,v()};let B;const c0=()=>{clearTimeout(B),B=setTimeout(()=>{E.value=0,v()},500)},m0=s=>{N.value=s,R.value=!0},f0=s=>{W.confirmDelete("запчасть",async()=>{try{await h.delete({where:{id:s.id}}),v()}catch{p.error("Ошибка","Не удалось удалить запчасть",5e3)}})},g0=s=>{v(),N.value=null,R.value=!1},v0=s=>{C.value=s,U.value=!0,J()},J=async()=>{if(C.value){z.value=!0,V.value=null;try{const s=await r.findMatchingCatalogItems({partId:C.value.id});V.value=s?s.candidates||[]:[]}catch(s){console.error("Ошибка подбора для Part:",s),V.value=[]}finally{z.value=!1}}},x0=s=>{_.value=s,g.value.accuracy=s.accuracySuggestion,g.value.notes="";const m=(s.details||[]).find(y=>String(y.kind).includes("NEAR")||String(y.kind).includes("LEGACY"));m?.notes&&(g.value.notes=m.notes),(s.details||[]).find(y=>y.kind==="NUMBER_WITHIN_TOLERANCE")&&!g.value.notes&&(g.value.notes="Совпадение по допуску"),I.value=!0},Z=()=>{I.value=!1,_.value=null,g.value.accuracy="EXACT_MATCH",g.value.notes=""},C0=async()=>{if(!(!C.value||!_.value)){H.value=!0;try{await q.upsert({where:{partId_catalogItemId:{partId:C.value.id,catalogItemId:_.value.catalogItem.id}},create:{partId:C.value.id,catalogItemId:_.value.catalogItem.id,accuracy:g.value.accuracy,notes:g.value.notes||void 0},update:{accuracy:g.value.accuracy,notes:g.value.notes||void 0}}),U.value=!1,I.value=!1,p.success("Успешно","Позиция привязана к группе"),v(),Z()}catch(s){console.error("Ошибка привязки:",s),p.error("Ошибка","Не удалось привязать позицию")}finally{H.value=!1}}},_0=s=>new Date(s).toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric"}),y0=s=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[s]||s,b0=s=>{const m=s.query.toLowerCase();m?A.value=k.value.filter(F=>F.name.toLowerCase().includes(m)):A.value=k.value},$=()=>{A.value=k.value};$0(()=>{K(),v(),$()});const u0={loading:G,error:u,clearError:P,partsApi:h,partCategories:a,matching:r,partApplicability:q,confirm:W,toast:p,getAccuracyLabel:t0,getAccuracySeverity:a0,parts:Q,categories:k,totalCount:X,pageSize:w,currentPage:E,expandedRows:s0,partAttributes:S,searchQuery:L,selectedCategory:D,categorySuggestions:A,editDialogVisible:R,selectedPartForEdit:N,matchingDialogVisible:U,selectedPartForMatching:C,matchingLoading:z,matchingResults:V,showLinkConfirmDialog:I,selectedLinkCandidate:_,linking:H,linkConfirmForm:g,accuracyOptions:o0,sortField:O,sortOrder:j,loadParts:v,loadCategories:K,loadPartAttributes:Y,onPageChange:r0,onSort:l0,onRowExpand:i0,refreshData:n0,applyFilters:d0,get searchTimeout(){return B},set searchTimeout(s){B=s},debouncedSearch:c0,editPart:m0,deletePart:f0,onPartSaved:g0,openMatching:v0,runPartMatching:J,openLinkConfirmDialog:x0,closeLinkConfirmDialog:Z,confirmLinkItem:C0,formatDate:_0,getUnitLabel:y0,filterCategories:b0,initializeCategorySuggestions:$,VCard:A0,VButton:V0,VInputText:I0,VDataTable:B0,get VColumn(){return T0},VTag:M0,VMessage:P0,VDialog:S0,PartWizard:L0,VConfirmDialog:E0,VAutoComplete:R0,VSelect:N0,VTextarea:U0,Icon:z0,MatchingDetailsGrid:F0,MatchingEmptyState:O0,MatchingLoadingState:H0,get RefreshCcwIcon(){return G0},get PlusIcon(){return J0},SecondaryButton:q0,get LinkIcon(){return j0},get TrashIcon(){return Y0},DangerButton:W0,get PencilIcon(){return K0},Toast:Q0};return Object.defineProperty(u0,"__isScriptSetup",{enumerable:!1,value:!0}),u0}}),e4={class:"space-y-6"},t4={class:"flex items-center justify-between"},a4={class:"flex gap-3"},s4={href:"/admin/parts/create"},o4={class:"p-6"},r4={class:"grid grid-cols-1 gap-4 md:grid-cols-3"},l4={class:"flex-1"},i4={class:"flex items-end"},n4={class:"text-surface-600 dark:text-surface-400 text-sm"},d4={class:"text-surface-900 dark:text-surface-100 font-medium"},c4={class:"text-surface-900 dark:text-surface-100 font-medium"},m4={class:"text-surface-700 dark:text-surface-300 font-mono text-sm"},f4={class:"text-surface-900 dark:text-surface-100 font-medium"},g4=["href"],v4={class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},x4={key:1,class:"text-surface-500 dark:text-surface-400 text-sm"},C4={class:"flex items-center gap-2"},_4={class:"text-surface-600 dark:text-surface-400 text-sm"},y4={class:"flex gap-2"},b4=["href"],h4=["href"],k4={class:"bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4"},E4={class:"grid grid-cols-1 gap-6 lg:grid-cols-2"},D4={class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},F4={key:0,class:"space-y-2"},p4={class:"flex-1"},w4={class:"text-surface-900 dark:text-surface-100 text-sm font-medium"},A4={key:0,class:"text-surface-500 dark:text-surface-400 mt-1 text-sm"},V4={class:"ml-3 text-right"},I4={class:"text-surface-700 dark:text-surface-300 font-medium"},B4={key:0,class:"text-surface-500 dark:text-surface-400 text-sm"},T4={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},M4={class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},P4={key:0,class:"space-y-2"},S4={class:"flex items-start justify-between"},L4={class:"flex-1"},R4={class:"text-surface-900 dark:text-surface-100 font-medium"},N4={class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},U4={key:0,class:"text-surface-500 dark:text-surface-400 mt-1 text-sm"},z4={class:"ml-3"},H4={key:0,class:"bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"},O4={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},j4={class:"lg:col-span-2"},G4={key:0,class:"grid grid-cols-1 gap-3 md:grid-cols-2"},q4={class:"flex items-start justify-between"},W4={class:"flex-1"},Q4={class:"text-surface-900 dark:text-surface-100 font-medium"},X4={key:0,class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},K4={class:"ml-3"},Y4={key:0,class:"bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"},J4={key:1,class:"text-surface-500 dark:text-surface-400 py-6 text-center"},Z4={class:"p-4"},$4={class:"mb-4 flex items-center justify-between"},uu={class:"font-semibold"},eu={key:2,class:"space-y-3"},tu={class:"grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3"},au={class:"md:col-span-1"},su={class:"font-mono font-semibold"},ou={class:"text-surface-500"},ru={class:"mt-2"},lu={class:"mt-3"},iu={class:"md:col-span-2"},nu={key:0,class:"space-y-4"},du={class:"bg-surface-50 dark:bg-surface-900 grid grid-cols-1 gap-4 rounded p-4 md:grid-cols-2"},cu={class:"font-semibold"},mu={class:"font-semibold"},fu={class:"text-sm"},gu={class:"space-y-3"},vu={class:"flex justify-between"};function xu(e0,t,G,u,P,h){return i(),d("div",e4,[e("div",t4,[t[10]||(t[10]=e("div",null,[e("h2",{class:"text-surface-900 dark:text-surface-0 text-xl font-semibold"},"Запчасти"),e("p",{class:"text-surface-600 dark:text-surface-400 mt-1 text-sm"},"Управление группами взаимозаменяемости")],-1)),e("div",a4,[o(u.SecondaryButton,{onClick:u.refreshData,disabled:u.loading,outlined:""},{default:l(()=>[t[8]||(t[8]=f(" Обновить ")),o(u.RefreshCcwIcon,{class:"h-4 w-4"})]),_:1,__:[8]},8,["disabled"]),e("a",s4,[o(u.VButton,{outlined:""},{default:l(()=>[t[9]||(t[9]=f(" Создать ")),o(u.PlusIcon)]),_:1,__:[9]})])])]),o(u.VCard,null,{content:l(()=>[e("div",o4,[e("div",r4,[e("div",null,[t[11]||(t[11]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Поиск по названию ",-1)),o(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":t[0]||(t[0]=a=>u.searchQuery=a),placeholder:"Введите название запчасти...",class:"w-full",onInput:u.debouncedSearch},null,8,["modelValue"])]),e("div",l4,[t[12]||(t[12]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Фильтр по категории ",-1)),o(u.VAutoComplete,{modelValue:u.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=a=>u.selectedCategory=a),suggestions:u.categorySuggestions,onComplete:u.filterCategories,"option-label":"name","option-value":"id",placeholder:"Поиск категории...",class:"w-full",dropdown:"","show-clear":"",onChange:u.applyFilters},null,8,["modelValue","suggestions"])]),e("div",i4,[e("div",n4,[e("div",null,[t[13]||(t[13]=f(" Всего запчастей: ")),e("span",d4,n(u.totalCount),1)]),e("div",null,[t[14]||(t[14]=f(" Показано: ")),e("span",c4,n(u.parts.length),1)])])])])])]),_:1}),o(u.VCard,null,{content:l(()=>[o(u.VDataTable,{value:u.parts,loading:u.loading,paginator:"",rows:u.pageSize,"total-records":u.totalCount,"rows-per-page-options":[10,25,50],lazy:"",onPage:u.onPageChange,onSort:u.onSort,"table-style":"min-width: 50rem",class:"p-datatable-sm","striped-rows":"",expandedRows:u.expandedRows,"onUpdate:expandedRows":t[2]||(t[2]=a=>u.expandedRows=a),onRowExpand:u.onRowExpand},{expansion:l(({data:a})=>[e("div",k4,[e("div",E4,[e("div",null,[e("h4",D4,[o(u.Icon,{name:"pi pi-list",class:"h-4 w-4 text-blue-600"}),t[16]||(t[16]=f(" Атрибуты запчасти "))]),u.partAttributes[a.id]?.length>0?(i(),d("div",F4,[(i(!0),d(T,null,M(u.partAttributes[a.id],r=>(i(),d("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"},[e("div",p4,[e("div",w4,n(r.template?.title||"Без названия"),1),r.description?(i(),d("div",A4,n(r.description),1)):x("",!0)]),e("div",V4,[e("div",I4,n(r.value||"Не указано"),1),r.unit?(i(),d("div",B4,n(u.getUnitLabel(r.unit)),1)):x("",!0)])]))),128))])):(i(),d("div",T4,[o(u.Icon,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[17]||(t[17]=f(" Атрибуты не заданы "))]))]),e("div",null,[e("h4",M4,[o(u.Icon,{name:"pi pi-box",class:"h-4 w-4 text-green-600"}),t[18]||(t[18]=f(" Каталожные позиции "))]),a.applicabilities?.length>0?(i(),d("div",P4,[(i(!0),d(T,null,M(a.applicabilities,r=>(i(),d("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"},[e("div",S4,[e("div",L4,[e("div",R4,n(r.catalogItem?.sku||"N/A"),1),e("div",N4,n(r.catalogItem?.brand?.name||"Неизвестный бренд"),1),r.catalogItem?.description?(i(),d("div",U4,n(r.catalogItem.description),1)):x("",!0)]),e("div",z4,[o(u.VTag,{severity:u.getAccuracySeverity(r.accuracy),class:"text-sm"},{default:l(()=>[f(n(u.getAccuracyLabel(r.accuracy)),1)]),_:2},1032,["severity"])])]),r.notes?(i(),d("div",H4,[o(u.Icon,{name:"pi pi-info-circle",class:"mr-1 inline-block h-4 w-4"}),f(" "+n(r.notes),1)])):x("",!0)]))),128))])):(i(),d("div",O4,[o(u.Icon,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[19]||(t[19]=f(" Каталожные позиции не добавлены "))]))]),e("div",j4,[t[22]||(t[22]=e("h4",{class:"text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"},"Применимость к технике",-1)),a.equipmentApplicabilities?.length>0?(i(),d("div",G4,[(i(!0),d(T,null,M(a.equipmentApplicabilities,r=>(i(),d("div",{key:r.id,class:"bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"},[e("div",q4,[e("div",W4,[e("div",Q4,n(r.equipmentModel?.name||"N/A"),1),r.equipmentModel?.brand?(i(),d("div",X4,n(r.equipmentModel.brand.name),1)):x("",!0)]),e("div",K4,[o(u.VTag,{severity:"info",class:"text-sm"},{default:l(()=>t[20]||(t[20]=[f(" Техника ")])),_:1,__:[20]})])]),r.notes?(i(),d("div",Y4,[o(u.Icon,{name:"pi pi-info-circle",class:"mr-1 inline-block h-4 w-4"}),f(" "+n(r.notes),1)])):x("",!0)]))),128))])):(i(),d("div",J4,[o(u.Icon,{name:"pi pi-info-circle",class:"mb-2 inline-block text-2xl"}),t[21]||(t[21]=f(" Применимость к технике не указана "))]))])])])]),default:l(()=>[o(u.VColumn,{expander:"",style:{width:"50px"}}),o(u.VColumn,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:l(({data:a})=>[e("span",m4,"#"+n(a.id),1)]),_:1}),o(u.VColumn,{field:"name",header:"Название",sortable:"",style:{width:"30%"}},{body:l(({data:a})=>[e("div",null,[e("div",f4,[e("a",{href:`/admin/parts/${a.id}`,class:"hover:underline text-primary"},n(a.name||"Без названия"),9,g4)]),e("div",v4,"Уровень: "+n(a.level)+" | Путь: "+n(a.path),1)])]),_:1}),o(u.VColumn,{field:"partCategory.name",header:"Категория",style:{width:"20%"}},{body:l(({data:a})=>[a.partCategory?(i(),b(u.VTag,{key:0,severity:"info",class:"text-sm"},{default:l(()=>[f(n(a.partCategory.name),1)]),_:2},1024)):(i(),d("span",x4,"Не указана"))]),_:1}),o(u.VColumn,{header:"Детали",style:{width:"20%"}},{body:l(({data:a})=>[e("div",C4,[o(u.VTag,{severity:"secondary",class:"text-sm"},{default:l(()=>[f(n(u.partAttributes[a.id]?.length||a.attributes?.length||0)+" атр. ",1)]),_:2},1024),o(u.VTag,{severity:"success",class:"text-sm"},{default:l(()=>[f(n(a.applicabilities?.length||0)+" поз. ",1)]),_:2},1024)])]),_:1}),o(u.VColumn,{field:"createdAt",header:"Создано",sortable:"",style:{width:"120px"}},{body:l(({data:a})=>[e("span",_4,n(u.formatDate(a.createdAt)),1)]),_:1}),o(u.VColumn,{header:"Действия",style:{width:"140px"}},{body:l(({data:a})=>[e("div",y4,[e("a",{href:`/admin/parts/${a.id}`},[o(u.VButton,{size:"small",outlined:""},{default:l(()=>[o(u.Icon,{name:"pi pi-eye",class:"h-4 w-4"})]),_:1})],8,b4),e("a",{href:`/admin/parts/${a.id}/edit`},[o(u.VButton,{outlined:""},{default:l(()=>[o(u.PencilIcon,{class:"h-4 w-4"})]),_:1})],8,h4),o(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:r=>u.openMatching(a)},{default:l(()=>[t[15]||(t[15]=f(" Подобрать ")),o(u.LinkIcon,{class:"h-4 w-4"})]),_:2,__:[15]},1032,["onClick"]),o(u.DangerButton,{size:"small",outlined:"",onClick:r=>u.deletePart(a)},{default:l(()=>[o(u.TrashIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading","rows","total-records","expandedRows"])]),_:1}),u.error?(i(),b(u.VMessage,{key:0,severity:"error",class:"mt-4"},{default:l(()=>[f(n(u.error),1)]),_:1})):x("",!0),o(u.VDialog,{visible:u.editDialogVisible,"onUpdate:visible":t[3]||(t[3]=a=>u.editDialogVisible=a),modal:"",header:"Редактировать запчасть",class:""},{default:l(()=>[u.selectedPartForEdit?(i(),b(u.PartWizard,{key:0,part:u.selectedPartForEdit,mode:"edit",onUpdated:u.onPartSaved},null,8,["part"])):x("",!0)]),_:1},8,["visible"]),o(u.VDialog,{visible:u.matchingDialogVisible,"onUpdate:visible":t[4]||(t[4]=a=>u.matchingDialogVisible=a),modal:"",header:"Подбор каталожных позиций",class:"w-full max-w-4xl"},{default:l(()=>[e("div",Z4,[e("div",$4,[t[23]||(t[23]=e("div",{class:"text-surface-500 text-sm"},"Группа",-1)),e("div",uu,n(u.selectedPartForMatching?.name||"#"+u.selectedPartForMatching?.id),1),o(u.VButton,{severity:"secondary",outlined:"",size:"small",loading:u.matchingLoading,onClick:u.runPartMatching},{default:l(()=>[o(u.RefreshCcwIcon)]),_:1},8,["loading"])]),u.matchingLoading?(i(),b(u.MatchingLoadingState,{key:0,paddingClass:"py-8"})):!u.matchingResults||u.matchingResults.length===0?(i(),b(u.MatchingEmptyState,{key:1})):(i(),d("div",eu,[(i(!0),d(T,null,M(u.matchingResults,a=>(i(),b(u.VCard,{key:a.catalogItem.id,class:"border"},{content:l(()=>[e("div",tu,[e("div",au,[e("div",su,n(a.catalogItem.sku),1),e("div",ou,n(a.catalogItem.brand?.name),1),e("div",ru,[o(u.VTag,{value:u.getAccuracyLabel(a.accuracySuggestion),severity:u.getAccuracySeverity(a.accuracySuggestion)},null,8,["value","severity"])]),e("div",lu,[o(u.VButton,{size:"small",label:"Привязать",onClick:r=>u.openLinkConfirmDialog(a)},{icon:l(()=>[o(u.Icon,{name:"pi pi-link",class:"h-5 w-5"})]),_:2},1032,["onClick"])])]),e("div",iu,[t[24]||(t[24]=e("div",{class:"text-surface-500 mb-2 text-sm"},"Детали сопоставления",-1)),o(u.MatchingDetailsGrid,{details:a.details},null,8,["details"])])])]),_:2},1024))),128))]))])]),_:1},8,["visible"]),o(u.VDialog,{visible:u.showLinkConfirmDialog,"onUpdate:visible":t[7]||(t[7]=a=>u.showLinkConfirmDialog=a),modal:"",header:"Подтверждение привязки",class:"w-full max-w-3xl"},{footer:l(()=>[e("div",vu,[o(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.closeLinkConfirmDialog}),o(u.VButton,{label:"Создать связь",severity:"success",onClick:u.confirmLinkItem,loading:u.linking},null,8,["loading"])])]),default:l(()=>[u.selectedLinkCandidate?(i(),d("div",nu,[e("div",du,[e("div",null,[t[25]||(t[25]=e("div",{class:"text-surface-500 text-sm"},"Группа взаимозаменяемости",-1)),e("div",cu,n(u.selectedPartForMatching?.name||`Группа #${u.selectedPartForMatching?.id}`),1)]),e("div",null,[t[26]||(t[26]=e("div",{class:"text-surface-500 text-sm"},"Каталожная позиция",-1)),e("div",mu,n(u.selectedLinkCandidate.catalogItem.sku),1),e("div",fu,n(u.selectedLinkCandidate.catalogItem.brand?.name),1)])]),e("div",null,[t[27]||(t[27]=e("h3",{class:"mb-3 text-lg font-semibold"},"Детали сопоставления",-1)),o(u.MatchingDetailsGrid,{details:u.selectedLinkCandidate.details},null,8,["details"])]),e("div",gu,[e("div",null,[t[28]||(t[28]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Точность совпадения ",-1)),o(u.VSelect,{modelValue:u.linkConfirmForm.accuracy,"onUpdate:modelValue":t[5]||(t[5]=a=>u.linkConfirmForm.accuracy=a),options:u.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue"])]),e("div",null,[t[29]||(t[29]=e("label",{class:"text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"}," Примечания ",-1)),o(u.VTextarea,{modelValue:u.linkConfirmForm.notes,"onUpdate:modelValue":t[6]||(t[6]=a=>u.linkConfirmForm.notes=a),rows:"3",placeholder:"Дополнительная информация о совместимости...",class:"w-full"},null,8,["modelValue"]),t[30]||(t[30]=e("small",{class:"text-surface-500"}," Укажите особенности применения, ограничения или условия замены ",-1))])])])):x("",!0)]),_:1},8,["visible"]),o(u.VConfirmDialog),o(u.Toast)])}const he=X0(u4,[["render",xu]]);export{he as default};

import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { I as Icon, T as Toast, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { createElementBlock, openBlock, mergeProps, createElementVNode, defineComponent, useSSRContext, resolveDirective, withCtx, createVNode, createBlock, createCommentVNode, toDisplayString, Fragment, renderList, withDirectives, ref, computed, watch, onMounted, createTextVNode, reactive } from 'vue';
import { E as ErrorBoundary } from '../../chunks/ErrorBoundary_BKC82unE.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_CjmFMz0m.mjs';
import { u as useUrlParams } from '../../chunks/useUrlParams_CGyErwK-.mjs';
import { u as useConfirm, C as ConfirmDialog } from '../../chunks/ConfirmDialog_D9uxkFze.mjs';
import { _ as _export_sfc, u as useToast } from '../../chunks/ClientRouter_avhRMbqw.mjs';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_BeMdq3W3.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { D as Dialog } from '../../chunks/Dialog_DqmfICId.mjs';
import { D as DataTable, s as script$2 } from '../../chunks/index_CxapvcaX.mjs';
import { T as Tag } from '../../chunks/Tag_B6nH2bAR.mjs';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/progressspinner';
import BaseStyle from '@primevue/core/base/style';
import { LinkIcon, TrashIcon, ScanEyeIcon, PencilIcon, SendIcon, RefreshCcwIcon, PlusIcon, SearchIcon } from 'lucide-vue-next';
import { ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrRenderList, ssrGetDirectiveProps, ssrRenderAttrs } from 'vue/server-renderer';
import { V as VTextarea } from '../../chunks/Textarea_nBNQZgaf.mjs';
import { C as Checkbox } from '../../chunks/Checkbox_Ca7GoCvq.mjs';
import { Q as QuickCreateBrand, A as AttributeManager } from '../../chunks/QuickCreateBrand_CEsgsHEq.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import { M as MatchingDetailsGrid, u as useMatchingLabels } from '../../chunks/MatchingDetailsGrid_qP1_u1_F.mjs';
import { M as MatchingLoadingState, a as MatchingEmptyState } from '../../chunks/MatchingLoadingState_CU-YySWT.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

var classes = {
  root: 'p-progressspinner',
  spin: 'p-progressspinner-spin',
  circle: 'p-progressspinner-circle'
};
var ProgressSpinnerStyle = BaseStyle.extend({
  name: 'progressspinner',
  style: style,
  classes: classes
});

var script$1 = {
  name: 'BaseProgressSpinner',
  "extends": BaseComponent,
  props: {
    strokeWidth: {
      type: String,
      "default": '2'
    },
    fill: {
      type: String,
      "default": 'none'
    },
    animationDuration: {
      type: String,
      "default": '2s'
    }
  },
  style: ProgressSpinnerStyle,
  provide: function provide() {
    return {
      $pcProgressSpinner: this,
      $parentInstance: this
    };
  }
};

var script = {
  name: 'ProgressSpinner',
  "extends": script$1,
  inheritAttrs: false,
  computed: {
    svgStyle: function svgStyle() {
      return {
        'animation-duration': this.animationDuration
      };
    }
  }
};

var _hoisted_1 = ["fill", "stroke-width"];
function render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", mergeProps({
    "class": _ctx.cx('root'),
    role: "progressbar"
  }, _ctx.ptmi('root')), [(openBlock(), createElementBlock("svg", mergeProps({
    "class": _ctx.cx('spin'),
    viewBox: "25 25 50 50",
    style: $options.svgStyle
  }, _ctx.ptm('spin')), [createElementVNode("circle", mergeProps({
    "class": _ctx.cx('circle'),
    cx: "50",
    cy: "50",
    r: "20",
    fill: _ctx.fill,
    "stroke-width": _ctx.strokeWidth,
    strokeMiterlimit: "10"
  }, _ctx.ptm('circle')), null, 16, _hoisted_1)], 16))], 16);
}

script.render = render;

const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "CatalogItemsTable",
  props: {
    items: {},
    loading: { type: Boolean },
    totalRecords: {},
    rows: {},
    first: {}
  },
  emits: ["page", "sort", "edit", "delete", "view-details", "match"],
  setup(__props, { expose: __expose }) {
    __expose();
    const formatDate = (dateString) => {
      if (!dateString) return "";
      const date = new Date(dateString);
      return new Intl.DateTimeFormat("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      }).format(date);
    };
    const __returned__ = { formatDate, VCard: Card, VDataTable: DataTable, get VColumn() {
      return script$2;
    }, VButton: Button, VTag: Tag, get VProgressSpinner() {
      return script;
    }, get PencilIcon() {
      return PencilIcon;
    }, get ScanEyeIcon() {
      return ScanEyeIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, Icon, get LinkIcon() {
      return LinkIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$5(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _directive_tooltip = resolveDirective("tooltip");
  _push(ssrRenderComponent($setup["VCard"], _attrs, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VDataTable"], {
          value: $props.items,
          loading: $props.loading,
          paginator: true,
          rows: $props.rows,
          "total-records": $props.totalRecords,
          first: $props.first,
          lazy: true,
          sortable: true,
          "paginator-template": "FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown",
          "current-page-report-template": "\u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E {first} - {last} \u0438\u0437 {totalRecords} \u0437\u0430\u043F\u0438\u0441\u0435\u0439",
          "rows-per-page-options": [10, 20, 50],
          onPage: ($event) => _ctx.$emit("page", $event),
          onSort: ($event) => _ctx.$emit("sort", $event)
        }, {
          empty: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="py-8 text-center"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-inbox",
                class: "text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl"
              }, null, _parent3, _scopeId2));
              _push3(`<p class="text-surface-500 dark:text-surface-400 mb-2 text-lg"${_scopeId2}>\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B</p><p class="text-surface-400 dark:text-surface-500 text-sm"${_scopeId2}>\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u044B \u043F\u043E\u0438\u0441\u043A\u0430 \u0438\u043B\u0438 \u0441\u043E\u0437\u0434\u0430\u0439\u0442\u0435 \u043D\u043E\u0432\u0443\u044E \u043F\u043E\u0437\u0438\u0446\u0438\u044E</p></div>`);
            } else {
              return [
                createVNode("div", { class: "py-8 text-center" }, [
                  createVNode($setup["Icon"], {
                    name: "pi pi-inbox",
                    class: "text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl"
                  }),
                  createVNode("p", { class: "text-surface-500 dark:text-surface-400 mb-2 text-lg" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B"),
                  createVNode("p", { class: "text-surface-400 dark:text-surface-500 text-sm" }, "\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u044B \u043F\u043E\u0438\u0441\u043A\u0430 \u0438\u043B\u0438 \u0441\u043E\u0437\u0434\u0430\u0439\u0442\u0435 \u043D\u043E\u0432\u0443\u044E \u043F\u043E\u0437\u0438\u0446\u0438\u044E")
                ])
              ];
            }
          }),
          loading: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="py-8 text-center"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["VProgressSpinner"], { size: "50" }, null, _parent3, _scopeId2));
              _push3(`<p class="text-surface-500 dark:text-surface-400 mt-4"${_scopeId2}>\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439...</p></div>`);
            } else {
              return [
                createVNode("div", { class: "py-8 text-center" }, [
                  createVNode($setup["VProgressSpinner"], { size: "50" }),
                  createVNode("p", { class: "text-surface-500 dark:text-surface-400 mt-4" }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439...")
                ])
              ];
            }
          }),
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "sku",
                header: "\u0410\u0440\u0442\u0438\u043A\u0443\u043B",
                sortable: true,
                class: "min-w-32"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex items-center gap-2"${_scopeId3}><span class="font-mono font-medium"${_scopeId3}>${ssrInterpolate(data.sku)}</span>`);
                    if (!data.isPublic) {
                      _push4(ssrRenderComponent($setup["VTag"], {
                        value: "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
                        severity: "warning",
                        size: "small"
                      }, null, _parent4, _scopeId3));
                    } else {
                      _push4(`<!---->`);
                    }
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex items-center gap-2" }, [
                        createVNode("span", { class: "font-mono font-medium" }, toDisplayString(data.sku), 1),
                        !data.isPublic ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
                          severity: "warning",
                          size: "small"
                        })) : createCommentVNode("", true)
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "brand.name",
                header: "\u0411\u0440\u0435\u043D\u0434",
                sortable: true,
                class: "min-w-32"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex items-center gap-2"${_scopeId3}><span class="font-medium"${_scopeId3}>${ssrInterpolate(data.brand?.name || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D")}</span>`);
                    if (data.brand?.isOem) {
                      _push4(ssrRenderComponent($setup["VTag"], {
                        value: "OEM",
                        severity: "info",
                        size: "small"
                      }, null, _parent4, _scopeId3));
                    } else {
                      _push4(`<!---->`);
                    }
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex items-center gap-2" }, [
                        createVNode("span", { class: "font-medium" }, toDisplayString(data.brand?.name || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"), 1),
                        data.brand?.isOem ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: "OEM",
                          severity: "info",
                          size: "small"
                        })) : createCommentVNode("", true)
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "description",
                header: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435",
                class: "min-w-48"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="max-w-xs"${_scopeId3}>`);
                    if (data.description) {
                      _push4(`<p class="text-surface-600 dark:text-surface-400 truncate text-sm"${ssrRenderAttr("title", data.description)}${_scopeId3}>${ssrInterpolate(data.description)}</p>`);
                    } else {
                      _push4(`<span class="text-surface-400 italic"${_scopeId3}>\u041D\u0435\u0442 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F</span>`);
                    }
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "max-w-xs" }, [
                        data.description ? (openBlock(), createBlock("p", {
                          key: 0,
                          class: "text-surface-600 dark:text-surface-400 truncate text-sm",
                          title: data.description
                        }, toDisplayString(data.description), 9, ["title"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 italic"
                        }, "\u041D\u0435\u0442 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F"))
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B",
                class: "min-w-32"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex flex-wrap gap-1"${_scopeId3}><!--[-->`);
                    ssrRenderList(data.attributes?.slice(0, 3), (attr) => {
                      _push4(ssrRenderComponent($setup["VTag"], {
                        key: attr.id,
                        value: `${attr.template?.title}: ${attr.value}`,
                        severity: "secondary",
                        size: "small",
                        class: "text-xs"
                      }, null, _parent4, _scopeId3));
                    });
                    _push4(`<!--]-->`);
                    if (data.attributes?.length > 3) {
                      _push4(ssrRenderComponent($setup["VTag"], {
                        value: `+${data.attributes.length - 3}`,
                        severity: "secondary",
                        size: "small",
                        class: "text-xs"
                      }, null, _parent4, _scopeId3));
                    } else {
                      _push4(`<!---->`);
                    }
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex flex-wrap gap-1" }, [
                        (openBlock(true), createBlock(Fragment, null, renderList(data.attributes?.slice(0, 3), (attr) => {
                          return openBlock(), createBlock($setup["VTag"], {
                            key: attr.id,
                            value: `${attr.template?.title}: ${attr.value}`,
                            severity: "secondary",
                            size: "small",
                            class: "text-xs"
                          }, null, 8, ["value"]);
                        }), 128)),
                        data.attributes?.length > 3 ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: `+${data.attributes.length - 3}`,
                          severity: "secondary",
                          size: "small",
                          class: "text-xs"
                        }, null, 8, ["value"])) : createCommentVNode("", true)
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                header: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C",
                class: "min-w-24"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="text-center"${_scopeId3}>`);
                    if (data.applicabilities?.length > 0) {
                      _push4(ssrRenderComponent($setup["VTag"], {
                        value: `${data.applicabilities.length} \u0433\u0440\u0443\u043F\u043F`,
                        severity: "success",
                        size: "small"
                      }, null, _parent4, _scopeId3));
                    } else {
                      _push4(`<span class="text-surface-400 text-sm"${_scopeId3}>\u041D\u0435 \u043D\u0430\u0437\u043D\u0430\u0447\u0435\u043D\u0430</span>`);
                    }
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "text-center" }, [
                        data.applicabilities?.length > 0 ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: `${data.applicabilities.length} \u0433\u0440\u0443\u043F\u043F`,
                          severity: "success",
                          size: "small"
                        }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 text-sm"
                        }, "\u041D\u0435 \u043D\u0430\u0437\u043D\u0430\u0447\u0435\u043D\u0430"))
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "source",
                header: "\u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A",
                class: "min-w-32"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    if (data.source) {
                      _push4(`<span class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId3}>${ssrInterpolate(data.source)}</span>`);
                    } else {
                      _push4(`<span class="text-surface-400 italic"${_scopeId3}>\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D</span>`);
                    }
                  } else {
                    return [
                      data.source ? (openBlock(), createBlock("span", {
                        key: 0,
                        class: "text-surface-600 dark:text-surface-400 text-sm"
                      }, toDisplayString(data.source), 1)) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 italic"
                      }, "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"))
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "id",
                header: "ID",
                sortable: true,
                class: "min-w-20"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<span class="text-surface-600 dark:text-surface-400 font-mono text-sm"${_scopeId3}> #${ssrInterpolate(data.id)}</span>`);
                  } else {
                    return [
                      createVNode("span", { class: "text-surface-600 dark:text-surface-400 font-mono text-sm" }, " #" + toDisplayString(data.id), 1)
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                class: "min-w-32"
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex gap-1"${_scopeId3}>`);
                    _push4(ssrRenderComponent($setup["VButton"], mergeProps({
                      onClick: ($event) => _ctx.$emit("match", data),
                      severity: "secondary",
                      size: "small",
                      text: ""
                    }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u041D\u0430\u0439\u0442\u0438 \u0433\u0440\u0443\u043F\u043F\u0443 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438")), {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["LinkIcon"], { class: "w-5 h-5" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["LinkIcon"], { class: "w-5 h-5" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("view-details", data),
                      severity: "secondary",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["ScanEyeIcon"], { class: "w-5 h-5" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["ScanEyeIcon"], { class: "w-5 h-5" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("edit", data),
                      severity: "secondary",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["PencilIcon"], { class: "w-5 h-5" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("delete", data),
                      severity: "danger",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["TrashIcon"], { class: "w-5 h-5" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex gap-1" }, [
                        withDirectives((openBlock(), createBlock($setup["VButton"], {
                          onClick: ($event) => _ctx.$emit("match", data),
                          severity: "secondary",
                          size: "small",
                          text: ""
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["LinkIcon"], { class: "w-5 h-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])), [
                          [_directive_tooltip, "\u041D\u0430\u0439\u0442\u0438 \u0433\u0440\u0443\u043F\u043F\u0443 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438"]
                        ]),
                        createVNode($setup["VButton"], {
                          onClick: ($event) => _ctx.$emit("view-details", data),
                          severity: "secondary",
                          size: "small",
                          text: ""
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["ScanEyeIcon"], { class: "w-5 h-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["VButton"], {
                          onClick: ($event) => _ctx.$emit("edit", data),
                          severity: "secondary",
                          size: "small",
                          text: ""
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["VButton"], {
                          onClick: ($event) => _ctx.$emit("delete", data),
                          severity: "danger",
                          size: "small",
                          text: ""
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VColumn"], {
                  field: "sku",
                  header: "\u0410\u0440\u0442\u0438\u043A\u0443\u043B",
                  sortable: true,
                  class: "min-w-32"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex items-center gap-2" }, [
                      createVNode("span", { class: "font-mono font-medium" }, toDisplayString(data.sku), 1),
                      !data.isPublic ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
                        severity: "warning",
                        size: "small"
                      })) : createCommentVNode("", true)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "brand.name",
                  header: "\u0411\u0440\u0435\u043D\u0434",
                  sortable: true,
                  class: "min-w-32"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex items-center gap-2" }, [
                      createVNode("span", { class: "font-medium" }, toDisplayString(data.brand?.name || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"), 1),
                      data.brand?.isOem ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: "OEM",
                        severity: "info",
                        size: "small"
                      })) : createCommentVNode("", true)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "description",
                  header: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435",
                  class: "min-w-48"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "max-w-xs" }, [
                      data.description ? (openBlock(), createBlock("p", {
                        key: 0,
                        class: "text-surface-600 dark:text-surface-400 truncate text-sm",
                        title: data.description
                      }, toDisplayString(data.description), 9, ["title"])) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 italic"
                      }, "\u041D\u0435\u0442 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F"))
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B",
                  class: "min-w-32"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex flex-wrap gap-1" }, [
                      (openBlock(true), createBlock(Fragment, null, renderList(data.attributes?.slice(0, 3), (attr) => {
                        return openBlock(), createBlock($setup["VTag"], {
                          key: attr.id,
                          value: `${attr.template?.title}: ${attr.value}`,
                          severity: "secondary",
                          size: "small",
                          class: "text-xs"
                        }, null, 8, ["value"]);
                      }), 128)),
                      data.attributes?.length > 3 ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: `+${data.attributes.length - 3}`,
                        severity: "secondary",
                        size: "small",
                        class: "text-xs"
                      }, null, 8, ["value"])) : createCommentVNode("", true)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  header: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C",
                  class: "min-w-24"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "text-center" }, [
                      data.applicabilities?.length > 0 ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: `${data.applicabilities.length} \u0433\u0440\u0443\u043F\u043F`,
                        severity: "success",
                        size: "small"
                      }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 text-sm"
                      }, "\u041D\u0435 \u043D\u0430\u0437\u043D\u0430\u0447\u0435\u043D\u0430"))
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "source",
                  header: "\u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A",
                  class: "min-w-32"
                }, {
                  body: withCtx(({ data }) => [
                    data.source ? (openBlock(), createBlock("span", {
                      key: 0,
                      class: "text-surface-600 dark:text-surface-400 text-sm"
                    }, toDisplayString(data.source), 1)) : (openBlock(), createBlock("span", {
                      key: 1,
                      class: "text-surface-400 italic"
                    }, "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"))
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "id",
                  header: "ID",
                  sortable: true,
                  class: "min-w-20"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("span", { class: "text-surface-600 dark:text-surface-400 font-mono text-sm" }, " #" + toDisplayString(data.id), 1)
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                  class: "min-w-32"
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex gap-1" }, [
                      withDirectives((openBlock(), createBlock($setup["VButton"], {
                        onClick: ($event) => _ctx.$emit("match", data),
                        severity: "secondary",
                        size: "small",
                        text: ""
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["LinkIcon"], { class: "w-5 h-5" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])), [
                        [_directive_tooltip, "\u041D\u0430\u0439\u0442\u0438 \u0433\u0440\u0443\u043F\u043F\u0443 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438"]
                      ]),
                      createVNode($setup["VButton"], {
                        onClick: ($event) => _ctx.$emit("view-details", data),
                        severity: "secondary",
                        size: "small",
                        text: ""
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["ScanEyeIcon"], { class: "w-5 h-5" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        onClick: ($event) => _ctx.$emit("edit", data),
                        severity: "secondary",
                        size: "small",
                        text: ""
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        onClick: ($event) => _ctx.$emit("delete", data),
                        severity: "danger",
                        size: "small",
                        text: ""
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])
                    ])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VDataTable"], {
            value: $props.items,
            loading: $props.loading,
            paginator: true,
            rows: $props.rows,
            "total-records": $props.totalRecords,
            first: $props.first,
            lazy: true,
            sortable: true,
            "paginator-template": "FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown",
            "current-page-report-template": "\u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E {first} - {last} \u0438\u0437 {totalRecords} \u0437\u0430\u043F\u0438\u0441\u0435\u0439",
            "rows-per-page-options": [10, 20, 50],
            onPage: ($event) => _ctx.$emit("page", $event),
            onSort: ($event) => _ctx.$emit("sort", $event)
          }, {
            empty: withCtx(() => [
              createVNode("div", { class: "py-8 text-center" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-inbox",
                  class: "text-surface-300 dark:text-surface-600 mb-4 inline-block text-4xl"
                }),
                createVNode("p", { class: "text-surface-500 dark:text-surface-400 mb-2 text-lg" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B"),
                createVNode("p", { class: "text-surface-400 dark:text-surface-500 text-sm" }, "\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u043F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u044B \u043F\u043E\u0438\u0441\u043A\u0430 \u0438\u043B\u0438 \u0441\u043E\u0437\u0434\u0430\u0439\u0442\u0435 \u043D\u043E\u0432\u0443\u044E \u043F\u043E\u0437\u0438\u0446\u0438\u044E")
              ])
            ]),
            loading: withCtx(() => [
              createVNode("div", { class: "py-8 text-center" }, [
                createVNode($setup["VProgressSpinner"], { size: "50" }),
                createVNode("p", { class: "text-surface-500 dark:text-surface-400 mt-4" }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439...")
              ])
            ]),
            default: withCtx(() => [
              createVNode($setup["VColumn"], {
                field: "sku",
                header: "\u0410\u0440\u0442\u0438\u043A\u0443\u043B",
                sortable: true,
                class: "min-w-32"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "flex items-center gap-2" }, [
                    createVNode("span", { class: "font-mono font-medium" }, toDisplayString(data.sku), 1),
                    !data.isPublic ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
                      severity: "warning",
                      size: "small"
                    })) : createCommentVNode("", true)
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "brand.name",
                header: "\u0411\u0440\u0435\u043D\u0434",
                sortable: true,
                class: "min-w-32"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "flex items-center gap-2" }, [
                    createVNode("span", { class: "font-medium" }, toDisplayString(data.brand?.name || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"), 1),
                    data.brand?.isOem ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: "OEM",
                      severity: "info",
                      size: "small"
                    })) : createCommentVNode("", true)
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "description",
                header: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435",
                class: "min-w-48"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "max-w-xs" }, [
                    data.description ? (openBlock(), createBlock("p", {
                      key: 0,
                      class: "text-surface-600 dark:text-surface-400 truncate text-sm",
                      title: data.description
                    }, toDisplayString(data.description), 9, ["title"])) : (openBlock(), createBlock("span", {
                      key: 1,
                      class: "text-surface-400 italic"
                    }, "\u041D\u0435\u0442 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F"))
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B",
                class: "min-w-32"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "flex flex-wrap gap-1" }, [
                    (openBlock(true), createBlock(Fragment, null, renderList(data.attributes?.slice(0, 3), (attr) => {
                      return openBlock(), createBlock($setup["VTag"], {
                        key: attr.id,
                        value: `${attr.template?.title}: ${attr.value}`,
                        severity: "secondary",
                        size: "small",
                        class: "text-xs"
                      }, null, 8, ["value"]);
                    }), 128)),
                    data.attributes?.length > 3 ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: `+${data.attributes.length - 3}`,
                      severity: "secondary",
                      size: "small",
                      class: "text-xs"
                    }, null, 8, ["value"])) : createCommentVNode("", true)
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                header: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C",
                class: "min-w-24"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "text-center" }, [
                    data.applicabilities?.length > 0 ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: `${data.applicabilities.length} \u0433\u0440\u0443\u043F\u043F`,
                      severity: "success",
                      size: "small"
                    }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                      key: 1,
                      class: "text-surface-400 text-sm"
                    }, "\u041D\u0435 \u043D\u0430\u0437\u043D\u0430\u0447\u0435\u043D\u0430"))
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "source",
                header: "\u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A",
                class: "min-w-32"
              }, {
                body: withCtx(({ data }) => [
                  data.source ? (openBlock(), createBlock("span", {
                    key: 0,
                    class: "text-surface-600 dark:text-surface-400 text-sm"
                  }, toDisplayString(data.source), 1)) : (openBlock(), createBlock("span", {
                    key: 1,
                    class: "text-surface-400 italic"
                  }, "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"))
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "id",
                header: "ID",
                sortable: true,
                class: "min-w-20"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("span", { class: "text-surface-600 dark:text-surface-400 font-mono text-sm" }, " #" + toDisplayString(data.id), 1)
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                class: "min-w-32"
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "flex gap-1" }, [
                    withDirectives((openBlock(), createBlock($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("match", data),
                      severity: "secondary",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["LinkIcon"], { class: "w-5 h-5" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"])), [
                      [_directive_tooltip, "\u041D\u0430\u0439\u0442\u0438 \u0433\u0440\u0443\u043F\u043F\u0443 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438"]
                    ]),
                    createVNode($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("view-details", data),
                      severity: "secondary",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["ScanEyeIcon"], { class: "w-5 h-5" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"]),
                    createVNode($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("edit", data),
                      severity: "secondary",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"]),
                    createVNode($setup["VButton"], {
                      onClick: ($event) => _ctx.$emit("delete", data),
                      severity: "danger",
                      size: "small",
                      text: ""
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"])
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, ["value", "loading", "rows", "total-records", "first", "onPage", "onSort"])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/CatalogItemsTable.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const CatalogItemsTable = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["ssrRender", _sfc_ssrRender$5]]);

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "CatalogItemForm",
  props: {
    item: {}
  },
  emits: ["save", "cancel"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { brands, loading } = useTrpc();
    const formData = ref({
      sku: "",
      selectedBrand: null,
      description: "",
      source: "",
      isPublic: true,
      attributes: []
    });
    const errors = ref({
      sku: "",
      brandId: ""
    });
    const showAttributeManager = ref(false);
    const showCreateBrand = ref(false);
    const brandSuggestions = ref([]);
    const validateSku = () => {
      errors.value.sku = "";
      if (!formData.value.sku.trim()) {
        errors.value.sku = "\u0410\u0440\u0442\u0438\u043A\u0443\u043B \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u0435\u043D";
        return false;
      }
      if (formData.value.sku.length < 2) {
        errors.value.sku = "\u0410\u0440\u0442\u0438\u043A\u0443\u043B \u0434\u043E\u043B\u0436\u0435\u043D \u0441\u043E\u0434\u0435\u0440\u0436\u0430\u0442\u044C \u043C\u0438\u043D\u0438\u043C\u0443\u043C 2 \u0441\u0438\u043C\u0432\u043E\u043B\u0430";
        return false;
      }
      if (formData.value.sku.length > 64) {
        errors.value.sku = "\u0410\u0440\u0442\u0438\u043A\u0443\u043B \u043D\u0435 \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u0434\u043B\u0438\u043D\u043D\u0435\u0435 64 \u0441\u0438\u043C\u0432\u043E\u043B\u043E\u0432";
        return false;
      }
      return true;
    };
    const validateBrand = () => {
      errors.value.brandId = "";
      if (!formData.value.selectedBrand) {
        errors.value.brandId = "\u0411\u0440\u0435\u043D\u0434 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u0435\u043D";
        return false;
      }
      return true;
    };
    const isFormValid = computed(() => {
      return formData.value.sku.trim() && formData.value.selectedBrand && !errors.value.sku && !errors.value.brandId;
    });
    const searchBrands = async (event) => {
      try {
        const query = event.query.toLowerCase();
        const result = await brands.findMany({
          where: {
            name: { contains: query, mode: "insensitive" }
          },
          take: 10
        });
        if (result) {
          brandSuggestions.value = result;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0431\u0440\u0435\u043D\u0434\u043E\u0432:", err);
      }
    };
    const onBrandCreated = (brand) => {
      formData.value.selectedBrand = brand;
      brandSuggestions.value = [brand, ...brandSuggestions.value];
    };
    const removeAttribute = (index) => {
      formData.value.attributes.splice(index, 1);
    };
    const getUnitLabel = (unit) => {
      const labels = {
        MM: "\u043C\u043C",
        INCH: "\u0434\u044E\u0439\u043C\u044B",
        FT: "\u0444\u0443\u0442\u044B",
        G: "\u0433",
        KG: "\u043A\u0433",
        T: "\u0442",
        LB: "\u0444\u0443\u043D\u0442\u044B",
        ML: "\u043C\u043B",
        L: "\u043B",
        GAL: "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        PCS: "\u0448\u0442",
        SET: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        PAIR: "\u043F\u0430\u0440\u0430",
        BAR: "\u0431\u0430\u0440",
        PSI: "PSI",
        KW: "\u043A\u0412\u0442",
        HP: "\u043B.\u0441.",
        NM: "\u041D\u22C5\u043C",
        RPM: "\u043E\u0431/\u043C\u0438\u043D",
        C: "\xB0C",
        F: "\xB0F",
        PERCENT: "%"
      };
      return labels[unit] || unit;
    };
    const onSubmit = () => {
      const isSkuValid = validateSku();
      const isBrandValid = validateBrand();
      if (!isSkuValid || !isBrandValid) {
        return;
      }
      const saveData = {
        sku: formData.value.sku.toUpperCase().trim(),
        brandId: formData.value.selectedBrand.id,
        description: formData.value.description.trim() || void 0,
        source: formData.value.source.trim() || void 0,
        isPublic: formData.value.isPublic
      };
      const validAttributes = formData.value.attributes ? formData.value.attributes.filter((attr) => {
        const hasValue = attr.value && String(attr.value).trim() !== "";
        const hasTemplateId = attr.templateId || attr.template?.id;
        return hasValue && hasTemplateId;
      }) : [];
      if (validAttributes.length > 0) {
        if (props.item) {
          saveData.attributes = {
            deleteMany: {},
            // Удаляем все существующие атрибуты
            create: validAttributes.map((attr) => ({
              templateId: attr.templateId || attr.template?.id,
              value: attr.value
            }))
          };
        } else {
          saveData.attributes = {
            create: validAttributes.map((attr) => ({
              templateId: attr.templateId || attr.template?.id,
              value: attr.value
            }))
          };
        }
      } else if (props.item) {
        saveData.attributes = {
          deleteMany: {}
        };
      }
      emit("save", saveData);
    };
    const loadItemData = () => {
      if (props.item) {
        formData.value = {
          sku: props.item.sku || "",
          selectedBrand: props.item.brand || null,
          description: props.item.description || "",
          source: props.item.source || "",
          isPublic: props.item.isPublic ?? true,
          attributes: props.item.attributes || []
        };
      }
    };
    watch(() => props.item, loadItemData, { immediate: true });
    onMounted(() => {
      loadItemData();
    });
    const __returned__ = { props, emit, brands, loading, formData, errors, showAttributeManager, showCreateBrand, brandSuggestions, validateSku, validateBrand, isFormValid, searchBrands, onBrandCreated, removeAttribute, getUnitLabel, onSubmit, loadItemData, VInputText: InputText, VTextarea, VAutoComplete, VCheckbox: Checkbox, VButton: Button, VDialog: Dialog, AttributeManager, QuickCreateBrand, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _directive_tooltip = resolveDirective("tooltip");
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-item-form" }, _attrs))}><form class="space-y-6"><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0410\u0440\u0442\u0438\u043A\u0443\u043B (SKU) * </label>`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.formData.sku,
    "onUpdate:modelValue": ($event) => $setup.formData.sku = $event,
    placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: 12345-ABC",
    class: ["w-full", { "p-invalid": $setup.errors.sku }],
    onBlur: $setup.validateSku
  }, null, _parent));
  if ($setup.errors.sku) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.sku)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0411\u0440\u0435\u043D\u0434 * </label><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.formData.selectedBrand,
    "onUpdate:modelValue": ($event) => $setup.formData.selectedBrand = $event,
    suggestions: $setup.brandSuggestions,
    onComplete: $setup.searchBrands,
    "option-label": "name",
    placeholder: "\u041F\u043E\u0438\u0441\u043A \u0431\u0440\u0435\u043D\u0434\u0430...",
    class: ["flex-1", { "p-invalid": $setup.errors.brandId }],
    dropdown: ""
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], mergeProps({
    onClick: ($event) => $setup.showCreateBrand = true,
    severity: "secondary",
    outlined: "",
    size: "small"
  }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u044B\u0439 \u0431\u0440\u0435\u043D\u0434")), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-plus",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-plus",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.errors.brandId) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.brandId)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
  _push(ssrRenderComponent($setup["VTextarea"], {
    modelValue: $setup.formData.description,
    "onUpdate:modelValue": ($event) => $setup.formData.description = $event,
    placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u043E\u0439 \u043F\u043E\u0437\u0438\u0446\u0438\u0438...",
    rows: "3",
    class: "w-full"
  }, null, _parent));
  _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 </label>`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.formData.source,
    "onUpdate:modelValue": ($event) => $setup.formData.source = $event,
    placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u041E\u0444\u0438\u0446\u0438\u0430\u043B\u044C\u043D\u044B\u0439 \u043A\u0430\u0442\u0430\u043B\u043E\u0433, \u0414\u0430\u043D\u043D\u044B\u0435 \u043A\u043B\u0438\u0435\u043D\u0442\u0430",
    class: "w-full"
  }, null, _parent));
  _push(`</div><div class="flex items-center gap-3">`);
  _push(ssrRenderComponent($setup["VCheckbox"], {
    modelValue: $setup.formData.isPublic,
    "onUpdate:modelValue": ($event) => $setup.formData.isPublic = $event,
    "input-id": "isPublic",
    binary: ""
  }, null, _parent));
  _push(`<label for="isPublic" class="text-sm font-medium text-surface-700 dark:text-surface-300"> \u041F\u0443\u0431\u043B\u0438\u0447\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F (\u0432\u0438\u0434\u043D\u0430 \u0432\u0441\u0435\u043C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F\u043C) </label></div><div><div class="flex items-center justify-between mb-4"><h4 class="text-lg font-medium text-surface-900 dark:text-surface-0"> \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B </h4>`);
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => $setup.showAttributeManager = true,
    severity: "secondary",
    outlined: "",
    size: "small",
    label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-plus",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-plus",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.formData.attributes.length > 0) {
    _push(`<div class="space-y-3"><!--[-->`);
    ssrRenderList($setup.formData.attributes, (attribute, index) => {
      _push(`<div class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"><div class="flex-1"><div class="font-medium text-surface-900 dark:text-surface-0">${ssrInterpolate(attribute.template?.title || attribute.templateTitle)} `);
      if (attribute.template?.isRequired) {
        _push(`<span class="text-red-500 ml-1">*</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="text-sm text-surface-600 dark:text-surface-400">${ssrInterpolate(attribute.value)} ${ssrInterpolate(attribute.template?.unit ? $setup.getUnitLabel(attribute.template.unit) : "")} `);
      if (attribute.template?.group?.name) {
        _push(`<span class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs">${ssrInterpolate(attribute.template.group.name)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
      _push(ssrRenderComponent($setup["VButton"], {
        onClick: ($event) => $setup.removeAttribute(index),
        severity: "danger",
        size: "small",
        text: ""
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent($setup["Icon"], {
              name: "pi pi-trash",
              class: "w-5 h-5"
            }, null, _parent2, _scopeId));
          } else {
            return [
              createVNode($setup["Icon"], {
                name: "pi pi-trash",
                class: "w-5 h-5"
              })
            ];
          }
        }),
        _: 2
      }, _parent));
      _push(`</div>`);
    });
    _push(`<!--]--></div>`);
  } else {
    _push(`<div class="text-center py-6 text-surface-500"> \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043D\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u044B </div>`);
  }
  _push(`</div><div class="flex justify-end gap-3 pt-4 border-t border-surface-200 dark:border-surface-700">`);
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => _ctx.$emit("cancel"),
    severity: "secondary",
    outlined: "",
    label: "\u041E\u0442\u043C\u0435\u043D\u0430"
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    type: "submit",
    loading: $setup.loading,
    disabled: !$setup.isFormValid,
    label: $props.item ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E"
  }, null, _parent));
  _push(`</div></form>`);
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showAttributeManager,
    "onUpdate:visible": ($event) => $setup.showAttributeManager = $event,
    modal: "",
    header: "\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430\u043C\u0438",
    class: "w-full max-w-2xl"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["AttributeManager"], {
          modelValue: $setup.formData.attributes,
          "onUpdate:modelValue": ($event) => $setup.formData.attributes = $event,
          onClose: ($event) => $setup.showAttributeManager = false
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["AttributeManager"], {
            modelValue: $setup.formData.attributes,
            "onUpdate:modelValue": ($event) => $setup.formData.attributes = $event,
            onClose: ($event) => $setup.showAttributeManager = false
          }, null, 8, ["modelValue", "onUpdate:modelValue", "onClose"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["QuickCreateBrand"], {
    visible: $setup.showCreateBrand,
    "onUpdate:visible": ($event) => $setup.showCreateBrand = $event,
    onCreated: $setup.onBrandCreated
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/CatalogItemForm.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const CatalogItemForm = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4]]);

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "CatalogItemCard",
  props: {
    item: {}
  },
  emits: ["edit", "close", "match", "unlink"],
  setup(__props, { expose: __expose }) {
    __expose();
    const getUnitLabel = (unit) => {
      const labels = {
        MM: "\u043C\u043C",
        INCH: "\u0434\u044E\u0439\u043C\u044B",
        FT: "\u0444\u0443\u0442\u044B",
        G: "\u0433",
        KG: "\u043A\u0433",
        T: "\u0442",
        LB: "\u0444\u0443\u043D\u0442\u044B",
        ML: "\u043C\u043B",
        L: "\u043B",
        GAL: "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        PCS: "\u0448\u0442",
        SET: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        PAIR: "\u043F\u0430\u0440\u0430",
        BAR: "\u0431\u0430\u0440",
        PSI: "PSI",
        KW: "\u043A\u0412\u0442",
        HP: "\u043B.\u0441.",
        NM: "\u041D\u22C5\u043C",
        RPM: "\u043E\u0431/\u043C\u0438\u043D",
        C: "\xB0C",
        F: "\xB0F",
        PERCENT: "%"
      };
      return labels[unit] || unit;
    };
    const getDataTypeLabel = (dataType) => {
      if (!dataType) return "";
      const labels = {
        STRING: "\u0421\u0442\u0440\u043E\u043A\u0430",
        NUMBER: "\u0427\u0438\u0441\u043B\u043E",
        BOOLEAN: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435",
        DATE: "\u0414\u0430\u0442\u0430",
        JSON: "JSON"
      };
      return labels[dataType] || dataType;
    };
    const getAccuracyLabel = (accuracy) => {
      const labels = {
        EXACT_MATCH: "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435",
        MATCH_WITH_NOTES: "\u0421 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438",
        REQUIRES_MODIFICATION: "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438",
        PARTIAL_MATCH: "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435"
      };
      return labels[accuracy] || accuracy;
    };
    const getAccuracySeverity = (accuracy) => {
      const severities = {
        EXACT_MATCH: "success",
        MATCH_WITH_NOTES: "info",
        REQUIRES_MODIFICATION: "warning",
        PARTIAL_MATCH: "secondary"
      };
      return severities[accuracy] || "secondary";
    };
    const __returned__ = { getUnitLabel, getDataTypeLabel, getAccuracyLabel, getAccuracySeverity, VCard: Card, VButton: Button, VTag: Tag, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _directive_tooltip = resolveDirective("tooltip");
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-item-card" }, _attrs))}><div class="flex items-start justify-between mb-6"><div class="flex items-center gap-4"><div><h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 font-mono">${ssrInterpolate($props.item.sku)}</h2><div class="flex flex-wrap items-center gap-2 mt-1">`);
  _push(ssrRenderComponent($setup["VTag"], {
    value: $props.item.brand?.name || "\u0411\u0440\u0435\u043D\u0434 \u043D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D",
    severity: $props.item.brand?.isOem ? "info" : "secondary"
  }, null, _parent));
  if ($props.item.brand?.isOem) {
    _push(ssrRenderComponent($setup["VTag"], {
      value: "OEM",
      severity: "info",
      size: "small"
    }, null, _parent));
  } else {
    _push(`<!---->`);
  }
  if (!$props.item.isPublic) {
    _push(ssrRenderComponent($setup["VTag"], {
      value: "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
      severity: "warning",
      size: "small"
    }, null, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(ssrRenderComponent($setup["VTag"], {
    value: "#" + $props.item.id,
    severity: "secondary",
    size: "small"
  }, null, _parent));
  if ($props.item.source) {
    _push(ssrRenderComponent($setup["VTag"], {
      value: "\u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A: " + $props.item.source,
      severity: "secondary",
      size: "small"
    }, null, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(ssrRenderComponent($setup["VTag"], {
    value: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432: " + ($props.item.attributes?.length || 0),
    severity: "secondary",
    size: "small"
  }, null, _parent));
  _push(ssrRenderComponent($setup["VTag"], {
    value: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u0435\u0439: " + ($props.item.applicabilities?.length || 0),
    severity: ($props.item.applicabilities?.length || 0) > 0 ? "success" : "secondary",
    size: "small"
  }, null, _parent));
  _push(`</div></div></div><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => _ctx.$emit("edit"),
    severity: "secondary",
    outlined: "",
    size: "small",
    label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-pencil",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-pencil",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => _ctx.$emit("match"),
    severity: "secondary",
    outlined: "",
    size: "small",
    label: "\u041F\u043E\u0434\u043E\u0431\u0440\u0430\u0442\u044C"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-link",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-link",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => _ctx.$emit("close"),
    severity: "secondary",
    text: "",
    size: "small"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-times",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-times",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6"><div class="space-y-4">`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </h3></div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 border-b border-surface-200 dark:border-surface-700" }, [
            createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0" }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 ")
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4"${_scopeId}>`);
        if ($props.item.description) {
          _push2(`<p class="text-surface-700 dark:text-surface-300"${_scopeId}>${ssrInterpolate($props.item.description)}</p>`);
        } else {
          _push2(`<span class="text-surface-400 italic"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E </span>`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4" }, [
            $props.item.description ? (openBlock(), createBlock("p", {
              key: 0,
              class: "text-surface-700 dark:text-surface-300"
            }, toDisplayString($props.item.description), 1)) : (openBlock(), createBlock("span", {
              key: 1,
              class: "text-surface-400 italic"
            }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u043D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E "))
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u041C\u0435\u0442\u0430\u0434\u0430\u043D\u043D\u044B\u0435 </h3></div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 border-b border-surface-200 dark:border-surface-700" }, [
            createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0" }, " \u041C\u0435\u0442\u0430\u0434\u0430\u043D\u043D\u044B\u0435 ")
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A:</span><span class="font-medium"${_scopeId}>${ssrInterpolate($props.item.source || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D")}</span></div><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u0412\u0438\u0434\u0438\u043C\u043E\u0441\u0442\u044C:</span>`);
        _push2(ssrRenderComponent($setup["VTag"], {
          value: $props.item.isPublic ? "\u041F\u0443\u0431\u043B\u0438\u0447\u043D\u0430\u044F" : "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
          severity: $props.item.isPublic ? "success" : "warning",
          size: "small"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>ID:</span><span class="font-medium font-mono"${_scopeId}> #${ssrInterpolate($props.item.id)}</span></div></div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 space-y-3" }, [
            createVNode("div", { class: "flex justify-between" }, [
              createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u0418\u0441\u0442\u043E\u0447\u043D\u0438\u043A:"),
              createVNode("span", { class: "font-medium" }, toDisplayString($props.item.source || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D"), 1)
            ]),
            createVNode("div", { class: "flex justify-between" }, [
              createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u0412\u0438\u0434\u0438\u043C\u043E\u0441\u0442\u044C:"),
              createVNode($setup["VTag"], {
                value: $props.item.isPublic ? "\u041F\u0443\u0431\u043B\u0438\u0447\u043D\u0430\u044F" : "\u041F\u0440\u0438\u0432\u0430\u0442\u043D\u0430\u044F",
                severity: $props.item.isPublic ? "success" : "warning",
                size: "small"
              }, null, 8, ["value", "severity"])
            ]),
            createVNode("div", { class: "flex justify-between" }, [
              createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "ID:"),
              createVNode("span", { class: "font-medium font-mono" }, " #" + toDisplayString($props.item.id), 1)
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div><div class="space-y-4">`);
  if ($props.item.brand) {
    _push(ssrRenderComponent($setup["VCard"], null, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0431\u0440\u0435\u043D\u0434\u0435 </h3></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 border-b border-surface-200 dark:border-surface-700" }, [
              createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0" }, " \u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0431\u0440\u0435\u043D\u0434\u0435 ")
            ])
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435:</span><span class="font-medium"${_scopeId}>${ssrInterpolate($props.item.brand.name)}</span></div><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u0422\u0438\u043F:</span>`);
          _push2(ssrRenderComponent($setup["VTag"], {
            value: $props.item.brand.isOem ? "OEM \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044C" : "Aftermarket",
            severity: $props.item.brand.isOem ? "info" : "secondary",
            size: "small"
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
          if ($props.item.brand.country) {
            _push2(`<div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u0421\u0442\u0440\u0430\u043D\u0430:</span><span class="font-medium"${_scopeId}>${ssrInterpolate($props.item.brand.country)}</span></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 space-y-3" }, [
              createVNode("div", { class: "flex justify-between" }, [
                createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435:"),
                createVNode("span", { class: "font-medium" }, toDisplayString($props.item.brand.name), 1)
              ]),
              createVNode("div", { class: "flex justify-between" }, [
                createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u0422\u0438\u043F:"),
                createVNode($setup["VTag"], {
                  value: $props.item.brand.isOem ? "OEM \u043F\u0440\u043E\u0438\u0437\u0432\u043E\u0434\u0438\u0442\u0435\u043B\u044C" : "Aftermarket",
                  severity: $props.item.brand.isOem ? "info" : "secondary",
                  size: "small"
                }, null, 8, ["value", "severity"])
              ]),
              $props.item.brand.country ? (openBlock(), createBlock("div", {
                key: 0,
                class: "flex justify-between"
              }, [
                createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u0421\u0442\u0440\u0430\u043D\u0430:"),
                createVNode("span", { class: "font-medium" }, toDisplayString($props.item.brand.country), 1)
              ])) : createCommentVNode("", true)
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(ssrRenderComponent($setup["VCard"], null, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 </h3></div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 border-b border-surface-200 dark:border-surface-700" }, [
            createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0" }, " \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 ")
          ])
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432:</span>`);
        _push2(ssrRenderComponent($setup["VTag"], {
          value: $props.item.attributes?.length || 0,
          severity: "secondary",
          size: "small"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex justify-between"${_scopeId}><span class="text-surface-600 dark:text-surface-400"${_scopeId}>\u0413\u0440\u0443\u043F\u043F \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u0438:</span>`);
        _push2(ssrRenderComponent($setup["VTag"], {
          value: $props.item.applicabilities?.length || 0,
          severity: ($props.item.applicabilities?.length || 0) > 0 ? "success" : "secondary",
          size: "small"
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 space-y-3" }, [
            createVNode("div", { class: "flex justify-between" }, [
              createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432:"),
              createVNode($setup["VTag"], {
                value: $props.item.attributes?.length || 0,
                severity: "secondary",
                size: "small"
              }, null, 8, ["value"])
            ]),
            createVNode("div", { class: "flex justify-between" }, [
              createVNode("span", { class: "text-surface-600 dark:text-surface-400" }, "\u0413\u0440\u0443\u043F\u043F \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u0438:"),
              createVNode($setup["VTag"], {
                value: $props.item.applicabilities?.length || 0,
                severity: ($props.item.applicabilities?.length || 0) > 0 ? "success" : "secondary",
                size: "small"
              }, null, 8, ["value", "severity"])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
  if ($props.item.attributes?.length > 0) {
    _push(ssrRenderComponent($setup["VCard"], { class: "mb-6" }, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B (${ssrInterpolate($props.item.attributes.length)}) </h3></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 border-b border-surface-200 dark:border-surface-700" }, [
              createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0" }, " \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B (" + toDisplayString($props.item.attributes.length) + ") ", 1)
            ])
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"${_scopeId}><!--[-->`);
          ssrRenderList($props.item.attributes, (attribute) => {
            _push2(`<div class="p-3 bg-surface-50 dark:bg-surface-900 rounded border"${_scopeId}><div class="font-medium text-surface-900 dark:text-surface-0 mb-1"${_scopeId}>${ssrInterpolate(attribute.template?.title || "\u041D\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043D\u044B\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442")} `);
            if (attribute.template?.isRequired) {
              _push2(`<span class="text-red-500 ml-1"${_scopeId}>*</span>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div class="text-lg font-semibold text-primary-600 dark:text-primary-400 mb-1"${_scopeId}>${ssrInterpolate(attribute.value)} ${ssrInterpolate(attribute.template?.unit ? $setup.getUnitLabel(attribute.template.unit) : "")}</div><div class="flex items-center gap-2"${_scopeId}>`);
            if (attribute.template?.group?.name) {
              _push2(ssrRenderComponent($setup["VTag"], {
                value: attribute.template.group.name,
                severity: "secondary",
                size: "small"
              }, null, _parent2, _scopeId));
            } else {
              _push2(`<!---->`);
            }
            _push2(`<span class="text-xs text-surface-500"${_scopeId}>${ssrInterpolate($setup.getDataTypeLabel(attribute.template?.dataType))}</span></div></div>`);
          });
          _push2(`<!--]--></div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4" }, [
              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" }, [
                (openBlock(true), createBlock(Fragment, null, renderList($props.item.attributes, (attribute) => {
                  return openBlock(), createBlock("div", {
                    key: attribute.id,
                    class: "p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                  }, [
                    createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0 mb-1" }, [
                      createTextVNode(toDisplayString(attribute.template?.title || "\u041D\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043D\u044B\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442") + " ", 1),
                      attribute.template?.isRequired ? (openBlock(), createBlock("span", {
                        key: 0,
                        class: "text-red-500 ml-1"
                      }, "*")) : createCommentVNode("", true)
                    ]),
                    createVNode("div", { class: "text-lg font-semibold text-primary-600 dark:text-primary-400 mb-1" }, toDisplayString(attribute.value) + " " + toDisplayString(attribute.template?.unit ? $setup.getUnitLabel(attribute.template.unit) : ""), 1),
                    createVNode("div", { class: "flex items-center gap-2" }, [
                      attribute.template?.group?.name ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: attribute.template.group.name,
                        severity: "secondary",
                        size: "small"
                      }, null, 8, ["value"])) : createCommentVNode("", true),
                      createVNode("span", { class: "text-xs text-surface-500" }, toDisplayString($setup.getDataTypeLabel(attribute.template?.dataType)), 1)
                    ])
                  ]);
                }), 128))
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  if ($props.item.applicabilities?.length > 0) {
    _push(ssrRenderComponent($setup["VCard"], null, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 border-b border-surface-200 dark:border-surface-700"${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}> \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C (${ssrInterpolate($props.item.applicabilities.length)}) </h3></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 border-b border-surface-200 dark:border-surface-700" }, [
              createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0" }, " \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C (" + toDisplayString($props.item.applicabilities.length) + ") ", 1)
            ])
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4"${_scopeId}><div class="space-y-3"${_scopeId}><!--[-->`);
          ssrRenderList($props.item.applicabilities, (applicability) => {
            _push2(`<div class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"${_scopeId}><div class="flex-1 min-w-0"${_scopeId}><div class="font-medium text-surface-900 dark:text-surface-0 truncate"${_scopeId}>`);
            if (applicability.part?.id) {
              _push2(`<a class="hover:text-primary-600 dark:hover:text-primary-400 underline-offset-2 hover:underline"${ssrRenderAttr("href", `/admin/parts/${applicability.part.id}`)} target="_blank" rel="noopener"${_scopeId}>${ssrInterpolate(applicability.part?.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${applicability.part?.id}`)}</a>`);
            } else {
              _push2(`<span${_scopeId}>${ssrInterpolate(applicability.part?.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${applicability.part?.id}`)}</span>`);
            }
            _push2(`</div><div class="flex flex-wrap items-center gap-2 mt-1"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VTag"], {
              value: $setup.getAccuracyLabel(applicability.accuracy),
              severity: $setup.getAccuracySeverity(applicability.accuracy),
              size: "small"
            }, null, _parent2, _scopeId));
            if (applicability.notes) {
              _push2(`<span class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>${ssrInterpolate(applicability.notes)}</span>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div><div class="ml-3 shrink-0"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], mergeProps({
              severity: "danger",
              text: "",
              size: "small",
              onClick: ($event) => _ctx.$emit("unlink", applicability)
            }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u041E\u0442\u0432\u044F\u0437\u0430\u0442\u044C")), {
              icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Icon"], {
                    name: "pi pi-trash",
                    class: "w-4 h-4"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Icon"], {
                      name: "pi pi-trash",
                      class: "w-4 h-4"
                    })
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(`</div></div>`);
          });
          _push2(`<!--]--></div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4" }, [
              createVNode("div", { class: "space-y-3" }, [
                (openBlock(true), createBlock(Fragment, null, renderList($props.item.applicabilities, (applicability) => {
                  return openBlock(), createBlock("div", {
                    key: applicability.id,
                    class: "flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded border"
                  }, [
                    createVNode("div", { class: "flex-1 min-w-0" }, [
                      createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0 truncate" }, [
                        applicability.part?.id ? (openBlock(), createBlock("a", {
                          key: 0,
                          class: "hover:text-primary-600 dark:hover:text-primary-400 underline-offset-2 hover:underline",
                          href: `/admin/parts/${applicability.part.id}`,
                          target: "_blank",
                          rel: "noopener"
                        }, toDisplayString(applicability.part?.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${applicability.part?.id}`), 9, ["href"])) : (openBlock(), createBlock("span", { key: 1 }, toDisplayString(applicability.part?.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${applicability.part?.id}`), 1))
                      ]),
                      createVNode("div", { class: "flex flex-wrap items-center gap-2 mt-1" }, [
                        createVNode($setup["VTag"], {
                          value: $setup.getAccuracyLabel(applicability.accuracy),
                          severity: $setup.getAccuracySeverity(applicability.accuracy),
                          size: "small"
                        }, null, 8, ["value", "severity"]),
                        applicability.notes ? (openBlock(), createBlock("span", {
                          key: 0,
                          class: "text-sm text-surface-600 dark:text-surface-400"
                        }, toDisplayString(applicability.notes), 1)) : createCommentVNode("", true)
                      ])
                    ]),
                    createVNode("div", { class: "ml-3 shrink-0" }, [
                      withDirectives((openBlock(), createBlock($setup["VButton"], {
                        severity: "danger",
                        text: "",
                        size: "small",
                        onClick: ($event) => _ctx.$emit("unlink", applicability)
                      }, {
                        icon: withCtx(() => [
                          createVNode($setup["Icon"], {
                            name: "pi pi-trash",
                            class: "w-4 h-4"
                          })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])), [
                        [_directive_tooltip, "\u041E\u0442\u0432\u044F\u0437\u0430\u0442\u044C"]
                      ])
                    ])
                  ]);
                }), 128))
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/CatalogItemCard.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const CatalogItemCard = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "MatchingResults",
  props: {
    item: {},
    results: {},
    loading: { type: Boolean }
  },
  emits: ["refresh", "link"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { getAccuracyLabel, getAccuracySeverity } = useMatchingLabels();
    const showConfirmDialog = ref(false);
    const selectedCandidate = ref(null);
    const linking = ref(false);
    const confirmForm = reactive({
      accuracy: "EXACT_MATCH",
      notes: ""
    });
    const accuracyOptions = [
      { label: "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435", value: "EXACT_MATCH" },
      { label: "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u0441 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438", value: "MATCH_WITH_NOTES" },
      { label: "\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043C\u043E\u0434\u0438\u0444\u0438\u043A\u0430\u0446\u0438\u044F", value: "REQUIRES_MODIFICATION" },
      { label: "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435", value: "PARTIAL_MATCH" }
    ];
    const openConfirmDialog = (candidate) => {
      selectedCandidate.value = candidate;
      confirmForm.accuracy = candidate.accuracySuggestion;
      confirmForm.notes = "";
      const withNotes = (candidate.details || []).find(
        (d) => String(d.kind).includes("NEAR") || String(d.kind).includes("LEGACY")
      );
      if (withNotes?.notes) {
        confirmForm.notes = withNotes.notes;
      }
      const tol = (candidate.details || []).find((d) => d.kind === "NUMBER_WITHIN_TOLERANCE");
      if (tol && !confirmForm.notes) {
        confirmForm.notes = "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u043F\u043E \u0434\u043E\u043F\u0443\u0441\u043A\u0443";
      }
      showConfirmDialog.value = true;
    };
    const closeConfirmDialog = () => {
      showConfirmDialog.value = false;
      selectedCandidate.value = null;
      confirmForm.accuracy = "EXACT_MATCH";
      confirmForm.notes = "";
    };
    const confirmLink = () => {
      if (!selectedCandidate.value) return;
      linking.value = true;
      emit("link", {
        partId: selectedCandidate.value.part.id,
        accuracy: confirmForm.accuracy,
        notes: confirmForm.notes || void 0
      });
      linking.value = false;
      closeConfirmDialog();
    };
    const { matching } = useTrpc();
    const toast = useToast();
    const queueProposal = async (c) => {
      try {
        const payload = { partId: c.part.id, accuracy: c.accuracySuggestion, notes: void 0 };
        const withNotes = (c.details || []).find((d) => String(d.kind).includes("NEAR") || String(d.kind).includes("LEGACY"));
        if (withNotes?.notes) payload.notes = withNotes.notes;
        const tol = (c.details || []).find((d) => d.kind === "NUMBER_WITHIN_TOLERANCE");
        if (tol && !payload.notes) payload.notes = "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u043F\u043E \u0434\u043E\u043F\u0443\u0441\u043A\u0443";
        await matching.proposeLink({ catalogItemId: props.item.id, partId: payload.partId, accuracySuggestion: payload.accuracy, notesSuggestion: payload.notes, details: c.details });
      } catch (err) {
        toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435");
      }
    };
    const __returned__ = { props, emit, getAccuracyLabel, getAccuracySeverity, showConfirmDialog, selectedCandidate, linking, confirmForm, accuracyOptions, openConfirmDialog, closeConfirmDialog, confirmLink, matching, toast, queueProposal, VButton: Button, VCard: Card, VTag: Tag, VDialog: Dialog, VSelect: Select, VTextarea, MatchingDetailsGrid, MatchingEmptyState, get RefreshCcwIcon() {
      return RefreshCcwIcon;
    }, get LinkIcon() {
      return LinkIcon;
    }, get SendIcon() {
      return SendIcon;
    }, MatchingLoadingState };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _directive_tooltip = resolveDirective("tooltip");
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-4 space-y-4" }, _attrs))}><div class="flex items-center justify-between"><div><div class="text-sm text-surface-500">\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F</div><div class="text-lg font-mono font-semibold">${ssrInterpolate($props.item.sku)} <span class="text-surface-500">\u2014</span> ${ssrInterpolate($props.item.brand?.name)}</div></div><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["VButton"], {
    severity: "secondary",
    outlined: "",
    size: "small",
    loading: $props.loading,
    onClick: ($event) => _ctx.$emit("refresh")
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["RefreshCcwIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["RefreshCcwIcon"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
  if ($props.loading) {
    _push(ssrRenderComponent($setup["MatchingLoadingState"], null, null, _parent));
  } else if (!$props.results || $props.results.length === 0) {
    _push(ssrRenderComponent($setup["MatchingEmptyState"], null, null, _parent));
  } else {
    _push(`<div class="space-y-3"><!--[-->`);
    ssrRenderList($props.results, (c) => {
      _push(ssrRenderComponent($setup["VCard"], {
        key: c.part.id,
        class: "border"
      }, {
        content: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="p-4 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"${_scopeId}><div class="md:col-span-1"${_scopeId}><div class="font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(c.part.name || "\u0413\u0440\u0443\u043F\u043F\u0430 #" + c.part.id)}</div><div class="mt-2"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VTag"], {
              value: $setup.getAccuracyLabel(c.accuracySuggestion),
              severity: $setup.getAccuracySeverity(c.accuracySuggestion)
            }, null, _parent2, _scopeId));
            _push2(`</div><div class="mt-3"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], mergeProps({
              size: "small",
              severity: "secondary",
              outlined: "",
              onClick: ($event) => $setup.openConfirmDialog(c)
            }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0432\u044F\u0437\u044C")), {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["LinkIcon"], null, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["LinkIcon"])
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent($setup["VButton"], mergeProps({
              size: "small",
              severity: "secondary",
              outlined: "",
              onClick: ($event) => $setup.queueProposal(c),
              class: "ml-2"
            }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u0412 \u043E\u0447\u0435\u0440\u0435\u0434\u044C \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439")), {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["SendIcon"], null, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["SendIcon"])
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(`</div></div><div class="md:col-span-2"${_scopeId}><div class="text-sm text-surface-500 mb-2"${_scopeId}>\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F</div>`);
            _push2(ssrRenderComponent($setup["MatchingDetailsGrid"], {
              details: c.details,
              controls: false
            }, null, _parent2, _scopeId));
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "p-4 grid grid-cols-1 md:grid-cols-3 gap-3 items-start" }, [
                createVNode("div", { class: "md:col-span-1" }, [
                  createVNode("div", { class: "font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString(c.part.name || "\u0413\u0440\u0443\u043F\u043F\u0430 #" + c.part.id), 1),
                  createVNode("div", { class: "mt-2" }, [
                    createVNode($setup["VTag"], {
                      value: $setup.getAccuracyLabel(c.accuracySuggestion),
                      severity: $setup.getAccuracySeverity(c.accuracySuggestion)
                    }, null, 8, ["value", "severity"])
                  ]),
                  createVNode("div", { class: "mt-3" }, [
                    withDirectives((openBlock(), createBlock($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: ($event) => $setup.openConfirmDialog(c)
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["LinkIcon"])
                      ]),
                      _: 2
                    }, 1032, ["onClick"])), [
                      [_directive_tooltip, "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0432\u044F\u0437\u044C"]
                    ]),
                    withDirectives((openBlock(), createBlock($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: ($event) => $setup.queueProposal(c),
                      class: "ml-2"
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["SendIcon"])
                      ]),
                      _: 2
                    }, 1032, ["onClick"])), [
                      [_directive_tooltip, "\u0412 \u043E\u0447\u0435\u0440\u0435\u0434\u044C \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439"]
                    ])
                  ])
                ]),
                createVNode("div", { class: "md:col-span-2" }, [
                  createVNode("div", { class: "text-sm text-surface-500 mb-2" }, "\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F"),
                  createVNode($setup["MatchingDetailsGrid"], {
                    details: c.details,
                    controls: false
                  }, null, 8, ["details"])
                ])
              ])
            ];
          }
        }),
        _: 2
      }, _parent));
    });
    _push(`<!--]--></div>`);
  }
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showConfirmDialog,
    "onUpdate:visible": ($event) => $setup.showConfirmDialog = $event,
    modal: "",
    header: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435 \u0441\u0432\u044F\u0437\u0438",
    class: "w-full max-w-3xl"
  }, {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-between"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: $setup.closeConfirmDialog
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0432\u044F\u0437\u044C",
          severity: "success",
          onClick: $setup.confirmLink,
          loading: $setup.linking
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-between" }, [
            createVNode($setup["VButton"], {
              label: "\u041E\u0442\u043C\u0435\u043D\u0430",
              severity: "secondary",
              onClick: $setup.closeConfirmDialog
            }),
            createVNode($setup["VButton"], {
              label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0432\u044F\u0437\u044C",
              severity: "success",
              onClick: $setup.confirmLink,
              loading: $setup.linking
            }, null, 8, ["loading"])
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.selectedCandidate) {
          _push2(`<div class="space-y-4"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface-50 dark:bg-surface-900 rounded"${_scopeId}><div${_scopeId}><div class="text-sm text-surface-500"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F</div><div class="font-semibold"${_scopeId}>${ssrInterpolate($props.item.sku)}</div><div class="text-sm"${_scopeId}>${ssrInterpolate($props.item.brand?.name)}</div></div><div${_scopeId}><div class="text-sm text-surface-500"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u0430 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438</div><div class="font-semibold"${_scopeId}>${ssrInterpolate($setup.selectedCandidate.part.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${$setup.selectedCandidate.part.id}`)}</div></div></div><div${_scopeId}><h3 class="text-lg font-semibold mb-3"${_scopeId}>\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F</h3>`);
          _push2(ssrRenderComponent($setup["MatchingDetailsGrid"], {
            details: $setup.selectedCandidate.details,
            controls: false
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="space-y-3"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u044F </label>`);
          _push2(ssrRenderComponent($setup["VSelect"], {
            modelValue: $setup.confirmForm.accuracy,
            "onUpdate:modelValue": ($event) => $setup.confirmForm.accuracy = $event,
            options: $setup.accuracyOptions,
            "option-label": "label",
            "option-value": "value",
            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043D\u043E\u0441\u0442\u044C",
            class: "w-full"
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F </label>`);
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.confirmForm.notes,
            "onUpdate:modelValue": ($event) => $setup.confirmForm.notes = $event,
            rows: "3",
            placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438...",
            class: "w-full"
          }, null, _parent2, _scopeId));
          _push2(`<small class="text-surface-500"${_scopeId}> \u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u043E\u0441\u043E\u0431\u0435\u043D\u043D\u043E\u0441\u0442\u0438 \u043F\u0440\u0438\u043C\u0435\u043D\u0435\u043D\u0438\u044F, \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0435\u043D\u0438\u044F \u0438\u043B\u0438 \u0443\u0441\u043B\u043E\u0432\u0438\u044F \u0437\u0430\u043C\u0435\u043D\u044B </small></div></div></div>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $setup.selectedCandidate ? (openBlock(), createBlock("div", {
            key: 0,
            class: "space-y-4"
          }, [
            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface-50 dark:bg-surface-900 rounded" }, [
              createVNode("div", null, [
                createVNode("div", { class: "text-sm text-surface-500" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F"),
                createVNode("div", { class: "font-semibold" }, toDisplayString($props.item.sku), 1),
                createVNode("div", { class: "text-sm" }, toDisplayString($props.item.brand?.name), 1)
              ]),
              createVNode("div", null, [
                createVNode("div", { class: "text-sm text-surface-500" }, "\u0413\u0440\u0443\u043F\u043F\u0430 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438"),
                createVNode("div", { class: "font-semibold" }, toDisplayString($setup.selectedCandidate.part.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${$setup.selectedCandidate.part.id}`), 1)
              ])
            ]),
            createVNode("div", null, [
              createVNode("h3", { class: "text-lg font-semibold mb-3" }, "\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F"),
              createVNode($setup["MatchingDetailsGrid"], {
                details: $setup.selectedCandidate.details,
                controls: false
              }, null, 8, ["details"])
            ]),
            createVNode("div", { class: "space-y-3" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u044F "),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.confirmForm.accuracy,
                  "onUpdate:modelValue": ($event) => $setup.confirmForm.accuracy = $event,
                  options: $setup.accuracyOptions,
                  "option-label": "label",
                  "option-value": "value",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043D\u043E\u0441\u0442\u044C",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F "),
                createVNode($setup["VTextarea"], {
                  modelValue: $setup.confirmForm.notes,
                  "onUpdate:modelValue": ($event) => $setup.confirmForm.notes = $event,
                  rows: "3",
                  placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438...",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                createVNode("small", { class: "text-surface-500" }, " \u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u043E\u0441\u043E\u0431\u0435\u043D\u043D\u043E\u0441\u0442\u0438 \u043F\u0440\u0438\u043C\u0435\u043D\u0435\u043D\u0438\u044F, \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0435\u043D\u0438\u044F \u0438\u043B\u0438 \u0443\u0441\u043B\u043E\u0432\u0438\u044F \u0437\u0430\u043C\u0435\u043D\u044B ")
              ])
            ])
          ])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/MatchingResults.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const MatchingResults = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CatalogItemsManager",
  setup(__props, { expose: __expose }) {
    __expose();
    const { catalogItems: catalogItemsApi, brands, loading, error, matching, partApplicability, client } = useTrpc();
    const catalogItemAttributesApi = client.crud.catalogItemAttribute;
    const confirm = useConfirm();
    const toast = useToast();
    const catalogItems = ref([]);
    const totalRecords = ref(0);
    const currentPage = ref(1);
    const pageSize = ref(20);
    const searchQuery = ref("");
    const selectedBrand = ref(null);
    const brandSuggestions = ref([]);
    const urlSync = useUrlParams({
      q: "",
      brandId: void 0,
      page: 1,
      rows: 20,
      sortField: "",
      sortOrder: 1
    }, {
      prefix: "ci_",
      numberParams: ["brandId", "page", "rows"],
      debounceMs: 300
    });
    const showCreateDialog = ref(false);
    const showDetailsDialog = ref(false);
    const editingItem = ref(null);
    const selectedItem = ref(null);
    const matchingItem = ref(null);
    const showMatchingDialog = ref(false);
    const matchingResults = ref(null);
    const matchingLoading = ref(false);
    const sortField = ref("");
    const sortOrder = ref(1);
    const loadCatalogItems = async () => {
      try {
        const filters = {
          skip: (currentPage.value - 1) * pageSize.value,
          take: pageSize.value,
          include: {
            brand: true,
            attributes: {
              include: {
                template: true
              }
            },
            applicabilities: {
              include: {
                part: true
              }
            }
          }
        };
        if (searchQuery.value.trim()) {
          filters.where = {
            OR: [
              { sku: { contains: searchQuery.value.trim(), mode: "insensitive" } },
              { description: { contains: searchQuery.value.trim(), mode: "insensitive" } },
              { brand: { name: { contains: searchQuery.value.trim(), mode: "insensitive" } } }
            ]
          };
        }
        if (selectedBrand.value) {
          const brandFilter = { brandId: selectedBrand.value.id };
          if (filters.where) {
            filters.where = { AND: [filters.where, brandFilter] };
          } else {
            filters.where = brandFilter;
          }
        }
        if (sortField.value) {
          filters.orderBy = { [sortField.value]: sortOrder.value === 1 ? "asc" : "desc" };
        } else {
          filters.orderBy = { id: "desc" };
        }
        const result = await catalogItemsApi.findMany(filters);
        if (Array.isArray(result)) {
          catalogItems.value = result;
        } else {
          catalogItems.value = [];
        }
        const countResult = await catalogItemsApi.findMany({
          where: filters.where,
          select: { id: true }
        });
        totalRecords.value = Array.isArray(countResult) ? countResult.length : 0;
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439:", err);
        toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438");
      }
    };
    const searchBrands = async (event) => {
      try {
        const query = event.query.toLowerCase();
        const result = await brands.findMany({
          where: {
            name: { contains: query, mode: "insensitive" }
          },
          take: 10
        });
        brandSuggestions.value = Array.isArray(result) ? result : [];
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0431\u0440\u0435\u043D\u0434\u043E\u0432:", err);
      }
    };
    let searchTimeout;
    const debouncedSearch = () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        currentPage.value = 1;
        urlSync.updateFilters({ q: searchQuery.value || void 0, page: 1 });
        loadCatalogItems();
      }, 300);
    };
    const onBrandFilterChange = () => {
      currentPage.value = 1;
      urlSync.updateFilters({ brandId: selectedBrand.value?.id, page: 1 });
      loadCatalogItems();
    };
    const onPageChange = (event) => {
      currentPage.value = Math.floor(event.first / event.rows) + 1;
      pageSize.value = event.rows;
      urlSync.updateFilters({ page: currentPage.value, rows: pageSize.value });
      loadCatalogItems();
    };
    const onSort = (event) => {
      sortField.value = event.sortField;
      sortOrder.value = event.sortOrder;
      urlSync.updateFilters({ sortField: sortField.value, sortOrder: sortOrder.value });
      loadCatalogItems();
    };
    const refreshData = () => {
      loadCatalogItems();
    };
    const onEdit = (item) => {
      editingItem.value = { ...item };
      showCreateDialog.value = true;
    };
    const onDelete = (item) => {
      confirm.confirmDelete(
        `\u043F\u043E\u0437\u0438\u0446\u0438\u044E "${item.sku}" (${item.brand?.name})`,
        async () => {
          try {
            await catalogItemsApi.delete({ where: { id: item.id } });
            toast.success("\u0423\u0441\u043F\u0435\u0448\u043D\u043E", "\u041F\u043E\u0437\u0438\u0446\u0438\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0430");
            loadCatalogItems();
          } catch (err) {
            console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F:", err);
            toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E");
          }
        }
      );
    };
    const onViewDetails = (item) => {
      selectedItem.value = item;
      showDetailsDialog.value = true;
    };
    const onMatch = (item) => {
      matchingItem.value = item;
      showMatchingDialog.value = true;
      runMatching();
    };
    const runMatching = async () => {
      if (!matchingItem.value) return;
      matchingLoading.value = true;
      matchingResults.value = null;
      try {
        const res = await matching.findMatchingParts({ catalogItemId: matchingItem.value.id });
        matchingResults.value = res ? res.candidates || [] : [];
        if (matchingResults.value && matchingResults.value.length === 1 && matchingResults.value[0].accuracySuggestion === "EXACT_MATCH") {
          const candidate = matchingResults.value[0];
          await linkToPart({ partId: candidate.part.id, accuracy: "EXACT_MATCH" });
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0434\u0431\u043E\u0440\u0430:", err);
        matchingResults.value = [];
      } finally {
        matchingLoading.value = false;
      }
    };
    const linkToPart = async (payload) => {
      if (!matchingItem.value) return;
      try {
        await partApplicability.upsert({
          where: { partId_catalogItemId: { partId: payload.partId, catalogItemId: matchingItem.value.id } },
          create: { partId: payload.partId, catalogItemId: matchingItem.value.id, accuracy: payload.accuracy, notes: payload.notes },
          update: { accuracy: payload.accuracy, notes: payload.notes }
        });
        showMatchingDialog.value = false;
        loadCatalogItems();
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438\u0432\u044F\u0437\u043A\u0438:", err);
        toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E");
      }
    };
    const onSave = async (itemData) => {
      try {
        const { attributes, ...catalogItemData } = itemData;
        let catalogItem = null;
        if (editingItem.value) {
          catalogItem = await catalogItemsApi.update({
            where: { id: editingItem.value.id },
            data: catalogItemData
          });
          if (attributes?.deleteMany !== void 0) {
            await catalogItemAttributesApi.deleteMany.mutate({
              where: { catalogItemId: editingItem.value.id }
            });
          }
        } else {
          catalogItem = await catalogItemsApi.create({ data: catalogItemData });
        }
        if (attributes?.create && attributes.create.length > 0) {
          for (const attr of attributes.create) {
            try {
              await catalogItemAttributesApi.create.mutate({
                data: {
                  value: String(attr.value),
                  // Преобразуем в строку
                  catalogItem: {
                    connect: { id: catalogItem.id }
                  },
                  template: {
                    connect: { id: attr.templateId }
                  }
                }
              });
            } catch (attrErr) {
              console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430:", attrErr);
            }
          }
        }
        showCreateDialog.value = false;
        editingItem.value = null;
        loadCatalogItems();
      } catch (err) {
        console.error("\u274C \u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F:", err);
        toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E");
      }
    };
    const onCancel = () => {
      showCreateDialog.value = false;
      editingItem.value = null;
    };
    const onEditFromDetails = () => {
      editingItem.value = { ...selectedItem.value };
      showDetailsDialog.value = false;
      showCreateDialog.value = true;
    };
    const onMatchFromCard = () => {
      matchingItem.value = selectedItem.value;
      showMatchingDialog.value = true;
      runMatching();
    };
    const onUnlink = async (applicability) => {
      if (!applicability?.id) return;
      confirm.show({
        header: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0441\u0432\u044F\u0437\u044C?",
        message: `\u041E\u0442\u0432\u044F\u0437\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E \u043E\u0442 \u0433\u0440\u0443\u043F\u043F\u044B #${applicability.part?.id || ""}?`,
        icon: "pi pi-trash",
        acceptLabel: "\u041E\u0442\u0432\u044F\u0437\u0430\u0442\u044C",
        rejectLabel: "\u041E\u0442\u043C\u0435\u043D\u0430",
        acceptClass: "bg-red-500 hover:bg-red-600",
        accept: async () => {
          try {
            await partApplicability.delete({ where: { id: applicability.id } });
            if (selectedItem.value) {
              selectedItem.value = {
                ...selectedItem.value,
                applicabilities: (selectedItem.value.applicabilities || []).filter((a) => a.id !== applicability.id)
              };
            }
            loadCatalogItems();
          } catch (err) {
            console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043E\u0442\u0432\u044F\u0437\u043A\u0438:", err);
            toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043E\u0442\u0432\u044F\u0437\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E");
          }
        }
      });
    };
    onMounted(() => {
      const f = urlSync.filters.value;
      searchQuery.value = f.q || "";
      selectedBrand.value = f.brandId ? { id: f.brandId } : null;
      currentPage.value = f.page || 1;
      pageSize.value = f.rows || 20;
      sortField.value = f.sortField || "";
      sortOrder.value = f.sortOrder === -1 ? -1 : 1;
      loadCatalogItems();
    });
    watch(urlSync.filters, (f) => {
      const next = f;
      const nextBrand = next.brandId ? { id: next.brandId } : null;
      if (searchQuery.value !== (next.q || "")) searchQuery.value = next.q || "";
      if ((selectedBrand.value?.id || null) !== (next.brandId ?? null)) selectedBrand.value = nextBrand;
      if (currentPage.value !== (next.page || 1)) currentPage.value = next.page || 1;
      if (pageSize.value !== (next.rows || 20)) pageSize.value = next.rows || 20;
      if (sortField.value !== (next.sortField || "")) sortField.value = next.sortField || "";
      if (sortOrder.value !== (next.sortOrder === -1 ? -1 : 1)) sortOrder.value = next.sortOrder === -1 ? -1 : 1;
      loadCatalogItems();
    });
    const __returned__ = { catalogItemsApi, brands, loading, error, matching, partApplicability, client, catalogItemAttributesApi, confirm, toast, catalogItems, totalRecords, currentPage, pageSize, searchQuery, selectedBrand, brandSuggestions, urlSync, showCreateDialog, showDetailsDialog, editingItem, selectedItem, matchingItem, showMatchingDialog, matchingResults, matchingLoading, sortField, sortOrder, loadCatalogItems, searchBrands, get searchTimeout() {
      return searchTimeout;
    }, set searchTimeout(v) {
      searchTimeout = v;
    }, debouncedSearch, onBrandFilterChange, onPageChange, onSort, refreshData, onEdit, onDelete, onViewDetails, onMatch, runMatching, linkToPart, onSave, onCancel, onEditFromDetails, onMatchFromCard, onUnlink, VCard: Card, VInputText: InputText, VAutoComplete, VButton: Button, VDialog: Dialog, VConfirmDialog: ConfirmDialog, Toast, CatalogItemsTable, CatalogItemForm, CatalogItemCard, MatchingResults, get SearchIcon() {
      return SearchIcon;
    }, get PlusIcon() {
      return PlusIcon;
    }, get RefreshCcwIcon() {
      return RefreshCcwIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-items-manager" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["VCard"], { class: "mb-6" }, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-6"${_scopeId}><div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between"${_scopeId}><div class="flex flex-col sm:flex-row gap-4 flex-1"${_scopeId}><div class="flex-1"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.searchQuery,
          "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u0430\u0440\u0442\u0438\u043A\u0443\u043B\u0443, \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E \u0438\u043B\u0438 \u0431\u0440\u0435\u043D\u0434\u0443...",
          class: "w-full",
          onInput: $setup.debouncedSearch
        }, {
          prefix: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["SearchIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["SearchIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
        _push2(ssrRenderComponent($setup["VAutoComplete"], {
          modelValue: $setup.selectedBrand,
          "onUpdate:modelValue": ($event) => $setup.selectedBrand = $event,
          suggestions: $setup.brandSuggestions,
          onComplete: $setup.searchBrands,
          "option-label": "name",
          placeholder: "\u0424\u0438\u043B\u044C\u0442\u0440 \u043F\u043E \u0431\u0440\u0435\u043D\u0434\u0443",
          class: "w-full sm:w-64",
          onChange: $setup.onBrandFilterChange,
          dropdown: ""
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: ($event) => $setup.showCreateDialog = true,
          severity: "secondary",
          outlined: "",
          label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["PlusIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["PlusIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          onClick: $setup.refreshData,
          severity: "secondary",
          outlined: "",
          loading: $setup.loading
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["RefreshCcwIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["RefreshCcwIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div></div></div>`);
      } else {
        return [
          createVNode("div", { class: "p-6" }, [
            createVNode("div", { class: "flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between" }, [
              createVNode("div", { class: "flex flex-col sm:flex-row gap-4 flex-1" }, [
                createVNode("div", { class: "flex-1" }, [
                  createVNode($setup["VInputText"], {
                    modelValue: $setup.searchQuery,
                    "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u0430\u0440\u0442\u0438\u043A\u0443\u043B\u0443, \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E \u0438\u043B\u0438 \u0431\u0440\u0435\u043D\u0434\u0443...",
                    class: "w-full",
                    onInput: $setup.debouncedSearch
                  }, {
                    prefix: withCtx(() => [
                      createVNode($setup["SearchIcon"])
                    ]),
                    _: 1
                  }, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                createVNode($setup["VAutoComplete"], {
                  modelValue: $setup.selectedBrand,
                  "onUpdate:modelValue": ($event) => $setup.selectedBrand = $event,
                  suggestions: $setup.brandSuggestions,
                  onComplete: $setup.searchBrands,
                  "option-label": "name",
                  placeholder: "\u0424\u0438\u043B\u044C\u0442\u0440 \u043F\u043E \u0431\u0440\u0435\u043D\u0434\u0443",
                  class: "w-full sm:w-64",
                  onChange: $setup.onBrandFilterChange,
                  dropdown: ""
                }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
              ]),
              createVNode("div", { class: "flex gap-2" }, [
                createVNode($setup["VButton"], {
                  onClick: ($event) => $setup.showCreateDialog = true,
                  severity: "secondary",
                  outlined: "",
                  label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PlusIcon"])
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode($setup["VButton"], {
                  onClick: $setup.refreshData,
                  severity: "secondary",
                  outlined: "",
                  loading: $setup.loading
                }, {
                  default: withCtx(() => [
                    createVNode($setup["RefreshCcwIcon"])
                  ]),
                  _: 1
                }, 8, ["loading"])
              ])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["CatalogItemsTable"], {
    items: $setup.catalogItems,
    loading: $setup.loading,
    "total-records": $setup.totalRecords,
    rows: $setup.pageSize,
    first: ($setup.currentPage - 1) * $setup.pageSize,
    onPage: $setup.onPageChange,
    onSort: $setup.onSort,
    onEdit: $setup.onEdit,
    onDelete: $setup.onDelete,
    onViewDetails: $setup.onViewDetails,
    onMatch: $setup.onMatch
  }, null, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showCreateDialog,
    "onUpdate:visible": ($event) => $setup.showCreateDialog = $event,
    modal: "",
    header: $setup.editingItem ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E",
    class: "w-full max-w-2xl"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["CatalogItemForm"], {
          item: $setup.editingItem,
          onSave: $setup.onSave,
          onCancel: $setup.onCancel
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["CatalogItemForm"], {
            item: $setup.editingItem,
            onSave: $setup.onSave,
            onCancel: $setup.onCancel
          }, null, 8, ["item"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showDetailsDialog,
    "onUpdate:visible": ($event) => $setup.showDetailsDialog = $event,
    modal: "",
    header: "\u0414\u0435\u0442\u0430\u043B\u0438 \u043F\u043E\u0437\u0438\u0446\u0438\u0438",
    class: "w-full max-w-3xl"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.selectedItem) {
          _push2(ssrRenderComponent($setup["CatalogItemCard"], {
            item: $setup.selectedItem,
            onEdit: $setup.onEditFromDetails,
            onMatch: $setup.onMatchFromCard,
            onClose: ($event) => $setup.showDetailsDialog = false,
            onUnlink: $setup.onUnlink
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $setup.selectedItem ? (openBlock(), createBlock($setup["CatalogItemCard"], {
            key: 0,
            item: $setup.selectedItem,
            onEdit: $setup.onEditFromDetails,
            onMatch: $setup.onMatchFromCard,
            onClose: ($event) => $setup.showDetailsDialog = false,
            onUnlink: $setup.onUnlink
          }, null, 8, ["item", "onClose"])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showMatchingDialog,
    "onUpdate:visible": ($event) => $setup.showMatchingDialog = $event,
    modal: "",
    header: "\u041F\u043E\u0434\u0431\u043E\u0440 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0433\u0440\u0443\u043F\u043F",
    class: "w-full max-w-4xl"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.matchingItem) {
          _push2(ssrRenderComponent($setup["MatchingResults"], {
            item: $setup.matchingItem,
            results: $setup.matchingResults,
            loading: $setup.matchingLoading,
            onRefresh: $setup.runMatching,
            onLink: $setup.linkToPart
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $setup.matchingItem ? (openBlock(), createBlock($setup["MatchingResults"], {
            key: 0,
            item: $setup.matchingItem,
            results: $setup.matchingResults,
            loading: $setup.matchingLoading,
            onRefresh: $setup.runMatching,
            onLink: $setup.linkToPart
          }, null, 8, ["item", "results", "loading"])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VConfirmDialog"], null, null, _parent));
  _push(ssrRenderComponent($setup["Toast"], null, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/CatalogItemsManager.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const CatalogItemsManager = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "CatalogItemsManagerBoundary",
  setup(__props, { expose: __expose }) {
    __expose();
    const key = ref(0);
    const onRetry = () => {
      key.value++;
    };
    const __returned__ = { key, onRetry, ErrorBoundary, CatalogItemsManager };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ErrorBoundary"], mergeProps({
    variant: "detailed",
    title: "\u041E\u0448\u0438\u0431\u043A\u0430 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439",
    message: "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0438\u043B\u0438 \u043E\u0442\u0440\u0438\u0441\u043E\u0432\u0430\u0442\u044C \u0442\u0430\u0431\u043B\u0438\u0446\u0443. \u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u043F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C.",
    onRetry: $setup.onRetry
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["CatalogItemsManager"], { key: $setup.key }, null, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock($setup["CatalogItemsManager"], { key: $setup.key }))
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/CatalogItemsManagerBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const CatalogItemsManagerBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="p-6"> <div class="flex items-center justify-between mb-6"> <div> <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
Каталожные позиции
</h1> <p class="text-surface-600 dark:text-surface-400 mt-1">
Управление каталожными позициями (артикулами) от различных производителей
</p> </div> </div> ${renderComponent($$result2, "CatalogItemsManagerBoundary", CatalogItemsManagerBoundary, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/admin/catalogitems/CatalogItemsManagerBoundary.vue", "client:component-export": "default" })} </div> ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/catalogitems/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/catalogitems/index.astro";
const $$url = "/admin/catalogitems";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

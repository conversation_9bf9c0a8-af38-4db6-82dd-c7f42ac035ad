import{_ as N}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as T,c as l,o as i,p as L,a,b,e as x,f as h,N as V,h as r}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{c as y}from"./createLucideIcon.NtN1-Ts2.js";import{T as z}from"./triangle-alert.CP-lXbmj.js";import{n as t,t as v,r as C}from"./reactivity.esm-bundler.BQ12LWmY.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=y("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j=y("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),H=T({__name:"ErrorBoundary",props:{title:{},message:{},variant:{default:"default"},showActions:{type:Boolean,default:!0},showReload:{type:Boolean,default:!0},showDetails:{type:Boolean,default:!1},onRetry:{}},emits:["error","retry"],setup(m,{expose:o,emit:n}){o();const e=m,d=n,c=C(!1),u=C("");V(s=>(c.value=!0,u.value=s.stack||s.message,d("error",s),!1));const p=()=>{c.value=!1,u.value="",e.onRetry&&e.onRetry(),d("retry")},w=()=>{window.location.reload()},k=r(()=>`w-full ${{default:"p-6 bg-[--color-card] border border-[--color-border] rounded-lg",minimal:"p-4",detailed:"p-8 bg-[--color-card] border border-[--color-border] rounded-xl shadow-sm"}[e.variant]}`),_=r(()=>e.variant==="minimal"?"flex items-start gap-3":"flex flex-col items-center text-center"),B=r(()=>`flex-shrink-0 ${{default:"text-[--color-danger] mb-4",minimal:"text-[--color-danger] mt-0.5",detailed:"text-[--color-danger] mb-6 p-3 bg-red-50 dark:bg-red-950/20 rounded-full"}[e.variant]}`),R=r(()=>({default:"w-8 h-8",minimal:"w-5 h-5",detailed:"w-12 h-12"})[e.variant]),E=r(()=>`font-semibold text-[--color-foreground] ${{default:"text-lg mb-2",minimal:"text-base mb-1",detailed:"text-xl mb-3"}[e.variant]}`),D=r(()=>`text-[--color-muted] ${{default:"text-sm",minimal:"text-sm",detailed:"text-base max-w-md"}[e.variant]}`),M=r(()=>"mt-2 p-3 bg-[--color-background] border border-[--color-border] rounded text-xs text-[--color-muted] overflow-auto max-h-32"),S=r(()=>"inline-flex items-center gap-2 px-4 py-2 bg-[--color-primary] text-[--color-primary-foreground] rounded-md hover:bg-[--color-primary-hover] transition-colors text-sm font-medium"),A=r(()=>"inline-flex items-center gap-2 px-4 py-2 bg-[--color-card] text-[--color-foreground] border border-[--color-border] rounded-md hover:bg-[--color-hover] transition-colors text-sm font-medium"),g={props:e,emit:d,hasError:c,errorDetails:u,retry:p,reload:w,errorContainerClasses:k,errorContentClasses:_,iconClasses:B,iconSizeClasses:R,titleClasses:E,messageClasses:D,detailsClasses:M,retryButtonClasses:S,reloadButtonClasses:A,get AlertTriangle(){return z},get RotateCcw(){return j},get RefreshCw(){return q}};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}}),I={class:"flex-1"},O={key:0,class:"mt-4"},P={key:0,class:"flex gap-2 mt-4"};function F(m,o,n,e,d,c){return i(),l("div",null,[e.hasError?(i(),l("div",{key:0,class:t(e.errorContainerClasses)},[a("div",{class:t(e.errorContentClasses)},[a("div",{class:t(e.iconClasses)},[x(e.AlertTriangle,{class:t(e.iconSizeClasses)},null,8,["class"])],2),a("div",I,[a("h3",{class:t(e.titleClasses)},v(n.title||"Произошла ошибка"),3),a("p",{class:t(e.messageClasses)},v(n.message||"Что-то пошло не так. Пожалуйста, попробуйте обновить страницу."),3),n.showDetails&&e.errorDetails?(i(),l("details",O,[o[0]||(o[0]=a("summary",{class:"cursor-pointer text-sm text-[--color-muted] hover:text-[--color-foreground]"}," Подробности ошибки ",-1)),a("pre",{class:t(e.detailsClasses)},v(e.errorDetails),3)])):b("",!0)]),n.showActions?(i(),l("div",P,[a("button",{onClick:e.retry,class:t(e.retryButtonClasses)},[x(e.RotateCcw,{class:"w-4 h-4"}),o[1]||(o[1]=h(" Повторить "))],2),n.showReload?(i(),l("button",{key:0,onClick:e.reload,class:t(e.reloadButtonClasses)},[x(e.RefreshCw,{class:"w-4 h-4"}),o[2]||(o[2]=h(" Обновить страницу "))],2)):b("",!0)])):b("",!0)],2)],2)):L(m.$slots,"default",{key:1})])}const W=N(H,[["render",F]]);export{W as E};

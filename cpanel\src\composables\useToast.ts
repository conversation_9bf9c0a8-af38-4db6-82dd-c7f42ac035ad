/**
 * Composable для работы с уведомлениями (Toast)
 * Предоставляет удобные методы для показа различных типов уведомлений
 */

import { useToast as usePrimeToast } from 'primevue/usetoast'

export interface ToastMessage {
  severity?: 'success' | 'info' | 'warn' | 'error'
  summary?: string
  detail?: string
  life?: number
  closable?: boolean
  group?: string
}

export const useToast = () => {
  const toast = usePrimeToast()

  // Базовый метод для показа уведомления
  const show = (message: ToastMessage) => {
    toast.add({
      severity: message.severity || 'info',
      summary: message.summary,
      detail: message.detail,
      life: message.life || 5000,
      closable: message.closable !== false,
      group: message.group
    })
  }

  // Успешное уведомление
  const success = (summary: string, detail?: string, life?: number) => {
    show({
      severity: 'success',
      summary,
      detail,
      life
    })
  }

  // Информационное уведомление
  const info = (summary: string, detail?: string, life?: number) => {
    show({
      severity: 'info',
      summary,
      detail,
      life
    })
  }

  // Предупреждение
  const warn = (summary: string, detail?: string, life?: number) => {
    show({
      severity: 'warn',
      summary,
      detail,
      life
    })
  }

  // Ошибка
  const error = (summary: string, detail?: string, life?: number) => {
    show({
      severity: 'error',
      summary,
      detail,
      life: life || 8000 // Ошибки показываем дольше
    })
  }

  // Очистка всех уведомлений
  const clear = (group?: string) => {
    toast.removeAllGroups()
  }

  // Удаление конкретного уведомления
  const remove = (message: any) => {
    toast.remove(message)
  }

  // Удобные методы для частых случаев
  const showSaveSuccess = (entityName = 'Запись') => {
    success('Сохранено', `${entityName} успешно сохранена`)
  }

  const showDeleteSuccess = (entityName = 'Запись') => {
    success('Удалено', `${entityName} успешно удалена`)
  }

  const showSaveError = (entityName = 'Запись', errorMessage?: string) => {
    error(
      'Ошибка сохранения',
      errorMessage || `Не удалось сохранить ${entityName.toLowerCase()}`
    )
  }

  const showDeleteError = (entityName = 'Запись', errorMessage?: string) => {
    error(
      'Ошибка удаления',
      errorMessage || `Не удалось удалить ${entityName.toLowerCase()}`
    )
  }

  const showLoadError = (entityName = 'Данные', errorMessage?: string) => {
    error(
      'Ошибка загрузки',
      errorMessage || `Не удалось загрузить ${entityName.toLowerCase()}`
    )
  }

  const showValidationError = (message = 'Проверьте правильность заполнения полей') => {
    warn('Ошибка валидации', message)
  }

  const showNetworkError = () => {
    error(
      'Ошибка сети',
      'Проверьте подключение к интернету и попробуйте снова'
    )
  }

  const showUnauthorizedError = () => {
    error(
      'Нет доступа',
      'У вас недостаточно прав для выполнения этого действия'
    )
  }

  return {
    // Базовые методы
    show,
    success,
    info,
    warn,
    error,
    clear,
    remove,

    // Удобные методы
    showSaveSuccess,
    showDeleteSuccess,
    showSaveError,
    showDeleteError,
    showLoadError,
    showValidationError,
    showNetworkError,
    showUnauthorizedError
  }
}

import{s as y}from"./index.BH7IgUdp.js";import{c as g,o as S,a as E,m as C}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{B as O,L as w,b as v,v as b,U as j,I as h,a as L}from"./index.BaVCXmir.js";import{B as P}from"./index.CDQpPXyE.js";var x={name:"ChevronRightIcon",extends:y};function T(t,e,o,r,n,s){return S(),g("svg",C({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t.pti()),e[0]||(e[0]=[E("path",{d:"M4.38708 13C4.28408 13.0005 4.18203 12.9804 4.08691 12.9409C3.99178 12.9014 3.9055 12.8433 3.83313 12.7701C3.68634 12.6231 3.60388 12.4238 3.60388 12.2161C3.60388 12.0084 3.68634 11.8091 3.83313 11.6622L8.50507 6.99022L3.83313 2.31827C3.69467 2.16968 3.61928 1.97313 3.62287 1.77005C3.62645 1.56698 3.70872 1.37322 3.85234 1.22959C3.99596 1.08597 4.18972 1.00371 4.3928 1.00012C4.59588 0.996539 4.79242 1.07192 4.94102 1.21039L10.1669 6.43628C10.3137 6.58325 10.3962 6.78249 10.3962 6.99022C10.3962 7.19795 10.3137 7.39718 10.1669 7.54416L4.94102 12.7701C4.86865 12.8433 4.78237 12.9014 4.68724 12.9409C4.59212 12.9804 4.49007 13.0005 4.38708 13Z",fill:"currentColor"},null,-1)]),16)}x.render=T;var B=O.extend({name:"focustrap-directive"}),H=P.extend({style:B});function m(t){"@babel/helpers - typeof";return m=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(t)}function $(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),o.push.apply(o,r)}return o}function F(t){for(var e=1;e<arguments.length;e++){var o=arguments[e]!=null?arguments[e]:{};e%2?$(Object(o),!0).forEach(function(r){I(t,r,o[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):$(Object(o)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(o,r))})}return t}function I(t,e,o){return(e=D(e))in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function D(t){var e=k(t,"string");return m(e)=="symbol"?e:e+""}function k(t,e){if(m(t)!="object"||!t)return t;var o=t[Symbol.toPrimitive];if(o!==void 0){var r=o.call(t,e);if(m(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var U=H.extend("focustrap",{mounted:function(e,o){var r=o.value||{},n=r.disabled;n||(this.createHiddenFocusableElements(e,o),this.bind(e,o),this.autoElementFocus(e,o)),e.setAttribute("data-pd-focustrap",!0),this.$el=e},updated:function(e,o){var r=o.value||{},n=r.disabled;n&&this.unbind(e)},unmounted:function(e){this.unbind(e)},methods:{getComputedSelector:function(e){return':not(.p-hidden-focusable):not([data-p-hidden-focusable="true"])'.concat(e??"")},bind:function(e,o){var r=this,n=o.value||{},s=n.onFocusIn,i=n.onFocusOut;e.$_pfocustrap_mutationobserver=new MutationObserver(function(u){u.forEach(function(l){if(l.type==="childList"&&!e.contains(document.activeElement)){var f=function(a){var c=h(a)?h(a,r.getComputedSelector(e.$_pfocustrap_focusableselector))?a:b(e,r.getComputedSelector(e.$_pfocustrap_focusableselector)):b(a);return L(c)?c:a.nextSibling&&f(a.nextSibling)};v(f(l.nextSibling))}})}),e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_mutationobserver.observe(e,{childList:!0}),e.$_pfocustrap_focusinlistener=function(u){return s&&s(u)},e.$_pfocustrap_focusoutlistener=function(u){return i&&i(u)},e.addEventListener("focusin",e.$_pfocustrap_focusinlistener),e.addEventListener("focusout",e.$_pfocustrap_focusoutlistener)},unbind:function(e){e.$_pfocustrap_mutationobserver&&e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_focusinlistener&&e.removeEventListener("focusin",e.$_pfocustrap_focusinlistener)&&(e.$_pfocustrap_focusinlistener=null),e.$_pfocustrap_focusoutlistener&&e.removeEventListener("focusout",e.$_pfocustrap_focusoutlistener)&&(e.$_pfocustrap_focusoutlistener=null)},autoFocus:function(e){this.autoElementFocus(this.$el,{value:F(F({},e),{},{autoFocus:!0})})},autoElementFocus:function(e,o){var r=o.value||{},n=r.autoFocusSelector,s=n===void 0?"":n,i=r.firstFocusableSelector,u=i===void 0?"":i,l=r.autoFocus,f=l===void 0?!1:l,p=b(e,"[autofocus]".concat(this.getComputedSelector(s)));f&&!p&&(p=b(e,this.getComputedSelector(u))),v(p)},onFirstHiddenElementFocus:function(e){var o,r=e.currentTarget,n=e.relatedTarget,s=n===r.$_pfocustrap_lasthiddenfocusableelement||!((o=this.$el)!==null&&o!==void 0&&o.contains(n))?b(r.parentElement,this.getComputedSelector(r.$_pfocustrap_focusableselector)):r.$_pfocustrap_lasthiddenfocusableelement;v(s)},onLastHiddenElementFocus:function(e){var o,r=e.currentTarget,n=e.relatedTarget,s=n===r.$_pfocustrap_firsthiddenfocusableelement||!((o=this.$el)!==null&&o!==void 0&&o.contains(n))?w(r.parentElement,this.getComputedSelector(r.$_pfocustrap_focusableselector)):r.$_pfocustrap_firsthiddenfocusableelement;v(s)},createHiddenFocusableElements:function(e,o){var r=this,n=o.value||{},s=n.tabIndex,i=s===void 0?0:s,u=n.firstFocusableSelector,l=u===void 0?"":u,f=n.lastFocusableSelector,p=f===void 0?"":f,a=function(_){return j("span",{class:"p-hidden-accessible p-hidden-focusable",tabIndex:i,role:"presentation","aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0,onFocus:_?.bind(r)})},c=a(this.onFirstHiddenElementFocus),d=a(this.onLastHiddenElementFocus);c.$_pfocustrap_lasthiddenfocusableelement=d,c.$_pfocustrap_focusableselector=l,c.setAttribute("data-pc-section","firstfocusableelement"),d.$_pfocustrap_firsthiddenfocusableelement=c,d.$_pfocustrap_focusableselector=p,d.setAttribute("data-pc-section","lastfocusableelement"),e.prepend(c),e.append(d)}}});export{U as F,x as s};

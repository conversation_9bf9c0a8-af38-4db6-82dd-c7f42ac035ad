import{s as b,p as g}from"./utils.BUKUcbtE.js";import{B as y}from"./index.BaVCXmir.js";import{c as a,o,b as n,a as p,m as t,p as s,d as $,g as h,n as v,r as k,w as C,q as w}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{_ as x}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{r as B,b as S}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./bundle-mjs.D6B6e0vX.js";var P=`
    .p-card {
        background: dt('card.background');
        color: dt('card.color');
        box-shadow: dt('card.shadow');
        border-radius: dt('card.border.radius');
        display: flex;
        flex-direction: column;
    }

    .p-card-caption {
        display: flex;
        flex-direction: column;
        gap: dt('card.caption.gap');
    }

    .p-card-body {
        padding: dt('card.body.padding');
        display: flex;
        flex-direction: column;
        gap: dt('card.body.gap');
    }

    .p-card-title {
        font-size: dt('card.title.font.size');
        font-weight: dt('card.title.font.weight');
    }

    .p-card-subtitle {
        color: dt('card.subtitle.color');
    }
`,V={root:"p-card p-component",header:"p-card-header",body:"p-card-body",caption:"p-card-caption",title:"p-card-title",subtitle:"p-card-subtitle",content:"p-card-content",footer:"p-card-footer"},z=y.extend({name:"card",style:P,classes:V}),M={name:"BaseCard",extends:b,style:z,provide:function(){return{$pcCard:this,$parentInstance:this}}},c={name:"Card",extends:M,inheritAttrs:!1};function O(e,d,i,r,f,u){return o(),a("div",t({class:e.cx("root")},e.ptmi("root")),[e.$slots.header?(o(),a("div",t({key:0,class:e.cx("header")},e.ptm("header")),[s(e.$slots,"header")],16)):n("",!0),p("div",t({class:e.cx("body")},e.ptm("body")),[e.$slots.title||e.$slots.subtitle?(o(),a("div",t({key:0,class:e.cx("caption")},e.ptm("caption")),[e.$slots.title?(o(),a("div",t({key:0,class:e.cx("title")},e.ptm("title")),[s(e.$slots,"title")],16)):n("",!0),e.$slots.subtitle?(o(),a("div",t({key:1,class:e.cx("subtitle")},e.ptm("subtitle")),[s(e.$slots,"subtitle")],16)):n("",!0)],16)):n("",!0),p("div",t({class:e.cx("content")},e.ptm("content")),[s(e.$slots,"content")],16),e.$slots.footer?(o(),a("div",t({key:1,class:e.cx("footer")},e.ptm("footer")),[s(e.$slots,"footer")],16)):n("",!0)],16)],16)}c.render=O;const j=$({__name:"Card",setup(e,{expose:d}){d();const r={theme:B({root:`flex flex-col rounded-xl
        bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0
        shadow-md`,header:"",body:"p-5 flex flex-col gap-2",caption:"flex flex-col gap-2",title:"font-medium text-xl",subtitle:"text-surface-500 dark:text-surface-400",content:"",footer:""}),get Card(){return c},get ptViewMerge(){return g}};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}});function q(e,d,i,r,f,u){return o(),h(r.Card,{unstyled:"",pt:r.theme,ptOptions:{mergeProps:r.ptViewMerge}},v({_:2},[k(e.$slots,(A,l)=>({name:l,fn:C(m=>[s(e.$slots,l,S(w(m??{})))])}))]),1032,["pt","ptOptions"])}const F=x(j,[["render",q]]);export{F as default};

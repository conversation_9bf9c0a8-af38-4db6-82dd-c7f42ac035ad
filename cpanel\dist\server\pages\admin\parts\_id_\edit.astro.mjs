import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout, I as Icon } from '../../../../chunks/AdminLayout_DrlBSzRq.mjs';
import { t as trpc } from '../../../../chunks/trpc_DApR3DD7.mjs';
import { P as PartWizard } from '../../../../chunks/PartWizard_DfzK7ldw.mjs';
export { r as renderers } from '../../../../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Astro = createAstro();
const $$Edit = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Edit;
  const { id } = Astro2.params;
  if (!id) {
    return Astro2.redirect("/admin/parts");
  }
  let part;
  try {
    part = await trpc.crud.part.findUnique.query({
      where: { id: Number(id) },
      include: {
        image: true,
        partCategory: true
      }
    });
    if (!part) {
      return Astro2.redirect("/admin/parts");
    }
  } catch (error) {
    console.error("Error loading part:", error);
    return Astro2.redirect("/admin/parts");
  }
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-6xl mx-auto"> <div class="mb-6"> <div class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400 mb-2"> <a href="/admin/parts" class="hover:text-primary">Запчасти</a> <span>/</span> <a${addAttribute(`/admin/parts/${part.id}`, "href")} class="hover:text-primary">#${part.id}</a> <span>/</span> <span>Редактирование</span> </div> <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
Редактирование: ${part.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"} </h1> </div> ${renderComponent($$result2, "PartWizard", PartWizard, { "client:load": true, "part": part, "mode": "edit", "client:component-hydration": "load", "client:component-path": "@/components/admin/parts/PartWizard.vue", "client:component-export": "default" })} <div class="mt-6 flex gap-3"> <a${addAttribute(`/admin/parts/${part.id}`, "href")} class="inline-flex items-center px-4 py-2 bg-primary text-primary-contrast rounded-lg hover:bg-primary-600 transition-colors"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-eye", "class": "mr-2 w-4 h-4 inline-block" })}
Просмотр запчасти
</a> <a href="/admin/parts" class="inline-flex items-center px-4 py-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-lg hover:bg-surface-300 dark:hover:bg-surface-600 transition-colors"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-arrow-left", "class": "mr-2 w-4 h-4 inline-block" })}
Назад к списку
</a> </div> </div> ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/[id]/edit.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/[id]/edit.astro";
const $$url = "/admin/parts/[id]/edit";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Edit,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

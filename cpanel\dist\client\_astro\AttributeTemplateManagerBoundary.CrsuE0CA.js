import{E as Iu}from"./ErrorBoundary.B0AhELGe.js";import{u as W}from"./useTrpc.spLZjt2f.js";import{a as Gu}from"./useUrlParams.D0jSWJSf.js";import bu from"./Card.C4y0_bWr.js";import Z from"./Button.DrThv2lH.js";import{I as $}from"./InputText.DOJMNEP-.js";import{D as mu,s as fu,a as Su}from"./index.BWD5ZO4k.js";import{T as Cu}from"./Tag.DTFTku6q.js";import{D as vu}from"./Dialog.Ct7C9BO5.js";import{u as tu}from"./useToast.pIbuf2bs.js";import{u as Lu}from"./useAuth.D4HmQrUw.js";import{I as pu}from"./Icon.By8t0-Wj.js";import{V as Du}from"./Textarea.BLEHJ3ym.js";import{I as Uu}from"./InputNumber.vgPO18dj.js";import{w as gu,c as Mu,a as cu}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{a as Ru}from"./index.By2TJOuX.js";import{_ as H}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as J,c as g,o as i,l as Nu,F as xu,r as Eu,a as t,e as a,h as M,U as Pu,g as G,w as s,b as w,j as R,i as au,A as qu}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{n as z,t as E,r as d}from"./reactivity.esm-bundler.BQ12LWmY.js";import{C as Ou}from"./Checkbox.C7bFmmzc.js";import{V as Bu}from"./AutoComplete.rPzROuMW.js";import{S as ju}from"./Select.CQBzSu6y.js";import{T as zu,P as Ku}from"./trash.4HbnIIsp.js";import{c as Vu}from"./createLucideIcon.NtN1-Ts2.js";import"./triangle-alert.CP-lXbmj.js";import"./trpc.BpyaUO08.js";import"./useErrorHandler.DVDazL16.js";import"./router.DKcY2uv6.js";import"./utils.BUKUcbtE.js";import"./index.BaVCXmir.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.6ykohhwZ.js";import"./index.BH7IgUdp.js";import"./index.CDQpPXyE.js";import"./index.COq_zjeV.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";import"./index.D4QD70nN.js";import"./SecondaryButton.DkELYl7Q.js";import"./index.PhWaFJhe.js";import"./auth-client.CMFsScx1.js";import"./types.C07aSKae.js";import"./types.FgRm47Sn.js";/* empty css                       */import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xu=Vu("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hu=Vu("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),Ju=`m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`,Yu=`py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`,Qu="leading-none",Wu="ml-2 w-4 h-4 cursor-pointer",Zu=`border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500 flex-1 inline-flex`,$u=J({__name:"InputChips",props:{modelValue:{},separator:{},addOnBlur:{type:Boolean},allowDuplicate:{type:Boolean},max:{}},emits:["update:modelValue"],setup(T,{expose:e,emit:p}){e();const u=T,f=p,c=M({get:()=>u.modelValue,set:m=>f("update:modelValue",m)}),l=d(""),y=d(null),n=()=>{if(l.value.trim()!==""){if(u.max&&c.value.length>=u.max)return;if(!u.allowDuplicate&&c.value.includes(l.value.trim())){l.value="";return}c.value=[...c.value,l.value.trim()],l.value=""}},v=m=>{c.value=c.value.filter((B,F)=>F!==m)},b={props:u,emit:f,model:c,inputValue:l,inputRef:y,addValue:n,removeValue:v,handleBackspace:()=>{l.value===""&&c.value.length>0&&v(c.value.length-1)},focusInput:async()=>{await Pu(),y.value?.focus()},containerClass:Ju,tokenClass:Yu,labelClass:Qu,removeIconClass:Wu,inputClass:Zu,get TimesIcon(){return Ru}};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}}),u4=["onClick"],e4=["onKeydown"];function l4(T,e,p,u,f,c){return i(),g("div",{class:z(u.containerClass),onClick:u.focusInput},[(i(!0),g(xu,null,Eu(u.model,(l,y)=>(i(),g("div",{key:y,class:z(u.tokenClass)},[t("span",{class:z(u.labelClass)},E(l),1),t("span",{class:z(u.removeIconClass),onClick:gu(n=>u.removeValue(y),["stop"])},[a(u.TimesIcon)],8,u4)]))),128)),Nu(t("input",{ref:"inputRef",type:"text","onUpdate:modelValue":e[0]||(e[0]=l=>u.inputValue=l),class:z(u.inputClass),onKeydown:[cu(gu(u.addValue,["prevent"]),["enter"]),cu(u.handleBackspace,["backspace"])]},null,40,e4),[[Mu,u.inputValue]])])}const t4=H($u,[["render",l4]]),a4=J({__name:"EditSynonymGroupDialog",props:{visible:{type:Boolean},templateId:{},group:{}},emits:["update:visible","saved"],setup(T,{expose:e,emit:p}){e();const u=T,f=p,{attributeSynonyms:c}=W(),l=tu(),y=M({get:()=>u.visible,set:V=>f("update:visible",V)}),n=M(()=>!!u.group?.id),v=M(()=>n.value?"Редактировать группу":"Создать группу"),k=[{label:"EXACT",value:"EXACT"},{label:"NEAR",value:"NEAR"},{label:"LEGACY",value:"LEGACY"}],x=d({name:"",description:"",compatibilityLevel:"EXACT",notes:""}),b=d({}),m=d(!1);R(()=>u.group,V=>{V?x.value={name:V.name||"",description:V.description||null,compatibilityLevel:V.compatibilityLevel||"EXACT",notes:V.notes||null}:x.value={name:"",description:null,compatibilityLevel:"EXACT",notes:null}},{immediate:!0});const B=()=>(b.value={},x.value.name.trim()||(b.value.name="Введите название"),Object.keys(b.value).length===0),F=()=>{y.value=!1},_={props:u,emit:f,attributeSynonyms:c,toast:l,visible:y,isEdit:n,dialogTitle:v,compatibilityOptions:k,form:x,errors:b,saving:m,validate:B,close:F,save:async()=>{if(B()){m.value=!0;try{n.value?(await c.groups.update({id:u.group.id,...x.value}),l.success("Группа обновлена")):(await c.groups.create({templateId:u.templateId,...x.value}),l.success("Группа создана")),f("saved"),F()}catch(V){l.error(V?.message||"Не удалось сохранить группу")}finally{m.value=!1}}},VDialog:vu,VInputText:$,VTextarea:Du,VButton:Z,VSelect:ju};return Object.defineProperty(_,"__isScriptSetup",{enumerable:!1,value:!0}),_}}),o4={class:"space-y-4"},s4={key:0,class:"p-error"},r4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"};function n4(T,e,p,u,f,c){return i(),G(u.VDialog,{visible:u.visible,"onUpdate:visible":e[4]||(e[4]=l=>u.visible=l),modal:"",header:u.dialogTitle,style:{width:"34rem"}},{footer:s(()=>[a(u.VButton,{label:"Отмена",severity:"secondary",onClick:u.close}),a(u.VButton,{label:u.isEdit?"Сохранить":"Создать",loading:u.saving,onClick:u.save},null,8,["label","loading"])]),default:s(()=>[t("div",o4,[t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Название *",-1)),a(u.VInputText,{modelValue:u.form.name,"onUpdate:modelValue":e[0]||(e[0]=l=>u.form.name=l),placeholder:"Стандартные типы уплотнений",class:z(["w-full",{"p-invalid":!!u.errors.name}])},null,8,["modelValue","class"]),u.errors.name?(i(),g("small",s4,E(u.errors.name),1)):w("",!0)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Описание",-1)),a(u.VTextarea,{modelValue:u.form.description,"onUpdate:modelValue":e[1]||(e[1]=l=>u.form.description=l),rows:"2",class:"w-full"},null,8,["modelValue"])]),t("div",r4,[t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Уровень совместимости",-1)),a(u.VSelect,{modelValue:u.form.compatibilityLevel,"onUpdate:modelValue":e[2]||(e[2]=l=>u.form.compatibilityLevel=l),options:u.compatibilityOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue"])]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"},"Заметки",-1)),a(u.VInputText,{modelValue:u.form.notes,"onUpdate:modelValue":e[3]||(e[3]=l=>u.form.notes=l),placeholder:"Например: для старых спецификаций",class:"w-full"},null,8,["modelValue"])])])])]),_:1},8,["visible","header"])}const i4=H(a4,[["render",n4]]),d4=J({__name:"SynonymValueEditor",props:{groupId:{}},setup(T,{expose:e}){e();const p=T,{attributeSynonyms:u}=W(),f=tu(),c=d([]),l=d(!1),y=d(""),n=d(!1);R(y,m=>{const B=(m||"").trim();n.value=B.length>0&&!c.value.some(F=>F.value.toLowerCase()===B.toLowerCase())});const v=async()=>{l.value=!0;try{const m=await u.synonyms.findMany({groupId:p.groupId});Array.isArray(m)&&(c.value=m)}catch(m){f.error(m?.message||"Не удалось загрузить значения")}finally{l.value=!1}},k=async()=>{const m=y.value.trim();if(m){if(c.value.some(B=>B.value.toLowerCase()===m.toLowerCase())){f.error("Дубликаты не допускаются");return}try{const B=await u.synonyms.create({groupId:p.groupId,value:m});B&&typeof B=="object"&&(c.value.push(B),y.value="")}catch(B){f.error(B?.message||"Не удалось добавить значение")}}},x=async m=>{if(confirm("Удалить значение?"))try{await u.synonyms.delete({id:m.id}),c.value=c.value.filter(B=>B.id!==m.id)}catch(B){f.error(B?.message||"Не удалось удалить значение")}};au(v),R(()=>p.groupId,v);const b={props:p,attributeSynonyms:u,toast:f,synonyms:c,loading:l,newValue:y,canAdd:n,load:v,addValue:k,removeValue:x,VInputText:$,VButton:Z,VDataTable:mu,get Column(){return fu},Icon:pu};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}}),c4={class:"space-y-4"},m4={class:"flex gap-2"};function f4(T,e,p,u,f,c){return i(),g("div",c4,[t("div",m4,[a(u.VInputText,{modelValue:u.newValue,"onUpdate:modelValue":e[0]||(e[0]=l=>u.newValue=l),placeholder:"Введите значение и нажмите Добавить",class:"flex-1",onKeyup:cu(u.addValue,["enter"])},null,8,["modelValue"]),a(u.VButton,{label:"Добавить",onClick:u.addValue,disabled:!u.canAdd},{icon:s(()=>[a(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1},8,["disabled"])]),a(u.VDataTable,{value:u.synonyms,loading:u.loading,class:"p-datatable-sm","table-style":"min-width: 24rem","striped-rows":""},{default:s(()=>[a(u.Column,{field:"value",header:"Значение"}),a(u.Column,{header:"",style:{width:"80px"}},{body:s(({data:l})=>[a(u.VButton,{size:"small",severity:"danger",outlined:"",onClick:y=>u.removeValue(l)},{default:s(()=>[a(u.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["value","loading"])])}const v4=H(d4,[["render",f4]]),p4=J({__name:"AttributeSynonymManager",props:{template:{}},setup(T,{expose:e}){e();const p=T,u=tu(),{attributeSynonyms:f}=W(),c=d([]),l=d(0),y=d(!1),n=d(""),v=d(null),k=d(!1),x=d(null);let b;const m=async()=>{if(p.template?.id){y.value=!0;try{const C=await f.groups.findMany({templateId:p.template.id,search:n.value||void 0,limit:50,offset:0});if(C&&typeof C=="object"&&(c.value=C.groups||[],l.value=C.total||0,v.value)){const N=c.value.find(X=>X.id===v.value.id);N&&(v.value=N)}}catch(C){u.error(C?.message||"Не удалось загрузить группы")}finally{y.value=!1}}},B=()=>{clearTimeout(b),b=setTimeout(()=>m(),400)},F=C=>{switch(C){case"EXACT":return"EXACT";case"NEAR":return"NEAR";case"LEGACY":return"LEGACY";default:return String(C)}},A=C=>C==="EXACT"?"success":C==="NEAR"?"warn":"secondary",_=C=>{v.value=C},V=()=>{x.value=null,k.value=!0},S=C=>{x.value=C,k.value=!0},L=async C=>{if(confirm(`Удалить группу "${C.name}"?`))try{await f.groups.delete({id:C.id}),v.value?.id===C.id&&(v.value=null),m()}catch(N){u.error(N?.message||"Не удалось удалить группу")}},U=()=>{k.value=!1,m()};au(()=>{m()});const O={props:p,toast:u,attributeSynonyms:f,groups:c,total:l,loadingGroups:y,search:n,selectedGroup:v,showGroupDialog:k,editingGroup:x,get reloadTimer(){return b},set reloadTimer(C){b=C},loadGroups:m,debouncedReload:B,compatibilityLabel:F,compatibilitySeverity:A,selectGroup:_,openCreateGroup:V,openEditGroup:S,deleteGroup:L,onGroupSaved:U,VCard:bu,VButton:Z,VInputText:$,VDataTable:mu,VTag:Cu,get Column(){return fu},EditSynonymGroupDialog:i4,SynonymValueEditor:v4,get PlusCircleIcon(){return Xu},get ListIcon(){return Hu},get PencilIcon(){return Ku},get TrashIcon(){return zu}};return Object.defineProperty(O,"__isScriptSetup",{enumerable:!1,value:!0}),O}}),y4={class:"flex justify-center gap-4"},g4={class:"p-4 space-y-3"},b4={class:"flex items-center justify-between"},C4={class:"flex flex-col"},D4={class:"font-medium"},x4={key:0,class:"text-surface-500"},E4={class:"flex gap-2"},B4={class:"p-4 space-y-4"},V4={class:"flex items-center justify-between"},F4={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},w4={key:0,class:"flex items-center gap-2 mt-1"},T4={key:0,class:"text-surface-500"},k4={key:0,class:"flex gap-2"},_4={key:0,class:"text-surface-500"},h4={key:1};function A4(T,e,p,u,f,c){return i(),g("div",y4,[a(u.VCard,null,{content:s(()=>[t("div",g4,[t("div",b4,[e[4]||(e[4]=t("h3",{class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},"Группы синонимов",-1)),a(u.VButton,{size:"small",onClick:u.openCreateGroup},{default:s(()=>[a(u.PlusCircleIcon,{class:"w-4 h-4"})]),_:1})]),a(u.VInputText,{modelValue:u.search,"onUpdate:modelValue":e[0]||(e[0]=l=>u.search=l),placeholder:"Поиск по названию / описанию...",onInput:u.debouncedReload},null,8,["modelValue"]),a(u.VDataTable,{value:u.groups,loading:u.loadingGroups,"table-style":"min-width: 24rem",class:"p-datatable-sm","striped-rows":""},{default:s(()=>[a(u.Column,{header:"Название"},{body:s(({data:l})=>[t("div",C4,[t("div",D4,E(l.name),1),l.description?(i(),g("small",x4,E(l.description),1)):w("",!0)])]),_:1}),a(u.Column,{header:"Уровень",style:{width:"120px"}},{body:s(({data:l})=>[a(u.VTag,{value:u.compatibilityLabel(l.compatibilityLevel),severity:u.compatibilitySeverity(l.compatibilityLevel)},null,8,["value","severity"])]),_:1}),a(u.Column,{header:"#",style:{width:"64px"}},{body:s(({data:l})=>[a(u.VTag,{value:l._count?.synonyms||0,severity:"secondary"},null,8,["value"])]),_:1}),a(u.Column,{header:"",style:{width:"150px"}},{body:s(({data:l})=>[t("div",E4,[a(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:y=>u.selectGroup(l)},{default:s(()=>[a(u.ListIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:y=>u.openEditGroup(l)},{default:s(()=>[a(u.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),a(u.VButton,{size:"small",severity:"danger",outlined:"",onClick:y=>u.deleteGroup(l)},{default:s(()=>[a(u.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","loading"])])]),_:1}),a(u.VCard,null,{content:s(()=>[t("div",B4,[t("div",V4,[t("div",null,[t("h3",F4,E(u.selectedGroup?u.selectedGroup.name:"Выберите группу"),1),u.selectedGroup?(i(),g("div",w4,[a(u.VTag,{value:u.compatibilityLabel(u.selectedGroup.compatibilityLevel),severity:u.compatibilitySeverity(u.selectedGroup.compatibilityLevel)},null,8,["value","severity"]),u.selectedGroup.notes?(i(),g("small",T4,E(u.selectedGroup.notes),1)):w("",!0)])):w("",!0)]),u.selectedGroup?(i(),g("div",k4,[a(u.VButton,{size:"small",severity:"secondary",outlined:"",onClick:e[1]||(e[1]=l=>u.openEditGroup(u.selectedGroup))},{default:s(()=>[a(u.PencilIcon,{class:"w-4 h-4"})]),_:1}),a(u.VButton,{size:"small",severity:"danger",outlined:"",onClick:e[2]||(e[2]=l=>u.deleteGroup(u.selectedGroup))},{default:s(()=>[a(u.TrashIcon,{class:"w-4 h-4"})]),_:1})])):w("",!0)]),u.selectedGroup?(i(),g("div",h4,[a(u.SynonymValueEditor,{"group-id":u.selectedGroup.id},null,8,["group-id"])])):(i(),g("div",_4,"Слева выберите группу, чтобы редактировать значения."))])]),_:1}),a(u.EditSynonymGroupDialog,{visible:u.showGroupDialog,"onUpdate:visible":e[3]||(e[3]=l=>u.showGroupDialog=l),"template-id":p.template.id,group:u.editingGroup,onSaved:u.onGroupSaved},null,8,["visible","template-id","group"])])}const Fu=H(p4,[["render",A4]]),I4=J({__name:"TemplateForm",props:{modelValue:{},groups:{},loading:{type:Boolean,default:!1}},emits:["update:modelValue","save","cancel","group-created"],setup(T,{expose:e,emit:p}){e();const u=T,f=p,{attributeTemplates:c}=W(),l=tu(),{userRole:y}=Lu(),n=M({get:()=>u.modelValue,set:r=>f("update:modelValue",r)}),v=d({}),k=d(!1),x=d(!1),b=d({name:"",description:""}),m=d(!1),B=()=>{if(!n.value.id){l.info("Сначала сохраните шаблон");return}m.value=!0},F=d(null),A=d([]),_=d(null),V=[{label:"Строка",value:"STRING"},{label:"Число",value:"NUMBER"},{label:"Логическое",value:"BOOLEAN"},{label:"Дата",value:"DATE"},{label:"JSON",value:"JSON"}],S=[{label:"мм",value:"MM"},{label:"дюймы",value:"INCH"},{label:"футы",value:"FT"},{label:"г",value:"G"},{label:"кг",value:"KG"},{label:"т",value:"T"},{label:"фунты",value:"LB"},{label:"мл",value:"ML"},{label:"л",value:"L"},{label:"галлоны",value:"GAL"},{label:"шт",value:"PCS"},{label:"комплект",value:"SET"},{label:"пара",value:"PAIR"},{label:"бар",value:"BAR"},{label:"PSI",value:"PSI"},{label:"кВт",value:"KW"},{label:"л.с.",value:"HP"},{label:"Н⋅м",value:"NM"},{label:"об/мин",value:"RPM"},{label:"°C",value:"C"},{label:"°F",value:"F"},{label:"%",value:"PERCENT"}],L=d(V),U=d(S),O=M({get:()=>V.find(r=>r.value===n.value.dataType)||null,set:r=>{n.value.dataType=r?r.value:null}}),C=M({get:()=>S.find(r=>r.value===n.value.unit)||null,set:r=>{n.value.unit=r?r.value:null}}),N=r=>{console.log("filterDataTypes вызван в TemplateForm",{event:r});const I=r.query?.toLowerCase()||"";I.trim()?(L.value=V.filter(q=>q.label.toLowerCase().includes(I)),console.log("Фильтруем типы данных:",L.value.length)):(L.value=[...V],console.log("Показываем все типы данных:",L.value.length))},X=r=>{console.log("filterUnits вызван в TemplateForm",{event:r});const I=r.query?.toLowerCase()||"";I.trim()?(U.value=S.filter(q=>q.label.toLowerCase().includes(I)),console.log("Фильтруем единицы:",U.value.length)):(U.value=[...S],console.log("Показываем все единицы:",U.value.length))},K=()=>{n.value.groupId&&(F.value=u.groups.find(r=>r.id===n.value.groupId)||null)},ou=M(()=>!!n.value.id),uu=M(()=>n.value.name&&n.value.title&&n.value.dataType&&!Object.keys(v.value).length),su=M(()=>`w-full ${v.value.dataType?"p-invalid":""}`),eu=()=>{v.value={},n.value.name?/^[a-z0-9_]+$/.test(n.value.name)||(v.value.name="Только строчные буквы, цифры и подчеркивания"):v.value.name="Системное имя обязательно",n.value.title||(v.value.title="Отображаемое название обязательно"),n.value.dataType||(v.value.dataType="Тип данных обязателен")},ru=()=>{eu(),uu.value?f("save",n.value):l.error("Пожалуйста, исправьте ошибки в форме")},nu=async r=>{console.log("searchGroups вызван в TemplateForm",{event:r,groupsLength:u.groups.length});const I=r.query||"";if(_.value&&clearTimeout(_.value),!I.trim()){A.value=[...u.groups],console.log("Показываем все группы:",A.value.length);return}_.value=setTimeout(async()=>{try{A.value=u.groups.filter(q=>q.name.toLowerCase().includes(I.toLowerCase())||q.description&&q.description.toLowerCase().includes(I.toLowerCase()))}catch(q){console.error("Ошибка поиска групп:",q),l.error("Не удалось выполнить поиск групп"),A.value=u.groups.filter(Q=>Q.name.toLowerCase().includes(I.toLowerCase())||Q.description&&Q.description.toLowerCase().includes(I.toLowerCase()))}},300)},iu=r=>{n.value.groupId=r.value.id,F.value=r.value,l.info(`Выбрана группа: ${r.value.name}`)},Y=()=>{n.value.groupId=null,F.value=null,l.info("Группа сброшена")},du=async()=>{if(!b.value.name){l.error("Введите название группы");return}try{x.value=!0,l.info("Создание группы...");const r=await c.createGroup(b.value);r&&typeof r=="object"&&"id"in r&&(n.value.groupId=r.id,F.value=r,k.value=!1,b.value={name:"",description:""},f("group-created",r))}catch(r){console.error("Ошибка создания группы:",r),r.message?.includes("уже существует")?l.error("Группа с таким названием уже существует"):l.error(r.message||"Не удалось создать группу")}finally{x.value=!1}};R(()=>u.modelValue,r=>{if(!r){n.value={dataType:"STRING",isRequired:!1,allowedValues:[]};return}r.dataType||(n.value.dataType="STRING"),r.isRequired||(n.value.isRequired=!1),r.allowedValues||(n.value.allowedValues=[]),K()},{immediate:!0}),R(()=>u.groups,()=>{A.value=u.groups,K()},{immediate:!0}),au(()=>{console.log("TemplateForm монтируется"),L.value=[...V],U.value=[...S],A.value=[...u.groups],console.log("Инициализированы автокомплиты в TemplateForm:",{dataTypes:L.value.length,units:U.value.length,groups:A.value.length}),K()}),qu(()=>{_.value&&clearTimeout(_.value)}),R(()=>n.value.name,()=>{v.value.name&&delete v.value.name}),R(()=>n.value.title,()=>{v.value.title&&delete v.value.title}),R(()=>n.value.dataType,()=>{v.value.dataType&&delete v.value.dataType});const P={props:u,emit:f,attributeTemplates:c,toast:l,userRole:y,form:n,errors:v,showCreateGroupDialog:k,creatingGroup:x,newGroupForm:b,showSynonymsDialog:m,openSynonyms:B,selectedGroup:F,filteredGroups:A,searchTimeout:_,dataTypeOptions:V,unitOptions:S,filteredDataTypeOptions:L,filteredUnitOptions:U,selectedDataType:O,selectedUnit:C,filterDataTypes:N,filterUnits:X,findSelectedGroup:K,isEditing:ou,isValid:uu,dataTypeClass:su,validateForm:eu,save:ru,searchGroups:nu,onGroupSelect:iu,onGroupClear:Y,createGroup:du,Icon:pu,VInputText:$,VTextarea:Du,VInputNumber:Uu,InputChips:t4,VCheckbox:Ou,VButton:Z,VDialog:vu,VAutoComplete:Bu,AttributeSynonymManager:Fu};return Object.defineProperty(P,"__isScriptSetup",{enumerable:!1,value:!0}),P}}),G4={class:"template-form"},S4={class:"space-y-4"},L4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},U4={key:0,class:"p-error"},M4={key:0,class:"p-error"},R4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},N4={key:0,class:"p-error"},P4={class:"flex gap-2"},q4={key:0,class:"p-error"},O4={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-4"},j4={key:1},z4={key:2},K4={class:"mt-3"},X4={class:"flex items-center gap-4"},H4={class:"flex justify-end gap-3 mt-6 pt-4 border-t border-surface-200 dark:border-surface-700"},J4={class:"space-y-4"},Y4={key:1,class:"p-4 text-surface-500"};function Q4(T,e,p,u,f,c){return i(),g("div",G4,[t("div",S4,[t("div",L4,[t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Системное имя * ",-1)),a(u.VInputText,{modelValue:u.form.name,"onUpdate:modelValue":e[0]||(e[0]=l=>u.form.name=l),placeholder:"inner_diameter",class:z(["w-full",{"p-invalid":u.errors.name}])},null,8,["modelValue","class"]),u.errors.name?(i(),g("small",U4,E(u.errors.name),1)):w("",!0),e[22]||(e[22]=t("small",{class:"text-surface-500 dark:text-surface-400"}," Только строчные буквы, цифры и подчеркивания ",-1))]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Отображаемое название * ",-1)),a(u.VInputText,{modelValue:u.form.title,"onUpdate:modelValue":e[1]||(e[1]=l=>u.form.title=l),placeholder:"Внутренний диаметр",class:z(["w-full",{"p-invalid":u.errors.title}])},null,8,["modelValue","class"]),u.errors.title?(i(),g("small",M4,E(u.errors.title),1)):w("",!0)])]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),a(u.VTextarea,{modelValue:u.form.description,"onUpdate:modelValue":e[2]||(e[2]=l=>u.form.description=l),placeholder:"Подробное описание атрибута...",rows:"3",class:"w-full"},null,8,["modelValue"])]),t("div",R4,[t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип данных * ",-1)),a(u.VAutoComplete,{modelValue:u.selectedDataType,"onUpdate:modelValue":e[3]||(e[3]=l=>u.selectedDataType=l),suggestions:u.filteredDataTypeOptions,onComplete:u.filterDataTypes,onDropdownClick:e[4]||(e[4]=()=>u.filterDataTypes({query:""})),"option-label":"label","option-value":"value",placeholder:"Выберите тип",class:z(u.dataTypeClass),dropdown:""},null,8,["modelValue","suggestions","class"]),u.errors.dataType?(i(),g("small",N4,E(u.errors.dataType),1)):w("",!0)]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Единица измерения ",-1)),a(u.VAutoComplete,{modelValue:u.selectedUnit,"onUpdate:modelValue":e[5]||(e[5]=l=>u.selectedUnit=l),suggestions:u.filteredUnitOptions,onComplete:u.filterUnits,onDropdownClick:e[6]||(e[6]=()=>u.filterUnits({query:""})),"option-label":"label","option-value":"value",placeholder:"Выберите единицу",class:"w-full",dropdown:"","show-clear":""},null,8,["modelValue","suggestions"])])]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа ",-1)),t("div",P4,[a(u.VAutoComplete,{modelValue:u.selectedGroup,"onUpdate:modelValue":e[7]||(e[7]=l=>u.selectedGroup=l),suggestions:u.filteredGroups,onComplete:u.searchGroups,onDropdownClick:e[8]||(e[8]=()=>u.searchGroups({query:""})),"option-label":"name",placeholder:"Поиск группы...",class:z(["flex-1",{"p-invalid":u.errors.groupId}]),dropdown:"","dropdown-mode":"current","show-clear":"",onItemSelect:u.onGroupSelect,onClear:u.onGroupClear},null,8,["modelValue","suggestions","class"]),a(u.VButton,{onClick:e[9]||(e[9]=l=>u.showCreateGroupDialog=!0),severity:"secondary",outlined:"",size:"small",label:"Создать новую группу"},{icon:s(()=>[a(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})]),u.errors.groupId?(i(),g("small",q4,E(u.errors.groupId),1)):w("",!0)]),u.form.dataType==="NUMBER"?(i(),g("div",O4,[t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Минимальное значение ",-1)),a(u.VInputNumber,{modelValue:u.form.minValue,"onUpdate:modelValue":e[10]||(e[10]=l=>u.form.minValue=l),placeholder:"0",class:"w-full","use-grouping":!1,"min-fraction-digits":2,"max-fraction-digits":2},null,8,["modelValue"])]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Максимальное значение ",-1)),a(u.VInputNumber,{modelValue:u.form.maxValue,"onUpdate:modelValue":e[11]||(e[11]=l=>u.form.maxValue=l),placeholder:"100",class:"w-full","use-grouping":!1,"min-fraction-digits":2,"max-fraction-digits":2},null,8,["modelValue"])])])):w("",!0),u.form.dataType==="NUMBER"?(i(),g("div",j4,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Допустимое отклонение (tolerance) ",-1)),a(u.VInputNumber,{modelValue:u.form.tolerance,"onUpdate:modelValue":e[12]||(e[12]=l=>u.form.tolerance=l),placeholder:"0.1",class:"w-full","use-grouping":!1,"min-fraction-digits":1,"max-fraction-digits":4,min:0},null,8,["modelValue"]),e[31]||(e[31]=t("small",{class:"text-surface-500 dark:text-surface-400"}," Допустимое отклонение при сопоставлении числовых значений. Например: если эталон = 30.0 и допуск = 0.1, то значения от 29.9 до 30.1 будут считаться эквивалентными. ",-1))])):w("",!0),u.form.dataType==="STRING"?(i(),g("div",z4,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Допустимые значения ",-1)),a(u.InputChips,{modelValue:u.form.allowedValues,"onUpdate:modelValue":e[13]||(e[13]=l=>u.form.allowedValues=l),placeholder:"Введите значение и нажмите Enter",class:"w-full"},null,8,["modelValue"]),e[34]||(e[34]=t("small",{class:"text-surface-500 dark:text-surface-400"}," Оставьте пустым для любых значений. Например: steel, aluminum, plastic ",-1)),t("div",K4,[a(u.VButton,{severity:"secondary",outlined:"",label:"Синонимы",onClick:u.openSynonyms},{icon:s(()=>[a(u.Icon,{name:"pi pi-tags",class:"w-5 h-5"})]),_:1}),e[32]||(e[32]=t("small",{class:"ml-2 text-surface-500"},"Доступно после сохранения шаблона",-1))])])):w("",!0),t("div",X4,[a(u.VCheckbox,{modelValue:u.form.isRequired,"onUpdate:modelValue":e[14]||(e[14]=l=>u.form.isRequired=l),"input-id":"required",binary:""},null,8,["modelValue"]),e[35]||(e[35]=t("label",{for:"required",class:"text-sm text-surface-700 dark:text-surface-300"}," Обязательный атрибут ",-1))])]),t("div",H4,[a(u.VButton,{label:"Отмена",severity:"secondary",onClick:e[15]||(e[15]=l=>T.$emit("cancel"))}),a(u.VButton,{label:u.isEditing?"Обновить":"Создать",onClick:u.save,loading:p.loading,disabled:!u.isValid},null,8,["label","loading","disabled"])]),a(u.VDialog,{visible:u.showCreateGroupDialog,"onUpdate:visible":e[19]||(e[19]=l=>u.showCreateGroupDialog=l),modal:"",header:"Создать группу атрибутов",style:{width:"30rem"}},{footer:s(()=>[a(u.VButton,{label:"Отмена",severity:"secondary",onClick:e[18]||(e[18]=l=>u.showCreateGroupDialog=!1)}),a(u.VButton,{label:"Создать",onClick:u.createGroup,loading:u.creatingGroup,disabled:!u.newGroupForm.name},null,8,["loading","disabled"])]),default:s(()=>[t("div",J4,[t("div",null,[e[36]||(e[36]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название группы * ",-1)),a(u.VInputText,{modelValue:u.newGroupForm.name,"onUpdate:modelValue":e[16]||(e[16]=l=>u.newGroupForm.name=l),placeholder:"Размеры",class:"w-full"},null,8,["modelValue"])]),t("div",null,[e[37]||(e[37]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),a(u.VTextarea,{modelValue:u.newGroupForm.description,"onUpdate:modelValue":e[17]||(e[17]=l=>u.newGroupForm.description=l),placeholder:"Описание группы...",rows:"2",class:"w-full"},null,8,["modelValue"])])])]),_:1},8,["visible"]),a(u.VDialog,{visible:u.showSynonymsDialog,"onUpdate:visible":e[20]||(e[20]=l=>u.showSynonymsDialog=l),modal:"",header:"Синонимы значения",style:{width:"80rem"},breakpoints:{"1199px":"90vw","575px":"98vw"}},{default:s(()=>[u.form.id&&u.form.dataType==="STRING"?(i(),G(u.AttributeSynonymManager,{key:0,template:{id:u.form.id,dataType:u.form.dataType,title:u.form.title,name:u.form.name}},null,8,["template"])):(i(),g("div",Y4,"Сохраните шаблон, чтобы управлять синонимами."))]),_:1},8,["visible"])])}const W4=H(I4,[["render",Q4]]),Z4=J({__name:"AttributeTemplateManager",setup(T,{expose:e}){e();const{attributeTemplates:p,loading:u}=W(),f=d([]),c=d([]),l=d(0),y=d(25),n=d(0),v=d(!1);let k=0;const x=d(""),b=d(null),m=d(null),B=d("table"),F=Gu({search:"",groupId:void 0,dataType:void 0},{prefix:"attr_",numberParams:["groupId"],debounceMs:300});R(x,o=>{F.updateFilter("search",o||void 0)}),R(b,o=>{const D=o&&typeof o=="object"?o.id:o;F.updateFilter("groupId",D??void 0)}),R(m,o=>{const D=o&&typeof o=="object"?o.value:o;F.updateFilter("dataType",D??void 0)}),R(F.filters,o=>{const D=o.search||"",h=o.groupId??null,j=o.dataType??null;x.value!==D&&(x.value=D),b.value!==h&&(b.value=h),m.value!==j&&(m.value=j),n.value=0,P()});const A=d(!1),_=d(null),V=d({}),S=d(!1),L=d(!1),U=d(null),O=[{label:"Строка",value:"STRING"},{label:"Число",value:"NUMBER"},{label:"Логическое",value:"BOOLEAN"},{label:"Дата",value:"DATE"},{label:"JSON",value:"JSON"}],C=[{label:"Таблица",value:"table"},{label:"Карточки",value:"cards"}],N=d([]),X=d([]),K=d([]),ou=o=>{const D=o.query?.toLowerCase()||"";D.trim()?N.value=c.value.filter(h=>h.name.toLowerCase().includes(D)):N.value=[...c.value]},uu=o=>{const D=o.query?.toLowerCase()||"";D.trim()?X.value=O.filter(h=>h.label.toLowerCase().includes(D)):X.value=[...O]},su=o=>{const D=o.query?.toLowerCase()||"";D.trim()?K.value=C.filter(h=>h.label.toLowerCase().includes(D)):K.value=[...C]},eu=M(()=>f.value.filter(o=>Y(o._count)>0).length),ru=M(()=>f.value.filter(o=>Y(o._count)===0).length),nu=o=>O.find(h=>h.value===o)?.label||o,iu=o=>({MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"})[o]||o,Y=o=>o?(o.partAttributes||0)+(o.catalogItemAttributes||0)+(o.equipmentAttributes||0):0,du=o=>{if(!o)return"";const D=o.partAttributes||0,h=o.catalogItemAttributes||0,j=o.equipmentAttributes||0,lu=[];return D>0&&lu.push(`${D} зап.`),h>0&&lu.push(`${h} кат.`),j>0&&lu.push(`${j} тех.`),lu.join(", ")},P=async()=>{const o=++k;v.value=!0;try{const D=b.value?typeof b.value=="object"?b.value.id:b.value:void 0,h=m.value&&typeof m.value=="object"?m.value.value:void 0,j=await p.findMany({groupId:D,search:x.value||void 0,dataType:h,limit:y.value,offset:n.value*y.value});o===k&&j&&typeof j=="object"&&(f.value=j.templates||[],l.value=j.total||0)}catch(D){console.error("Ошибка загрузки шаблонов:",D),console.error("Не удалось загрузить шаблоны")}finally{o===k&&(v.value=!1)}},r=async()=>{try{const o=await p.findAllGroups();o&&Array.isArray(o)&&(c.value=o,N.value=[...o])}catch(o){console.error("Ошибка загрузки групп:",o)}};let I;const q=()=>{clearTimeout(I),I=setTimeout(()=>{n.value=0,F.updateFilter("search",x.value||void 0),P()},500)},Q=async()=>{await r(),await P()},wu=o=>{n.value=o.page,y.value=o.rows,P()},Tu=o=>{_.value=o,V.value={...o},A.value=!0},ku=o=>{U.value=o,L.value=!0},_u=()=>{_.value=null,V.value={dataType:"STRING",isRequired:!1,allowedValues:[]},A.value=!0},hu=async o=>{if(window.confirm(`Вы уверены, что хотите удалить шаблон "${o.title}"?`))try{await p.delete({id:o.id}),console.log("Шаблон успешно удален"),P()}catch(h){console.error("Ошибка удаления шаблона:",h),alert(h.message||"Не удалось удалить шаблон")}},Au=async o=>{try{S.value=!0,_.value?(await p.update({id:_.value.id,...o}),console.log("Шаблон успешно обновлен")):(await p.create(o),console.log("Шаблон успешно создан")),A.value=!1,_.value=null,V.value={},await r(),await P()}catch(D){console.error("Ошибка сохранения шаблона:",D),alert(D.message||"Не удалось сохранить шаблон")}finally{S.value=!1}};au(async()=>{X.value=[...O],K.value=[...C];const o=F.filters.value;x.value=o.search||"",b.value=o.groupId??null,m.value=o.dataType??null,await r(),await P()});const yu={attributeTemplates:p,loading:u,templates:f,groups:c,totalCount:l,pageSize:y,currentPage:n,tableLoading:v,get lastRequestId(){return k},set lastRequestId(o){k=o},searchQuery:x,selectedGroup:b,selectedDataType:m,viewMode:B,urlSync:F,showCreateDialog:A,editingTemplate:_,templateForm:V,saving:S,showSynonymsDialog:L,selectedTemplateForSynonyms:U,dataTypeOptions:O,viewModeOptions:C,groupSuggestions:N,dataTypeSuggestions:X,viewModeSuggestions:K,filterGroups:ou,filterDataTypes:uu,filterViewModes:su,usedTemplatesCount:eu,unusedTemplatesCount:ru,getDataTypeLabel:nu,getUnitLabel:iu,getTotalUsage:Y,getUsageDetails:du,loadTemplates:P,loadGroups:r,get searchTimeout(){return I},set searchTimeout(o){I=o},debouncedSearch:q,refreshData:Q,onPageChange:wu,editTemplate:Tu,openSynonyms:ku,createNewTemplate:_u,deleteTemplate:hu,saveTemplate:Au,VCard:bu,VButton:Z,VInputText:$,VDataTable:mu,VTag:Cu,VDialog:vu,get Column(){return fu},get Paginator(){return Su},TemplateForm:W4,VAutoComplete:Bu,AttributeSynonymManager:Fu,Icon:pu};return Object.defineProperty(yu,"__isScriptSetup",{enumerable:!1,value:!0}),yu}}),$4={class:"attribute-template-manager"},u0={key:0,class:"text-center py-12"},e0={key:1},l0={class:"flex items-center justify-between mb-6"},t0={class:"flex gap-3"},a0={class:"p-4"},o0={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},s0={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},r0={class:"p-4 text-center"},n0={class:"text-2xl font-bold text-primary mb-2"},i0={class:"p-4 text-center"},d0={class:"text-2xl font-bold text-green-600 mb-2"},c0={class:"p-4 text-center"},m0={class:"text-2xl font-bold text-blue-600 mb-2"},f0={class:"p-4 text-center"},v0={class:"text-2xl font-bold text-orange-600 mb-2"},p0={class:"font-mono text-sm text-surface-700 dark:text-surface-300"},y0={class:"font-medium text-surface-900 dark:text-surface-0"},g0={class:"text-sm text-surface-600 dark:text-surface-400 font-mono"},b0={key:1,class:"text-surface-400 dark:text-surface-600"},C0={key:1,class:"text-surface-400 dark:text-surface-600"},D0={class:"text-sm"},x0={key:0},E0={class:"text-surface-700 dark:text-surface-300"},B0={class:"text-xs text-surface-500 dark:text-surface-400"},V0={key:1,class:"text-surface-400 dark:text-surface-600"},F0={class:"flex gap-2"},w0={key:1,class:"grid gap-4"},T0={class:"p-6"},k0={class:"flex items-start justify-between mb-4"},_0={class:"flex-1"},h0={class:"flex items-center gap-3 mb-2"},A0={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},I0={class:"text-sm text-surface-600 dark:text-surface-400 font-mono mb-2"},G0={key:0,class:"text-surface-600 dark:text-surface-400 mb-3"},S0={class:"flex gap-2 ml-4"},L0={class:"flex items-center gap-4 mb-4"},U0={key:0,class:"border-t border-surface-200 dark:border-surface-700 pt-4"},M0={class:"grid grid-cols-3 gap-4 text-center"},R0={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},N0={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},P0={class:"text-lg font-semibold text-surface-900 dark:text-surface-0"},q0={class:"p-4"};function O0(T,e,p,u,f,c){return i(),g("div",$4,[u.loading?(i(),g("div",u0,[a(u.Icon,{name:"pi pi-spinner pi-spin",class:"inline-block text-4xl text-primary mb-4"}),e[7]||(e[7]=t("p",{class:"text-surface-600 dark:text-surface-400"},"Загрузка шаблонов атрибутов...",-1))])):(i(),g("div",e0,[t("div",l0,[e[8]||(e[8]=t("div",null,[t("h2",{class:"text-xl font-semibold text-surface-900 dark:text-surface-0"}," Шаблоны атрибутов "),t("p",{class:"text-surface-600 dark:text-surface-400 text-sm mt-1"}," Управление шаблонами атрибутов для запчастей, каталожных позиций и техники ")],-1)),t("div",t0,[a(u.VButton,{onClick:u.refreshData,disabled:u.loading,severity:"secondary",outlined:"",label:"Обновить"},{icon:s(()=>[a(u.Icon,{name:"pi pi-refresh",class:"w-5 h-5"})]),_:1},8,["disabled"]),a(u.VButton,{onClick:u.createNewTemplate,label:"Создать шаблон"},{icon:s(()=>[a(u.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})])]),a(u.VCard,{class:"mb-6"},{content:s(()=>[t("div",a0,[t("div",o0,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск ",-1)),a(u.VInputText,{modelValue:u.searchQuery,"onUpdate:modelValue":e[0]||(e[0]=l=>u.searchQuery=l),placeholder:"Поиск по названию, имени или описанию...",class:"w-full",onInput:u.debouncedSearch},null,8,["modelValue"])]),t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа ",-1)),a(u.VAutoComplete,{modelValue:u.selectedGroup,"onUpdate:modelValue":e[1]||(e[1]=l=>u.selectedGroup=l),suggestions:u.groupSuggestions,onComplete:u.filterGroups,"option-label":"name","option-value":"id",placeholder:"Все группы",class:"w-full",dropdown:"","show-clear":"",onChange:u.loadTemplates},null,8,["modelValue","suggestions"])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип данных ",-1)),a(u.VAutoComplete,{modelValue:u.selectedDataType,"onUpdate:modelValue":e[2]||(e[2]=l=>u.selectedDataType=l),suggestions:u.dataTypeSuggestions,onComplete:u.filterDataTypes,"option-label":"label","option-value":"value",placeholder:"Все типы",class:"w-full",dropdown:"","show-clear":"",onChange:u.loadTemplates},null,8,["modelValue","suggestions"])])])])]),_:1}),t("div",s0,[a(u.VCard,null,{content:s(()=>[t("div",r0,[t("div",n0,E(u.totalCount),1),e[12]||(e[12]=t("div",{class:"text-sm text-surface-600 dark:text-surface-400"},"Всего шаблонов",-1))])]),_:1}),a(u.VCard,null,{content:s(()=>[t("div",i0,[t("div",d0,E(u.groups.length),1),e[13]||(e[13]=t("div",{class:"text-sm text-surface-600 dark:text-surface-400"},"Групп",-1))])]),_:1}),a(u.VCard,null,{content:s(()=>[t("div",c0,[t("div",m0,E(u.usedTemplatesCount),1),e[14]||(e[14]=t("div",{class:"text-sm text-surface-600 dark:text-surface-400"},"Используется",-1))])]),_:1}),a(u.VCard,null,{content:s(()=>[t("div",f0,[t("div",v0,E(u.unusedTemplatesCount),1),e[15]||(e[15]=t("div",{class:"text-sm text-surface-600 dark:text-surface-400"},"Не используется",-1))])]),_:1})]),u.viewMode==="table"?(i(),G(u.VCard,{key:0},{content:s(()=>[a(u.VDataTable,{value:u.templates,loading:u.tableLoading&&u.templates.length===0,paginator:"",rows:u.pageSize,"total-records":u.totalCount,"rows-per-page-options":[10,25,50],lazy:"",onPage:u.onPageChange,"table-style":"min-width: 50rem",class:"p-datatable-sm","striped-rows":""},{default:s(()=>[a(u.Column,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:s(({data:l})=>[t("span",p0,"#"+E(l.id),1)]),_:1}),a(u.Column,{field:"title",header:"Название",sortable:""},{body:s(({data:l})=>[t("div",null,[t("div",y0,E(l.title),1),t("div",g0,E(l.name),1)])]),_:1}),a(u.Column,{field:"group.name",header:"Группа",sortable:""},{body:s(({data:l})=>[l.group?(i(),G(u.VTag,{key:0,value:l.group.name,severity:"secondary"},null,8,["value"])):(i(),g("span",b0,"—"))]),_:1}),a(u.Column,{field:"dataType",header:"Тип",sortable:""},{body:s(({data:l})=>[a(u.VTag,{value:u.getDataTypeLabel(l.dataType),severity:"info"},null,8,["value"])]),_:1}),a(u.Column,{field:"unit",header:"Единица"},{body:s(({data:l})=>[l.unit?(i(),G(u.VTag,{key:0,value:u.getUnitLabel(l.unit),severity:"success"},null,8,["value"])):(i(),g("span",C0,"—"))]),_:1}),a(u.Column,{header:"Использование",style:{width:"120px"}},{body:s(({data:l})=>[t("div",D0,[l._count?(i(),g("div",x0,[t("div",E0,E(u.getTotalUsage(l._count))+" исп. ",1),t("div",B0,E(u.getUsageDetails(l._count)),1)])):(i(),g("span",V0,"—"))])]),_:1}),a(u.Column,{header:"Действия",style:{width:"120px"}},{body:s(({data:l})=>[t("div",F0,[l.dataType==="STRING"?(i(),G(u.VButton,{key:0,onClick:y=>u.openSynonyms(l),severity:"secondary",outlined:"",size:"small",label:"Синонимы"},{icon:s(()=>[a(u.Icon,{name:"pi pi-tags",class:"w-5 h-5"})]),_:2},1032,["onClick"])):w("",!0),a(u.VButton,{onClick:y=>u.editTemplate(l),severity:"secondary",outlined:"",size:"small",label:"Редактировать"},{icon:s(()=>[a(u.Icon,{name:"pi pi-pencil",class:"w-5 h-5"})]),_:2},1032,["onClick"]),a(u.VButton,{onClick:y=>u.deleteTemplate(l),severity:"danger",outlined:"",size:"small",label:"Удалить",disabled:u.getTotalUsage(l._count)>0},{icon:s(()=>[a(u.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["value","loading","rows","total-records"])]),_:1})):u.viewMode==="cards"?(i(),g("div",w0,[(i(!0),g(xu,null,Eu(u.templates,l=>(i(),G(u.VCard,{key:l.id,class:"border border-surface-200 dark:border-surface-700 hover:border-primary transition-colors"},{content:s(()=>[t("div",T0,[t("div",k0,[t("div",_0,[t("div",h0,[a(u.Icon,{name:"pi pi-tag",class:"text-blue-600 w-5 h-5"}),t("h3",A0,E(l.title),1),l.isRequired?(i(),G(u.VTag,{key:0,value:"Обязательный",severity:"danger",size:"small"})):w("",!0)]),t("div",I0,E(l.name),1),l.description?(i(),g("p",G0,E(l.description),1)):w("",!0)]),t("div",S0,[a(u.VButton,{onClick:y=>u.editTemplate(l),severity:"secondary",outlined:"",size:"small",label:"Редактировать"},{icon:s(()=>[a(u.Icon,{name:"pi pi-pencil",class:"w-5 h-5"})]),_:2},1032,["onClick"]),a(u.VButton,{onClick:y=>u.deleteTemplate(l),severity:"danger",outlined:"",size:"small",label:"Удалить",disabled:u.getTotalUsage(l._count)>0},{icon:s(()=>[a(u.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick","disabled"])])]),t("div",L0,[a(u.VTag,{value:u.getDataTypeLabel(l.dataType),severity:"info"},null,8,["value"]),l.unit?(i(),G(u.VTag,{key:0,value:u.getUnitLabel(l.unit),severity:"success"},null,8,["value"])):w("",!0),l.group?(i(),G(u.VTag,{key:1,value:l.group.name,severity:"secondary"},null,8,["value"])):w("",!0)]),l._count?(i(),g("div",U0,[e[19]||(e[19]=t("div",{class:"text-sm text-surface-600 dark:text-surface-400 mb-2"}," Использование: ",-1)),t("div",M0,[t("div",null,[t("div",R0,E(l._count.partAttributes||0),1),e[16]||(e[16]=t("div",{class:"text-xs text-surface-500"},"Запчасти",-1))]),t("div",null,[t("div",N0,E(l._count.catalogItemAttributes||0),1),e[17]||(e[17]=t("div",{class:"text-xs text-surface-500"},"Каталог",-1))]),t("div",null,[t("div",P0,E(l._count.equipmentAttributes||0),1),e[18]||(e[18]=t("div",{class:"text-xs text-surface-500"},"Техника",-1))])])])):w("",!0)])]),_:2},1024))),128)),u.totalCount>u.pageSize?(i(),G(u.VCard,{key:0},{content:s(()=>[t("div",q0,[a(u.Paginator,{rows:u.pageSize,"total-records":u.totalCount,"rows-per-page-options":[10,25,50],onPage:u.onPageChange},null,8,["rows","total-records"])])]),_:1})):w("",!0)])):w("",!0),a(u.VDialog,{visible:u.showCreateDialog,"onUpdate:visible":e[5]||(e[5]=l=>u.showCreateDialog=l),modal:"",header:u.editingTemplate?"Редактировать шаблон":"Создать шаблон",style:{width:"50rem"},breakpoints:{"1199px":"75vw","575px":"90vw"}},{default:s(()=>[a(u.TemplateForm,{modelValue:u.templateForm,"onUpdate:modelValue":e[3]||(e[3]=l=>u.templateForm=l),groups:u.groups,loading:u.saving,onSave:u.saveTemplate,onCancel:e[4]||(e[4]=l=>u.showCreateDialog=!1)},null,8,["modelValue","groups","loading"])]),_:1},8,["visible","header"]),a(u.VDialog,{visible:u.showSynonymsDialog,"onUpdate:visible":e[6]||(e[6]=l=>u.showSynonymsDialog=l),modal:"",header:"Управление синонимами",style:{width:"80rem"},breakpoints:{"1199px":"90vw","575px":"98vw"}},{default:s(()=>[u.selectedTemplateForSynonyms?(i(),G(u.AttributeSynonymManager,{key:0,template:u.selectedTemplateForSynonyms},null,8,["template"])):w("",!0)]),_:1},8,["visible"])]))])}const j0=H(Z4,[["render",O0]]),z0=J({__name:"AttributeTemplateManagerBoundary",setup(T,{expose:e}){e();const p=d(0),f={key:p,onRetry:()=>{p.value++},ErrorBoundary:Iu,AttributeTemplateManager:j0};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}});function K0(T,e,p,u,f,c){return i(),G(u.ErrorBoundary,{variant:"detailed",onRetry:u.onRetry,title:"Проблема при загрузке шаблонов",message:"Попробуйте обновить список или повторить попытку."},{default:s(()=>[(i(),G(u.AttributeTemplateManager,{key:u.key}))]),_:1})}const ze=H(z0,[["render",K0]]);export{ze as default};

import{s as m,p as b}from"./utils.BUKUcbtE.js";import{B as k}from"./index.BaVCXmir.js";import{f as v}from"./index.BH7IgUdp.js";import{c,o as s,g,b as p,p as u,m as o,M as h,a as w,d as x,n as S,r as P,w as $,q as _}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as T,b as B,r as z}from"./reactivity.esm-bundler.BQ12LWmY.js";import{_ as j}from"./_plugin-vue_export-helper.DlAUqK2U.js";var V=`
    .p-tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: dt('tag.primary.background');
        color: dt('tag.primary.color');
        font-size: dt('tag.font.size');
        font-weight: dt('tag.font.weight');
        padding: dt('tag.padding');
        border-radius: dt('tag.border.radius');
        gap: dt('tag.gap');
    }

    .p-tag-icon {
        font-size: dt('tag.icon.size');
        width: dt('tag.icon.size');
        height: dt('tag.icon.size');
    }

    .p-tag-rounded {
        border-radius: dt('tag.rounded.border.radius');
    }

    .p-tag-success {
        background: dt('tag.success.background');
        color: dt('tag.success.color');
    }

    .p-tag-info {
        background: dt('tag.info.background');
        color: dt('tag.info.color');
    }

    .p-tag-warn {
        background: dt('tag.warn.background');
        color: dt('tag.warn.color');
    }

    .p-tag-danger {
        background: dt('tag.danger.background');
        color: dt('tag.danger.color');
    }

    .p-tag-secondary {
        background: dt('tag.secondary.background');
        color: dt('tag.secondary.color');
    }

    .p-tag-contrast {
        background: dt('tag.contrast.background');
        color: dt('tag.contrast.color');
    }
`,C={root:function(r){var t=r.props;return["p-tag p-component",{"p-tag-info":t.severity==="info","p-tag-success":t.severity==="success","p-tag-warn":t.severity==="warn","p-tag-danger":t.severity==="danger","p-tag-secondary":t.severity==="secondary","p-tag-contrast":t.severity==="contrast","p-tag-rounded":t.rounded}]},icon:"p-tag-icon",label:"p-tag-label"},M=k.extend({name:"tag",style:V,classes:C}),O={name:"BaseTag",extends:m,props:{value:null,severity:null,rounded:Boolean,icon:String},style:M,provide:function(){return{$pcTag:this,$parentInstance:this}}};function a(e){"@babel/helpers - typeof";return a=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},a(e)}function D(e,r,t){return(r=E(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function E(e){var r=N(e,"string");return a(r)=="symbol"?r:r+""}function N(e,r){if(a(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var n=t.call(e,r);if(a(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}var l={name:"Tag",extends:O,inheritAttrs:!1,computed:{dataP:function(){return v(D({rounded:this.rounded},this.severity,this.severity))}}},q=["data-p"];function A(e,r,t,n,f,i){return s(),c("span",o({class:e.cx("root"),"data-p":i.dataP},e.ptmi("root")),[e.$slots.icon?(s(),g(h(e.$slots.icon),o({key:0,class:e.cx("icon")},e.ptm("icon")),null,16,["class"])):e.icon?(s(),c("span",o({key:1,class:[e.cx("icon"),e.icon]},e.ptm("icon")),null,16)):p("",!0),e.value!=null||e.$slots.default?u(e.$slots,"default",{key:2},function(){return[w("span",o({class:e.cx("label")},e.ptm("label")),T(e.value),17)]}):p("",!0)],16,q)}l.render=A;const I=x({__name:"Tag",setup(e,{expose:r}){r();const n={theme:z({root:`inline-flex items-center justify-center text-sm font-bold py-1 px-2 rounded-md gap-1 p-rounded:rounded-2xl
        bg-primary-100 dark:bg-primary-500/15 text-primary-700 dark:text-primary-300
        p-success:bg-green-100 dark:p-success:bg-green-500/15 p-success:text-green-700 dark:p-success:text-green-300
        p-info:bg-sky-100 dark:p-info:bg-sky-500/15 p-info:text-sky-700 dark:p-info:text-sky-300
        p-warn:bg-orange-100 dark:p-warn:bg-orange-500/15 p-warn:text-orange-700 dark:p-warn:text-orange-300
        p-danger:bg-red-100 dark:p-danger:bg-red-500/15 p-danger:text-red-700 dark:p-danger:text-red-300
        p-secondary:bg-surface-100 dark:p-secondary:bg-surface-800 p-secondary:text-surface-600 dark:p-secondary:text-surface-300
        p-contrast:bg-surface-950 dark:p-contrast:bg-surface-0 p-contrast:text-surface-0 dark:p-contrast:text-surface-950`,icon:"text-xs w-3 h-3"}),get Tag(){return l},get ptViewMerge(){return b}};return Object.defineProperty(n,"__isScriptSetup",{enumerable:!1,value:!0}),n}});function K(e,r,t,n,f,i){return s(),g(n.Tag,{unstyled:"",pt:n.theme,ptOptions:{mergeProps:n.ptViewMerge}},S({_:2},[P(e.$slots,(L,d)=>({name:d,fn:$(y=>[u(e.$slots,d,B(_(y??{})))])}))]),1032,["pt","ptOptions"])}const U=j(I,[["render",K]]);export{U as T};

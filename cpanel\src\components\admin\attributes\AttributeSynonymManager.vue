<template>
  <div class="flex justify-center gap-4">
    <!-- Левая колонка: группы -->
    <VCard>
      <template #content>
        <div class="p-4 space-y-3">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">Группы синонимов</h3>
            <VButton size="small" @click="openCreateGroup">
              <PlusCircleIcon class="w-4 h-4" />
            </VButton>
          </div>
          <VInputText v-model="search" placeholder="Поиск по названию / описанию..." @input="debouncedReload" />

          <VDataTable :value="groups" :loading="loadingGroups" table-style="min-width: 24rem" class="p-datatable-sm"
            striped-rows>
            <Column header="Название">
              <template #body="{ data }">
                <div class="flex flex-col">
                  <div class="font-medium">{{ data.name }}</div>
                  <small class="text-surface-500" v-if="data.description">{{ data.description }}</small>
                </div>
              </template>
            </Column>
            <Column header="Уровень" style="width: 120px">
              <template #body="{ data }">
                <VTag :value="compatibilityLabel(data.compatibilityLevel)"
                  :severity="compatibilitySeverity(data.compatibilityLevel)" />
              </template>
            </Column>
            <Column header="#" style="width: 64px">
              <template #body="{ data }">
                <VTag :value="data._count?.synonyms || 0" severity="secondary" />
              </template>
            </Column>
            <Column header="" style="width: 150px">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <VButton size="small" severity="secondary" outlined @click="selectGroup(data)">
                    <ListIcon class="w-4 h-4" />
                  </VButton>
                  <VButton size="small" severity="secondary" outlined @click="openEditGroup(data)">
                    <PencilIcon class="w-4 h-4" />
                  </VButton>
                  <VButton size="small" severity="danger" outlined @click="deleteGroup(data)">
                    <TrashIcon class="w-4 h-4" />
                  </VButton>
                </div>
              </template>
            </Column>
          </VDataTable>
        </div>
      </template>
    </VCard>

    <!-- Правая колонка: значения -->
    <VCard>
      <template #content>
        <div class="p-4 space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                {{ selectedGroup ? selectedGroup.name : 'Выберите группу' }}
              </h3>
              <div v-if="selectedGroup" class="flex items-center gap-2 mt-1">
                <VTag :value="compatibilityLabel(selectedGroup.compatibilityLevel)"
                  :severity="compatibilitySeverity(selectedGroup.compatibilityLevel)" />
                <small v-if="selectedGroup.notes" class="text-surface-500">{{ selectedGroup.notes }}</small>
              </div>
            </div>
            <div class="flex gap-2" v-if="selectedGroup">
              <VButton size="small" severity="secondary" outlined @click="openEditGroup(selectedGroup)">
                <PencilIcon class="w-4 h-4" />
              </VButton>
              <VButton size="small" severity="danger" outlined @click="deleteGroup(selectedGroup)">
                <TrashIcon class="w-4 h-4" />
              </VButton>
            </div>
          </div>

          <div v-if="!selectedGroup" class="text-surface-500">Слева выберите группу, чтобы редактировать значения.</div>

          <div v-else>
            <SynonymValueEditor :group-id="selectedGroup.id" />
          </div>
        </div>
      </template>
    </VCard>

    <!-- Диалог создания/редактирования группы -->
    <EditSynonymGroupDialog v-model:visible="showGroupDialog" :template-id="template.id" :group="editingGroup"
      @saved="onGroupSaved" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import VCard from '@/volt/Card.vue'
import VButton from '@/volt/Button.vue'
import VInputText from '@/volt/InputText.vue'
import VDataTable from '@/volt/DataTable.vue'
import VTag from '@/volt/Tag.vue'
import Column from 'primevue/column'
import { useToast } from '@/composables/useToast'
import { useTrpc } from '@/composables/useTrpc'
import EditSynonymGroupDialog from './EditSynonymGroupDialog.vue'
import SynonymValueEditor from './SynonymValueEditor.vue'
import { PlusCircleIcon } from 'lucide-vue-next'
import { ListIcon } from 'lucide-vue-next'
import { PencilIcon } from 'lucide-vue-next'
import { TrashIcon } from 'lucide-vue-next'

interface AttributeTemplate { id: number; dataType: string; title: string; name: string }

const props = defineProps<{ template: AttributeTemplate }>()

const toast = useToast()
const { attributeSynonyms } = useTrpc()

const groups = ref<any[]>([])
const total = ref(0)
const loadingGroups = ref(false)
const search = ref('')
const selectedGroup = ref<any | null>(null)
const showGroupDialog = ref(false)
const editingGroup = ref<any | null>(null)
let reloadTimer: any

const loadGroups = async () => {
  if (!props.template?.id) return
  loadingGroups.value = true
  try {
    const result = await attributeSynonyms.groups.findMany({ templateId: props.template.id, search: search.value || undefined, limit: 50, offset: 0 })
    if (result && typeof result === 'object') {
      groups.value = (result as any).groups || []
      total.value = (result as any).total || 0
      if (selectedGroup.value) {
        // Обновить выбранную группу по id
        const updated = groups.value.find(g => g.id === selectedGroup.value.id)
        if (updated) selectedGroup.value = updated
      }
    }
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось загрузить группы')
  } finally {
    loadingGroups.value = false
  }
}

const debouncedReload = () => {
  clearTimeout(reloadTimer)
  reloadTimer = setTimeout(() => loadGroups(), 400)
}

const compatibilityLabel = (level: 'EXACT'|'NEAR'|'LEGACY') => {
  switch (level) {
    case 'EXACT': return 'EXACT'
    case 'NEAR': return 'NEAR'
    case 'LEGACY': return 'LEGACY'
    default: return String(level)
  }
}

const compatibilitySeverity = (level: 'EXACT'|'NEAR'|'LEGACY') => {
  return level === 'EXACT' ? 'success' : level === 'NEAR' ? 'warn' : 'secondary'
}

const selectGroup = (group: any) => {
  selectedGroup.value = group
}

const openCreateGroup = () => {
  editingGroup.value = null
  showGroupDialog.value = true
}

const openEditGroup = (group: any) => {
  editingGroup.value = group
  showGroupDialog.value = true
}

const deleteGroup = async (group: any) => {
  if (!confirm(`Удалить группу "${group.name}"?`)) return
  try {
    await attributeSynonyms.groups.delete({ id: group.id })
    if (selectedGroup.value?.id === group.id) selectedGroup.value = null
    loadGroups()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось удалить группу')
  }
}

const onGroupSaved = () => {
  showGroupDialog.value = false
  loadGroups()
}

onMounted(() => {
  loadGroups()
})
</script>



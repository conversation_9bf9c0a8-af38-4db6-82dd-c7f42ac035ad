import{s as go,Y as yo,j as vo,h as To,Z as xo,x as bo,_ as So,i as Ao,W as Po,A as wo,y as Vo,G as Eo,d as Mo,C as Os,$ as Co,m as Do,a0 as Ro}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as Lo}from"./reactivity.esm-bundler.BQ12LWmY.js";const jt=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function Bo(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(u){o.has(u)&&(c.schedule(u),t()),u(a)}const c={schedule:(u,h=!1,f=!1)=>{const p=f&&i?n:s;return h&&o.add(u),p.has(u)||p.add(u),u},cancel:u=>{s.delete(u),o.delete(u)},process:u=>{if(a=u,i){r=!0;return}i=!0,[n,s]=[s,n],n.forEach(l),n.clear(),i=!1,r&&(r=!1,c.process(u))}};return c}const G={},Fo=40;function ks(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,o=jt.reduce((x,A)=>(x[A]=Bo(r),x),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:f,render:d,postRender:p}=o,m=()=>{const x=G.useManualTiming?i.timestamp:performance.now();n=!1,G.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(x-i.timestamp,Fo),1)),i.timestamp=x,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),h.process(i),f.process(i),d.process(i),p.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(m))},g=()=>{n=!0,s=!0,i.isProcessing||t(m)};return{schedule:jt.reduce((x,A)=>{const v=o[A];return x[A]=(P,M=!1,S=!1)=>(n||g(),v.schedule(P,M,S)),x},{}),cancel:x=>{for(let A=0;A<jt.length;A++)o[jt[A]].cancel(x)},state:i,steps:o}}const j=t=>t,{schedule:V,cancel:z,state:B,steps:Zt}=ks(typeof requestAnimationFrame<"u"?requestAnimationFrame:j,!0);let _t;function jo(){_t=void 0}const F={now:()=>(_t===void 0&&F.set(B.isProcessing||G.useManualTiming?B.timestamp:performance.now()),_t),set:t=>{_t=t,queueMicrotask(jo)}},_=t=>t*1e3,$=t=>t/1e3;function Oo(t,e){const n=F.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(z(s),t(r-e))};return V.setup(s,!0),()=>z(s)}const Is=t=>e=>typeof e=="string"&&e.startsWith(t),Me=Is("--"),ko=Is("var(--"),Ce=t=>ko(t)?Io.test(t.split("/*")[0].trim()):!1,Io=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Pt={};function _s(t){for(const e in t)Pt[e]=t[e],Me(e)&&(Pt[e].isCSSVariable=!0)}function Mt(t,e){const n=typeof t=="string"&&!e?`${t}Context`:e,s=Symbol(n);return[o=>{const a=go(s,o);if(a===void 0)throw new Error(`Injection \`${s.toString()}\` not found. Component must be used within ${Array.isArray(t)?`one of the following components: ${t.join(", ")}`:`\`${t}\``}`);return a},o=>(yo(s,o),o)]}const[_o,Uo]=Mt("Motion"),[No,Zc]=Mt("LayoutGroup");function ln(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}class Y{constructor(e){this.state=e}beforeMount(){}mount(){}unmount(){}update(){}beforeUpdate(){}beforeUnmount(){}}function vt(t,e,n){if(Array.isArray(t))return t.reduce((s,i)=>{const r=vt(i,e,n);return r?{...s,...r}:s},{});if(typeof t=="object")return t;if(t&&e){const s=e[t];return typeof s=="function"?s(n):s}}function $o(t,e){return typeof t!=typeof e?!0:Array.isArray(t)&&Array.isArray(e)?!Ko(t,e):t!==e}function Ko(t,e){const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function un(t){return t?.startsWith("--")}const Wo=t=>t;function Go(t){return typeof t=="number"}const zo=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","svg","switch","symbol","text","tspan","use","view","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],Ho=new Set(zo);function Us(t){return Ho.has(t)}class Yo{constructor(e){this.features=[];const{features:n=[],lazyMotionContext:s}=e.options,i=n.concat(s.features.value);this.features=i.map(o=>new o(e));const r=this.features;vo(s.features,o=>{o.forEach(a=>{if(!i.includes(a)){i.push(a);const l=new a(e);r.push(l),e.isMounted()&&(l.beforeMount(),l.mount())}})},{flush:"pre"})}mount(){this.features.forEach(e=>e.mount())}beforeMount(){this.features.forEach(e=>{var n;return(n=e.beforeMount)==null?void 0:n.call(e)})}unmount(){this.features.forEach(e=>e.unmount())}update(){this.features.forEach(e=>{var n;return(n=e.update)==null?void 0:n.call(e)})}beforeUpdate(){this.features.forEach(e=>e.beforeUpdate())}beforeUnmount(){this.features.forEach(e=>e.beforeUnmount())}}const De=new WeakMap,[Xo,Jc]=Mt("AnimatePresenceContext");function qo(t){return typeof t=="string"||t===!1||Array.isArray(t)}const Ns=new WeakMap;let Zo=0;const Jt=new Set;class Jo{constructor(e,n){var s;this.element=null,this.isSafeToRemove=!1,this.isVShow=!1,this.children=new Set,this.activeStates={initial:!0,animate:!0},this.currentProcess=null,this._context=null,this.animateUpdates=j,this.id=`motion-state-${Zo++}`,this.options=e,this.parent=n,(s=n?.children)==null||s.add(this),this.depth=n?.depth+1||0;const r=(e.initial===void 0&&e.variants?this.context.initial:e.initial)===!1?["initial","animate"]:["initial"];this.initTarget(r),this.featureManager=new Yo(this),this.type=Us(this.options.as)?"svg":"html"}get context(){if(!this._context){const e={get:(n,s)=>{var i;return qo(this.options[s])?this.options[s]:(i=this.parent)==null?void 0:i.context[s]}};this._context=new Proxy({},e)}return this._context}initTarget(e){var n;const s=this.options.custom??((n=this.options.animatePresenceContext)==null?void 0:n.custom);this.baseTarget=e.reduce((i,r)=>({...i,...vt(this.options[r]||this.context[r],this.options.variants,s)}),{}),this.target={}}updateOptions(e){var n;this.options=e,(n=this.visualElement)==null||n.update({...this.options,whileTap:this.options.whilePress},{isPresent:!De.has(this.element)})}beforeMount(){this.featureManager.beforeMount()}mount(e,n,s=!1){var i;this.element=e,this.updateOptions(n),this.featureManager.mount(),!s&&this.options.animate&&((i=this.startAnimation)==null||i.call(this)),this.options.layoutId&&(Jt.add(this.options.layoutId),V.render(()=>{Jt.clear()}))}clearAnimation(){var e,n;this.currentProcess&&z(this.currentProcess),this.currentProcess=null,(n=(e=this.visualElement)==null?void 0:e.variantChildren)==null||n.forEach(s=>{s.state.clearAnimation()})}startAnimation(){this.clearAnimation(),this.currentProcess=V.render(()=>{this.currentProcess=null,this.animateUpdates()})}beforeUnmount(){this.featureManager.beforeUnmount()}unmount(e=!1){const n=this.options.layoutId&&!Jt.has(this.options.layoutId);(()=>{const i=()=>{var r,o,a;e&&Array.from(this.children).reverse().forEach(this.unmountChild),(o=(r=this.parent)==null?void 0:r.children)==null||o.delete(this),Ns.delete(this.element),this.featureManager.unmount(),(a=this.visualElement)==null||a.unmount(),this.clearAnimation()};n?Promise.resolve().then(i):i()})()}unmountChild(e){e.unmount(!0)}beforeUpdate(){this.featureManager.beforeUpdate()}update(e){this.updateOptions(e),this.featureManager.update(),this.startAnimation()}setActive(e,n,s=!0){var i;!this.element||this.activeStates[e]===n||(this.activeStates[e]=n,(i=this.visualElement.variantChildren)==null||i.forEach(r=>{r.state.setActive(e,n,!1)}),s&&this.animateUpdates({isFallback:!n&&e!=="exit"&&this.visualElement.isControllingVariants,isExit:e==="exit"&&this.activeStates.exit}))}isMounted(){return!!this.element}willUpdate(e){var n;(this.options.layout||this.options.layoutId)&&((n=this.visualElement.projection)==null||n.willUpdate())}}const st=new WeakMap;function cn(t,e,n){return new CustomEvent(t,{detail:{target:e,isExit:n}})}const hn={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>`${t}deg`},Qo={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>`${t}px`},rotate:hn,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:Wo},skew:hn},tr=["translate","scale","rotate","skew"],er=["","X","Y","Z"],Re=new Map,$t=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"];tr.forEach(t=>{er.forEach(e=>{$t.push(t+e),Re.set(t+e,Qo[t])})});const nr=new Set($t),sr=t=>nr.has(t),fn={x:"translateX",y:"translateY",z:"translateZ"};function ir([t],[e]){return $t.indexOf(t)-$t.indexOf(e)}function or(t,[e,n]){return`${t} ${e}(${n})`}function rr(t){return t.sort(ir).reduce(or,"").trim()}const dn={translate:[0,0],rotate:0,scale:1,skew:0,x:0,y:0,z:0},Ct=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),q=Ct("deg"),K=Ct("%"),T=Ct("px"),ar=Ct("vh"),lr=Ct("vw"),pn={...K,parse:t=>K.parse(t)/100,transform:t=>K.transform(t*100)},L=t=>!!(t&&t.getVelocity),ur={get:(t,e)=>{let n=un(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&n!=="0"){const s=Re.get(e);s&&(n=s.initialValue)}return n},set:(t,e,n)=>{un(e)?t.style.setProperty(e,n):t.style[e]=n}};function cr(t){var e;const n={},s=[];for(let i in t){let r=t[i];r=L(r)?r.get():r,sr(i)&&i in fn&&(i=fn[i]);let o=Array.isArray(r)?r[0]:r;const a=Re.get(i);a?(o=Go(r)?(e=a.toDefaultUnit)==null?void 0:e.call(a,r):r,s.push([i,o])):n[i]=o}return s.length&&(n.transform=rr(s)),Object.keys(n).length===0?null:n}const hr={fill:!0,stroke:!0,opacity:!0,"stroke-width":!0,"fill-opacity":!0,"stroke-opacity":!0,"stroke-linecap":!0,"stroke-linejoin":!0,"stroke-dasharray":!0,"stroke-dashoffset":!0,cx:!0,cy:!0,r:!0,d:!0,x1:!0,y1:!0,x2:!0,y2:!0,points:!0,"path-length":!0,viewBox:!0,width:!0,height:!0,"preserve-aspect-ratio":!0,"clip-path":!0,filter:!0,mask:!0,"stop-color":!0,"stop-opacity":!0,"gradient-transform":!0,"gradient-units":!0,"spread-method":!0,"marker-end":!0,"marker-mid":!0,"marker-start":!0,"text-anchor":!0,"dominant-baseline":!0,"font-family":!0,"font-size":!0,"font-weight":!0,"letter-spacing":!0,"vector-effect":!0};function fr(t){return t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}function dr(t,e,n=1,s=0){t.pathLength=1,delete t["path-length"],t["stroke-dashoffset"]=T.transform(-s);const i=T.transform(e),r=T.transform(n);t["stroke-dasharray"]=`${i} ${r}`}function pr(t){const e={},n={};for(const s in t){const i=fr(s);if(i in hr){const r=t[s];e[i]=L(r)?r.get():r}else n[s]=t[s]}return e["path-length"]!==void 0&&dr(e,e["path-length"],e["path-spacing"],e["path-offset"]),{attrs:e,style:n}}typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const mn=t=>typeof t<"u";function mr({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function gr(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function yr(t,e){return mr(gr(t.getBoundingClientRect(),e))}const gn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},he={};for(const t in gn)he[t]={isEnabled:e=>gn[t].some(n=>!!e[n])};const yn=()=>({translate:0,scale:1,origin:0,originPoint:0}),it=()=>({x:yn(),y:yn()}),vn=()=>({min:0,max:0}),D=()=>({x:vn(),y:vn()}),vr=typeof window<"u",Kt={current:null},$s={current:!1};function Tr(){if($s.current=!0,!!vr)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Kt.current=t.matches;t.addEventListener("change",e),e()}else Kt.current=!1}function xr(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function br(t){return typeof t=="string"||Array.isArray(t)}const Sr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ar=["initial",...Sr];function Ks(t){return xr(t.animate)||Ar.some(e=>br(t[e]))}function Pr(t){return!!(Ks(t)||t.variants)}function Le(t,e){t.indexOf(e)===-1&&t.push(e)}function Xt(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Be{constructor(){this.subscriptions=[]}add(e){return Le(this.subscriptions,e),()=>Xt(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Ws(t,e){return e?t*(1e3/e):0}const Tn=30,wr=t=>!isNaN(parseFloat(t));class Vr{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{var r,o;const a=F.now();if(this.updatedAt!==a&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();i&&((o=this.events.renderRequest)==null||o.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=F.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=wr(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Be);const s=this.events[e].add(n);return e==="change"?()=>{s(),V.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=F.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Tn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Tn);return Ws(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function at(t,e){return new Vr(t,e)}function Er(t,e,n){for(const s in e){const i=e[s],r=n[s];if(L(i))t.addValue(s,i);else if(L(r))t.addValue(s,at(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const o=t.getValue(s);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=t.getStaticValue(s);t.addValue(s,at(o!==void 0?o:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}function xn(t){const e=[{},{}];return t?.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function Gs(t,e,n,s){if(typeof e=="function"){const[i,r]=xn(s);e=e(n!==void 0?n:t.custom,i,r)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,r]=xn(s);e=e(n!==void 0?n:t.custom,i,r)}return e}function Mr(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Q=t=>t*180/Math.PI,fe=t=>{const e=Q(Math.atan2(t[1],t[0]));return de(e)},Cr={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:fe,rotateZ:fe,skewX:t=>Q(Math.atan(t[1])),skewY:t=>Q(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},de=t=>(t=t%360,t<0&&(t+=360),t),bn=fe,Sn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),An=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Dr={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Sn,scaleY:An,scale:t=>(Sn(t)+An(t))/2,rotateX:t=>de(Q(Math.atan2(t[6],t[5]))),rotateY:t=>de(Q(Math.atan2(-t[2],t[0]))),rotateZ:bn,rotate:bn,skewX:t=>Q(Math.atan(t[4])),skewY:t=>Q(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function pe(t){return t.includes("scale")?1:0}function me(t,e){if(!t||t==="none")return pe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Dr,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Cr,i=a}if(!i)return pe(e);const r=s[e],o=i[1].split(",").map(Lr);return typeof r=="function"?r(o):o[r]}const Rr=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return me(n,e)};function Lr(t){return parseFloat(t.trim())}const ut=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ct=new Set(ut),H=(t,e,n)=>n>e?e:n<t?t:n,ht={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},wt={...ht,transform:t=>H(0,1,t)},Ot={...ht,default:1},Pn=t=>t===ht||t===T,Br=new Set(["x","y","z"]),Fr=ut.filter(t=>!Br.has(t));function jr(t){const e=[];return Fr.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const et={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>me(e,"x"),y:(t,{transform:e})=>me(e,"y")};et.translateX=et.x;et.translateY=et.y;const nt=new Set;let ge=!1,ye=!1,ve=!1;function zs(){if(ye){const t=Array.from(nt).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=jr(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,o])=>{var a;(a=s.getValue(r))==null||a.set(o)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}ye=!1,ge=!1,nt.forEach(t=>t.complete(ve)),nt.clear()}function Hs(){nt.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ye=!0)})}function Or(){ve=!0,Hs(),zs(),ve=!1}class Fe{constructor(e,n,s,i,r,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(nt.add(this),ge||(ge=!0,V.read(Hs),V.resolveKeyframes(zs))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const r=i?.get(),o=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const a=s.readValue(n,o);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=o),i&&r===void 0&&i.set(e[0])}Mr(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),nt.delete(this)}cancel(){this.state==="scheduled"&&(nt.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Ys=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Xs=t=>/^0[^.\s]+$/u.test(t),Tt=t=>Math.round(t*1e5)/1e5,je=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function kr(t){return t==null}const Ir=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Oe=(t,e)=>n=>!!(typeof n=="string"&&Ir.test(n)&&n.startsWith(t)||e&&!kr(n)&&Object.prototype.hasOwnProperty.call(n,e)),qs=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,o,a]=s.match(je);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},_r=t=>H(0,255,t),Qt={...ht,transform:t=>Math.round(_r(t))},tt={test:Oe("rgb","red"),parse:qs("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Qt.transform(t)+", "+Qt.transform(e)+", "+Qt.transform(n)+", "+Tt(wt.transform(s))+")"};function Ur(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Te={test:Oe("#"),parse:Ur,transform:tt.transform},ot={test:Oe("hsl","hue"),parse:qs("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+K.transform(Tt(e))+", "+K.transform(Tt(n))+", "+Tt(wt.transform(s))+")"},R={test:t=>tt.test(t)||Te.test(t)||ot.test(t),parse:t=>tt.test(t)?tt.parse(t):ot.test(t)?ot.parse(t):Te.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?tt.transform(t):ot.transform(t),getAnimatableNone:t=>{const e=R.parse(t);return e.alpha=0,R.transform(e)}},Nr=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function $r(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(je))==null?void 0:e.length)||0)+(((n=t.match(Nr))==null?void 0:n.length)||0)>0}const Zs="number",Js="color",Kr="var",Wr="var(",wn="${}",Gr=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Vt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(Gr,l=>(R.test(l)?(s.color.push(r),i.push(Js),n.push(R.parse(l))):l.startsWith(Wr)?(s.var.push(r),i.push(Kr),n.push(l)):(s.number.push(r),i.push(Zs),n.push(parseFloat(l))),++r,wn)).split(wn);return{values:n,split:a,indexes:s,types:i}}function Qs(t){return Vt(t).values}function ti(t){const{split:e,types:n}=Vt(t),s=e.length;return i=>{let r="";for(let o=0;o<s;o++)if(r+=e[o],i[o]!==void 0){const a=n[o];a===Zs?r+=Tt(i[o]):a===Js?r+=R.transform(i[o]):r+=i[o]}return r}}const zr=t=>typeof t=="number"?0:R.test(t)?R.getAnimatableNone(t):t;function Hr(t){const e=Qs(t);return ti(t)(e.map(zr))}const Z={test:$r,parse:Qs,createTransformer:ti,getAnimatableNone:Hr},Yr={test:t=>t==="auto",parse:t=>t},ei=t=>e=>e.test(t),ni=[ht,T,K,q,lr,ar,Yr],Vn=t=>ni.find(ei(t)),Xr=[...ni,R,Z],qr=t=>Xr.find(ei(t)),Zr=new Set(["brightness","contrast","saturate","opacity"]);function Jr(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(je)||[];if(!s)return t;const i=n.replace(s,"");let r=Zr.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Qr=/\b([a-z-]*)\(.*?\)/gu,xe={...Z,getAnimatableNone:t=>{const e=t.match(Qr);return e?e.map(Jr).join(" "):t}},En={...ht,transform:Math.round},ta={rotate:q,rotateX:q,rotateY:q,rotateZ:q,scale:Ot,scaleX:Ot,scaleY:Ot,scaleZ:Ot,skew:q,skewX:q,skewY:q,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:wt,originX:pn,originY:pn,originZ:T},ke={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,backgroundPositionX:T,backgroundPositionY:T,...ta,zIndex:En,fillOpacity:wt,strokeOpacity:wt,numOctaves:En},ea={...ke,color:R,backgroundColor:R,outlineColor:R,fill:R,stroke:R,borderColor:R,borderTopColor:R,borderRightColor:R,borderBottomColor:R,borderLeftColor:R,filter:xe,WebkitFilter:xe},si=t=>ea[t];function ii(t,e){let n=si(t);return n!==xe&&(n=Z),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Mn=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class oi{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Fe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=F.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,V.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=Ks(n),this.isVariantNode=Pr(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&L(d)&&d.set(l[f],!1)}}mount(e){this.current=e,st.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),$s.current||Tr(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Kt.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),z(this.notifyUpdate),z(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=ct.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&V.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r(),o&&o(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in he){const n=he[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):D()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Mn.length;s++){const i=Mn[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,o=e[r];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Er(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=at(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Ys(s)||Xs(s))?s=parseFloat(s):!qr(s)&&Z.test(n)&&(s=ii(e,n)),this.setBaseTarget(e,L(s)?s.get():s)),L(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props;let i;if(typeof s=="string"||typeof s=="object"){const o=Gs(this.props,s,(n=this.presenceContext)==null?void 0:n.custom);o&&(i=o[e])}if(s&&i!==void 0)return i;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!L(r)?r:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Be),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}const ri=new Set(["width","height","top","left","right","bottom",...ut]);let Ie=()=>{};const na=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function sa(t){const e=na.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function ai(t,e,n=1){const[s,i]=sa(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return Ys(o)?parseFloat(o):o}return Ce(i)?ai(i,e,n+1):i}function ia(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Xs(t):!0}const oa=new Set(["auto","none","0"]);function ra(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!oa.has(r)&&Vt(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=ii(n,i)}class aa extends Fe{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Ce(c))){const u=ai(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!ri.has(s)||e.length!==2)return;const[i,r]=e,o=Vn(i),a=Vn(r);if(o!==a)if(Pn(o)&&Pn(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else et[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||ia(e[i]))&&s.push(i);s.length&&ra(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=et[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const r=n.getValue(s);r&&r.jump(this.measuredOrigin,!1);const o=i.length-1,a=i[o];i[o]=et[s](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),(e=this.removedTransforms)!=null&&e.length&&this.removedTransforms.forEach(([l,c])=>{n.getValue(l).set(c)}),this.resolveNoneKeyframes()}}class li extends oi{constructor(){super(...arguments),this.KeyframeResolver=aa}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}const ui=(t,e)=>e&&typeof t=="number"?e.transform(t):t,la={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ua=ut.length;function ca(t,e,n){let s="",i=!0;for(let r=0;r<ua;r++){const o=ut[r],a=t[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=ui(a,ke[o]);if(!l){i=!1;const u=la[o]||o;s+=`${u}(${c}) `}n&&(e[o]=c)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function ci(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let o=!1,a=!1;for(const l in e){const c=e[l];if(ct.has(l)){o=!0;continue}else if(Me(l)){i[l]=c;continue}else{const u=ui(c,ke[l]);l.startsWith("origin")?(a=!0,r[l]=u):s[l]=u}}if(e.transform||(o||n?s.transform=ca(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=r;s.transformOrigin=`${l} ${c} ${u}`}}function hi(t,{style:e,vars:n},s,i){const r=t.style;let o;for(o in e)r[o]=e[o];i?.applyProjectionStyles(r,s);for(o in n)r.setProperty(o,n[o])}function ha(t,{layout:e,layoutId:n}){return ct.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Pt[t]||t==="opacity")}function fi(t,e,n){var s;const{style:i}=t,r={};for(const o in i)(L(i[o])||e.style&&L(e.style[o])||ha(o,t)||((s=n?.getValue(o))==null?void 0:s.liveStyle)!==void 0)&&(r[o]=i[o]);return r}function fa(t){return window.getComputedStyle(t)}class di extends li{constructor(){super(...arguments),this.type="html",this.renderInstance=hi}readValueFromInstance(e,n){var s;if(ct.has(n))return(s=this.projection)!=null&&s.isProjecting?pe(n):Rr(e,n);{const i=fa(e),r=(Me(n)?i.getPropertyValue(n):i[n])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:n}){return yr(e,n)}build(e,n,s){ci(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return fi(e,n,s)}}const _e=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),da={offset:"stroke-dashoffset",array:"stroke-dasharray"},pa={offset:"strokeDashoffset",array:"strokeDasharray"};function ma(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?da:pa;t[r.offset]=T.transform(-s);const o=T.transform(e),a=T.transform(n);t[r.array]=`${o} ${a}`}function ga(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:o=0,...a},l,c,u){if(ci(t,a,c),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:f}=t;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=u?.transformBox??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&ma(h,i,r,o,!1)}const pi=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),ya=t=>typeof t=="string"&&t.toLowerCase()==="svg";function va(t,e,n,s){hi(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(pi.has(i)?i:_e(i),e.attrs[i])}function Ta(t,e,n){const s=fi(t,e,n);for(const i in t)if(L(t[i])||L(e[i])){const r=ut.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[r]=t[i]}return s}class mi extends li{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=D}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(ct.has(n)){const s=si(n);return s&&s.default||0}return n=pi.has(n)?n:_e(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Ta(e,n,s)}build(e,n,s){ga(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){va(e,n,s,i)}mount(e){this.isSVGTag=ya(e.tagName),super.mount(e)}}function xa(t,e){return Us(t)?new mi(e):new di(e)}function Ue(t){return typeof t=="object"&&!Array.isArray(t)}function Ne(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}function gi(t,e,n,s){return typeof t=="string"&&Ue(e)?Ne(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function ba(t,e,n){return t*(e+1)}function Cn(t,e,n,s){return typeof e=="number"?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):e==="<"?n:e.startsWith("<")?Math.max(0,n+parseFloat(e.slice(1))):s.get(e)??t}const E=(t,e,n)=>t+(e-t)*n,Sa=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t},yi=t=>Array.isArray(t)&&typeof t[0]!="number";function vi(t,e){return yi(t)?t[Sa(0,t.length,e)]:t}function Aa(t,e,n){for(let s=0;s<t.length;s++){const i=t[s];i.at>e&&i.at<n&&(Xt(t,i),s--)}}function Pa(t,e,n,s,i,r){Aa(t,i,r);for(let o=0;o<e.length;o++)t.push({value:e[o],at:E(i,r,s[o]),easing:vi(n,o)})}function wa(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Va(t,e){return t.at===e.at?t.value===null?1:e.value===null?-1:0:t.at-e.at}const lt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};function Ti(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=lt(0,e,s);t.push(E(n,1,i))}}function xi(t){const e=[0];return Ti(e,t.length-1),e}const Wt=2e4;function $e(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Wt;)e+=n,s=t.next(e);return e>=Wt?1/0:e}function bi(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min($e(s),Wt);return{type:"keyframes",ease:r=>s.next(i*r).value/e,duration:$(i)}}function Ke(t){return typeof t=="function"&&"applyToOptions"in t}const Ea="easeInOut";function Ma(t,{defaultTransition:e={},...n}={},s,i){const r=e.duration||.3,o=new Map,a=new Map,l={},c=new Map;let u=0,h=0,f=0;for(let d=0;d<t.length;d++){const p=t[d];if(typeof p=="string"){c.set(p,h);continue}else if(!Array.isArray(p)){c.set(p.name,Cn(h,p.at,u,c));continue}let[m,g,y={}]=p;y.at!==void 0&&(h=Cn(h,y.at,u,c));let b=0;const x=(A,v,P,M=0,S=0)=>{const w=Ca(A),{delay:O=0,times:U=xi(w),type:qt="keyframes",repeat:Lt,repeatType:Hc,repeatDelay:Yc=0,...mo}=v;let{ease:X=e.ease||"easeOut",duration:W}=v;const en=typeof O=="function"?O(M,S):O,nn=w.length,sn=Ke(qt)?qt:i?.[qt||"keyframes"];if(nn<=2&&sn){let ft=100;if(nn===2&&La(w)){const dt=w[1]-w[0];ft=Math.abs(dt)}const Bt={...mo};W!==void 0&&(Bt.duration=_(W));const Ft=bi(Bt,ft,sn);X=Ft.ease,W=Ft.duration}W??(W=r);const on=h+en;U.length===1&&U[0]===0&&(U[1]=1);const rn=U.length-w.length;if(rn>0&&Ti(U,rn),w.length===1&&w.unshift(null),Lt){W=ba(W,Lt);const ft=[...w],Bt=[...U];X=Array.isArray(X)?[...X]:[X];const Ft=[...X];for(let dt=0;dt<Lt;dt++){w.push(...ft);for(let pt=0;pt<ft.length;pt++)U.push(Bt[pt]+(dt+1)),X.push(pt===0?"linear":vi(Ft,pt-1))}wa(U,Lt)}const an=on+W;Pa(P,w,X,U,on,an),b=Math.max(en+W,b),f=Math.max(an,f)};if(L(m)){const A=Dn(m,a);x(g,y,Rn("default",A))}else{const A=gi(m,g,s,l),v=A.length;for(let P=0;P<v;P++){g=g,y=y;const M=A[P],S=Dn(M,a);for(const w in g)x(g[w],Da(y,w),Rn(w,S),P,v)}}u=h,h+=b}return a.forEach((d,p)=>{for(const m in d){const g=d[m];g.sort(Va);const y=[],b=[],x=[];for(let v=0;v<g.length;v++){const{at:P,value:M,easing:S}=g[v];y.push(M),b.push(lt(0,f,P)),x.push(S||"easeOut")}b[0]!==0&&(b.unshift(0),y.unshift(y[0]),x.unshift(Ea)),b[b.length-1]!==1&&(b.push(1),y.push(null)),o.has(p)||o.set(p,{keyframes:{},transition:{}});const A=o.get(p);A.keyframes[m]=y,A.transition[m]={...e,duration:f,ease:x,times:b,...n}}}),o}function Dn(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Rn(t,e){return e[t]||(e[t]=[]),e[t]}function Ca(t){return Array.isArray(t)?t:[t]}function Da(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Ra=t=>typeof t=="number",La=t=>t.every(Ra),Ba=t=>Array.isArray(t);function Fa(t,e,n){const s=t.getProps();return Gs(s,e,s.custom,t)}function ja(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,at(n))}function Oa(t){return Ba(t)?t[t.length-1]||0:t}function ka(t,e){const n=Fa(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const o in r){const a=Oa(r[o]);ja(t,o,a)}}function Ia(t){return!!(L(t)&&t.add)}function _a(t,e){const n=t.getValue("willChange");if(Ia(n))return n.add(e);if(!n&&G.WillChange){const s=new G.WillChange("auto");t.addValue("willChange",s),s.add(e)}}const Ua="framerAppearId",Na="data-"+_e(Ua);function Si(t){return t.props[Na]}const $a=t=>t!==null;function Ka(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter($a),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[r]}const Wa={type:"spring",stiffness:500,damping:25,restSpeed:10},Ga=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),za={type:"keyframes",duration:.8},Ha={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ya=(t,{keyframes:e})=>e.length>2?za:ct.has(t)?t.startsWith("scale")?Ga(e[1]):Wa:Ha;function Xa({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function We(t,e){return t?.[e]??t?.default??t}function te(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function qa({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=te(l,a,t+1/3),r=te(l,a,t),o=te(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}function Gt(t,e){return n=>n>0?e:t}const ee=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Za=[Te,tt,ot],Ja=t=>Za.find(e=>e.test(t));function Ln(t){const e=Ja(t);if(!e)return!1;let n=e.parse(t);return e===ot&&(n=qa(n)),n}const Bn=(t,e)=>{const n=Ln(t),s=Ln(e);if(!n||!s)return Gt(t,e);const i={...n};return r=>(i.red=ee(n.red,s.red,r),i.green=ee(n.green,s.green,r),i.blue=ee(n.blue,s.blue,r),i.alpha=E(n.alpha,s.alpha,r),tt.transform(i))},be=new Set(["none","hidden"]);function Qa(t,e){return be.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}const tl=(t,e)=>n=>e(t(n)),Dt=(...t)=>t.reduce(tl);function el(t,e){return n=>E(t,e,n)}function Ge(t){return typeof t=="number"?el:typeof t=="string"?Ce(t)?Gt:R.test(t)?Bn:il:Array.isArray(t)?Ai:typeof t=="object"?R.test(t)?Bn:nl:Gt}function Ai(t,e){const n=[...t],s=n.length,i=t.map((r,o)=>Ge(r)(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}}function nl(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ge(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function sl(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}const il=(t,e)=>{const n=Z.createTransformer(e),s=Vt(t),i=Vt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?be.has(t)&&!i.values.length||be.has(e)&&!s.values.length?Qa(t,e):Dt(Ai(sl(s,i),i.values),n):Gt(t,e)};function Pi(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?E(t,e,n):Ge(t)(t,e)}const ol=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>V.update(e,n),stop:()=>z(e),now:()=>B.isProcessing?B.timestamp:F.now()}},wi=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=Math.round(t(r/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},rl=5;function Vi(t,e,n){const s=Math.max(e-rl,0);return Ws(n-t(s),e-s)}const C={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ne=.001;function al({duration:t=C.duration,bounce:e=C.bounce,velocity:n=C.velocity,mass:s=C.mass}){let i,r,o=1-e;o=H(C.minDamping,C.maxDamping,o),t=H(C.minDuration,C.maxDuration,$(t)),o<1?(i=c=>{const u=c*o,h=u*t,f=u-n,d=Se(c,o),p=Math.exp(-h);return ne-f/d*p},r=c=>{const h=c*o*t,f=h*n+n,d=Math.pow(o,2)*Math.pow(c,2)*t,p=Math.exp(-h),m=Se(Math.pow(c,2),o);return(-i(c)+ne>0?-1:1)*((f-d)*p)/m}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-ne+u*h},r=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=ul(i,r,a);if(t=_(t),isNaN(l))return{stiffness:C.stiffness,damping:C.damping,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:o*2*Math.sqrt(s*c),duration:t}}}const ll=12;function ul(t,e,n){let s=n;for(let i=1;i<ll;i++)s=s-t(s)/e(s);return s}function Se(t,e){return t*Math.sqrt(1-e*e)}const cl=["duration","bounce"],hl=["stiffness","damping","mass"];function Fn(t,e){return e.some(n=>t[n]!==void 0)}function fl(t){let e={velocity:C.velocity,stiffness:C.stiffness,damping:C.damping,mass:C.mass,isResolvedFromDuration:!1,...t};if(!Fn(t,hl)&&Fn(t,cl))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*H(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:C.mass,stiffness:i,damping:r}}else{const n=al(t);e={...e,...n,mass:C.mass},e.isResolvedFromDuration=!0}return e}function Et(t=C.visualDuration,e=C.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:r},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:d}=fl({...n,velocity:-$(n.velocity||0)}),p=f||0,m=c/(2*Math.sqrt(l*u)),g=o-r,y=$(Math.sqrt(l/u)),b=Math.abs(g)<5;s||(s=b?C.restSpeed.granular:C.restSpeed.default),i||(i=b?C.restDelta.granular:C.restDelta.default);let x;if(m<1){const v=Se(y,m);x=P=>{const M=Math.exp(-m*y*P);return o-M*((p+m*y*g)/v*Math.sin(v*P)+g*Math.cos(v*P))}}else if(m===1)x=v=>o-Math.exp(-y*v)*(g+(p+y*g)*v);else{const v=y*Math.sqrt(m*m-1);x=P=>{const M=Math.exp(-m*y*P),S=Math.min(v*P,300);return o-M*((p+m*y*g)*Math.sinh(S)+v*g*Math.cosh(S))/v}}const A={calculatedDuration:d&&h||null,next:v=>{const P=x(v);if(d)a.done=v>=h;else{let M=v===0?p:0;m<1&&(M=v===0?_(p):Vi(x,v,P));const S=Math.abs(M)<=s,w=Math.abs(o-P)<=i;a.done=S&&w}return a.value=a.done?o:P,a},toString:()=>{const v=Math.min($e(A),Wt),P=wi(M=>A.next(v*M).value,v,30);return v+"ms "+P},toTransition:()=>{}};return A}Et.applyToOptions=t=>{const e=bi(t,100,Et);return t.ease=e.ease,t.duration=_(e.duration),t.type="keyframes",t};function Ae({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=S=>a!==void 0&&S<a||l!==void 0&&S>l,p=S=>a===void 0?l:l===void 0||Math.abs(a-S)<Math.abs(l-S)?a:l;let m=n*e;const g=h+m,y=o===void 0?g:o(g);y!==g&&(m=y-h);const b=S=>-m*Math.exp(-S/s),x=S=>y+b(S),A=S=>{const w=b(S),O=x(S);f.done=Math.abs(w)<=c,f.value=f.done?y:O};let v,P;const M=S=>{d(f.value)&&(v=S,P=Et({keyframes:[f.value,p(f.value)],velocity:Vi(x,S,f.value),damping:i,stiffness:r,restDelta:c,restSpeed:u}))};return M(0),{calculatedDuration:null,next:S=>{let w=!1;return!P&&v===void 0&&(w=!0,A(S),M(S)),v!==void 0&&S>=v?P.next(S-v):(!w&&A(S),f)}}}function dl(t,e,n){const s=[],i=n||G.mix||Pi,r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const l=Array.isArray(e)?e[o]||j:e;a=Dt(l,a)}s.push(a)}return s}function pl(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(Ie(r===e.length),r===1)return()=>e[0];if(r===2&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=dl(e,s,i),l=a.length,c=u=>{if(o&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const f=lt(t[h],t[h+1],u);return a[h](f)};return n?u=>c(H(t[0],t[r-1],u)):c}function ml(t,e){return t.map(n=>n*e)}const Ei=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,gl=1e-7,yl=12;function vl(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=Ei(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>gl&&++a<yl);return o}function Rt(t,e,n,s){if(t===e&&n===s)return j;const i=r=>vl(r,0,1,t,n);return r=>r===0||r===1?r:Ei(i(r),e,s)}const Tl=Rt(.42,0,1,1),xl=Rt(0,0,.58,1),Mi=Rt(.42,0,.58,1),Ci=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Di=t=>e=>1-t(1-e),Ri=Rt(.33,1.53,.69,.99),ze=Di(Ri),Li=Ci(ze),Bi=t=>(t*=2)<1?.5*ze(t):.5*(2-Math.pow(2,-10*(t-1))),He=t=>1-Math.sin(Math.acos(t)),Fi=Di(He),ji=Ci(He),Oi=t=>Array.isArray(t)&&typeof t[0]=="number",bl={linear:j,easeIn:Tl,easeInOut:Mi,easeOut:xl,circIn:He,circInOut:ji,circOut:Fi,backIn:ze,backInOut:Li,backOut:Ri,anticipate:Bi},Sl=t=>typeof t=="string",jn=t=>{if(Oi(t)){Ie(t.length===4);const[e,n,s,i]=t;return Rt(e,n,s,i)}else if(Sl(t))return bl[t];return t};function Al(t,e){return t.map(()=>e||Mi).splice(0,t.length-1)}function xt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=yi(s)?s.map(jn):jn(s),r={done:!1,value:e[0]},o=ml(n&&n.length===e.length?n:xi(e),t),a=pl(o,e,{ease:Array.isArray(i)?i:Al(e,i)});return{calculatedDuration:t,next:l=>(r.value=a(l),r.done=l>=t,r)}}const Pl=t=>t!==null;function Ye(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(Pl),a=i<0||e&&n!=="loop"&&e%2===1?0:r.length-1;return!a||s===void 0?r[a]:s}const wl={decay:Ae,inertia:Ae,tween:xt,keyframes:xt,spring:Et};function ki(t){typeof t.type=="string"&&(t.type=wl[t.type])}class Xe{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const Vl=t=>t/100;class qe extends Xe{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var n,s;const{motionValue:i}=this.options;i&&i.updatedAt!==F.now()&&this.tick(F.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(s=(n=this.options).onStop)==null||s.call(n))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;ki(e);const{type:n=xt,repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:o=0}=e;let{keyframes:a}=e;const l=n||xt;l!==xt&&typeof a[0]!="number"&&(this.mixKeyframes=Dt(Vl,Pi(a[0],a[1])),a=[0,100]);const c=l({...e,keyframes:a});r==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=$e(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:f,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const y=this.currentTime-c*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let x=this.currentTime,A=s;if(h){const S=Math.min(this.currentTime,i)/a;let w=Math.floor(S),O=S%1;!O&&S>=1&&(O=1),O===1&&w--,w=Math.min(w,h+1),!!(w%2)&&(f==="reverse"?(O=1-O,d&&(O-=d/a)):f==="mirror"&&(A=o)),x=H(0,1,O)*a}const v=b?{done:!1,value:u[0]}:A.next(x);r&&(v.value=r(v.value));let{done:P}=v;!b&&l!==null&&(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const M=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&P);return M&&p!==Ae&&(v.value=Ye(u,this.options,g,this.speed)),m&&m(v.value),M&&this.finish(),v}then(e,n){return this.finished.then(e,n)}get duration(){return $(this.calculatedDuration)}get time(){return $(this.currentTime)}set time(e){var n;e=_(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(F.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=$(this.currentTime))}play(){var e,n;if(this.isStopped)return;const{driver:s=ol,startTime:i}=this.options;this.driver||(this.driver=s(o=>this.tick(o))),(n=(e=this.options).onPlay)==null||n.call(e);const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=i??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(F.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}const El=t=>t.startsWith("--");function Ml(t,e,n){El(e)?t.style.setProperty(e,n):t.style[e]=n}function Ze(t){let e;return()=>(e===void 0&&(e=t()),e)}const Cl=Ze(()=>window.ScrollTimeline!==void 0),Dl={};function Rl(t,e){const n=Ze(t);return()=>Dl[e]??n()}const Ii=Rl(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),gt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,On={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:gt([0,.65,.55,1]),circOut:gt([.55,0,1,.45]),backIn:gt([.31,.01,.66,-.59]),backOut:gt([.33,1.53,.69,.99])};function _i(t,e){if(t)return typeof t=="function"?Ii()?wi(t,e):"ease-out":Oi(t)?gt(t):Array.isArray(t)?t.map(n=>_i(n,e)||On.easeOut):On[t]}function Ll(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=_i(a,i);Array.isArray(h)&&(u.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),t.animate(u,f)}function Bl({type:t,...e}){return Ke(t)&&Ii()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Fl extends Xe{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=e,Ie(typeof e.type!="string");const c=Bl(e);this.animation=Ll(n,s,i,c,r),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const u=Ye(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):Ml(n,s,u),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var e,n;const s=((n=(e=this.animation.effect)==null?void 0:e.getComputedTiming)==null?void 0:n.call(e).duration)||0;return $(Number(s))}get time(){return $(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=_(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&Cl()?(this.animation.timeline=e,j):n(this)}}const Ui={anticipate:Bi,backInOut:Li,circInOut:ji};function jl(t){return t in Ui}function Ol(t){typeof t.ease=="string"&&jl(t.ease)&&(t.ease=Ui[t.ease])}const kn=10;class kl extends Fl{constructor(e){Ol(e),ki(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:r,...o}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new qe({...o,autoplay:!1}),l=_(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-kn).value,a.sample(l).value,kn),a.stop()}}const In=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Z.test(t)||t==="0")&&!t.startsWith("url("));function Il(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function _l(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],o=In(i,e),a=In(r,e);return!o||!a?!1:Il(t)||(n==="spring"||Ke(n))&&s}function Ni(t){return typeof t=="object"&&t!==null}function $i(t){return Ni(t)&&"offsetHeight"in t}const Ul=new Set(["opacity","clipPath","filter","transform"]),Nl=Ze(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function $l(t){var e;const{motionValue:n,name:s,repeatDelay:i,repeatType:r,damping:o,type:a}=t;if(!$i((e=n?.owner)==null?void 0:e.current))return!1;const{onUpdate:l,transformTemplate:c}=n.owner.getProps();return Nl()&&s&&Ul.has(s)&&(s!=="transform"||!c)&&!l&&!i&&r!=="mirror"&&o!==0&&a!=="inertia"}const Kl=40;class Wl extends Xe{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var f;super(),this.stop=()=>{var m,g;this._animation&&(this._animation.stop(),(m=this.stopTimeline)==null||m.call(this)),(g=this.keyframeResolver)==null||g.cancel()},this.createdAt=F.now();const d={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:o,name:l,motionValue:c,element:u,...h},p=u?.KeyframeResolver||Fe;this.keyframeResolver=new p(a,(m,g,y)=>this.onKeyframesResolved(m,g,d,!y),l,c,u),(f=this.keyframeResolver)==null||f.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:l,isHandoff:c,onUpdate:u}=s;this.resolvedAt=F.now(),_l(e,r,o,a)||((G.instantAnimations||!l)&&u?.(Ye(e,s,n)),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Kl?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!c&&$l(f)?new kl({...f,element:f.motionValue.owner.current}):new qe(f);d.finished.then(()=>this.notifyFinished()).catch(j),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),Or()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const Je=(t,e,n,s={},i,r)=>o=>{const a=We(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-_(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};Xa(a)||Object.assign(u,Ya(t,u)),u.duration&&(u.duration=_(u.duration)),u.repeatDelay&&(u.repeatDelay=_(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(G.instantAnimations||G.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!r&&e.get()!==void 0){const f=Ka(u.keyframes,a);if(f!==void 0){V.update(()=>{u.onUpdate(f),u.onComplete()});return}}return a.isSync?new qe(u):new Wl(u)};function Gl({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function zl(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const l=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const u in a){const h=t.getValue(u,t.latestValues[u]??null),f=a[u];if(f===void 0||c&&Gl(c,u))continue;const d={delay:n,...We(r||{},u)},p=h.get();if(p!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===p&&!d.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){const y=Si(t);if(y){const b=window.MotionHandoffAnimation(y,u,V);b!==null&&(d.startTime=b,m=!0)}}_a(t,u),h.start(Je(u,h,f,t.shouldReduceMotion&&ri.has(u)?{type:!1}:d,t,m));const g=h.animation;g&&l.push(g)}return o&&Promise.all(l).then(()=>{V.update(()=>{o&&ka(t,o)})}),l}function Hl(t,e){return t in e}class Yl extends oi{constructor(){super(...arguments),this.type="object"}readValueFromInstance(e,n){if(Hl(n,e)){const s=e[n];if(typeof s=="string"||typeof s=="number")return s}}getBaseTargetFromProps(){}removeValueFromRenderState(e,n){delete n.output[e]}measureInstanceViewportBox(){return D()}build(e,n){Object.assign(e.output,n)}renderInstance(e,{output:n}){Object.assign(e,n)}sortInstanceNodePosition(){return 0}}function Qe(t){return Ni(t)&&"ownerSVGElement"in t}function Ki(t){return Qe(t)&&t.tagName==="svg"}function Xl(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Qe(t)&&!Ki(t)?new mi(e):new di(e);n.mount(t),st.set(t,n)}function ql(t){const e={presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}},n=new Yl(e);n.mount(t),st.set(t,n)}function Wi(t,e,n){const s=L(t)?t:at(t);return s.start(Je("",s,e,n)),s.animation}function Zl(t,e){return L(t)||typeof t=="number"||typeof t=="string"&&!Ue(e)}function Gi(t,e,n,s){const i=[];if(Zl(t,e))i.push(Wi(t,Ue(e)&&e.default||e,n&&(n.default||n)));else{const r=gi(t,e,s),o=r.length;for(let a=0;a<o;a++){const l=r[a],c=l instanceof Element?Xl:ql;st.has(l)||c(l);const u=st.get(l),h={...n};"delay"in h&&typeof h.delay=="function"&&(h.delay=h.delay(a,o)),i.push(...zl(u,{...e,transition:h},{}))}}return i}function Jl(t,e,n){const s=[];return Ma(t,e,n,{spring:Et}).forEach(({keyframes:r,transition:o},a)=>{s.push(...Gi(a,r,o))}),s}class Ql{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>e.finished))}getAll(e){return this.animations[0][e]}setAll(e,n){for(let s=0;s<this.animations.length;s++)this.animations[s][e]=n}attachTimeline(e){const n=this.animations.map(s=>s.attachTimeline(e));return()=>{n.forEach((s,i)=>{s&&s(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class tu extends Ql{then(e,n){return this.finished.finally(e).then(()=>{})}}function eu(t){return Array.isArray(t)&&t.some(Array.isArray)}function nu(t){function e(n,s,i){let r=[];return eu(n)?r=Jl(n,s,t):r=Gi(n,s,i,t),new tu(r)}return e}const su=nu(),iu=["initial","animate","whileInView","whileHover","whilePress","whileDrag","whileFocus","exit"];class ou extends Y{constructor(e){var n;super(e),this.animateUpdates=({controlActiveState:s,directAnimate:i,directTransition:r,controlDelay:o=0,isFallback:a,isExit:l}={})=>{const{reducedMotion:c}=this.state.options.motionConfig;this.state.visualElement.shouldReduceMotion=c==="always"||c==="user"&&!!Kt.current;const u=this.state.target;this.state.target={...this.state.baseTarget};let h={};h=this.resolveStateAnimation({controlActiveState:s,directAnimate:i,directTransition:r});const f=this.createAnimationFactories(u,h,o),{getChildAnimations:d}=this.setupChildAnimations(h,this.state.activeStates,a);return this.executeAnimations({factories:f,getChildAnimations:d,transition:h,controlActiveState:s,isExit:l})},this.state.visualElement=xa(this.state.options.as,{presenceContext:null,parent:(n=this.state.parent)==null?void 0:n.visualElement,props:{...this.state.options,whileTap:this.state.options.whilePress},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{...this.state.baseTarget}},reducedMotionConfig:this.state.options.motionConfig.reducedMotion}),this.state.animateUpdates=this.animateUpdates,this.state.isMounted()&&this.state.startAnimation()}updateAnimationControlsSubscription(){const{animate:e}=this.state.options;ln(e)&&(this.unmountControls=e.subscribe(this.state))}executeAnimations({factories:e,getChildAnimations:n,transition:s,controlActiveState:i,isExit:r=!1}){const o=()=>Promise.all(e.map(h=>h()).filter(Boolean)),a={...this.state.target},l=this.state.element,c=h=>{var f,d;l.dispatchEvent(cn("motionstart",a)),(d=(f=this.state.options).onAnimationStart)==null||d.call(f,a),h.then(()=>{var p,m;l.dispatchEvent(cn("motioncomplete",a,r)),(m=(p=this.state.options).onAnimationComplete)==null||m.call(p,a)}).catch(j)},u=()=>{const h=s?.when?(s.when==="beforeChildren"?o():n()).then(()=>s.when==="beforeChildren"?n():o()):Promise.all([o(),n()]);return c(h),h};return i?u:u()}setupChildAnimations(e,n,s){var i;const r=this.state.visualElement;if(!((i=r.variantChildren)!=null&&i.size)||!n)return{getChildAnimations:()=>Promise.resolve()};const{staggerChildren:o=0,staggerDirection:a=1,delayChildren:l=0}=e||{},c=r.variantChildren.size,u=(c-1)*o,h=typeof l=="function",f=h?p=>l(p,c):a===1?(p=0)=>p*o:(p=0)=>u-p*o,d=Array.from(r.variantChildren).map((p,m)=>p.state.animateUpdates({controlActiveState:n,controlDelay:(h?0:l)+f(m)}));return{getChildAnimations:()=>Promise.all(d.map(p=>p()))}}createAnimationFactories(e,n,s){const i=[];return Object.keys(this.state.target).forEach(r=>{var o;if(!$o(e[r],this.state.target[r]))return;(o=this.state.baseTarget)[r]??(o[r]=ur.get(this.state.element,r));const a=this.state.target[r]==="none"&&mn(dn[r])?dn[r]:this.state.target[r];i.push(()=>{var l;return su(this.state.element,{[r]:a},{...n?.[r]||n,delay:(((l=n?.[r])==null?void 0:l.delay)||n?.delay||0)+s})})}),i}resolveStateAnimation({controlActiveState:e,directAnimate:n,directTransition:s}){let i=this.state.options.transition,r={};const{variants:o,custom:a,transition:l,animatePresenceContext:c}=this.state.options,u=a??c?.custom;return this.state.activeStates={...this.state.activeStates,...e},iu.forEach(h=>{if(!this.state.activeStates[h]||ln(this.state.options[h]))return;const f=this.state.options[h];let d=mn(f)?vt(f,o,u):void 0;if(this.state.visualElement.isVariantNode){const p=vt(this.state.context[h],o,u);d=p?Object.assign(p||{},d):r}d&&(h!=="initial"&&(i=d.transition||this.state.options.transition||{}),r=Object.assign(r,d))}),n&&(r=vt(n,o,u),i=r.transition||s||l),Object.entries(r).forEach(([h,f])=>{h!=="transition"&&(this.state.target[h]=f)}),i}mount(){const{element:e}=this.state;Ns.set(e,this.state),st.get(e)||(this.state.visualElement.mount(e),st.set(e,this.state.visualElement)),this.state.visualElement.state=this.state,this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.state.options,{animate:n}=this.state.visualElement.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;(e=this.unmountControls)==null||e.call(this)}}const ru={y:!1};function au(){return ru.y}const zi=(t,e)=>e?t===e?!0:zi(t,e.parentElement):!1,lu=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function Hi(t,e){const n=Ne(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}const uu=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function cu(t){return uu.has(t.tagName)||t.tabIndex!==-1}const Ut=new WeakSet;function _n(t){return e=>{e.key==="Enter"&&t(e)}}function se(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const hu=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=_n(()=>{if(Ut.has(n))return;se(n,"down");const i=_n(()=>{se(n,"up")}),r=()=>se(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function Un(t){return lu(t)&&!0}function fu(t,e,n={}){const[s,i,r]=Hi(t,n),o=a=>{const l=a.currentTarget;if(!Un(a))return;Ut.add(l);const c=e(l,a),u=(d,p)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Ut.has(l)&&Ut.delete(l),Un(d)&&typeof c=="function"&&c(d,{success:p})},h=d=>{u(d,l===window||l===document||n.useGlobalTarget||zi(l,d.target))},f=d=>{u(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,i),$i(a)&&(a.addEventListener("focus",c=>hu(c,i)),!cu(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),r}function Yi(t){return{point:{x:t.pageX,y:t.pageY}}}function Nn(t,e,n){const s=t.options;s.whilePress&&t.setActive("whilePress",n==="Start");const i=`onPress${n==="End"?"":n}`,r=s[i];r&&V.postRender(()=>r(e,Yi(e)))}class du extends Y{isActive(){const{whilePress:e,onPress:n,onPressCancel:s,onPressStart:i}=this.state.options;return!!(e||n||s||i)}constructor(e){super(e)}mount(){this.register()}update(){const{whilePress:e,onPress:n,onPressCancel:s,onPressStart:i}=this.state.options;e||n||s||i||this.register()}register(){const e=this.state.element;!e||!this.isActive()||(this.unmount(),this.unmount=fu(e,(n,s)=>(Nn(this.state,s,"Start"),(i,{success:r})=>Nn(this.state,i,r?"End":"Cancel")),{useGlobalTarget:this.state.options.globalPressTarget}))}}function $n(t){return!(t.pointerType==="touch"||au())}function pu(t,e,n={}){const[s,i,r]=Hi(t,n),o=a=>{if(!$n(a))return;const{target:l}=a,c=e(l,a);if(typeof c!="function"||!l)return;const u=h=>{$n(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,i)};return s.forEach(a=>{a.addEventListener("pointerenter",o,i)}),r}function Kn(t,e,n){const s=t.options;s.whileHover&&t.setActive("whileHover",n==="Start");const i=`onHover${n}`,r=s[i];r&&V.postRender(()=>r(e,Yi(e)))}class mu extends Y{isActive(){const{whileHover:e,onHoverStart:n,onHoverEnd:s}=this.state.options;return!!(e||n||s)}constructor(e){super(e)}mount(){this.register()}update(){const{whileHover:e,onHoverStart:n,onHoverEnd:s}=this.state.visualElement.prevProps;e||n||s||this.register()}register(){const e=this.state.element;!e||!this.isActive()||(this.unmount(),this.unmount=pu(e,(n,s)=>(Kn(this.state,s,"Start"),i=>{Kn(this.state,i,"End")})))}}const gu={some:0,all:1};function yu(t,e,{root:n,margin:s,amount:i="some"}={}){const r=Ne(t),o=new WeakMap,a=c=>{c.forEach(u=>{const h=o.get(u.target);if(u.isIntersecting!==!!h)if(u.isIntersecting){const f=e(u.target,u);typeof f=="function"?o.set(u.target,f):l.unobserve(u.target)}else typeof h=="function"&&(h(u),o.delete(u.target))})},l=new IntersectionObserver(a,{root:n,rootMargin:s,threshold:typeof i=="number"?i:gu[i]});return r.forEach(c=>l.observe(c)),()=>l.disconnect()}function Wn(t,e,n){const s=t.options;s.whileInView&&t.setActive("whileInView",n==="Enter");const i=`onViewport${n}`,r=s[i];r&&V.postRender(()=>r(e))}class vu extends Y{isActive(){const{whileInView:e,onViewportEnter:n,onViewportLeave:s}=this.state.options;return!!(e||n||s)}constructor(e){super(e)}startObserver(){const e=this.state.element;if(!e||!this.isActive())return;this.unmount();const{once:n,...s}=this.state.options.inViewOptions||{};this.unmount=yu(e,(i,r)=>{if(Wn(this.state,r,"Enter"),!n)return o=>{Wn(this.state,r,"Leave")}},s)}mount(){this.startObserver()}update(){const{props:e,prevProps:n}=this.state.visualElement;["amount","margin","root"].some(Tu(e,n))&&this.startObserver()}}function Tu({inViewOptions:t={}},{inViewOptions:e={}}={}){return n=>t[n]!==e[n]}function zt(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}class xu extends Y{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.state.element.matches(":focus-visible")}catch{e=!0}e&&(this.state.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&(this.state.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Dt(zt(this.state.element,"focus",()=>this.onFocus()),zt(this.state.element,"blur",()=>this.onBlur()))}}const bu=(t,e)=>t.depth-e.depth;class Su{constructor(){this.children=[],this.isDirty=!1}add(e){Le(this.children,e),this.isDirty=!0}remove(e){Xt(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(bu),this.isDirty=!1,this.children.forEach(e)}}function ie(t){return L(t)?t.get():t}const Xi=["TopLeft","TopRight","BottomLeft","BottomRight"],Au=Xi.length,Gn=t=>typeof t=="string"?parseFloat(t):t,zn=t=>typeof t=="number"||T.test(t);function Pu(t,e,n,s,i,r){i?(t.opacity=E(0,n.opacity??1,wu(s)),t.opacityExit=E(e.opacity??1,0,Vu(s))):r&&(t.opacity=E(e.opacity??1,n.opacity??1,s));for(let o=0;o<Au;o++){const a=`border${Xi[o]}Radius`;let l=Hn(e,a),c=Hn(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||zn(l)===zn(c)?(t[a]=Math.max(E(Gn(l),Gn(c),s),0),(K.test(c)||K.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=E(e.rotate||0,n.rotate||0,s))}function Hn(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const wu=qi(0,.5,Fi),Vu=qi(.5,.95,j);function qi(t,e,n){return s=>s<t?0:s>e?1:n(lt(t,e,s))}function Yn(t,e){t.min=e.min,t.max=e.max}function k(t,e){Yn(t.x,e.x),Yn(t.y,e.y)}function Xn(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function oe(t){return t===void 0||t===1}function Pe({scale:t,scaleX:e,scaleY:n}){return!oe(t)||!oe(e)||!oe(n)}function J(t){return Pe(t)||Zi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Zi(t){return qn(t.x)||qn(t.y)}function qn(t){return t&&t!=="0%"}function Ht(t,e,n){const s=t-n,i=e*s;return n+i}function Zn(t,e,n,s,i){return i!==void 0&&(t=Ht(t,i,s)),Ht(t,n,s)+e}function we(t,e=0,n=1,s,i){t.min=Zn(t.min,e,n,s,i),t.max=Zn(t.max,e,n,s,i)}function Ji(t,{x:e,y:n}){we(t.x,e.translate,e.scale,e.originPoint),we(t.y,n.translate,n.scale,n.originPoint)}const Jn=.999999999999,Qn=1.0000000000001;function Eu(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,o;for(let a=0;a<i;a++){r=n[a],o=r.projectionDelta;const{visualElement:l}=r.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&rt(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,Ji(t,o)),s&&J(r.latestValues)&&rt(t,r.latestValues))}e.x<Qn&&e.x>Jn&&(e.x=1),e.y<Qn&&e.y>Jn&&(e.y=1)}function kt(t,e){t.min=t.min+e,t.max=t.max+e}function ts(t,e,n,s,i=.5){const r=E(t.min,t.max,i);we(t,e,n,r,s)}function rt(t,e){ts(t.x,e.x,e.scaleX,e.scale,e.originX),ts(t.y,e.y,e.scaleY,e.scale,e.originY)}const Qi=1e-4,Mu=1-Qi,Cu=1+Qi,to=.01,Du=0-to,Ru=0+to;function I(t){return t.max-t.min}function Lu(t,e,n){return Math.abs(t-e)<=n}function es(t,e,n,s=.5){t.origin=s,t.originPoint=E(e.min,e.max,t.origin),t.scale=I(n)/I(e),t.translate=E(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Mu&&t.scale<=Cu||isNaN(t.scale))&&(t.scale=1),(t.translate>=Du&&t.translate<=Ru||isNaN(t.translate))&&(t.translate=0)}function bt(t,e,n,s){es(t.x,e.x,n.x,s?s.originX:void 0),es(t.y,e.y,n.y,s?s.originY:void 0)}function ns(t,e,n){t.min=n.min+e.min,t.max=t.min+I(e)}function Bu(t,e,n){ns(t.x,e.x,n.x),ns(t.y,e.y,n.y)}function ss(t,e,n){t.min=e.min-n.min,t.max=t.min+I(e)}function St(t,e,n){ss(t.x,e.x,n.x),ss(t.y,e.y,n.y)}function is(t,e,n,s,i){return t-=e,t=Ht(t,1/n,s),i!==void 0&&(t=Ht(t,1/i,s)),t}function Fu(t,e=0,n=1,s=.5,i,r=t,o=t){if(K.test(e)&&(e=parseFloat(e),e=E(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=E(r.min,r.max,s);t===r&&(a-=e),t.min=is(t.min,e,n,a,i),t.max=is(t.max,e,n,a,i)}function os(t,e,[n,s,i],r,o){Fu(t,e[n],e[s],e[i],e.scale,r,o)}const ju=["x","scaleX","originX"],Ou=["y","scaleY","originY"];function rs(t,e,n,s){os(t.x,e,ju,n?n.x:void 0,s?s.x:void 0),os(t.y,e,Ou,n?n.y:void 0,s?s.y:void 0)}function as(t){return t.translate===0&&t.scale===1}function eo(t){return as(t.x)&&as(t.y)}function ls(t,e){return t.min===e.min&&t.max===e.max}function ku(t,e){return ls(t.x,e.x)&&ls(t.y,e.y)}function us(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function no(t,e){return us(t.x,e.x)&&us(t.y,e.y)}function cs(t){return I(t.x)/I(t.y)}function hs(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Iu{constructor(){this.members=[]}add(e){Le(this.members,e),e.scheduleRender()}remove(e){if(Xt(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function _u(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y,o=n?.z||0;if((i||r||o)&&(s=`translate3d(${i}px, ${r}px, ${o}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:d,skewY:p}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),p&&(s+=`skewY(${p}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}function fs(t){return[t("x"),t("y")]}const Nt={hasAnimatedSinceResize:!0,hasEverUpdated:!1},{schedule:Uu}=ks(queueMicrotask,!1),re=["","X","Y","Z"],Nu=1e3;let $u=0;function ae(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function so(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Si(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(i||r))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&so(s)}function io({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o={},a=e?.()){this.id=$u++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Gu),this.nodes.forEach(Xu),this.nodes.forEach(qu),this.nodes.forEach(zu)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Su)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Be),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o){if(this.instance)return;this.isSVG=Qe(o)&&!Ki(o),this.instance=o;const{layoutId:a,layout:l,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let u,h=0;const f=()=>this.root.updateBlockedByResize=!1;V.read(()=>{h=window.innerWidth}),t(o,()=>{const d=window.innerWidth;d!==h&&(h=d,this.root.updateBlockedByResize=!0,u&&u(),u=Oo(f,250),Nt.hasAnimatedSinceResize&&(Nt.hasAnimatedSinceResize=!1,this.nodes.forEach(ms)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||c.getDefaultTransition()||ec,{onLayoutAnimationStart:m,onLayoutAnimationComplete:g}=c.getProps(),y=!this.targetLayout||!no(this.targetLayout,d),b=!h&&f;if(this.options.layoutRoot||this.resumeFrom||b||h&&(y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const x={...We(p,"layout"),onPlay:m,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x),this.setAnimationOrigin(u,b)}else h||ms(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Zu),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&so(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ds);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(ps);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Yu),this.nodes.forEach(Ku),this.nodes.forEach(Wu)):this.nodes.forEach(ps),this.clearAllSnapshots();const a=F.now();B.delta=H(0,1e3/60,a-B.timestamp),B.timestamp=a,B.isProcessing=!0,Zt.update.process(B),Zt.preRender.process(B),Zt.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Uu.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Hu),this.sharedNodes.forEach(Ju)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!I(this.snapshot.measuredBox.x)&&!I(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=D(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&this.instance){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!eo(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;o&&this.instance&&(a||J(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),nc(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return D();const l=a.measureViewportBox();if(!(((o=this.scroll)==null?void 0:o.wasRoot)||this.path.some(sc))){const{scroll:u}=this.root;u&&(kt(l.x,u.offset.x),kt(l.y,u.offset.y))}return l}removeElementScroll(o){var a;const l=D();if(k(l,o),(a=this.scroll)!=null&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&k(l,o),kt(l.x,h.offset.x),kt(l.y,h.offset.y))}return l}applyTransform(o,a=!1){const l=D();k(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&rt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),J(u.latestValues)&&rt(l,u.latestValues)}return J(this.latestValues)&&rt(l,this.latestValues),l}removeTransform(o){const a=D();k(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!J(c.latestValues))continue;Pe(c.latestValues)&&c.updateSnapshot();const u=D(),h=c.measurePageBox();k(u,h),rs(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return J(this.latestValues)&&rs(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||(a=this.parent)!=null&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=D(),this.relativeTargetOrigin=D(),St(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),k(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=D(),this.targetWithTransforms=D()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Bu(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):k(this.target,this.layout.layoutBox),Ji(this.target,this.targetDelta)):k(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=D(),this.relativeTargetOrigin=D(),St(this.relativeTargetOrigin,this.target,d.target),k(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Pe(this.parent.latestValues)||Zi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||(o=this.parent)!=null&&o.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===B.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;k(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Eu(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=D());const{target:p}=a;if(!p){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Xn(this.prevProjectionDelta.x,this.projectionDelta.x),Xn(this.prevProjectionDelta.y,this.projectionDelta.y)),bt(this.projectionDelta,this.layoutCorrected,p,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==d||!hs(this.projectionDelta.x,this.prevProjectionDelta.x)||!hs(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=it(),this.projectionDelta=it(),this.projectionDeltaWithTransform=it()}setAnimationOrigin(o,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=it();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=D(),d=l?l.source:void 0,p=this.layout?this.layout.source:void 0,m=d!==p,g=this.getStack(),y=!g||g.members.length<=1,b=!!(m&&!y&&this.options.crossfade===!0&&!this.path.some(tc));this.animationProgress=0;let x;this.mixTargetDelta=A=>{const v=A/1e3;gs(h.x,o.x,v),gs(h.y,o.y,v),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(St(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Qu(this.relativeTarget,this.relativeTargetOrigin,f,v),x&&ku(this.relativeTarget,x)&&(this.isProjectionDirty=!1),x||(x=D()),k(x,this.relativeTarget)),m&&(this.animationValues=u,Pu(u,c,this.latestValues,v,b,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=v},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){var a,l,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(c=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||c.stop(),this.pendingAnimation&&(z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{Nt.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=at(0)),this.currentAnimation=Wi(this.motionValue,[0,1e3],{...o,velocity:0,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),o.onUpdate&&o.onUpdate(u)},onStop:()=>{},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Nu),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=o;if(!(!a||!l||!c)){if(this!==o&&this.layout&&c&&oo(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||D();const h=I(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+h;const f=I(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}k(a,l),rt(a,u),bt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Iu),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())==null?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())==null?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&ae("z",o,c,this.animationValues);for(let u=0;u<re.length;u++)ae(`rotate${re[u]}`,o,c,this.animationValues),ae(`skew${re[u]}`,o,c,this.animationValues);o.render();for(const u in c)o.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);o.scheduleRender()}applyProjectionStyles(o,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){o.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,o.visibility="",o.opacity="",o.pointerEvents=ie(a?.pointerEvents)||"",o.transform=l?l(this.latestValues,""):"none";return}const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){this.options.layoutId&&(o.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,o.pointerEvents=ie(a?.pointerEvents)||""),this.hasProjected&&!J(this.latestValues)&&(o.transform=l?l({},""):"none",this.hasProjected=!1);return}o.visibility="";const u=c.animationValues||c.latestValues;this.applyTransformsToTarget();let h=_u(this.projectionDeltaWithTransform,this.treeScale,u);l&&(h=l(u,h)),o.transform=h;const{x:f,y:d}=this.projectionDelta;o.transformOrigin=`${f.origin*100}% ${d.origin*100}% 0`,c.animationValues?o.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:o.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const p in Pt){if(u[p]===void 0)continue;const{correct:m,applyTo:g,isCSSVariable:y}=Pt[p],b=h==="none"?u[p]:m(u[p],c);if(g){const x=g.length;for(let A=0;A<x;A++)o[g[A]]=b}else y?this.options.visualElement.renderState.vars[p]=b:o[p]=b}this.options.layoutId&&(o.pointerEvents=c===this?ie(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(ds),this.root.sharedNodes.clear()}}}function Ku(t){t.updateLayout()}function Wu(t){var e;const n=((e=t.resumeFrom)==null?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:r}=t.options,o=n.source!==t.layout.source;r==="size"?fs(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],d=I(f);f.min=s[h].min,f.max=f.min+d}):oo(r,n.layoutBox,s)&&fs(h=>{const f=o?n.measuredBox[h]:n.layoutBox[h],d=I(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=it();bt(a,s,n.layoutBox);const l=it();o?bt(l,t.applyTransform(i,!0),n.measuredBox):bt(l,s,n.layoutBox);const c=!eo(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const p=D();St(p,n.layoutBox,f.layoutBox);const m=D();St(m,s,d.layoutBox),no(p,m)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=m,t.relativeTargetOrigin=p,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function Gu(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function zu(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Hu(t){t.clearSnapshot()}function ds(t){t.clearMeasurements()}function ps(t){t.isLayoutDirty=!1}function Yu(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ms(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Xu(t){t.resolveTargetDelta()}function qu(t){t.calcProjection()}function Zu(t){t.resetSkewAndRotation()}function Ju(t){t.removeLeadSnapshot()}function gs(t,e,n){t.translate=E(e.translate,0,n),t.scale=E(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function ys(t,e,n,s){t.min=E(e.min,n.min,s),t.max=E(e.max,n.max,s)}function Qu(t,e,n,s){ys(t.x,e.x,n.x,s),ys(t.y,e.y,n.y,s)}function tc(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const ec={duration:.45,ease:[.4,0,.1,1]},vs=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Ts=vs("applewebkit/")&&!vs("chrome/")?Math.round:j;function xs(t){t.min=Ts(t.min),t.max=Ts(t.max)}function nc(t){xs(t.x),xs(t.y)}function oo(t,e,n){return t==="position"||t==="preserve-aspect"&&!Lu(cs(e),cs(n),.2)}function sc(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}function ic(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const oc=io({attachResizeListener:(t,e)=>ic(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),le={current:void 0},rc=io({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!le.current){const t=new oc({});t.mount(window),t.setOptions({layoutScroll:!0}),le.current=t}return le.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"});function ro(t){if(t)return t.options.allowProjection!==!1?t.projection:ro(t.parent)}function bs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const mt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(T.test(t))t=parseFloat(t);else return t;const n=bs(t,e.target.x),s=bs(t,e.target.y);return`${n}% ${s}%`}},ac={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Z.parse(t);if(i.length>5)return s;const r=Z.createTransformer(t),o=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+o]/=a,i[1+o]/=l;const c=E(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=c),typeof i[3+o]=="number"&&(i[3+o]/=c),r(i)}},ao={borderRadius:{...mt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:mt,borderTopRightRadius:mt,borderBottomLeftRadius:mt,borderBottomRightRadius:mt,boxShadow:ac};function yt(t){return typeof t=="object"&&t!==null&&"nodeType"in t}class lc extends Y{constructor(e){super(e),_s(ao)}initProjection(){const e=this.state.options;this.state.visualElement.projection=new rc(this.state.visualElement.latestValues,e["data-framer-portal-id"]?void 0:ro(this.state.visualElement.parent)),this.state.visualElement.projection.isPresent=!0,this.setOptions()}beforeMount(){this.initProjection()}setOptions(){const e=this.state.options;this.state.visualElement.projection.setOptions({layout:e.layout,layoutId:e.layoutId,alwaysMeasureLayout:!!e.drag||e.dragConstraints&&yt(e.dragConstraints),visualElement:this.state.visualElement,animationType:typeof e.layout=="string"?e.layout:"both",layoutRoot:e.layoutRoot,layoutScroll:e.layoutScroll,crossfade:e.crossfade,onExitComplete:()=>{var n;if(!((n=this.state.visualElement.projection)!=null&&n.isPresent)){const s=De.get(this.state.element);this.state.isSafeToRemove=!0,s&&s({detail:{isExit:!0}},!0)}}})}update(){this.setOptions()}mount(){var e;(e=this.state.visualElement.projection)==null||e.mount(this.state.element)}}function lo(t){return t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1}function tn(t,e="page"){return{point:{x:t[`${e}X`],y:t[`${e}Y`]}}}function uc(t){return e=>lo(e)&&t(e,tn(e))}function At(t,e,n,s){return zt(t,e,uc(n),s)}function uo(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const Ss=uo("dragHorizontal"),As=uo("dragVertical");function cc(t){let e=!1;if(t==="y")e=As();else if(t==="x")e=Ss();else{const n=Ss(),s=As();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Ve(t){return t.max-t.min}function hc(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?E(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?E(n,t,s.max):Math.min(t,n)),t}const Ee=.35;function fc(t,{top:e,left:n,bottom:s,right:i}){return{x:Ps(t.x,n,i),y:Ps(t.y,e,s)}}function Ps(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function dc(t=Ee){return t===!1?t=0:t===!0&&(t=Ee),{x:ws(t,"left","right"),y:ws(t,"top","bottom")}}function ws(t,e,n){return{min:Vs(t,e),max:Vs(t,n)}}function Vs(t,e){return typeof t=="number"?t:t[e]||0}function pc(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}function mc(t,e){return{x:Es(t.x,e.x),y:Es(t.y,e.y)}}function Es(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function gc(t,e){let n=.5;const s=Ve(t),i=Ve(e);return i>s?n=lt(e.min,e.max-s,t.min):s>i&&(n=lt(t.min,t.max-i,e.min)),H(0,1,n)}const Ms=(t,e)=>Math.abs(t-e);function yc(t,e){const n=Ms(t.x,e.x),s=Ms(t.y,e.y);return Math.sqrt(n**2+s**2)}class co{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ce(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=yc(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:p}=h,{timestamp:m}=B;this.history.push({...p,timestamp:m});const{onStart:g,onMove:y}=this.handlers;f||(g&&g(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ue(f,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:p,resumeAnimation:m}=this.handlers;if(this.dragSnapToOrigin&&m&&m(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=ce(h.type==="pointercancel"?this.lastMoveEventInfo:ue(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,g),p&&p(h,g)},!lo(e))return;this.dragSnapToOrigin=r,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const o=tn(e),a=ue(o,this.transformPagePoint),{point:l}=a,{timestamp:c}=B;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,ce(a,this.history)),this.removeListeners=Dt(At(this.contextWindow,"pointermove",this.handlePointerMove),At(this.contextWindow,"pointerup",this.handlePointerUp),At(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),z(this.updatePoint)}}function ue(t,e){return e?{point:e(t.point)}:t}function Cs(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ce({point:t},e){return{point:t,delta:Cs(t,ho(e)),offset:Cs(t,vc(e)),velocity:Tc(e,.1)}}function vc(t){return t[0]}function ho(t){return t[t.length-1]}function Tc(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=ho(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>_(e)));)n--;if(!s)return{x:0,y:0};const r=$(i.timestamp-s.timestamp);if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const Ds=()=>({min:0,max:0});function xc(){return{x:Ds(),y:Ds()}}function N(t){return[t("x"),t("y")]}function bc(t){return!!(L(t)&&t.add)}function Rs(t,e){const n=t.getValue("willChange");if(bc(n))return n.add(e)}function fo({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Sc(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function Ac({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Ls(t,e){t.min=t.min+e,t.max=t.max+e}function Pc(t,e){return fo(Sc(t.getBoundingClientRect(),e))}function wc(t,e,n){const s=Pc(t,n),{scroll:i}=e;return i&&(Ls(s.x,i.offset.x),Ls(s.y,i.offset.y)),s}function Bs(t){return!De.has(t.current)}function po({current:t}){return t?t.ownerDocument.defaultView:null}const Vc=new WeakMap;class Ec{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=xc(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){if(!Bs(this.visualElement))return;const s=c=>{const{dragSnapToOrigin:u}=this.getProps();u?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(tn(c,"page").point)},i=(c,u)=>{const{drag:h,dragPropagation:f,onDragStart:d}=this.getProps();if(h&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=cc(h),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),N(m=>{let g=this.getAxisMotionValue(m).get()||0;if(K.test(g)){const{projection:y}=this.visualElement;if(y&&y.layout){const b=y.layout.layoutBox[m];b&&(g=Ve(b)*(parseFloat(g)/100))}}this.originPoint[m]=g}),d&&V.postRender(()=>d(c,u)),Rs(this.visualElement,"transform"),this.visualElement.state.setActive("whileDrag",!0)},r=(c,u)=>{const{dragPropagation:h,dragDirectionLock:f,onDirectionLock:d,onDrag:p}=this.getProps();if(!h&&!this.openGlobalLock)return;const{offset:m}=u;if(f&&this.currentDirection===null){this.currentDirection=Mc(m),this.currentDirection!==null&&d&&d(this.currentDirection);return}this.updateAxis("x",u.point,m),this.updateAxis("y",u.point,m),this.visualElement.render(),p&&p(c,u)},o=(c,u)=>this.stop(c,u),a=()=>N(c=>{var u;return this.getAnimationState(c)==="paused"&&((u=this.getAxisMotionValue(c).animation)==null?void 0:u.play())}),{dragSnapToOrigin:l}=this.getProps();this.panSession=new co(e,{onSessionStart:s,onStart:i,onMove:r,onSessionEnd:o,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,contextWindow:po(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:r}=this.getProps();r&&V.postRender(()=>r(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),this.visualElement.state.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!It(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=hc(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)==null?void 0:e.layout,r=this.constraints;n&&yt(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=fc(i.layoutBox,n):this.constraints=!1,this.elastic=dc(s),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&N(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=pc(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!yt(e))return!1;const s=e,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=wc(s,i.root,this.visualElement.getTransformPagePoint());let o=mc(i.layout.layoutBox,r);if(n){const a=n(Ac(o));this.hasMutatedConstraints=!!a,a&&(o=fo(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=N(u=>{if(!It(u,n,this.currentDirection))return;let h=l&&l[u]||{};o&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,p={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...r,...h};return this.startAxisValueAnimation(u,p)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Rs(this.visualElement,e),s.start(Je(e,s,0,n,this.visualElement,!1))}stopAnimation(){Bs(this.visualElement)&&N(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){N(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps();return s[n]||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){N(n=>{const{drag:s}=this.getProps();if(!It(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-E(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!yt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};N(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();i[o]=gc({min:l,max:l},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),N(o=>{if(!It(o,e,null))return;const a=this.getAxisMotionValue(o),{min:l,max:c}=this.constraints[o];a.set(E(l,c,i[o]))})}addListeners(){if(!this.visualElement.current)return;Vc.set(this.visualElement,this);const e=this.visualElement.current,n=At(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();yt(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.read(s);const o=zt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(N(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{o(),n(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=Ee,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function It(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Mc(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Cc extends Y{constructor(e){super(e),this.removeGroupControls=j,this.removeListeners=j,this.controls=new Ec(e.visualElement)}mount(){const{dragControls:e}=this.state.options;e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||j}unmount(){this.removeGroupControls(),this.removeListeners()}}class Dc extends Y{constructor(e){super(e),_s(ao)}beforeUpdate(){this.state.willUpdate("beforeUpdate")}update(){this.didUpdate()}didUpdate(){var e,n;(this.state.options.layout||this.state.options.layoutId||this.state.options.drag)&&((n=(e=this.state.visualElement.projection)==null?void 0:e.root)==null||n.didUpdate())}mount(){var e;const n=this.state.options,s=this.state.options.layoutGroup;if(n.layout||n.layoutId){const i=this.state.visualElement.projection;i&&(i.promote(),(e=s?.group)==null||e.add(i)),Nt.hasEverUpdated=!0}this.didUpdate()}beforeUnmount(){const e=this.state.visualElement.projection;e&&(this.state.willUpdate("beforeUnmount"),this.state.options.layoutId?(e.isPresent=!1,e.relegate()):this.state.options.layout&&(this.state.isSafeToRemove=!0))}unmount(){const e=this.state.options.layoutGroup,n=this.state.visualElement.projection;n&&(e?.group&&(this.state.options.layout||this.state.options.layoutId)&&e.group.remove(n),this.didUpdate())}}function Fs(t){return(e,n)=>{t&&V.postRender(()=>t(e,n))}}class Rc extends Y{constructor(){super(...arguments),this.removePointerDownListener=j}onPointerDown(e){this.session=new co(e,this.createPanHandlers(),{transformPagePoint:this.state.visualElement.getTransformPagePoint(),contextWindow:po(this.state.visualElement)})}createPanHandlers(){return{onSessionStart:Fs((e,n)=>{const{onPanSessionStart:s}=this.state.options;s&&s(e,n)}),onStart:Fs((e,n)=>{const{onPanStart:s}=this.state.options;s&&s(e,n)}),onMove:(e,n)=>{const{onPan:s}=this.state.options;s&&s(e,n)},onEnd:(e,n)=>{const{onPanEnd:s}=this.state.options;delete this.session,s&&V.postRender(()=>s(e,n))}}}mount(){this.removePointerDownListener=At(this.state.element,"pointerdown",this.onPointerDown.bind(this))}update(){}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Lc=[ou,du,mu,vu,xu,lc,Rc,Cc,Dc],Bc={reducedMotion:"never",transition:void 0,nonce:void 0},[Fc,th]=Mt("MotionConfig");function jc(){return Fc(To(()=>Bc))}function Yt(t){if(t)return t.nodeType===3||t.nodeType===8?Yt(t.nextSibling):t}const[Oc,eh]=Mt("LazyMotionContext");function kc(t){const e=_o(null),n=No({}),s=jc(),i=Xo({}),r=Oc({features:Lo([]),strict:!1}),o=xo();function a(){return n.id&&t.layoutId?`${n.id}-${t.layoutId}`:t.layoutId||void 0}function l(){return{...t,lazyMotionContext:r,layoutId:a(),transition:t.transition??s.value.transition,layoutGroup:n,motionConfig:s.value,inViewOptions:t.inViewOptions??s.value.inViewOptions,animatePresenceContext:i,initial:i.initial===!1?i.initial:t.initial===!0?void 0:t.initial}}function c(){return{...o,...l()}}const u=new Jo(c(),e);Uo(u);function h(){var d;const p=u.type==="svg",m={...o};Object.keys(o).forEach(b=>{L(o[b])&&(m[b]=o[b].get())});let g={...t.style,...p?{}:((d=u.visualElement)==null?void 0:d.latestValues)||u.baseTarget};if(p){const{attrs:b,style:x}=pr({...u.isMounted()?u.target:u.baseTarget,...g});(x.transform||b.transformOrigin)&&(x.transformOrigin=b.transformOrigin??"50% 50%",delete b.transformOrigin),x.transform&&(x.transformBox=x.transformBox??"fill-box",delete b.transformBox),Object.assign(m,b),g=x}t.drag&&t.dragListener!==!1&&Object.assign(g,{userSelect:"none",WebkitUserSelect:"none",WebkitTouchCallout:"none",touchAction:t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`});const y=cr(g);return y&&(m.style=y),m}const f=bo().proxy;return So(()=>{u.beforeMount()}),Ao(()=>{u.mount(Yt(f.$el),c(),_c(f))}),Po(()=>u.beforeUnmount()),wo(()=>{const d=Yt(f.$el);d?.isConnected||u.unmount()}),Vo(()=>{u.beforeUpdate()}),Eo(()=>{u.update(c())}),{getProps:l,getAttrs:h,layoutGroup:n,state:u}}const Ic={ignoreStrict:{type:Boolean},forwardMotionProps:{type:Boolean,default:!1},asChild:{type:Boolean,default:!1},hover:{type:[String,Array,Object]},press:{type:[String,Array,Object]},inView:{type:[String,Array,Object]},focus:{type:[String,Array,Object]},whileDrag:{type:[String,Array,Object]},whileHover:{type:[String,Array,Object],default:({hover:t})=>t},whilePress:{type:[String,Array,Object],default:({press:t})=>t},whileInView:{type:[String,Array,Object],default:({inView:t})=>t},whileFocus:{type:[String,Array,Object],default:({focus:t})=>t},custom:{type:[String,Number,Object,Array]},initial:{type:[String,Array,Object,Boolean],default:void 0},animate:{type:[String,Array,Object],default:void 0},exit:{type:[String,Array,Object]},variants:{type:Object},inherit:{type:Boolean},style:{type:Object},transformTemplate:{type:Function},transition:{type:Object},layoutGroup:{type:Object},motionConfig:{type:Object},onAnimationComplete:{type:Function},onUpdate:{type:Function},layout:{type:[Boolean,String],default:!1},layoutId:{type:String,default:void 0},layoutScroll:{type:Boolean,default:!1},layoutRoot:{type:Boolean,default:!1},"data-framer-portal-id":{type:String},crossfade:{type:Boolean,default:!0},layoutDependency:{type:[String,Number,Object,Array]},onBeforeLayoutMeasure:{type:Function},onLayoutMeasure:{type:Function},onLayoutAnimationStart:{type:Function},onLayoutAnimationComplete:{type:Function},globalPressTarget:{type:Boolean},onPressStart:{type:Function},onPress:{type:Function},onPressCancel:{type:Function},onHoverStart:{type:Function},onHoverEnd:{type:Function},inViewOptions:{type:Object},onViewportEnter:{type:Function},onViewportLeave:{type:Function},drag:{type:[Boolean,String]},dragSnapToOrigin:{type:Boolean},dragDirectionLock:{type:Boolean},dragPropagation:{type:Boolean},dragConstraints:{type:[Boolean,Object]},dragElastic:{type:[Boolean,Number,Object],default:.5},dragMomentum:{type:Boolean,default:!0},dragTransition:{type:Object},dragListener:{type:Boolean,default:!0},dragControls:{type:Object},onDragStart:{type:Function},onDragEnd:{type:Function},onDrag:{type:Function},onDirectionLock:{type:Function},onDragTransitionEnd:{type:Function},onMeasureDragConstraints:{type:Function},onPanSessionStart:{type:Function},onPanStart:{type:Function},onPan:{type:Function},onPanEnd:{type:Function}};function _c(t){var e;const n=((e=Yt(t.$el))==null?void 0:e.style.display)==="none";return t.$.vnode.transition&&n}const Uc=new Map,Nc=new Map;function $c(t){if(!Array.isArray(t))return[t];const e=[];for(const n of t)Array.isArray(n)?e.push(...n):e.push(n);return e}const Kc=["area","img","input"];function Wc(t,e,n){var s,i;if(typeof t=="string"&&Kc.includes(t))return Os(t,e);if(t==="template"){if(!n.default)return null;const r=$c(n.default()),o=r.findIndex(u=>u.type!==Co);if(o===-1)return r;const a=r[o];(s=a.props)==null||delete s.ref;const l=a.props?Do(e,a.props):e;e.class&&((i=a.props)!=null&&i.class)&&delete a.props.class;const c=Ro(a,l);for(const u in l)u.startsWith("on")&&(c.props||(c.props={}),c.props[u]=l[u]);return r.length===1?c:(r[o]=c,r)}return null}function js(t,e={}){var n;const s=typeof t=="string",i=s?t:t.name||"",r=((n=e.features)==null?void 0:n.length)>0?Uc:Nc;if(s&&r?.has(t))return r.get(t);const o=Mo({inheritAttrs:!1,props:{...Ic,features:{type:Object,default:()=>e.features||[]},as:{type:[String,Object],default:t||"div"}},name:i?`motion.${i}`:"Motion",setup(a,{slots:l}){const{getProps:c,getAttrs:u,state:h}=kc(a);function f(){const d=h.element;if((!(typeof a.as=="object")||a.asChild)&&d){const{style:m}=u();if(m)for(const[g,y]of Object.entries(m))d.style[g]=y}}return()=>{const d=c(),p=u(),m=a.asChild?"template":a.as,g={...e.forwardMotionProps||a.forwardMotionProps?d:{},...p,onVnodeUpdated:f},y=Wc(m,g,l);return y!==null?y:Os(m,{...g},l)}}});return s&&r?.set(t,o),o}function Gc(t=[]){return new Proxy({},{get(e,n){return n==="create"?(s,i)=>js(s,{...i,features:t}):js(n,{features:t})}})}const zc=Gc(Lc),nh=zc.create("div");export{nh as M};

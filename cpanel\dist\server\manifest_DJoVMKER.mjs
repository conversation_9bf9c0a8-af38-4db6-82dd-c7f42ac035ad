import 'kleur/colors';
import { p as decodeKey } from './chunks/astro/server_DbndhTWv.mjs';
import 'clsx';
import 'cookie';
import './chunks/astro-designed-error-pages_V7kVn5wI.mjs';
import 'es-module-lexer';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_BVqN376b.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/Dev/PARTTEC/parttec3/frontend/","cacheDir":"file:///D:/Dev/PARTTEC/parttec3/frontend/node_modules/.astro/","outDir":"file:///D:/Dev/PARTTEC/parttec3/frontend/dist/","srcDir":"file:///D:/Dev/PARTTEC/parttec3/frontend/src/","publicDir":"file:///D:/Dev/PARTTEC/parttec3/frontend/public/","buildClientDir":"file:///D:/Dev/PARTTEC/parttec3/frontend/dist/client/","buildServerDir":"file:///D:/Dev/PARTTEC/parttec3/frontend/dist/server/","adapterName":"@astrojs/node","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/node.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/access-control","isIndex":false,"type":"page","pattern":"^\\/admin\\/access-control\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"access-control","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/access-control.astro","pathname":"/admin/access-control","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/attributes","isIndex":false,"type":"page","pattern":"^\\/admin\\/attributes\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"attributes","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/attributes.astro","pathname":"/admin/attributes","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/brands","isIndex":true,"type":"page","pattern":"^\\/admin\\/brands\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"brands","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/brands/index.astro","pathname":"/admin/brands","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/catalogitems","isIndex":true,"type":"page","pattern":"^\\/admin\\/catalogitems\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"catalogitems","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/catalogitems/index.astro","pathname":"/admin/catalogitems","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/categories","isIndex":true,"type":"page","pattern":"^\\/admin\\/categories\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"categories","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/categories/index.astro","pathname":"/admin/categories","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\n.line-clamp-2[data-v-b449289d]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.compact-table[data-v-b449289d] .p-datatable-tbody>tr>td{padding:.5rem}.compact-table[data-v-b449289d] .p-datatable-thead>tr>th{padding:.5rem;font-size:.875rem}[data-v-b449289d] .p-tag{font-weight:500}@media (max-width: 768px){.equipment-attributes-list[data-v-b449289d] .p-datatable-wrapper{overflow-x:auto}.equipment-attributes-list[data-v-b449289d] .p-datatable{min-width:600px}}.line-clamp-2[data-v-cb27c7ef]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.space-y-6[data-v-cb27c7ef]>*+*{margin-top:1.5rem}.space-y-4[data-v-cb27c7ef]>*+*{margin-top:1rem}.equipment-attributes-section[data-v-2cbb67a9]>*+*{margin-top:1rem}.attribute-group[data-v-2cbb67a9]>*+*{margin-top:.75rem}.attribute-card[data-v-2cbb67a9]{transition:all .2s}.attribute-card[data-v-2cbb67a9]:hover{border-color:#d1d5db}.dark .attribute-card[data-v-2cbb67a9]:hover{border-color:#4b5563}.line-clamp-2[data-v-2cbb67a9]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/equipment","isIndex":true,"type":"page","pattern":"^\\/admin\\/equipment\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"equipment","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/equipment/index.astro","pathname":"/admin/equipment","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/forbidden","isIndex":false,"type":"page","pattern":"^\\/admin\\/forbidden\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"forbidden","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/forbidden.astro","pathname":"/admin/forbidden","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.login-form-container[data-v-e3f15633]{background:linear-gradient(135deg,#667eea,#764ba2)}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/login","isIndex":false,"type":"page","pattern":"^\\/admin\\/login\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"login","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/login.astro","pathname":"/admin/login","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/logout","isIndex":false,"type":"page","pattern":"^\\/admin\\/logout\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"logout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/logout.astro","pathname":"/admin/logout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/parts/create","isIndex":false,"type":"page","pattern":"^\\/admin\\/parts\\/create\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"parts","dynamic":false,"spread":false}],[{"content":"create","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/parts/create.astro","pathname":"/admin/parts/create","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/parts/[id]/edit","isIndex":false,"type":"page","pattern":"^\\/admin\\/parts\\/([^/]+?)\\/edit\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"parts","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}],[{"content":"edit","dynamic":false,"spread":false}]],"params":["id"],"component":"src/pages/admin/parts/[id]/edit.astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/parts/[id]","isIndex":false,"type":"page","pattern":"^\\/admin\\/parts\\/([^/]+?)\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"parts","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/admin/parts/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/parts","isIndex":true,"type":"page","pattern":"^\\/admin\\/parts\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"parts","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/parts/index.astro","pathname":"/admin/parts","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/proposals","isIndex":true,"type":"page","pattern":"^\\/admin\\/proposals\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"proposals","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/proposals/index.astro","pathname":"/admin/proposals","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/register","isIndex":false,"type":"page","pattern":"^\\/admin\\/register\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"register","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/register.astro","pathname":"/admin/register","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/ui-demo","isIndex":false,"type":"page","pattern":"^\\/admin\\/ui-demo\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"ui-demo","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/ui-demo.astro","pathname":"/admin/ui-demo","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n"}],"routeData":{"route":"/admin/users","isIndex":true,"type":"page","pattern":"^\\/admin\\/users\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"users","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/users/index.astro","pathname":"/admin/users","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/access-control.B1Rb28Tx.css"},{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\ni[data-v-ae567aec]{font-style:normal;display:inline-flex;align-items:center;justify-content:center}i[data-v-ae567aec]:before{content:var(--b098c906)}\n.theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\n"}],"routeData":{"route":"/admin","isIndex":true,"type":"page","pattern":"^\\/admin\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/index.astro","pathname":"/admin","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/auth/[...all]","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/auth(?:\\/(.*?))?\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"auth","dynamic":false,"spread":false}],[{"content":"...all","dynamic":true,"spread":true}]],"params":["...all"],"component":"src/pages/api/auth/[...all].ts","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n"},{"type":"external","src":"/_astro/catalog.I-uCPq-V.css"},{"type":"inline","content":".catalog-grid[data-v-80e3f4e4]{width:100%}.part-card[data-v-80e3f4e4]{transform:scale(1);transition:transform .2s ease-in-out}.part-card[data-v-80e3f4e4]:hover{transform:scale(1.05)}.line-clamp-2[data-v-80e3f4e4]{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.catalog-list[data-v-8c03748a]{width:100%}.part-item[data-v-8c03748a]{transition:all .2s ease-in-out}.part-item[data-v-8c03748a]:hover{transform:scale(1.01)}.catalog-table[data-v-d4823d7d]{width:100%}[data-v-d4823d7d] .p-datatable-tbody>tr{cursor:pointer}[data-v-d4823d7d] .p-datatable-tbody>tr:hover{background-color:var(--p-surface-100)}[data-theme=dark][data-v-d4823d7d] .p-datatable-tbody>tr:hover{background-color:var(--p-surface-800)}.catalog-browser[data-v-662d9b80]{width:100%}.catalog-content[data-v-662d9b80]{min-height:24rem}.container[data-astro-cid-6kjp6l6a]{max-width:1200px}\n.catalog-pagination[data-v-ff69da72]{margin-top:1.5rem}\n"}],"routeData":{"route":"/catalog","isIndex":false,"type":"page","pattern":"^\\/catalog\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/catalog.astro","pathname":"/catalog","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n"},{"type":"external","src":"/_astro/catalog.I-uCPq-V.css"},{"type":"inline","content":"@keyframes dash-5a322aad{0%{stroke-dashoffset:24;opacity:.6}50%{stroke-dashoffset:0;opacity:1}to{stroke-dashoffset:24;opacity:.6}}\n"}],"routeData":{"route":"/landing","isIndex":false,"type":"page","pattern":"^\\/landing\\/?$","segments":[[{"content":"landing","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/landing.astro","pathname":"/landing","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n"},{"type":"external","src":"/_astro/catalog.I-uCPq-V.css"},{"type":"inline","content":".search-page[data-v-1bf673bf]{width:100%}.search-results[data-v-1bf673bf]{min-height:24rem}.container[data-astro-cid-ipsxrsrh]{max-width:1200px}\n.catalog-pagination[data-v-ff69da72]{margin-top:1.5rem}\n"}],"routeData":{"route":"/search","isIndex":false,"type":"page","pattern":"^\\/search\\/?$","segments":[[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/search.astro","pathname":"/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n"},{"type":"external","src":"/_astro/catalog.I-uCPq-V.css"},{"type":"inline","content":".stats-page[data-v-3e452373]{width:100%}.container[data-astro-cid-j3fvw3lo]{max-width:1200px}\n"}],"routeData":{"route":"/stats","isIndex":false,"type":"page","pattern":"^\\/stats\\/?$","segments":[[{"content":"stats","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/stats.astro","pathname":"/stats","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"inline","content":".astro-route-announcer{position:absolute;left:0;top:0;clip:rect(0 0 0 0);clip-path:inset(50%);overflow:hidden;white-space:nowrap;width:1px;height:1px}\n"},{"type":"external","src":"/_astro/catalog.I-uCPq-V.css"},{"type":"inline","content":".theme-toggle[data-v-94d58d16]{position:relative}.theme-toggle .absolute[data-v-94d58d16]{animation:fadeIn-94d58d16 .15s ease-out}@keyframes fadeIn-94d58d16{0%{opacity:0;transform:translateY(-4px)}to{opacity:1;transform:translateY(0)}}\n"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/access-control.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/attributes.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/brands/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/catalogitems/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/categories/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/equipment/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/forbidden.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/login.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/logout.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/[id].astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/[id]/edit.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/create.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/proposals/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/register.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/ui-demo.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/users/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/catalog.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/landing.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/search.astro",{"propagation":"none","containsHead":true}],["D:/Dev/PARTTEC/parttec3/frontend/src/pages/stats.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-page:src/pages/admin/access-control@_@astro":"pages/admin/access-control.astro.mjs","\u0000@astro-page:src/pages/admin/attributes@_@astro":"pages/admin/attributes.astro.mjs","\u0000@astro-page:src/pages/admin/brands/index@_@astro":"pages/admin/brands.astro.mjs","\u0000@astro-page:src/pages/admin/catalogitems/index@_@astro":"pages/admin/catalogitems.astro.mjs","\u0000@astro-page:src/pages/admin/categories/index@_@astro":"pages/admin/categories.astro.mjs","\u0000@astro-page:src/pages/admin/equipment/index@_@astro":"pages/admin/equipment.astro.mjs","\u0000@astro-page:src/pages/admin/forbidden@_@astro":"pages/admin/forbidden.astro.mjs","\u0000@astro-page:src/pages/admin/login@_@astro":"pages/admin/login.astro.mjs","\u0000@astro-page:src/pages/admin/logout@_@astro":"pages/admin/logout.astro.mjs","\u0000@astro-page:src/pages/admin/parts/create@_@astro":"pages/admin/parts/create.astro.mjs","\u0000@astro-page:src/pages/admin/parts/[id]/edit@_@astro":"pages/admin/parts/_id_/edit.astro.mjs","\u0000@astro-page:src/pages/admin/parts/[id]@_@astro":"pages/admin/parts/_id_.astro.mjs","\u0000@astro-page:src/pages/admin/parts/index@_@astro":"pages/admin/parts.astro.mjs","\u0000@astro-page:src/pages/admin/proposals/index@_@astro":"pages/admin/proposals.astro.mjs","\u0000@astro-page:src/pages/admin/register@_@astro":"pages/admin/register.astro.mjs","\u0000@astro-page:src/pages/admin/ui-demo@_@astro":"pages/admin/ui-demo.astro.mjs","\u0000@astro-page:src/pages/admin/users/index@_@astro":"pages/admin/users.astro.mjs","\u0000@astro-page:src/pages/admin/index@_@astro":"pages/admin.astro.mjs","\u0000@astro-page:src/pages/api/auth/[...all]@_@ts":"pages/api/auth/_---all_.astro.mjs","\u0000@astro-page:src/pages/catalog@_@astro":"pages/catalog.astro.mjs","\u0000@astro-page:src/pages/landing@_@astro":"pages/landing.astro.mjs","\u0000@astro-page:src/pages/search@_@astro":"pages/search.astro.mjs","\u0000@astro-page:src/pages/stats@_@astro":"pages/stats.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/node@_@js":"pages/_image.astro.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000@astrojs-manifest":"manifest_DJoVMKER.mjs","D:/Dev/PARTTEC/parttec3/frontend/node_modules/unstorage/drivers/fs-lite.mjs":"chunks/fs-lite_COtHaKzy.mjs","D:/Dev/PARTTEC/parttec3/frontend/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_DGOBAobs.mjs","@/components/admin/access/AccessControl.vue":"_astro/AccessControl.BDYCC6Rl.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/auth/LoginForm.vue":"_astro/LoginForm.Cg3jsUJr.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/parts/PartsTable.vue":"_astro/PartsTable.C7lmpLtG.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/catalogitems/ProposalsList.vue":"_astro/ProposalsList.DrB_S_DP.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/auth/RegisterForm.vue":"_astro/RegisterForm.C9G5LmfT.js","@/components/admin/users/UsersManager.vue":"_astro/UsersManager.B7ceHodj.js","@/components/admin/AdminDashboard.vue":"_astro/AdminDashboard.Dh73LHfz.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/ui/Navbar.vue":"_astro/Navbar.C6TbBz7C.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/HelloVue.vue":"_astro/HelloVue.Be4ZFtl_.js","@/components/system/GlobalErrorHandlerInit.vue":"_astro/GlobalErrorHandlerInit.Bb2fB8Ik.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/AdminSidebar.vue":"_astro/AdminSidebar.BQR42ni1.js","D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/forbidden.astro?astro&type=script&index=0&lang.ts":"_astro/forbidden.astro_astro_type_script_index_0_lang.Ctv-VbVi.js","D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/logout.astro?astro&type=script&index=0&lang.ts":"_astro/logout.astro_astro_type_script_index_0_lang.OUY384DT.js","@/components/search/SearchPageBoundary.vue":"_astro/SearchPageBoundary.BxJyvW8y.js","@/components/stats/StatsPageBoundary.vue":"_astro/StatsPageBoundary.B3cm1GeF.js","D:/Dev/PARTTEC/parttec3/frontend/node_modules/astro/components/ClientRouter.astro?astro&type=script&index=0&lang.ts":"_astro/ClientRouter.astro_astro_type_script_index_0_lang.B1rxwTih.js","@/components/admin/brands/BrandListBoundary.vue":"_astro/BrandListBoundary.DPBHdjkL.js","@/components/admin/categories/CategoryListBoundary.vue":"_astro/CategoryListBoundary.BCvALjrO.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/UIDemo.vue":"_astro/UIDemo.C6Uv6ZNy.js","@/components/catalog/CatalogBrowserBoundary.vue":"_astro/CatalogBrowserBoundary.DAftVRkG.js","@/components/admin/attributes/AttributeTemplateManagerBoundary.vue":"_astro/AttributeTemplateManagerBoundary.CrsuE0CA.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/AdminToolbar.vue":"_astro/AdminToolbar.Bi_3ygL1.js","@/components/admin/catalogitems/CatalogItemsManagerBoundary.vue":"_astro/CatalogItemsManagerBoundary.pSnI3HFy.js","@/components/admin/equipment/EquipmentList.vue":"_astro/EquipmentList.D8ETuGOC.js","@astrojs/vue/client.js":"_astro/client.BjeRTyLB.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/landing/LandingPage.vue":"_astro/LandingPage.B5q2aWge.js","D:/Dev/PARTTEC/parttec3/frontend/src/volt/Button.vue":"_astro/Button.DrThv2lH.js","D:/Dev/PARTTEC/parttec3/frontend/src/volt/Card.vue":"_astro/Card.C4y0_bWr.js","@/volt/Toast.vue":"_astro/Toast.DmmKUJB6.js","D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/parts/PartWizard.vue":"_astro/PartWizard.lhc8Ogic.js","@/components/admin/parts/PartWizard.vue":"_astro/PartWizard.CdN9gAyB.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/forbidden.astro?astro&type=script&index=0&lang.ts","typeof window<\"u\"&&console.warn(\"🚫 Access denied to:\",window.location.pathname);"]],"assets":["/_astro/access-control.B1Rb28Tx.css","/_astro/catalog.I-uCPq-V.css","/favicon.svg","/theme-init.js","/_astro/access-control.CRX8_BxR.css","/_astro/access-control.DAcr2cZn.css","/_astro/AccessControl.BDYCC6Rl.js","/_astro/AdminDashboard.Dh73LHfz.js","/_astro/AdminSidebar.BQR42ni1.js","/_astro/AdminToolbar.Bi_3ygL1.js","/_astro/AttributeTemplateManagerBoundary.CrsuE0CA.js","/_astro/AttributeValueInput.BnZ_HprM.js","/_astro/auth-client.CMFsScx1.js","/_astro/AutoComplete.rPzROuMW.js","/_astro/BrandListBoundary.DPBHdjkL.js","/_astro/bundle-mjs.D6B6e0vX.js","/_astro/Button.DrThv2lH.js","/_astro/Card.C4y0_bWr.js","/_astro/catalog.BmOgbNH2.css","/_astro/catalog.DCHLZ3tM.css","/_astro/CatalogBrowserBoundary.DAftVRkG.js","/_astro/CatalogItemsManagerBoundary.pSnI3HFy.js","/_astro/CategoryListBoundary.BCvALjrO.js","/_astro/check.B3pubfVf.js","/_astro/Checkbox.C7bFmmzc.js","/_astro/client.BjeRTyLB.js","/_astro/ClientRouter.astro_astro_type_script_index_0_lang.B1rxwTih.js","/_astro/ConfirmDialog.J0sszorU.js","/_astro/createLucideIcon.NtN1-Ts2.js","/_astro/DangerButton.Du4QYdLH.js","/_astro/Dialog.Ct7C9BO5.js","/_astro/Dropdown.Cj1958l9.js","/_astro/EquipmentList.D8ETuGOC.js","/_astro/ErrorBoundary.B0AhELGe.js","/_astro/fetchers.B4Ycwiwp.js","/_astro/GlobalErrorHandlerInit.Bb2fB8Ik.js","/_astro/HelloVue.Be4ZFtl_.js","/_astro/Icon.By8t0-Wj.js","/_astro/index.6ykohhwZ.js","/_astro/index.BaVCXmir.js","/_astro/index.BfoD-TOQ.css","/_astro/index.BH7IgUdp.js","/_astro/index.BpXFSz0M.js","/_astro/index.BWD5ZO4k.js","/_astro/index.By2TJOuX.js","/_astro/index.BZ4rDiaJ.js","/_astro/index.CBuVmUry.js","/_astro/index.CDQpPXyE.js","/_astro/index.CLs7nh7g.js","/_astro/index.CmzoVUnM.js","/_astro/index.COq_zjeV.js","/_astro/index.CS9OBiV4.js","/_astro/index.CUNrRq8E.js","/_astro/index.CwqAtb_i.js","/_astro/index.D2J2rxcx.css","/_astro/index.D4QD70nN.js","/_astro/index.DPMtieGJ.js","/_astro/index.J-5Oa3io.js","/_astro/index.n7VWMPJ9.js","/_astro/index.PhWaFJhe.js","/_astro/index.S_9XL1GF.js","/_astro/index.uDWUdklz.js","/_astro/info.B6miOEHp.js","/_astro/InputNumber.vgPO18dj.js","/_astro/InputText.DOJMNEP-.js","/_astro/LandingPage.B5q2aWge.js","/_astro/login.HFjs-wlr.css","/_astro/LoginForm.Cg3jsUJr.js","/_astro/logout.astro_astro_type_script_index_0_lang.OUY384DT.js","/_astro/MatchingDetailsGrid.BT5Doi8q.js","/_astro/MatchingLoadingState.BeWu0CtS.js","/_astro/menu.BH1dgvL-.js","/_astro/Menu.D9nHHLIB.js","/_astro/Message.BY1UiDHQ.js","/_astro/MultiSelect.B9h41NVw.js","/_astro/Navbar.C6TbBz7C.js","/_astro/PartsTable.C7lmpLtG.js","/_astro/PartWizard.CdN9gAyB.js","/_astro/PartWizard.lhc8Ogic.js","/_astro/Password.CGsZf6xB.js","/_astro/plus.CiWMw0wk.js","/_astro/ProposalsList.DrB_S_DP.js","/_astro/QuickCreateBrand.C_-iK7cE.js","/_astro/reactivity.esm-bundler.BQ12LWmY.js","/_astro/RegisterForm.C9G5LmfT.js","/_astro/router.DKcY2uv6.js","/_astro/runtime-core.esm-bundler.CRb7Pg8a.js","/_astro/runtime-dom.esm-bundler.DXo4nCak.js","/_astro/search.D5kgI6Rp.css","/_astro/search.DFOrFhbU.js","/_astro/SearchPageBoundary.BxJyvW8y.js","/_astro/SecondaryButton.DkELYl7Q.js","/_astro/Select.CQBzSu6y.js","/_astro/Spinner.nnVZHfjN.js","/_astro/stats.CRRiGBAu.css","/_astro/StatsPageBoundary.B3cm1GeF.js","/_astro/Tag.DTFTku6q.js","/_astro/Textarea.BLEHJ3ym.js","/_astro/ThemeToggle.BiFs6AFw.js","/_astro/Toast.DmmKUJB6.js","/_astro/ToggleSwitch.9ueDJKWv.js","/_astro/trash.4HbnIIsp.js","/_astro/triangle-alert.CP-lXbmj.js","/_astro/trpc.BpyaUO08.js","/_astro/types.C07aSKae.js","/_astro/types.FgRm47Sn.js","/_astro/UIDemo.C6Uv6ZNy.js","/_astro/useAuth.D4HmQrUw.js","/_astro/useErrorHandler.DVDazL16.js","/_astro/UsersManager.B7ceHodj.js","/_astro/useTheme.DwNY0gjL.js","/_astro/useToast.pIbuf2bs.js","/_astro/useTrpc.spLZjt2f.js","/_astro/useUrlParams.D0jSWJSf.js","/_astro/utils.BL5HZsed.js","/_astro/utils.BUKUcbtE.js","/_astro/utils.is9Ib0FR.js","/_astro/_plugin-vue_export-helper.DlAUqK2U.js"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"2W92npfwvX3pvsRcfF9eVcVr1Vz5w7l1a/6C7Eq8Ryo=","sessionConfig":{"driver":"fs-lite","options":{"base":"D:\\Dev\\PARTTEC\\parttec3\\frontend\\node_modules\\.astro\\sessions"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/fs-lite_COtHaKzy.mjs');

export { manifest };

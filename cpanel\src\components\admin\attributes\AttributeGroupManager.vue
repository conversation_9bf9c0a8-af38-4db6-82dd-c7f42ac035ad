<template>
  <div class="attribute-group-manager">
    <!-- Индикатор загрузки -->
    <div v-if="loading" class="text-center py-12">
      <Icon name="pi pi-spinner pi-spin" class="inline-block text-4xl text-primary mb-4" />
      <p class="text-surface-600 dark:text-surface-400">
        Загрузка групп атрибутов...
      </p>
    </div>

    <!-- Основной контент -->
    <div v-else>
      <!-- Заголовок и действия -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2
            class="text-xl font-semibold text-surface-900 dark:text-surface-0"
          >
            Группы атрибутов
          </h2>
          <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
            Управление группами для организации шаблонов атрибутов
          </p>
        </div>

        <div class="flex gap-3">
          <VButton
            @click="refreshData"
            :disabled="loading"
            severity="secondary"
            outlined
            label="Обновить"
          >
            <template #icon>
              <Icon name="pi pi-refresh" class="w-5 h-5" />
            </template>
          </VButton>

          <VButton
            @click="showCreateDialog = true"
            label="Создать группу"
          >
            <template #icon>
              <Icon name="pi pi-plus" class="w-5 h-5" />
            </template>
          </VButton>
        </div>
      </div>

      <!-- Поиск -->
      <VCard class="mb-6">
        <template #content>
          <div class="p-4">
            <div class="flex items-center gap-4">
              <div class="flex-1">
                <label
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Поиск групп
                </label>
                <VInputText
                  v-model="searchQuery"
                  placeholder="Поиск по названию или описанию..."
                  class="w-full"
                  @input="debouncedSearch"
                />
              </div>
              <div class="flex items-end">
                <VButton
                  @click="clearSearch"
                  severity="secondary"
                  outlined
                  size="small"
                  :disabled="!searchQuery"
                >
                  <template #icon>
                    <Icon name="pi pi-times" class="w-5 h-5" />
                  </template>
                  Очистить
                </VButton>
              </div>
            </div>
          </div>
        </template>
      </VCard>

      <!-- Список групп -->
      <div v-if="filteredGroups.length > 0" class="grid gap-4">
        <VCard
          v-for="group in filteredGroups"
          :key="group.id"
          class="border border-surface-200 dark:border-surface-700 hover:border-primary transition-colors"
        >
          <template #content>
            <div class="p-6">
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <Icon name="pi pi-folder" class="text-blue-600 w-5 h-5" />
                    <h3
                      class="text-lg font-semibold text-surface-900 dark:text-surface-0"
                    >
                      {{ group.name }}
                    </h3>
                    <VTag
                      :value="`${group._count?.templates || 0} шаблонов`"
                      severity="secondary"
                    />
                  </div>
                  <p
                    v-if="group.description"
                    class="text-surface-600 dark:text-surface-400 mb-3"
                  >
                    {{ group.description }}
                  </p>
                  <div class="text-sm text-surface-500 dark:text-surface-400">
                    Создана: {{ formatDate(group.createdAt) }}
                    <span
                      v-if="
                        group.updatedAt && group.updatedAt !== group.createdAt
                      "
                      class="ml-4"
                    >
                      Обновлена: {{ formatDate(group.updatedAt) }}
                    </span>
                  </div>
                </div>

                <div class="flex gap-2 ml-4">
                  <VButton
                    @click="editGroup(group)"
                    severity="secondary"
                    outlined
                    size="small"
                    label="Редактировать группу"
                  >
                    <template #icon>
                      <Icon name="pi pi-pencil" class="w-5 h-5" />
                    </template>
                  </VButton>
                  <VButton
                    @click="deleteGroup(group)"
                    severity="danger"
                    outlined
                    size="small"
                    label="Удалить группу"
                    :disabled="(group._count?.templates || 0) > 0"
                  >
                    <template #icon>
                      <Icon name="pi pi-trash" class="w-5 h-5" />
                    </template>
                  </VButton>
                </div>
              </div>

              <!-- Предварительный просмотр шаблонов -->
              <div
                v-if="group._count?.templates && group._count.templates > 0"
                class="border-t border-surface-200 dark:border-surface-700 pt-4"
              >
                <div class="flex items-center justify-between mb-3">
                  <span
                    class="text-sm font-medium text-surface-700 dark:text-surface-300"
                  >
                    Шаблоны в группе:
                  </span>
                  <VButton
                    :label="
                      expandedGroups.has(group.id) ? 'Скрыть' : 'Показать'
                    "
                    @click="toggleGroupTemplates(group.id)"
                    severity="secondary"
                    text
                    size="small"
                  >
                    <template #icon>
                      <Icon :name="expandedGroups.has(group.id) ? 'pi pi-chevron-up' : 'pi pi-chevron-down'" class="w-5 h-5" />
                    </template>
                  </VButton>
                </div>

                <div v-if="expandedGroups.has(group.id)" class="space-y-2">
                  <div v-if="groupTemplates[group.id]" class="grid gap-2">
                    <div
                      v-for="template in groupTemplates[group.id]"
                      :key="template.id"
                      class="flex items-center justify-between p-3 bg-surface-50 dark:bg-surface-900 rounded-lg"
                    >
                      <div class="flex items-center gap-3">
                        <Icon name="pi pi-tag" class="text-green-600 w-4 h-4" />
                        <div>
                          <div
                            class="font-medium text-surface-900 dark:text-surface-0"
                          >
                            {{ template.title }}
                          </div>
                          <div
                            class="text-sm text-surface-600 dark:text-surface-400"
                          >
                            {{ template.name }} •
                            {{ getDataTypeLabel(template.dataType) }}
                          </div>
                        </div>
                      </div>
                      <div class="flex gap-1">
                        <VTag
                          :value="getDataTypeLabel(template.dataType)"
                          severity="info"
                          size="small"
                        />
                        <VTag
                          v-if="template.unit"
                          :value="getUnitLabel(template.unit)"
                          severity="success"
                          size="small"
                        />
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-center py-4">
                    <VButton
                      @click="loadGroupTemplates(group.id)"
                      :loading="loadingTemplates[group.id]"
                      severity="secondary"
                      outlined
                      size="small"
                    >
                      Загрузить шаблоны
                    </VButton>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VCard>
      </div>

      <!-- Сообщение, если нет групп -->
      <div v-else-if="!loading" class="text-center py-12">
        <i
          class="pi pi-folder-open text-6xl text-surface-400 dark:text-surface-600 mb-4"
        ></i>
        <h3
          class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2"
        >
          {{ searchQuery ? "Группы не найдены" : "Нет групп атрибутов" }}
        </h3>
        <p class="text-surface-600 dark:text-surface-400 mb-4">
          {{
            searchQuery
              ? "Попробуйте изменить критерии поиска"
              : "Создайте первую группу для организации шаблонов атрибутов"
          }}
        </p>
        <VButton
          v-if="!searchQuery"
          @click="showCreateDialog = true"
          label="Создать группу"
        >
          <template #icon>
            <Icon name="pi pi-plus" class="w-5 h-5" />
          </template>
        </VButton>
      </div>
    </div>

    <!-- Диалог создания/редактирования группы -->
    <VDialog
      v-model:visible="showCreateDialog"
      modal
      :header="editingGroup ? 'Редактировать группу' : 'Создать группу'"
      :style="{ width: '40rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
    >
      <div class="space-y-4">
        <div>
          <label
            class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
          >
            Название группы *
          </label>
          <VInputText
            v-model="groupForm.name"
            placeholder="Например: Размеры, Материалы, Технические характеристики"
            class="w-full"
            :class="{ 'p-invalid': errors.name }"
          />
          <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
        </div>

        <div>
          <label
            class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
          >
            Описание
          </label>
          <VTextarea
            v-model="groupForm.description"
            placeholder="Подробное описание группы атрибутов..."
            rows="3"
            class="w-full"
          />
        </div>
      </div>

      <template #footer>
        <VButton
          label="Отмена"
          severity="secondary"
          @click="closeCreateDialog"
        />
        <VButton
          :label="editingGroup ? 'Обновить' : 'Создать'"
          @click="saveGroup"
          :loading="saving"
          :disabled="!isFormValid"
        />
      </template>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useTrpc } from "@/composables/useTrpc";

// Импорт компонентов
import VCard from "@/volt/Card.vue";
import VButton from "@/volt/Button.vue";
import VInputText from "@/volt/InputText.vue";
import VTextarea from "@/volt/Textarea.vue";
import VTag from "@/volt/Tag.vue";
import VDialog from "@/volt/Dialog.vue";
import Icon from "@/components/ui/Icon.vue";

// Composables
const { attributeTemplates, loading } = useTrpc();

// Состояние компонента
const groups = ref<any[]>([]);
const searchQuery = ref("");
const expandedGroups = ref(new Set<number>());
const groupTemplates = ref<Record<number, any[]>>({});
const loadingTemplates = ref<Record<number, boolean>>({});

// Диалоги
const showCreateDialog = ref(false);
const editingGroup = ref<any>(null);
const groupForm = ref({ name: "", description: "" });
const errors = ref<Record<string, string>>({});
const saving = ref(false);

// Вычисляемые свойства
const filteredGroups = computed(() => {
  if (!searchQuery.value) return groups.value;

  const query = searchQuery.value.toLowerCase();
  return groups.value.filter(
    (group) =>
      group.name.toLowerCase().includes(query) ||
      (group.description && group.description.toLowerCase().includes(query))
  );
});

const isFormValid = computed(() => {
  return groupForm.value.name.trim() && !Object.keys(errors.value).length;
});

// Основные функции
const loadGroups = async () => {
  try {
    const result = await attributeTemplates.findAllGroups();
    if (result) {
      groups.value = result;
    }
  } catch (error) {
    console.error("Ошибка загрузки групп:", error);
  }
};

const loadGroupTemplates = async (groupId: number) => {
  try {
    loadingTemplates.value[groupId] = true;
    const result = await attributeTemplates.findMany({
      groupId: groupId,
      limit: 100,
    });

    if (result?.templates) {
      groupTemplates.value[groupId] = result.templates;
    }
  } catch (error) {
    console.error("Ошибка загрузки шаблонов группы:", error);
  } finally {
    loadingTemplates.value[groupId] = false;
  }
};

const toggleGroupTemplates = (groupId: number) => {
  if (expandedGroups.value.has(groupId)) {
    expandedGroups.value.delete(groupId);
  } else {
    expandedGroups.value.add(groupId);
    // Загружаем шаблоны если они еще не загружены
    if (!groupTemplates.value[groupId]) {
      loadGroupTemplates(groupId);
    }
  }
};

const refreshData = () => {
  loadGroups();
  // Очищаем кэш шаблонов
  groupTemplates.value = {};
  expandedGroups.value.clear();
};

// Debounced search
let searchTimeout: NodeJS.Timeout;
const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // Поиск выполняется автоматически через computed свойство
  }, 300);
};

const clearSearch = () => {
  searchQuery.value = "";
};

// Управление группами
const editGroup = (group: any) => {
  editingGroup.value = group;
  groupForm.value = {
    name: group.name,
    description: group.description || "",
  };
  showCreateDialog.value = true;
};

const deleteGroup = async (group: any) => {
  const confirmed = window.confirm(
    `Вы уверены, что хотите удалить группу "${group.name}"?`
  );

  if (confirmed) {
    try {
      await attributeTemplates.deleteGroup({ id: group.id });
      console.log("Группа успешно удалена");
      loadGroups();
    } catch (error: any) {
      console.error("Ошибка удаления группы:", error);
      alert(error.message || "Не удалось удалить группу");
    }
  }
};

const validateForm = () => {
  errors.value = {};

  if (!groupForm.value.name.trim()) {
    errors.value.name = "Название группы обязательно";
  }
};

const saveGroup = async () => {
  validateForm();

  if (!isFormValid.value) return;

  try {
    saving.value = true;

    if (editingGroup.value) {
      await attributeTemplates.updateGroup({
        id: editingGroup.value.id,
        name: groupForm.value.name.trim(),
        description: groupForm.value.description || undefined,
      });
      console.log("Группа успешно обновлена");
    } else {
      await attributeTemplates.createGroup({
        name: groupForm.value.name.trim(),
        description: groupForm.value.description || undefined,
      });
      console.log("Группа успешно создана");
    }

    closeCreateDialog();
    loadGroups();
  } catch (error: any) {
    console.error("Ошибка сохранения группы:", error);
    alert(error.message || "Не удалось сохранить группу");
  } finally {
    saving.value = false;
  }
};

const closeCreateDialog = () => {
  showCreateDialog.value = false;
  editingGroup.value = null;
  groupForm.value = { name: "", description: "" };
  errors.value = {};
};

// Вспомогательные функции
const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString("ru-RU", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return dateString;
  }
};

const getDataTypeLabel = (dataType: string) => {
  const labels: Record<string, string> = {
    STRING: "Строка",
    NUMBER: "Число",
    BOOLEAN: "Логическое",
    DATE: "Дата",
    JSON: "JSON",
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    MM: "мм",
    INCH: "дюймы",
    FT: "футы",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунты",
    ML: "мл",
    L: "л",
    GAL: "галлоны",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "PSI",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%",
  };
  return labels[unit] || unit;
};

// Инициализация
onMounted(() => {
  loadGroups();
});
</script>

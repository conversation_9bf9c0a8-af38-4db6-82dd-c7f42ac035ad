import { e as createComponent, f as createAstro, r as renderTemplate, k as renderComponent, n as renderSlot, o as renderHead } from './astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { G as GlobalErrorHandlerInit, $ as $$ClientRouter } from './ClientRouter_avhRMbqw.mjs';
/* empty css                           */

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$Astro = createAstro();
const $$Layout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Layout;
  const { title } = Astro2.props;
  return renderTemplate(_a || (_a = __template(['<html lang="ru" class="h-full" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description" content="PartTec - \u0441\u0438\u0441\u0442\u0435\u043C\u0430 \u0443\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u043E\u043C \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg">', "<title>", '</title><!-- \u0421\u043A\u0440\u0438\u043F\u0442 \u0434\u043B\u044F \u043F\u0440\u0435\u0434\u043E\u0442\u0432\u0440\u0430\u0449\u0435\u043D\u0438\u044F \u043C\u0438\u0433\u0430\u043D\u0438\u044F \u0442\u0435\u043C\u044B --><script src="/theme-init.js"><\/script>', '</head> <body class="h-full" data-astro-cid-sckkx6r4> ', " ", " </body></html>"])), renderComponent($$result, "ClientRouter", $$ClientRouter, { "data-astro-cid-sckkx6r4": true }), title, renderHead(), renderSlot($$result, $$slots["default"]), renderComponent($$result, "GlobalErrorHandlerInit", GlobalErrorHandlerInit, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/system/GlobalErrorHandlerInit.vue", "client:component-export": "default", "data-astro-cid-sckkx6r4": true }));
}, "D:/Dev/PARTTEC/parttec3/frontend/src/layouts/Layout.astro", void 0);

export { $$Layout as $ };

import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { I as Icon, n as navigate, $ as $$AdminLayout } from '../chunks/AdminLayout_DrlBSzRq.mjs';
import { C as Card } from '../chunks/Card_aE2_b9LT.mjs';
import { B as Button } from '../chunks/Button_0V33JvkC.mjs';
import { defineComponent, useSSRContext, ref, onMounted, mergeProps, withCtx, createVNode, toDisplayString, createBlock, openBlock, Fragment, renderList } from 'vue';
import { t as trpc } from '../chunks/trpc_DApR3DD7.mjs';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent, ssrRenderList, ssrRenderStyle } from 'vue/server-renderer';
import { _ as _export_sfc } from '../chunks/ClientRouter_avhRMbqw.mjs';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const rangeDays = 30;
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "AdminDashboard",
  setup(__props, { expose: __expose }) {
    __expose();
    const loading = ref(true);
    const error = ref(null);
    const stats = ref(null);
    onMounted(async () => {
      try {
        const res = await trpc.admin.getDashboard.query({ rangeDays });
        stats.value = res;
      } catch (e) {
        error.value = e?.message ?? "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0434\u0430\u0448\u0431\u043E\u0440\u0434";
      } finally {
        loading.value = false;
      }
    });
    function sumSeries(series) {
      return series.reduce((acc, p) => acc + p.count, 0);
    }
    function last7(series) {
      return series.slice(-7);
    }
    function maxInSeries(series) {
      return series.reduce((m, p) => Math.max(m, p.count), 0) || 1;
    }
    function barHeight(value, series) {
      const max = maxInSeries(series);
      return Math.round(value / max * 100);
    }
    function shortDate(iso) {
      const d = new Date(iso);
      return `${d.getDate().toString().padStart(2, "0")}.${(d.getMonth() + 1).toString().padStart(2, "0")}`;
    }
    function formatDate(iso) {
      const d = new Date(iso);
      return d.toLocaleString();
    }
    function navigateTo(path) {
      navigate();
    }
    const __returned__ = { loading, error, stats, rangeDays, sumSeries, last7, maxInSeries, barHeight, shortDate, formatDate, navigateTo, Card, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-8" }, _attrs))}><div><h1 class="text-2xl font-bold text-surface-900">\u0414\u0430\u0448\u0431\u043E\u0440\u0434</h1><p class="mt-1 text-sm text-surface-600"> \u041E\u0431\u0437\u043E\u0440 \u0441\u0438\u0441\u0442\u0435\u043C\u044B \u0443\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u043E\u043C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 </p></div>`);
  if ($setup.loading) {
    _push(`<div class="text-sm text-surface-500">\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...</div>`);
  } else if ($setup.error) {
    _push(`<div class="text-sm text-red-600">${ssrInterpolate($setup.error)}</div>`);
  } else {
    _push(`<!--[--><div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">`);
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex-shrink-0"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-users",
            class: "text-2xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="ml-5 w-0 flex-1"${_scopeId}><dl${_scopeId}><dt class="text-sm font-medium text-surface-500 truncate"${_scopeId}>\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438</dt><dd class="text-lg font-medium text-surface-900"${_scopeId}>${ssrInterpolate($setup.stats?.totals.users.toLocaleString())}</dd></dl></div></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438",
              onClick: ($event) => $setup.navigateTo("/admin/users"),
              class: "flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex-shrink-0" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-users",
                  class: "text-2xl text-primary-600"
                })
              ]),
              createVNode("div", { class: "ml-5 w-0 flex-1" }, [
                createVNode("dl", null, [
                  createVNode("dt", { class: "text-sm font-medium text-surface-500 truncate" }, "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438"),
                  createVNode("dd", { class: "text-lg font-medium text-surface-900" }, toDisplayString($setup.stats?.totals.users.toLocaleString()), 1)
                ])
              ])
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u041C\u0430\u0433\u0430\u0437\u0438\u043D\u044B" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex-shrink-0"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-building",
            class: "text-2xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="ml-5 w-0 flex-1"${_scopeId}><dl${_scopeId}><dt class="text-sm font-medium text-surface-500 truncate"${_scopeId}>\u041C\u0430\u0433\u0430\u0437\u0438\u043D\u044B</dt><dd class="text-lg font-medium text-surface-900"${_scopeId}>${ssrInterpolate($setup.stats?.totals.shops.toLocaleString())}</dd></dl></div></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u041C\u0430\u0433\u0430\u0437\u0438\u043D\u044B",
              onClick: ($event) => $setup.navigateTo("/admin/users"),
              class: "flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex-shrink-0" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-building",
                  class: "text-2xl text-primary-600"
                })
              ]),
              createVNode("div", { class: "ml-5 w-0 flex-1" }, [
                createVNode("dl", null, [
                  createVNode("dt", { class: "text-sm font-medium text-surface-500 truncate" }, "\u041C\u0430\u0433\u0430\u0437\u0438\u043D\u044B"),
                  createVNode("dd", { class: "text-lg font-medium text-surface-900" }, toDisplayString($setup.stats?.totals.shops.toLocaleString()), 1)
                ])
              ])
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex-shrink-0"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-box",
            class: "text-2xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="ml-5 w-0 flex-1"${_scopeId}><dl${_scopeId}><dt class="text-sm font-medium text-surface-500 truncate"${_scopeId}>\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438</dt><dd class="text-lg font-medium text-surface-900"${_scopeId}>${ssrInterpolate($setup.stats?.totals.parts.toLocaleString())}</dd></dl></div></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438",
              onClick: ($event) => $setup.navigateTo("/admin/parts"),
              class: "flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex-shrink-0" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-box",
                  class: "text-2xl text-primary-600"
                })
              ]),
              createVNode("div", { class: "ml-5 w-0 flex-1" }, [
                createVNode("dl", null, [
                  createVNode("dt", { class: "text-sm font-medium text-surface-500 truncate" }, "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438"),
                  createVNode("dd", { class: "text-lg font-medium text-surface-900" }, toDisplayString($setup.stats?.totals.parts.toLocaleString()), 1)
                ])
              ])
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u0410\u043A\u0442\u0438\u0432\u043D\u044B\u0435 \u0441\u0435\u0441\u0441\u0438\u0438" class="flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex-shrink-0"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-bolt",
            class: "text-2xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="ml-5 w-0 flex-1"${_scopeId}><dl${_scopeId}><dt class="text-sm font-medium text-surface-500 truncate"${_scopeId}>\u0410\u043A\u0442\u0438\u0432\u043D\u044B\u0435 \u0441\u0435\u0441\u0441\u0438\u0438</dt><dd class="text-lg font-medium text-surface-900"${_scopeId}>${ssrInterpolate($setup.stats?.totals.sessionsActive.toLocaleString())}</dd></dl></div></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u0410\u043A\u0442\u0438\u0432\u043D\u044B\u0435 \u0441\u0435\u0441\u0441\u0438\u0438",
              onClick: ($event) => $setup.navigateTo("/admin/access-control"),
              class: "flex items-center w-full text-left hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex-shrink-0" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-bolt",
                  class: "text-2xl text-primary-600"
                })
              ]),
              createVNode("div", { class: "ml-5 w-0 flex-1" }, [
                createVNode("dl", null, [
                  createVNode("dt", { class: "text-sm font-medium text-surface-500 truncate" }, "\u0410\u043A\u0442\u0438\u0432\u043D\u044B\u0435 \u0441\u0435\u0441\u0441\u0438\u0438"),
                  createVNode("dd", { class: "text-lg font-medium text-surface-900" }, toDisplayString($setup.stats?.totals.sessionsActive.toLocaleString()), 1)
                ])
              ])
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">`);
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u0411\u0440\u0435\u043D\u0434\u044B" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex items-center"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-tags",
            class: "text-xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`<span class="ml-3 text-sm text-surface-600"${_scopeId}>\u0411\u0440\u0435\u043D\u0434\u044B</span></div><span class="text-base font-semibold"${_scopeId}>${ssrInterpolate($setup.stats?.totals.brands.toLocaleString())}</span></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u0411\u0440\u0435\u043D\u0434\u044B",
              onClick: ($event) => $setup.navigateTo("/admin/brands"),
              class: "flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex items-center" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-tags",
                  class: "text-xl text-primary-600"
                }),
                createVNode("span", { class: "ml-3 text-sm text-surface-600" }, "\u0411\u0440\u0435\u043D\u0434\u044B")
              ]),
              createVNode("span", { class: "text-base font-semibold" }, toDisplayString($setup.stats?.totals.brands.toLocaleString()), 1)
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex items-center"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-sitemap",
            class: "text-xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`<span class="ml-3 text-sm text-surface-600"${_scopeId}>\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438</span></div><span class="text-base font-semibold"${_scopeId}>${ssrInterpolate($setup.stats?.totals.partCategories.toLocaleString())}</span></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
              onClick: ($event) => $setup.navigateTo("/admin/categories"),
              class: "flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex items-center" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-sitemap",
                  class: "text-xl text-primary-600"
                }),
                createVNode("span", { class: "ml-3 text-sm text-surface-600" }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438")
              ]),
              createVNode("span", { class: "text-base font-semibold" }, toDisplayString($setup.stats?.totals.partCategories.toLocaleString()), 1)
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u041A\u0430\u0442\u0430\u043B\u043E\u0433" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex items-center"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-list",
            class: "text-xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`<span class="ml-3 text-sm text-surface-600"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0433</span></div><span class="text-base font-semibold"${_scopeId}>${ssrInterpolate($setup.stats?.totals.catalogItems.toLocaleString())}</span></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u041A\u0430\u0442\u0430\u043B\u043E\u0433",
              onClick: ($event) => $setup.navigateTo("/admin/catalogitems"),
              class: "flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex items-center" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-list",
                  class: "text-xl text-primary-600"
                }),
                createVNode("span", { class: "ml-3 text-sm text-surface-600" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0433")
              ]),
              createVNode("span", { class: "text-base font-semibold" }, toDisplayString($setup.stats?.totals.catalogItems.toLocaleString()), 1)
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<button type="button" aria-label="\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F" class="flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"${_scopeId}><div class="flex items-center"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-inbox",
            class: "text-xl text-primary-600"
          }, null, _parent2, _scopeId));
          _push2(`<span class="ml-3 text-sm text-surface-600"${_scopeId}>\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F (\u043E\u0436\u0438\u0434\u0430\u044E\u0442)</span></div><span class="text-base font-semibold"${_scopeId}>${ssrInterpolate($setup.stats?.totals.proposals.pending.toLocaleString())}</span></button>`);
        } else {
          return [
            createVNode("button", {
              type: "button",
              "aria-label": "\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F",
              onClick: ($event) => $setup.navigateTo("/admin/proposals"),
              class: "flex items-center justify-between w-full hover:bg-surface-50 dark:hover:bg-surface-800/60 p-2 rounded-md transition-colors cursor-pointer"
            }, [
              createVNode("div", { class: "flex items-center" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-inbox",
                  class: "text-xl text-primary-600"
                }),
                createVNode("span", { class: "ml-3 text-sm text-surface-600" }, "\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F (\u043E\u0436\u0438\u0434\u0430\u044E\u0442)")
              ]),
              createVNode("span", { class: "text-base font-semibold" }, toDisplayString($setup.stats?.totals.proposals.pending.toLocaleString()), 1)
            ], 8, ["onClick"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div><div class="grid grid-cols-1 lg:grid-cols-3 gap-8">`);
    _push(ssrRenderComponent($setup["Card"], { class: "lg:col-span-2" }, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<h3 class="text-lg leading-6 font-medium text-surface-900"${_scopeId}>\u0410\u043A\u0442\u0438\u0432\u043D\u043E\u0441\u0442\u044C \u0437\u0430 ${ssrInterpolate($setup.rangeDays)} \u0434\u043D\u0435\u0439</h3>`);
        } else {
          return [
            createVNode("h3", { class: "text-lg leading-6 font-medium text-surface-900" }, "\u0410\u043A\u0442\u0438\u0432\u043D\u043E\u0441\u0442\u044C \u0437\u0430 " + toDisplayString($setup.rangeDays) + " \u0434\u043D\u0435\u0439")
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="grid grid-cols-1 sm:grid-cols-3 gap-4"${_scopeId}><div class="p-3 rounded-lg bg-surface-50 dark:bg-surface-800"${_scopeId}><div class="text-xs text-surface-500 mb-1"${_scopeId}>\u041D\u043E\u0432\u044B\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438</div><div class="text-xl font-semibold"${_scopeId}>${ssrInterpolate($setup.sumSeries($setup.stats.trends.usersDaily).toLocaleString())}</div></div><div class="p-3 rounded-lg bg-surface-50 dark:bg-surface-800"${_scopeId}><div class="text-xs text-surface-500 mb-1"${_scopeId}>\u041D\u043E\u0432\u044B\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438</div><div class="text-xl font-semibold"${_scopeId}>${ssrInterpolate($setup.sumSeries($setup.stats.trends.partsDaily).toLocaleString())}</div></div><div class="p-3 rounded-lg bg-surface-50 dark:bg-surface-800"${_scopeId}><div class="text-xs text-surface-500 mb-1"${_scopeId}>\u041D\u043E\u0432\u044B\u0435 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F</div><div class="text-xl font-semibold"${_scopeId}>${ssrInterpolate($setup.sumSeries($setup.stats.trends.proposalsDaily).toLocaleString())}</div></div></div><div class="mt-4 grid grid-cols-7 gap-2 text-xs text-surface-500"${_scopeId}><!--[-->`);
          ssrRenderList($setup.last7($setup.stats.trends.usersDaily), (d) => {
            _push2(`<div class="flex flex-col items-center"${_scopeId}><div class="h-10 w-8 flex items-end"${_scopeId}><div class="w-full bg-primary-500/20" style="${ssrRenderStyle({ height: `${$setup.barHeight(d.count, $setup.stats.trends.usersDaily)}%` })}"${_scopeId}></div></div><div class="mt-1"${_scopeId}>${ssrInterpolate($setup.shortDate(d.date))}</div></div>`);
          });
          _push2(`<!--]--></div>`);
        } else {
          return [
            createVNode("div", { class: "grid grid-cols-1 sm:grid-cols-3 gap-4" }, [
              createVNode("div", { class: "p-3 rounded-lg bg-surface-50 dark:bg-surface-800" }, [
                createVNode("div", { class: "text-xs text-surface-500 mb-1" }, "\u041D\u043E\u0432\u044B\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u0438"),
                createVNode("div", { class: "text-xl font-semibold" }, toDisplayString($setup.sumSeries($setup.stats.trends.usersDaily).toLocaleString()), 1)
              ]),
              createVNode("div", { class: "p-3 rounded-lg bg-surface-50 dark:bg-surface-800" }, [
                createVNode("div", { class: "text-xs text-surface-500 mb-1" }, "\u041D\u043E\u0432\u044B\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438"),
                createVNode("div", { class: "text-xl font-semibold" }, toDisplayString($setup.sumSeries($setup.stats.trends.partsDaily).toLocaleString()), 1)
              ]),
              createVNode("div", { class: "p-3 rounded-lg bg-surface-50 dark:bg-surface-800" }, [
                createVNode("div", { class: "text-xs text-surface-500 mb-1" }, "\u041D\u043E\u0432\u044B\u0435 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F"),
                createVNode("div", { class: "text-xl font-semibold" }, toDisplayString($setup.sumSeries($setup.stats.trends.proposalsDaily).toLocaleString()), 1)
              ])
            ]),
            createVNode("div", { class: "mt-4 grid grid-cols-7 gap-2 text-xs text-surface-500" }, [
              (openBlock(true), createBlock(Fragment, null, renderList($setup.last7($setup.stats.trends.usersDaily), (d) => {
                return openBlock(), createBlock("div", {
                  key: d.date,
                  class: "flex flex-col items-center"
                }, [
                  createVNode("div", { class: "h-10 w-8 flex items-end" }, [
                    createVNode("div", {
                      class: "w-full bg-primary-500/20",
                      style: { height: `${$setup.barHeight(d.count, $setup.stats.trends.usersDaily)}%` }
                    }, null, 4)
                  ]),
                  createVNode("div", { class: "mt-1" }, toDisplayString($setup.shortDate(d.date)), 1)
                ]);
              }), 128))
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<h3 class="text-lg leading-6 font-medium text-surface-900"${_scopeId}>\u041F\u043E\u043F\u0443\u043B\u044F\u0440\u043D\u044B\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438</h3>`);
        } else {
          return [
            createVNode("h3", { class: "text-lg leading-6 font-medium text-surface-900" }, "\u041F\u043E\u043F\u0443\u043B\u044F\u0440\u043D\u044B\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438")
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="space-y-3"${_scopeId}><!--[-->`);
          ssrRenderList($setup.stats?.top.categories, (category) => {
            _push2(`<div class="flex justify-between items-center"${_scopeId}><span class="text-sm font-medium text-surface-900"${_scopeId}>${ssrInterpolate(category.name)}</span><span class="text-sm text-surface-500"${_scopeId}>${ssrInterpolate(category.count.toLocaleString())}</span></div>`);
          });
          _push2(`<!--]--></div>`);
        } else {
          return [
            createVNode("div", { class: "space-y-3" }, [
              (openBlock(true), createBlock(Fragment, null, renderList($setup.stats?.top.categories, (category) => {
                return openBlock(), createBlock("div", {
                  key: category.id,
                  class: "flex justify-between items-center"
                }, [
                  createVNode("span", { class: "text-sm font-medium text-surface-900" }, toDisplayString(category.name), 1),
                  createVNode("span", { class: "text-sm text-surface-500" }, toDisplayString(category.count.toLocaleString()), 1)
                ]);
              }), 128))
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div><div class="grid grid-cols-1 lg:grid-cols-2 gap-8">`);
    _push(ssrRenderComponent($setup["Card"], null, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<h3 class="text-lg leading-6 font-medium text-surface-900"${_scopeId}>\u041F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044F (\u0430\u0443\u0434\u0438\u0442)</h3>`);
        } else {
          return [
            createVNode("h3", { class: "text-lg leading-6 font-medium text-surface-900" }, "\u041F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044F (\u0430\u0443\u0434\u0438\u0442)")
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          if (($setup.stats?.recent.audit?.length || 0) === 0) {
            _push2(`<div class="text-sm text-surface-500"${_scopeId}>\u041D\u0435\u0442 \u0437\u0430\u043F\u0438\u0441\u0435\u0439</div>`);
          } else {
            _push2(`<div class="space-y-3"${_scopeId}><!--[-->`);
            ssrRenderList($setup.stats.recent.audit, (log) => {
              _push2(`<div class="flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-history",
                class: "text-surface-500 mr-3"
              }, null, _parent2, _scopeId));
              _push2(`<div class="flex-1"${_scopeId}><div class="text-sm text-surface-900"${_scopeId}>${ssrInterpolate(log.action)}</div><div class="text-xs text-surface-500"${_scopeId}>${ssrInterpolate($setup.formatDate(log.createdAt))} \u2022 ${ssrInterpolate(log.adminEmail)}</div></div></div>`);
            });
            _push2(`<!--]--></div>`);
          }
        } else {
          return [
            ($setup.stats?.recent.audit?.length || 0) === 0 ? (openBlock(), createBlock("div", {
              key: 0,
              class: "text-sm text-surface-500"
            }, "\u041D\u0435\u0442 \u0437\u0430\u043F\u0438\u0441\u0435\u0439")) : (openBlock(), createBlock("div", {
              key: 1,
              class: "space-y-3"
            }, [
              (openBlock(true), createBlock(Fragment, null, renderList($setup.stats.recent.audit, (log) => {
                return openBlock(), createBlock("div", {
                  key: log.id,
                  class: "flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg"
                }, [
                  createVNode($setup["Icon"], {
                    name: "pi pi-history",
                    class: "text-surface-500 mr-3"
                  }),
                  createVNode("div", { class: "flex-1" }, [
                    createVNode("div", { class: "text-sm text-surface-900" }, toDisplayString(log.action), 1),
                    createVNode("div", { class: "text-xs text-surface-500" }, toDisplayString($setup.formatDate(log.createdAt)) + " \u2022 " + toDisplayString(log.adminEmail), 1)
                  ])
                ]);
              }), 128))
            ]))
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["Card"], null, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<h3 class="text-lg leading-6 font-medium text-surface-900"${_scopeId}>\u041D\u043E\u0432\u044B\u0435 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u044D\u043A\u0432\u0438\u0432\u0430\u043B\u0435\u043D\u0442\u043E\u0432</h3>`);
        } else {
          return [
            createVNode("h3", { class: "text-lg leading-6 font-medium text-surface-900" }, "\u041D\u043E\u0432\u044B\u0435 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u044D\u043A\u0432\u0438\u0432\u0430\u043B\u0435\u043D\u0442\u043E\u0432")
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          if (($setup.stats?.recent.pendingProposals?.length || 0) === 0) {
            _push2(`<div class="text-sm text-surface-500"${_scopeId}>\u041D\u0435\u0442 \u043D\u043E\u0432\u044B\u0445 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439</div>`);
          } else {
            _push2(`<div class="space-y-3"${_scopeId}><!--[-->`);
            ssrRenderList($setup.stats.recent.pendingProposals, (p) => {
              _push2(`<div class="flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-link",
                class: "text-primary mr-3"
              }, null, _parent2, _scopeId));
              _push2(`<div class="flex-1"${_scopeId}><div class="text-sm text-surface-900"${_scopeId}>${ssrInterpolate(p.catalogItem?.brand?.name || "\u0411\u0440\u0435\u043D\u0434")} \u2022 ${ssrInterpolate(p.catalogItem?.sku)} \u2192 ${ssrInterpolate(p.part?.name || `Part #${p.part?.id}`)}</div><div class="text-xs text-surface-500"${_scopeId}>${ssrInterpolate(p.accuracySuggestion)} \u2022 ${ssrInterpolate($setup.formatDate(p.createdAt))}</div></div></div>`);
            });
            _push2(`<!--]--></div>`);
          }
        } else {
          return [
            ($setup.stats?.recent.pendingProposals?.length || 0) === 0 ? (openBlock(), createBlock("div", {
              key: 0,
              class: "text-sm text-surface-500"
            }, "\u041D\u0435\u0442 \u043D\u043E\u0432\u044B\u0445 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439")) : (openBlock(), createBlock("div", {
              key: 1,
              class: "space-y-3"
            }, [
              (openBlock(true), createBlock(Fragment, null, renderList($setup.stats.recent.pendingProposals, (p) => {
                return openBlock(), createBlock("div", {
                  key: p.id,
                  class: "flex items-center p-3 bg-surface-50 dark:bg-surface-800 rounded-lg"
                }, [
                  createVNode($setup["Icon"], {
                    name: "pi pi-link",
                    class: "text-primary mr-3"
                  }),
                  createVNode("div", { class: "flex-1" }, [
                    createVNode("div", { class: "text-sm text-surface-900" }, toDisplayString(p.catalogItem?.brand?.name || "\u0411\u0440\u0435\u043D\u0434") + " \u2022 " + toDisplayString(p.catalogItem?.sku) + " \u2192 " + toDisplayString(p.part?.name || `Part #${p.part?.id}`), 1),
                    createVNode("div", { class: "text-xs text-surface-500" }, toDisplayString(p.accuracySuggestion) + " \u2022 " + toDisplayString($setup.formatDate(p.createdAt)), 1)
                  ])
                ]);
              }), 128))
            ]))
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div><!--]-->`);
  }
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/AdminDashboard.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const AdminDashboard = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Index = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const user = Astro2.locals.user;
  const userRole = user?.role || "UNKNOWN";
  const userName = user?.name || user?.email || "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C";
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0414\u0430\u0448\u0431\u043E\u0440\u0434 - PartTec Admin" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="space-y-6"> <!-- Заголовок --> <div> <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
Добро пожаловать, ${userName}!
</h1> <p class="text-surface-600 dark:text-surface-400 mt-1">
Обзор системы управления каталогом запчастей
</p> <div class="mt-2 flex items-center space-x-2"> <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"> ${userRole === "ADMIN" ? "\u0410\u0434\u043C\u0438\u043D\u0438\u0441\u0442\u0440\u0430\u0442\u043E\u0440" : userRole === "SHOP" ? "\u041C\u0430\u0433\u0430\u0437\u0438\u043D" : userRole} </span> <span class="text-surface-500 text-sm">•</span> <span class="text-surface-500 text-sm">${user?.email}</span> </div> </div> <!-- Основной дашборд --> ${renderComponent($$result2, "AdminDashboard", AdminDashboard, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/admin/AdminDashboard.vue", "client:component-export": "default" })} <!-- Быстрые действия --> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> <!-- Создать запчасть --> ${renderComponent($$result2, "VCard", Card, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Card.vue", "client:component-export": "default" }, { "default": ($$result3) => renderTemplate` <template #content> <div class="p-6 text-center"> <div class="w-12 h-12 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-plus", "class": "text-primary w-5 h-5" })} </div> <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
Создать запчасть
</h3> <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
Добавить новую запчасть в каталог
</p> <a href="/admin/parts/create"> ${renderComponent($$result3, "VButton", Button, { "client:load": true, "class": "w-full", "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Button.vue", "client:component-export": "default" }, { "default": ($$result4) => renderTemplate`
Создать
` })} </a> </div> </template> ` })} <!-- Управление брендами --> ${renderComponent($$result2, "VCard", Card, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Card.vue", "client:component-export": "default" }, { "default": ($$result3) => renderTemplate` <template #content> <div class="p-6 text-center"> <div class="w-12 h-12 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-tags", "class": "text-blue-600 w-5 h-5" })} </div> <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
Бренды
</h3> <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
Управление брендами запчастей
</p> <a href="/admin/brands"> ${renderComponent($$result3, "VButton", Button, { "client:load": true, "severity": "secondary", "class": "w-full", "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Button.vue", "client:component-export": "default" }, { "default": ($$result4) => renderTemplate`
Управлять
` })} </a> </div> </template> ` })} <!-- Категории --> ${renderComponent($$result2, "VCard", Card, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Card.vue", "client:component-export": "default" }, { "default": ($$result3) => renderTemplate` <template #content> <div class="p-6 text-center"> <div class="w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-sitemap", "class": "text-green-600 w-5 h-5" })} </div> <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
Категории
</h3> <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
Структура категорий запчастей
</p> <a href="/admin/categories"> ${renderComponent($$result3, "VButton", Button, { "client:load": true, "severity": "secondary", "class": "w-full", "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Button.vue", "client:component-export": "default" }, { "default": ($$result4) => renderTemplate`
Управлять
` })} </a> </div> </template> ` })} <!-- Каталожные позиции --> ${renderComponent($$result2, "VCard", Card, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Card.vue", "client:component-export": "default" }, { "default": ($$result3) => renderTemplate` <template #content> <div class="p-6 text-center"> <div class="w-12 h-12 mx-auto mb-4 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-list", "class": "text-orange-600 w-5 h-5" })} </div> <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0 mb-2">
Каталог
</h3> <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
Каталожные позиции и артикулы
</p> <a href="/admin/catalog-items"> ${renderComponent($$result3, "VButton", Button, { "client:load": true, "severity": "secondary", "class": "w-full", "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Button.vue", "client:component-export": "default" }, { "default": ($$result4) => renderTemplate`
Просмотр
` })} </a> </div> </template> ` })} </div> <!-- Быстрые ссылки --> ${renderComponent($$result2, "VCard", Card, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/volt/Card.vue", "client:component-export": "default" }, { "default": ($$result3) => renderTemplate` <template #header> <div class="p-6 border-b border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
Быстрые ссылки
</h3> </div> </template> <template #content> <div class="p-6"> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> <a href="/admin/parts" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"> <div class="flex items-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-wrench", "class": "text-primary w-4 h-4 mr-3 inline-block" })} <span class="text-surface-900 dark:text-surface-0">Все запчасти</span> </div> </a> <a href="/admin/parts/create" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"> <div class="flex items-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-plus", "class": "text-primary w-4 h-4 mr-3 inline-block" })} <span class="text-surface-900 dark:text-surface-0">Создать запчасть</span> </div> </a> <a href="/admin/brands" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"> <div class="flex items-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-tags", "class": "text-primary w-4 h-4 mr-3 inline-block" })} <span class="text-surface-900 dark:text-surface-0">Управление брендами</span> </div> </a> <a href="/admin/categories" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"> <div class="flex items-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-sitemap", "class": "text-primary w-4 h-4 mr-3 inline-block" })} <span class="text-surface-900 dark:text-surface-0">Управление категориями</span> </div> </a> <a href="/admin/catalog-items" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"> <div class="flex items-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-list", "class": "text-primary w-4 h-4 mr-3 inline-block" })} <span class="text-surface-900 dark:text-surface-0">Каталожные позиции</span> </div> </a> <a href="/admin/test-trpc" class="block p-3 rounded-lg border border-surface-200 dark:border-surface-700 hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"> <div class="flex items-center"> ${renderComponent($$result3, "Icon", Icon, { "name": "pi pi-code", "class": "text-primary w-4 h-4 mr-3 inline-block" })} <span class="text-surface-900 dark:text-surface-0">Тест API</span> </div> </a> </div> </div> </template> ` })} </div> ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/index.astro";
const $$url = "/admin";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

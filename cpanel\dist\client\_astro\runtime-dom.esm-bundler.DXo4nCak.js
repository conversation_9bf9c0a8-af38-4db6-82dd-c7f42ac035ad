import{t as Ut,u as jt,v as zt,B as Wt,x as Tt,y as Xt,z as Jt,i as Zt,j as Qt,A as Yt,F as Et,S as kt,C as te,D as ee,E as ne,G as se,H as oe,I as k,J as tt,e as re}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{i as yt,l as et,h as j,e as nt,f as L,g as ie,j as P,k as H,m as ae,o as ce,p as le,q as wt,s as At,u as fe,v as ue,N as pe,w as de,x as me,y as he}from"./reactivity.esm-bundler.BQ12LWmY.js";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/let q;const st=typeof window<"u"&&window.trustedTypes;if(st)try{q=st.createPolicy("vue",{createHTML:t=>t})}catch{}const Nt=q?t=>q.createHTML(t):t=>t,ge="http://www.w3.org/2000/svg",ve="http://www.w3.org/1998/Math/MathML",g=typeof document<"u"?document:null,ot=g&&g.createElement("template"),Ce={insert:(t,e,n)=>{e.insertBefore(t,n||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,n,s)=>{const o=e==="svg"?g.createElementNS(ge,t):e==="mathml"?g.createElementNS(ve,t):n?g.createElement(t,{is:n}):g.createElement(t);return t==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:t=>g.createTextNode(t),createComment:t=>g.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>g.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,n,s,o,r){const i=n?n.previousSibling:e.lastChild;if(o&&(o===r||o.nextSibling))for(;e.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{ot.innerHTML=Nt(s==="svg"?`<svg>${t}</svg>`:s==="mathml"?`<math>${t}</math>`:t);const a=ot.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}e.insertBefore(a,n)}return[i?i.nextSibling:e.firstChild,n?n.previousSibling:e.lastChild]}},v="transition",N="animation",A=Symbol("_vtc"),Mt={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},_t=H({},Wt,Mt),be=t=>(t.displayName="Transition",t.props=_t,t),Ze=be((t,{slots:e})=>te(ee,Lt(t),e)),S=(t,e=[])=>{P(t)?t.forEach(n=>n(...e)):t&&t(...e)},rt=t=>t?P(t)?t.some(e=>e.length>1):t.length>1:!1;function Lt(t){const e={};for(const c in t)c in Mt||(e[c]=t[c]);if(t.css===!1)return e;const{name:n="v",type:s,duration:o,enterFromClass:r=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=r,appearActiveClass:f=i,appearToClass:u=a,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:E=`${n}-leave-to`}=t,y=Se(o),Bt=y&&y[0],Ft=y&&y[1],{onBeforeEnter:z,onEnter:W,onEnterCancelled:X,onLeave:J,onLeaveCancelled:Kt,onBeforeAppear:Vt=z,onAppear:qt=W,onAppearCancelled:Gt=X}=e,B=(c,m,b,$)=>{c._enterCancelled=$,C(c,m?u:a),C(c,m?f:i),b&&b()},Z=(c,m)=>{c._isLeaving=!1,C(c,d),C(c,E),C(c,p),m&&m()},Q=c=>(m,b)=>{const $=c?qt:W,Y=()=>B(m,c,b);S($,[m,Y]),it(()=>{C(m,c?l:r),h(m,c?u:a),rt($)||at(m,s,Bt,Y)})};return H(e,{onBeforeEnter(c){S(z,[c]),h(c,r),h(c,i)},onBeforeAppear(c){S(Vt,[c]),h(c,l),h(c,f)},onEnter:Q(!1),onAppear:Q(!0),onLeave(c,m){c._isLeaving=!0;const b=()=>Z(c,m);h(c,d),c._enterCancelled?(h(c,p),G()):(G(),h(c,p)),it(()=>{c._isLeaving&&(C(c,d),h(c,E),rt(J)||at(c,s,Ft,b))}),S(J,[c,b])},onEnterCancelled(c){B(c,!1,void 0,!0),S(X,[c])},onAppearCancelled(c){B(c,!0,void 0,!0),S(Gt,[c])},onLeaveCancelled(c){Z(c),S(Kt,[c])}})}function Se(t){if(t==null)return null;if(de(t))return[F(t.enter),F(t.leave)];{const e=F(t);return[e,e]}}function F(t){return me(t)}function h(t,e){e.split(/\s+/).forEach(n=>n&&t.classList.add(n)),(t[A]||(t[A]=new Set)).add(e)}function C(t,e){e.split(/\s+/).forEach(s=>s&&t.classList.remove(s));const n=t[A];n&&(n.delete(e),n.size||(t[A]=void 0))}function it(t){requestAnimationFrame(()=>{requestAnimationFrame(t)})}let Te=0;function at(t,e,n,s){const o=t._endId=++Te,r=()=>{o===t._endId&&s()};if(n!=null)return setTimeout(r,n);const{type:i,timeout:a,propCount:l}=Pt(t,e);if(!i)return s();const f=i+"end";let u=0;const d=()=>{t.removeEventListener(f,p),r()},p=E=>{E.target===t&&++u>=l&&d()};setTimeout(()=>{u<l&&d()},a+1),t.addEventListener(f,p)}function Pt(t,e){const n=window.getComputedStyle(t),s=y=>(n[y]||"").split(", "),o=s(`${v}Delay`),r=s(`${v}Duration`),i=ct(o,r),a=s(`${N}Delay`),l=s(`${N}Duration`),f=ct(a,l);let u=null,d=0,p=0;e===v?i>0&&(u=v,d=i,p=r.length):e===N?f>0&&(u=N,d=f,p=l.length):(d=Math.max(i,f),u=d>0?i>f?v:N:null,p=u?u===v?r.length:l.length:0);const E=u===v&&/\b(transform|all)(,|$)/.test(s(`${v}Property`).toString());return{type:u,timeout:d,propCount:p,hasTransform:E}}function ct(t,e){for(;t.length<e.length;)t=t.concat(t);return Math.max(...e.map((n,s)=>lt(n)+lt(t[s])))}function lt(t){return t==="auto"?0:Number(t.slice(0,-1).replace(",","."))*1e3}function G(){return document.body.offsetHeight}function Ee(t,e,n){const s=t[A];s&&(e=(e?[e,...s]:[...s]).join(" ")),e==null?t.removeAttribute("class"):n?t.setAttribute("class",e):t.className=e}const D=Symbol("_vod"),$t=Symbol("_vsh"),Qe={beforeMount(t,{value:e},{transition:n}){t[D]=t.style.display==="none"?"":t.style.display,n&&e?n.beforeEnter(t):M(t,e)},mounted(t,{value:e},{transition:n}){n&&e&&n.enter(t)},updated(t,{value:e,oldValue:n},{transition:s}){!e!=!n&&(s?e?(s.beforeEnter(t),M(t,!0),s.enter(t)):s.leave(t,()=>{M(t,!1)}):M(t,e))},beforeUnmount(t,{value:e}){M(t,e)}};function M(t,e){t.style.display=e?t[D]:"none",t[$t]=!e}const Ot=Symbol("");function Ye(t){const e=Tt();if(!e)return;const n=e.ut=(o=t(e.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${e.uid}"]`)).forEach(r=>I(r,o))},s=()=>{const o=t(e.proxy);e.ce?I(e.ce,o):U(e.subTree,o),n(o)};Xt(()=>{Jt(s)}),Zt(()=>{Qt(s,pe,{flush:"post"});const o=new MutationObserver(s);o.observe(e.subTree.el.parentNode,{childList:!0}),Yt(()=>o.disconnect())})}function U(t,e){if(t.shapeFlag&128){const n=t.suspense;t=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{U(n.activeBranch,e)})}for(;t.component;)t=t.component.subTree;if(t.shapeFlag&1&&t.el)I(t.el,e);else if(t.type===Et)t.children.forEach(n=>U(n,e));else if(t.type===kt){let{el:n,anchor:s}=t;for(;n&&(I(n,e),n!==s);)n=n.nextSibling}}function I(t,e){if(t.nodeType===1){const n=t.style;let s="";for(const o in e)n.setProperty(`--${o}`,e[o]),s+=`--${o}: ${e[o]};`;n[Ot]=s}}const ye=/(^|;)\s*display\s*:/;function we(t,e,n){const s=t.style,o=L(n);let r=!1;if(n&&!o){if(e)if(L(e))for(const i of e.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&O(s,a,"")}else for(const i in e)n[i]==null&&O(s,i,"");for(const i in n)i==="display"&&(r=!0),O(s,i,n[i])}else if(o){if(e!==n){const i=s[Ot];i&&(n+=";"+i),s.cssText=n,r=ye.test(n)}}else e&&t.removeAttribute("style");D in t&&(t[D]=r?s.display:"",t[$t]&&(s.display="none"))}const ft=/\s*!important$/;function O(t,e,n){if(P(n))n.forEach(s=>O(t,e,s));else if(n==null&&(n=""),e.startsWith("--"))t.setProperty(e,n);else{const s=Ae(t,e);ft.test(n)?t.setProperty(j(s),n.replace(ft,""),"important"):t[s]=n}}const ut=["Webkit","Moz","ms"],K={};function Ae(t,e){const n=K[e];if(n)return n;let s=wt(e);if(s!=="filter"&&s in t)return K[e]=s;s=ue(s);for(let o=0;o<ut.length;o++){const r=ut[o]+s;if(r in t)return K[e]=r}return e}const pt="http://www.w3.org/1999/xlink";function dt(t,e,n,s,o,r=le(e)){s&&e.startsWith("xlink:")?n==null?t.removeAttributeNS(pt,e.slice(6,e.length)):t.setAttributeNS(pt,e,n):n==null||r&&!At(n)?t.removeAttribute(e):t.setAttribute(e,r?"":fe(n)?String(n):n)}function mt(t,e,n,s,o){if(e==="innerHTML"||e==="textContent"){n!=null&&(t[e]=e==="innerHTML"?Nt(n):n);return}const r=t.tagName;if(e==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?t.getAttribute("value")||"":t.value,l=n==null?t.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in t))&&(t.value=l),n==null&&t.removeAttribute(e),t._value=n;return}let i=!1;if(n===""||n==null){const a=typeof t[e];a==="boolean"?n=At(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{t[e]=n}catch{}i&&t.removeAttribute(o||e)}function T(t,e,n,s){t.addEventListener(e,n,s)}function Ne(t,e,n,s){t.removeEventListener(e,n,s)}const ht=Symbol("_vei");function Me(t,e,n,s,o=null){const r=t[ht]||(t[ht]={}),i=r[e];if(s&&i)i.value=s;else{const[a,l]=_e(e);if(s){const f=r[e]=$e(s,o);T(t,a,f,l)}else i&&(Ne(t,a,i,l),r[e]=void 0)}}const gt=/(?:Once|Passive|Capture)$/;function _e(t){let e;if(gt.test(t)){e={};let s;for(;s=t.match(gt);)t=t.slice(0,t.length-s[0].length),e[s[0].toLowerCase()]=!0}return[t[2]===":"?t.slice(3):j(t.slice(2)),e]}let V=0;const Le=Promise.resolve(),Pe=()=>V||(Le.then(()=>V=0),V=Date.now());function $e(t,e){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;zt(Oe(s,n.value),e,5,[s])};return n.value=t,n.attached=Pe(),n}function Oe(t,e){if(P(e)){const n=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{n.call(t),t._stopped=!0},e.map(s=>o=>!o._stopped&&s&&s(o))}else return e}const vt=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&t.charCodeAt(2)>96&&t.charCodeAt(2)<123,De=(t,e,n,s,o,r)=>{const i=o==="svg";e==="class"?Ee(t,s,i):e==="style"?we(t,n,s):ae(e)?ce(e)||Me(t,e,n,s,r):(e[0]==="."?(e=e.slice(1),!0):e[0]==="^"?(e=e.slice(1),!1):Ie(t,e,s,i))?(mt(t,e,s),!t.tagName.includes("-")&&(e==="value"||e==="checked"||e==="selected")&&dt(t,e,s,i,r,e!=="value")):t._isVueCE&&(/[A-Z]/.test(e)||!L(s))?mt(t,wt(e),s,r,e):(e==="true-value"?t._trueValue=s:e==="false-value"&&(t._falseValue=s),dt(t,e,s,i))};function Ie(t,e,n,s){if(s)return!!(e==="innerHTML"||e==="textContent"||e in t&&vt(e)&&yt(n));if(e==="spellcheck"||e==="draggable"||e==="translate"||e==="autocorrect"||e==="form"||e==="list"&&t.tagName==="INPUT"||e==="type"&&t.tagName==="TEXTAREA")return!1;if(e==="width"||e==="height"){const o=t.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return vt(e)&&L(n)?!1:e in t}const Dt=new WeakMap,It=new WeakMap,R=Symbol("_moveCb"),Ct=Symbol("_enterCb"),Re=t=>(delete t.props.mode,t),xe=Re({name:"TransitionGroup",props:H({},_t,{tag:String,moveClass:String}),setup(t,{slots:e}){const n=Tt(),s=ne();let o,r;return se(()=>{if(!o.length)return;const i=t.moveClass||`${t.name||"v"}-move`;if(!Ke(o[0].el,n.vnode.el,i)){o=[];return}o.forEach(He),o.forEach(Be);const a=o.filter(Fe);G(),a.forEach(l=>{const f=l.el,u=f.style;h(f,i),u.transform=u.webkitTransform=u.transitionDuration="";const d=f[R]=p=>{p&&p.target!==f||(!p||/transform$/.test(p.propertyName))&&(f.removeEventListener("transitionend",d),f[R]=null,C(f,i))};f.addEventListener("transitionend",d)}),o=[]}),()=>{const i=he(t),a=Lt(i);let l=i.tag||Et;if(o=[],r)for(let f=0;f<r.length;f++){const u=r[f];u.el&&u.el instanceof Element&&(o.push(u),k(u,tt(u,a,s,n)),Dt.set(u,u.el.getBoundingClientRect()))}r=e.default?oe(e.default()):[];for(let f=0;f<r.length;f++){const u=r[f];u.key!=null&&k(u,tt(u,a,s,n))}return re(l,null,r)}}}),ke=xe;function He(t){const e=t.el;e[R]&&e[R](),e[Ct]&&e[Ct]()}function Be(t){It.set(t,t.el.getBoundingClientRect())}function Fe(t){const e=Dt.get(t),n=It.get(t),s=e.left-n.left,o=e.top-n.top;if(s||o){const r=t.el.style;return r.transform=r.webkitTransform=`translate(${s}px,${o}px)`,r.transitionDuration="0s",t}}function Ke(t,e,n){const s=t.cloneNode(),o=t[A];o&&o.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const r=e.nodeType===1?e:e.parentNode;r.appendChild(s);const{hasTransform:i}=Pt(s);return r.removeChild(s),i}const x=t=>{const e=t.props["onUpdate:modelValue"]||!1;return P(e)?n=>ie(e,n):e};function Ve(t){t.target.composing=!0}function bt(t){const e=t.target;e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}const w=Symbol("_assign"),tn={created(t,{modifiers:{lazy:e,trim:n,number:s}},o){t[w]=x(o);const r=s||o.props&&o.props.type==="number";T(t,e?"change":"input",i=>{if(i.target.composing)return;let a=t.value;n&&(a=a.trim()),r&&(a=nt(a)),t[w](a)}),n&&T(t,"change",()=>{t.value=t.value.trim()}),e||(T(t,"compositionstart",Ve),T(t,"compositionend",bt),T(t,"change",bt))},mounted(t,{value:e}){t.value=e??""},beforeUpdate(t,{value:e,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(t[w]=x(i),t.composing)return;const a=(r||t.type==="number")&&!/^0\d/.test(t.value)?nt(t.value):t.value,l=e??"";a!==l&&(document.activeElement===t&&t.type!=="range"&&(s&&e===n||o&&t.value.trim()===l)||(t.value=l))}},en={created(t,{value:e},n){t.checked=et(e,n.props.value),t[w]=x(n),T(t,"change",()=>{t[w](qe(t))})},beforeUpdate(t,{value:e,oldValue:n},s){t[w]=x(s),e!==n&&(t.checked=et(e,s.props.value))}};function qe(t){return"_value"in t?t._value:t.value}const Ge=["ctrl","shift","alt","meta"],Ue={stop:t=>t.stopPropagation(),prevent:t=>t.preventDefault(),self:t=>t.target!==t.currentTarget,ctrl:t=>!t.ctrlKey,shift:t=>!t.shiftKey,alt:t=>!t.altKey,meta:t=>!t.metaKey,left:t=>"button"in t&&t.button!==0,middle:t=>"button"in t&&t.button!==1,right:t=>"button"in t&&t.button!==2,exact:(t,e)=>Ge.some(n=>t[`${n}Key`]&&!e.includes(n))},nn=(t,e)=>{const n=t._withMods||(t._withMods={}),s=e.join(".");return n[s]||(n[s]=(o,...r)=>{for(let i=0;i<e.length;i++){const a=Ue[e[i]];if(a&&a(o,e))return}return t(o,...r)})},je={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},sn=(t,e)=>{const n=t._withKeys||(t._withKeys={}),s=e.join(".");return n[s]||(n[s]=o=>{if(!("key"in o))return;const r=j(o.key);if(e.some(i=>i===r||je[i]===r))return t(o)})},Rt=H({patchProp:De},Ce);let _,St=!1;function ze(){return _||(_=jt(Rt))}function We(){return _=St?_:Ut(Rt),St=!0,_}const on=(...t)=>{const e=ze().createApp(...t),{mount:n}=e;return e.mount=s=>{const o=Ht(s);if(!o)return;const r=e._component;!yt(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,xt(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},e},rn=(...t)=>{const e=We().createApp(...t),{mount:n}=e;return e.mount=s=>{const o=Ht(s);if(o)return n(o,!0,xt(o))},e};function xt(t){if(t instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&t instanceof MathMLElement)return"mathml"}function Ht(t){return L(t)?document.querySelector(t):t}export{Ze as T,sn as a,Qe as b,tn as c,rn as d,on as e,ke as f,Ye as u,en as v,nn as w};

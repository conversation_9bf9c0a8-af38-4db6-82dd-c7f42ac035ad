import { defineComponent, useSSRContext, ref, computed, mergeProps } from 'vue';
import { T as Tag } from './Tag_B6nH2bAR.mjs';
import { V as VToggleSwitch } from './ToggleSwitch_DRN4BNJ9.mjs';
import { S as Select } from './Select_DIHmHCCM.mjs';
import { I as InputText } from './InputText_DNFWprlB.mjs';
import { I as Icon } from './AdminLayout_DrlBSzRq.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList, ssrRenderClass, ssrRenderAttr, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_avhRMbqw.mjs';

const useMatchingLabels = () => {
  const getAccuracyLabel = (accuracy) => ({
    EXACT_MATCH: "Точное совпадение",
    MATCH_WITH_NOTES: "С примечаниями",
    REQUIRES_MODIFICATION: "Требует доработки",
    PARTIAL_MATCH: "Частичное совпадение"
  })[accuracy] || accuracy;
  const getAccuracySeverity = (accuracy) => ({
    EXACT_MATCH: "success",
    MATCH_WITH_NOTES: "info",
    REQUIRES_MODIFICATION: "warning",
    PARTIAL_MATCH: "secondary"
  })[accuracy] || "secondary";
  const getDetailSeverity = (kind) => {
    if (kind.includes("EXACT")) return "success";
    if (kind.includes("WITHIN_TOLERANCE") || kind.includes("NEAR")) return "info";
    if (kind.includes("LEGACY")) return "warning";
    return "secondary";
  };
  const getKindLabel = (kind) => ({
    NUMBER_EXACT: "Число: точное",
    NUMBER_WITHIN_TOLERANCE: "Число: в допуске",
    STRING_EXACT: "Строка: точное",
    STRING_SYNONYM_EXACT: "Строка: группа EXACT",
    STRING_SYNONYM_NEAR: "Строка: группа NEAR",
    STRING_SYNONYM_LEGACY: "Строка: группа LEGACY",
    EXACT_STRING: "Точное совпадение"
  })[kind] || kind;
  return { getAccuracyLabel, getAccuracySeverity, getDetailSeverity, getKindLabel };
};

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "MatchingDetailsGrid",
  props: {
    details: {},
    controls: { type: Boolean, default: true }
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const { getKindLabel, getDetailSeverity } = useMatchingLabels();
    const showOnlyImportant = ref(false);
    const showNotes = ref(true);
    const search = ref("");
    const sortMode = ref("importance");
    const sortOptions = [
      { label: "\u041F\u043E \u0432\u0430\u0436\u043D\u043E\u0441\u0442\u0438", value: "importance" },
      { label: "\u041F\u043E \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0443", value: "name" }
    ];
    const isNumeric = (d) => String(d?.kind || "").startsWith("NUMBER");
    const isStringSynonym = (d) => String(d?.kind || "").startsWith("STRING_SYNONYM");
    const isExact = (d) => String(d?.kind || "").includes("EXACT") && !String(d?.kind || "").includes("WITHIN_TOLERANCE");
    const isImportant = (d) => {
      const kind = String(d?.kind || "");
      if (d?.notes) return true;
      if (kind.includes("NEAR") || kind.includes("LEGACY") || kind.includes("WITHIN_TOLERANCE")) return true;
      if (isNumeric(d) && (d.delta !== void 0 || d.toleranceUsed !== void 0)) return true;
      return !isExact(d);
    };
    const filteredDetails = computed(() => {
      let src = Array.isArray(props.details) ? props.details : [];
      if (showOnlyImportant.value) src = src.filter(isImportant);
      const q = search.value.trim().toLowerCase();
      if (q) src = src.filter((d) => String(d?.templateTitle || d?.templateId || "").toLowerCase().includes(q));
      return src;
    });
    const importanceRank = (d) => {
      const kind = String(d?.kind || "");
      if (kind.includes("LEGACY")) return 0;
      if (kind.includes("NEAR") || kind.includes("WITHIN_TOLERANCE")) return 1;
      if (kind.includes("EXACT")) return 2;
      return 3;
    };
    const preparedDetails = computed(() => {
      const list = [...filteredDetails.value];
      if (sortMode.value === "name") {
        list.sort((a, b) => String(a?.templateTitle || a?.templateId).localeCompare(String(b?.templateTitle || b?.templateId)));
      } else {
        list.sort((a, b) => importanceRank(a) - importanceRank(b));
      }
      return list;
    });
    const summary = computed(() => {
      let exact = 0, near = 0, legacy = 0, tol = 0;
      const list = Array.isArray(props.details) ? props.details : [];
      for (const d of list) {
        const kind = String(d?.kind || "");
        if (kind.includes("EXACT")) exact++;
        if (kind.includes("NEAR")) near++;
        if (kind.includes("LEGACY")) legacy++;
        if (kind.includes("WITHIN_TOLERANCE")) tol++;
      }
      return { total: list.length, exact, near, legacy, tol };
    });
    const getKindIcon = (kind) => {
      const k = String(kind || "");
      if (k.includes("EXACT") && !k.includes("WITHIN_TOLERANCE")) return "pi pi-check-circle";
      if (k.includes("WITHIN_TOLERANCE") || k.includes("NEAR")) return "pi pi-info-circle";
      if (k.includes("LEGACY")) return "pi pi-exclamation-triangle";
      return "pi pi-circle";
    };
    const accentClass = (d) => {
      const k = String(d?.kind || "");
      if (k.includes("LEGACY")) return "border-yellow-400/60 dark:border-yellow-500/60";
      if (k.includes("WITHIN_TOLERANCE") || k.includes("NEAR")) return "border-sky-400/60 dark:border-sky-500/60";
      if (k.includes("EXACT")) return "border-emerald-400/60 dark:border-emerald-500/60";
      return "border-surface-200 dark:border-surface-600";
    };
    const valueClass = (d) => {
      const k = String(d?.kind || "");
      if (k.includes("LEGACY")) return "text-yellow-700 dark:text-yellow-300";
      if (k.includes("WITHIN_TOLERANCE") || k.includes("NEAR")) return "text-sky-700 dark:text-sky-300";
      if (k.includes("EXACT")) return "text-emerald-700 dark:text-emerald-300";
      return "text-surface-800 dark:text-surface-200";
    };
    const __returned__ = { props, getKindLabel, getDetailSeverity, showOnlyImportant, showNotes, search, sortMode, sortOptions, isNumeric, isStringSynonym, isExact, isImportant, filteredDetails, importanceRank, preparedDetails, summary, getKindIcon, accentClass, valueClass, VTag: Tag, VToggleSwitch, VSelect: Select, VInputText: InputText, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-2" }, _attrs))}>`);
  if ($props.controls) {
    _push(`<div class="flex flex-wrap items-center justify-between gap-2"><div class="flex items-center gap-3"><label class="inline-flex items-center gap-2 text-xs text-surface-600">`);
    _push(ssrRenderComponent($setup["VToggleSwitch"], {
      modelValue: $setup.showOnlyImportant,
      "onUpdate:modelValue": ($event) => $setup.showOnlyImportant = $event
    }, null, _parent));
    _push(`<span>\u0422\u043E\u043B\u044C\u043A\u043E \u0432\u0430\u0436\u043D\u043E\u0435</span></label><label class="inline-flex items-center gap-2 text-xs text-surface-600">`);
    _push(ssrRenderComponent($setup["VToggleSwitch"], {
      modelValue: $setup.showNotes,
      "onUpdate:modelValue": ($event) => $setup.showNotes = $event
    }, null, _parent));
    _push(`<span>\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0438</span></label><div class="hidden md:flex items-center gap-2"><span class="text-xs text-surface-500">\u0421\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0430</span>`);
    _push(ssrRenderComponent($setup["VSelect"], {
      modelValue: $setup.sortMode,
      "onUpdate:modelValue": ($event) => $setup.sortMode = $event,
      options: $setup.sortOptions,
      optionLabel: "label",
      optionValue: "value",
      class: "w-40"
    }, null, _parent));
    _push(`</div></div><div class="flex items-center gap-2 max-w-xs w-full md:max-w-sm">`);
    _push(ssrRenderComponent($setup["VInputText"], {
      modelValue: $setup.search,
      "onUpdate:modelValue": ($event) => $setup.search = $event,
      placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0443",
      class: "w-full"
    }, null, _parent));
    _push(`</div><div class="hidden md:flex items-center gap-1 text-xs text-surface-500">`);
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `\u0412\u0441\u0435\u0433\u043E ${$setup.summary.total}`,
      severity: "secondary"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `EXACT ${$setup.summary.exact}`,
      severity: "success"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `NEAR ${$setup.summary.near}`,
      severity: "info"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `TOL ${$setup.summary.tol}`,
      severity: "info"
    }, null, _parent));
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: `LEGACY ${$setup.summary.legacy}`,
      severity: "warning"
    }, null, _parent));
    _push(`</div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="hidden md:block"><div class="grid grid-cols-12 px-2 py-2 text-[11px] uppercase tracking-wide text-surface-500"><div class="col-span-3">\u0410\u0442\u0440\u0438\u0431\u0443\u0442</div><div class="col-span-3">\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0432 item</div><div class="col-span-3">\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0432 part</div><div class="col-span-2">\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442</div>`);
  if ($setup.showNotes) {
    _push(`<div class="col-span-1">\u0417\u0430\u043C\u0435\u0442\u043A\u0438</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="divide-y divide-surface-border rounded border"><!--[-->`);
  ssrRenderList($setup.preparedDetails, (d) => {
    _push(`<div class="${ssrRenderClass([$setup.accentClass(d), "grid grid-cols-12 items-center px-2 py-2 text-sm border-l-2"])}"><div class="col-span-3 flex items-center gap-2 min-w-0">`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: $setup.getKindIcon(d.kind),
      class: "w-4 h-4 text-surface-400"
    }, null, _parent));
    _push(`<span class="truncate"${ssrRenderAttr("title", d.templateTitle || "template #" + d.templateId)}>${ssrInterpolate(d.templateTitle || "template #" + d.templateId)}</span></div><div class="${ssrRenderClass([$setup.valueClass(d), "col-span-3 font-mono break-words"])}">${ssrInterpolate(d.itemValue)} `);
    if ($setup.isNumeric(d) && d.delta !== void 0) {
      _push(`<span class="ml-1 text-xs text-surface-500">\u0394=${ssrInterpolate(d.delta)}</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div><div class="${ssrRenderClass([$setup.valueClass(d), "col-span-3 font-mono break-words"])}">${ssrInterpolate(d.partValue)} `);
    if ($setup.isNumeric(d) && d.toleranceUsed !== void 0) {
      _push(`<span class="ml-1 text-xs text-surface-500">tol=${ssrInterpolate(d.toleranceUsed)}</span>`);
    } else {
      _push(`<!---->`);
    }
    if ($setup.isStringSynonym(d) && d.synonymLevel) {
      _push(`<span class="ml-1 text-xs text-surface-500">level=${ssrInterpolate(d.synonymLevel)}</span>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div><div class="col-span-2">`);
    _push(ssrRenderComponent($setup["VTag"], {
      size: "small",
      value: $setup.getKindLabel(d.kind),
      severity: $setup.getDetailSeverity(d.kind)
    }, null, _parent));
    _push(`</div>`);
    if ($setup.showNotes) {
      _push(`<div class="col-span-1 text-xs text-surface-500">`);
      if (d.notes) {
        _push(`<span>${ssrInterpolate(d.notes)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  });
  _push(`<!--]--></div></div><div class="md:hidden grid grid-cols-1 gap-2"><!--[-->`);
  ssrRenderList($setup.preparedDetails, (d) => {
    _push(`<div class="${ssrRenderClass([$setup.accentClass(d), "p-2 bg-surface-50 dark:bg-surface-900 rounded border border-l-2"])}"><div class="flex items-center justify-between"><div class="text-xs text-surface-700 dark:text-surface-300 flex items-center gap-2">`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: $setup.getKindIcon(d.kind),
      class: "w-4 h-4 text-surface-400"
    }, null, _parent));
    _push(`<span class="font-medium">${ssrInterpolate(d.templateTitle || "template #" + d.templateId)}</span></div>`);
    _push(ssrRenderComponent($setup["VTag"], {
      value: $setup.getKindLabel(d.kind),
      severity: $setup.getDetailSeverity(d.kind),
      size: "small"
    }, null, _parent));
    _push(`</div><div class="text-xs text-surface-500 mt-1">`);
    if ($setup.isNumeric(d)) {
      _push(`<!--[--> item: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.itemValue)}</span> \u2192 part: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.partValue)}</span>`);
      if (d.delta !== void 0) {
        _push(`<span> | \u0394=${ssrInterpolate(d.delta)}</span>`);
      } else {
        _push(`<!---->`);
      }
      if (d.toleranceUsed !== void 0) {
        _push(`<span> | tol=${ssrInterpolate(d.toleranceUsed)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<!--]-->`);
    } else {
      _push(`<!--[--> item: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.itemValue)}</span> \u2192 part: <span class="${ssrRenderClass([$setup.valueClass(d), "font-mono"])}">${ssrInterpolate(d.partValue)}</span>`);
      if (d.synonymLevel) {
        _push(`<span> | level=${ssrInterpolate(d.synonymLevel)}</span>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<!--]-->`);
    }
    _push(`</div>`);
    if ($setup.showNotes && d.notes) {
      _push(`<div class="text-xs text-surface-500 mt-1">${ssrInterpolate(d.notes)}</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  });
  _push(`<!--]--></div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/MatchingDetailsGrid.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const MatchingDetailsGrid = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { MatchingDetailsGrid as M, useMatchingLabels as u };

import{E as G}from"./ErrorBoundary.B0AhELGe.js";import{t as g}from"./trpc.BpyaUO08.js";import K from"./Button.DrThv2lH.js";import{D as J,s as Q}from"./index.BWD5ZO4k.js";import{D as W}from"./Dialog.Ct7C9BO5.js";import{I as H}from"./InputText.DOJMNEP-.js";import{V as X}from"./Textarea.BLEHJ3ym.js";import{D as Y}from"./Dropdown.Cj1958l9.js";import{S as Z}from"./SecondaryButton.DkELYl7Q.js";import{s as k,f as $,r as ee}from"./utils.BL5HZsed.js";import{u as ue}from"./useTrpc.spLZjt2f.js";import{_ as q}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as M,g as R,o as p,w as r,a as n,e as t,c as h,b as ae,f as P,K as z,L as le,j as L,i as oe,h as te,F as ne,r as ie,n as re}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as m,c as N,t as b}from"./reactivity.esm-bundler.BQ12LWmY.js";import{n as O}from"./router.DKcY2uv6.js";import se from"./Toast.DmmKUJB6.js";import{P as de}from"./plus.CiWMw0wk.js";import{T as ce,P as me}from"./trash.4HbnIIsp.js";import"./createLucideIcon.NtN1-Ts2.js";import"./triangle-alert.CP-lXbmj.js";import"./index.6ykohhwZ.js";import"./index.BaVCXmir.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.By2TJOuX.js";import"./index.COq_zjeV.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";import"./index.D4QD70nN.js";import"./Select.CQBzSu6y.js";import"./useErrorHandler.DVDazL16.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";import"./index.CmzoVUnM.js";import"./index.CwqAtb_i.js";const fe=M({__name:"EditCategoryDialog",props:z({category:{}},{isVisible:{type:Boolean,required:!0},isVisibleModifiers:{}}),emits:z(["save","cancel"],["update:isVisible"]),setup(v,{expose:u,emit:f}){u();const e=m([]),s=le(v,"isVisible"),d=v,a=f,o=m({}),y=m(!1),w=m("Создать категорию");L(s,i=>{i&&(o.value={...d.category||{}},w.value=d.category?.id?`Редактировать: ${d.category.name}`:"Создать категорию",y.value=!1)}),L(()=>o.value.name,i=>{const c=String(o.value.slug??"");(!y.value||c.length===0)&&(o.value.slug=k(String(i??"")))});function S(){y.value=!0,o.value.slug=k(String(o.value.slug??""))}function D(){s.value=!1,a("cancel")}function A(){(!o.value.slug||String(o.value.slug).trim()==="")&&o.value.name&&(o.value.slug=k(String(o.value.name))),a("save",{...o.value}),s.value=!1}async function I(){const i=await g.crud.partCategory.findMany.query({select:{id:!0,name:!0},orderBy:{name:"asc"}});e.value=i}oe(()=>{I()});const{media:E}=ue(),C=m(null),_=m(!1),V=te(()=>d.category?.image?.url||o.value?.image?.url||null);function F(i){const c=i.target;c.files&&c.files[0]&&(C.value=c.files[0])}async function T(){if(!(!o.value?.id||!C.value)){_.value=!0;try{const i=await $(C.value);await E.uploadPartCategoryImage({partCategoryId:o.value.id,fileName:C.value.name,fileData:i,mimeType:C.value.type||"image/png"});const c=await g.crud.partCategory.findUnique.query({where:{id:o.value.id},include:{image:!0}});c&&(o.value.image=c.image),C.value=null}finally{_.value=!1}}}async function B(){if(o.value?.id){_.value=!0;try{await E.deletePartCategoryImage({partCategoryId:o.value.id});const i=await g.crud.partCategory.findUnique.query({where:{id:o.value.id},include:{image:!0}});i&&(o.value.image=i.image)}finally{_.value=!1}}}const l={parentOptions:e,isVisible:s,props:d,emit:a,localCategory:o,userEditedSlug:y,dialogTitle:w,onSlugInput:S,handleCancel:D,handleSave:A,loadParentCategories:I,media:E,selectedImage:C,uploading:_,categoryImageUrl:V,onSelectImage:F,uploadImage:T,deleteImage:B,Button:K,Dialog:W,InputText:H,Textarea:X,Dropdown:Y,SecondaryButton:Z,get resolveMediaUrl(){return ee}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}}),ge={class:"flex flex-col gap-4 py-4"},pe={class:"flex flex-col"},ve={class:"flex flex-col"},ye={class:"flex flex-col"},Ce={class:"flex flex-col"},_e={class:"flex flex-col"},he={class:"flex flex-col"},Ee={class:"flex items-center gap-3"},xe={class:"w-24 h-24 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},be=["src"],we={key:1,class:"text-surface-500 text-xs"},Ie={class:"flex flex-col gap-2"},Ve={class:"flex gap-2"},Be={key:0,class:"text-surface-500"},Se={class:"flex justify-end gap-2"};function De(v,u,f,e,s,d){return p(),R(e.Dialog,{visible:e.isVisible,"onUpdate:visible":u[5]||(u[5]=a=>e.isVisible=a),modal:"",header:e.dialogTitle,class:"sm:w-100 w-9/10"},{default:r(()=>[n("div",ge,[n("div",pe,[u[6]||(u[6]=n("label",{for:"name"},"Наименование",-1)),t(e.InputText,{id:"name",modelValue:e.localCategory.name,"onUpdate:modelValue":u[0]||(u[0]=a=>e.localCategory.name=a)},null,8,["modelValue"])]),n("div",ve,[u[7]||(u[7]=n("label",{for:"slug"},"URL слаг",-1)),t(e.InputText,{id:"slug",modelValue:e.localCategory.slug,"onUpdate:modelValue":u[1]||(u[1]=a=>e.localCategory.slug=a),onInput:e.onSlugInput},null,8,["modelValue"]),u[8]||(u[8]=n("small",{class:"text-surface-500"},"Автогенерация из названия, можно отредактировать вручную",-1))]),n("div",ye,[u[9]||(u[9]=n("label",{for:"description"},"Описание",-1)),t(e.Textarea,{id:"description",modelValue:e.localCategory.description,"onUpdate:modelValue":u[2]||(u[2]=a=>e.localCategory.description=a),rows:"3"},null,8,["modelValue"])]),n("div",Ce,[u[10]||(u[10]=n("label",{for:"parent"},"Родительская категория",-1)),t(e.Dropdown,{id:"parent",modelValue:e.localCategory.parentId,"onUpdate:modelValue":u[3]||(u[3]=a=>e.localCategory.parentId=a),options:e.parentOptions,optionLabel:"name",optionValue:"id",placeholder:"Выберите родительскую категорию",showClear:""},null,8,["modelValue","options"])]),n("div",_e,[u[11]||(u[11]=n("label",{for:"icon"},"Иконка",-1)),t(e.InputText,{id:"icon",modelValue:e.localCategory.icon,"onUpdate:modelValue":u[4]||(u[4]=a=>e.localCategory.icon=a),placeholder:"Например: engine, filter, etc."},null,8,["modelValue"])]),n("div",he,[u[14]||(u[14]=n("label",null,"Изображение категории",-1)),n("div",Ee,[n("div",xe,[e.categoryImageUrl?(p(),h("img",{key:0,src:e.resolveMediaUrl(e.categoryImageUrl),class:"object-cover w-full h-full"},null,8,be)):(p(),h("span",we,"Нет"))]),n("div",Ie,[n("input",{type:"file",accept:"image/*",onChange:e.onSelectImage},null,32),n("div",Ve,[t(e.Button,{size:"small",disabled:!e.selectedImage||e.uploading,onClick:e.uploadImage},{default:r(()=>u[12]||(u[12]=[P("Загрузить")])),_:1,__:[12]},8,["disabled"]),t(e.Button,{size:"small",severity:"danger",outlined:"",disabled:!e.categoryImageUrl||e.uploading,onClick:e.deleteImage},{default:r(()=>u[13]||(u[13]=[P("Удалить")])),_:1,__:[13]},8,["disabled"])]),e.uploading?(p(),h("small",Be,"Загрузка...")):ae("",!0)])])])]),n("div",Se,[t(e.SecondaryButton,{type:"button",label:"Cancel",onClick:e.handleCancel}),t(e.Button,{type:"button",label:"Save",onClick:e.handleSave})])]),_:1},8,["visible","header"])}const Ae=q(fe,[["render",De]]),Fe=M({__name:"CategoryList",props:{initialData:{}},setup(v,{expose:u}){u();const f=m(""),e=m(!1),s=m(null),d=m([]),a=m({}),o=v,y=m(o.initialData),w={id:"ID",name:"Наименование",slug:"URL слаг",description:"Описание",parentId:"Родитель ID",level:"Уровень",path:"Путь",icon:"Иконка"},S=["id","name","slug","description","level","icon"];function D(){s.value={},e.value=!0}function A(l){s.value={...l},e.value=!0}async function I(l){if(l)try{if(l.id){const{id:i}=l,c={name:l.name,slug:l.slug,description:l.description,parentId:l.parentId??null,icon:l.icon},x=Object.fromEntries(Object.entries(c).filter(([j,U])=>U!==void 0));await g.crud.partCategory.update.mutate({where:{id:i},data:x})}else if(l.name&&l.slug){let i="01",c=0;if(l.parentId){const x=await g.crud.partCategory.findUnique.query({where:{id:l.parentId}});if(x){c=x.level+1;const j=await g.crud.partCategory.count.query({where:{parentId:l.parentId}}),U=String(j+1).padStart(2,"0");i=`${x.path}/${U}`}}await g.crud.partCategory.create.mutate({data:{name:l.name,slug:l.slug,description:l.description,parentId:l.parentId,level:c,path:i,icon:l.icon}})}else{console.error("Name and slug are required to create a category.");return}O(window.location.href)}catch(i){console.error("Failed to save category:",i)}finally{e.value=!1}}function E(){e.value=!1,s.value=null}async function C(l){e.value=!1,await g.crud.partCategory.delete.mutate({where:{id:l.id}}),O(window.location.href)}L(f,l=>{_(l)});async function _(l=""){console.log("value",l);const i=l?{AND:[{level:0},{OR:[{name:{contains:l}},{slug:{contains:l}},{description:{contains:l}}]}]}:{level:0};y.value=await g.crud.partCategory.findMany.query({where:i,include:{image:!0,_count:{select:{parts:!0,children:!0}}},orderBy:{name:"asc"}})}async function V(l){if(a.value[l])return a.value[l];const i=await g.crud.partCategory.findMany.query({where:{parentId:l},include:{image:!0,_count:{select:{parts:!0,children:!0}}},orderBy:{name:"asc"}});return a.value[l]=i,i}function F(l){return l._count.children>0}async function T(l){l.data._count.children>0&&await V(l.data.id)}const B={searchValue:f,dialogVisible:e,editingCategory:s,expandedRows:d,childrenCache:a,props:o,items:y,keyMapping:w,columnKeys:S,createCategory:D,editCategory:A,handleSave:I,handleCancel:E,deleteCategory:C,debouncedSearch:_,loadChildren:V,hasChildren:F,onRowExpand:T,Button:K,DataTable:J,get PencilIcon(){return me},get TrashIcon(){return ce},get PlusIcon(){return de},get Column(){return Q},EditCategoryDialog:Ae,InputText:H,Toast:se};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),Te={class:"flex justify-between items-center mb-4"},Ue={class:"flex justify-end"},ke={class:"flex items-center"},Re=["title"],Pe={class:"flex gap-2"},Le={class:"p-4 bg-surface-50 dark:bg-surface-800"},qe={class:"mb-3 font-semibold"},Me={key:0},je={class:"flex items-center"},ze=["title"],Ne={class:"flex gap-2"},Oe={key:1,class:"text-surface-500"};function Ke(v,u,f,e,s,d){return p(),h("div",null,[n("div",Te,[u[4]||(u[4]=n("h1",{class:"text-2xl font-bold"},"Категории запчастей",-1)),t(e.Button,{onClick:e.createCategory},{default:r(()=>[t(e.PlusIcon,{class:"w-5 h-5 mr-2"}),u[3]||(u[3]=P(" Создать категорию "))]),_:1,__:[3]})]),t(e.DataTable,{"show-headers":"",value:e.items,expandedRows:e.expandedRows,"onUpdate:expandedRows":u[1]||(u[1]=a=>e.expandedRows=a),onRowExpand:e.onRowExpand,rowHover:!0},{header:r(()=>[n("div",Ue,[t(e.InputText,{modelValue:e.searchValue,"onUpdate:modelValue":u[0]||(u[0]=a=>e.searchValue=a),placeholder:"Поиск"},null,8,["modelValue"])])]),expansion:r(({data:a})=>[n("div",Le,[n("h5",qe,"Подкатегории: "+b(a.name),1),e.childrenCache[a.id]&&e.childrenCache[a.id].length>0?(p(),h("div",Me,[t(e.DataTable,{value:e.childrenCache[a.id],class:"mb-4"},{default:r(()=>[t(e.Column,{field:"name",header:"Наименование"},{body:r(({data:o})=>[n("div",je,[n("div",{style:N({marginLeft:`${(o.level-a.level-1)*20}px`})},b(o.name),5)])]),_:2},1024),t(e.Column,{field:"slug",header:"URL слаг"}),t(e.Column,{field:"description",header:"Описание"},{body:r(({data:o})=>[n("div",{class:"max-w-xs truncate",title:o.description},b(o.description||"-"),9,ze)]),_:1}),t(e.Column,{field:"_count.parts",header:"Кол-во деталей"}),t(e.Column,{field:"_count.children",header:"Подкатегории"}),t(e.Column,{header:"Действия"},{body:r(({data:o})=>[n("div",Ne,[t(e.Button,{onClick:y=>e.editCategory(o),outlined:"",size:"small"},{default:r(()=>[t(e.PencilIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"]),t(e.Button,{onClick:y=>e.deleteCategory(o),outlined:"",severity:"danger",size:"small"},{default:r(()=>[t(e.TrashIcon,{class:"w-4 h-4"})]),_:2},1032,["onClick"])])]),_:1})]),_:2},1032,["value"])])):(p(),h("div",Oe," Подкатегории отсутствуют "))])]),default:r(()=>[t(e.Column,{expander:!0,headerStyle:"width: 3rem"}),(p(),h(ne,null,ie(e.columnKeys,a=>t(e.Column,{key:a,field:a,header:e.keyMapping[a]||a},re({_:2},[a==="name"?{name:"body",fn:r(({data:o})=>[n("div",ke,[n("div",{style:N({marginLeft:`${o.level*20}px`})},b(o[a]),5)])]),key:"0"}:a==="description"?{name:"body",fn:r(({data:o})=>[n("div",{class:"max-w-xs truncate",title:o[a]},b(o[a]||"-"),9,Re)]),key:"1"}:void 0]),1032,["field","header"])),64)),t(e.Column,{field:"_count.parts",header:"Кол-во деталей"}),t(e.Column,{field:"_count.children",header:"Подкатегории"}),t(e.Column,{header:"Действия"},{body:r(({data:a})=>[n("div",Pe,[t(e.Button,{onClick:o=>e.editCategory(a),outlined:"",size:"small"},{default:r(()=>[t(e.PencilIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"]),t(e.Button,{onClick:o=>e.deleteCategory(a),outlined:"",severity:"danger",size:"small"},{default:r(()=>[t(e.TrashIcon,{class:"w-5 h-5"})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["value","expandedRows"]),t(e.EditCategoryDialog,{isVisible:e.dialogVisible,"onUpdate:isVisible":u[2]||(u[2]=a=>e.dialogVisible=a),category:e.editingCategory,onSave:e.handleSave,onCancel:e.handleCancel},null,8,["isVisible","category"]),t(e.Toast)])}const He=q(Fe,[["render",Ke]]),Ge=M({__name:"CategoryListBoundary",props:{initialData:{}},setup(v,{expose:u}){u();const f=v,e=m(0),d={props:f,key:e,onRetry:()=>{e.value++},ErrorBoundary:G,CategoryList:He};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}});function Je(v,u,f,e,s,d){return p(),R(e.ErrorBoundary,{variant:"minimal",title:"Ошибка категорий",message:"Список категорий не отрисовался. Повторите попытку.",onRetry:e.onRetry},{default:r(()=>[(p(),R(e.CategoryList,{initialData:f.initialData,key:e.key},null,8,["initialData"]))]),_:1})}const qu=q(Ge,[["render",Je]]);export{qu as default};

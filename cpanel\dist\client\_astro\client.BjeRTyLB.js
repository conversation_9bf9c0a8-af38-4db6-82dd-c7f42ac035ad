import{T as P}from"./index.CmzoVUnM.js";import{P as ht}from"./index.PhWaFJhe.js";import{P as vt,C as q}from"./index.J-5Oa3io.js";import{a1 as mt,R as rt,f as H,B as O,a2 as gt,z as j,r as E,H as _,_ as F,O as yt,W as bt,a3 as wt,a4 as Et,U as B,a5 as _t,a6 as Ot,o as St,Q as M,n as W}from"./index.BaVCXmir.js";import{P as C,B as Tt,s as D}from"./index.CDQpPXyE.js";import{x as U}from"./index.CLs7nh7g.js";import{C as Mt}from"./index.S_9XL1GF.js";import{S as nt,k as st,Q as $t,e as v,m as K,R as At,l as Pt,o as z,n as g,q as Ct,v as Dt,w as Qt,f as Lt,x as V,a as G,y as xt,z as Y,A as J,B as Rt,h as l,g as Ht,i as Ft}from"./utils.is9Ib0FR.js";import{a as Bt,r as at}from"./reactivity.esm-bundler.BQ12LWmY.js";import{j as Q,U as Nt,d as It,C as R,V as kt}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{a as f}from"./index.BpXFSz0M.js";import{d as qt,e as jt}from"./runtime-dom.esm-bundler.DXo4nCak.js";function A(i){"@babel/helpers - typeof";return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(i)}function Z(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(i);t&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(i,r).enumerable})),e.push.apply(e,o)}return e}function L(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Z(Object(e),!0).forEach(function(o){Wt(i,o,e[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):Z(Object(e)).forEach(function(o){Object.defineProperty(i,o,Object.getOwnPropertyDescriptor(e,o))})}return i}function Wt(i,t,e){return(t=Ut(t))in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function Ut(i){var t=Kt(i,"string");return A(t)=="symbol"?t:t+""}function Kt(i,t){if(A(i)!="object"||!i)return i;var e=i[Symbol.toPrimitive];if(e!==void 0){var o=e.call(i,t);if(A(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(i)}var zt={ripple:!1,inputStyle:null,inputVariant:null,locale:{startsWith:"Starts with",contains:"Contains",notContains:"Not contains",endsWith:"Ends with",equals:"Equals",notEquals:"Not equals",noFilter:"No Filter",lt:"Less than",lte:"Less than or equal to",gt:"Greater than",gte:"Greater than or equal to",dateIs:"Date is",dateIsNot:"Date is not",dateBefore:"Date is before",dateAfter:"Date is after",clear:"Clear",apply:"Apply",matchAll:"Match All",matchAny:"Match Any",addRule:"Add Rule",removeRule:"Remove Rule",accept:"Yes",reject:"No",choose:"Choose",upload:"Upload",cancel:"Cancel",completed:"Completed",pending:"Pending",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],chooseYear:"Choose Year",chooseMonth:"Choose Month",chooseDate:"Choose Date",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",prevHour:"Previous Hour",nextHour:"Next Hour",prevMinute:"Previous Minute",nextMinute:"Next Minute",prevSecond:"Previous Second",nextSecond:"Next Second",am:"am",pm:"pm",today:"Today",weekHeader:"Wk",firstDayOfWeek:0,showMonthAfterYear:!1,dateFormat:"mm/dd/yy",weak:"Weak",medium:"Medium",strong:"Strong",passwordPrompt:"Enter a password",emptyFilterMessage:"No results found",searchMessage:"{0} results are available",selectionMessage:"{0} items selected",emptySelectionMessage:"No selected item",emptySearchMessage:"No results found",fileChosenMessage:"{0} files",noFileChosenMessage:"No file chosen",emptyMessage:"No available options",aria:{trueLabel:"True",falseLabel:"False",nullLabel:"Not Selected",star:"1 star",stars:"{star} stars",selectAll:"All items selected",unselectAll:"All items unselected",close:"Close",previous:"Previous",next:"Next",navigation:"Navigation",scrollTop:"Scroll Top",moveTop:"Move Top",moveUp:"Move Up",moveDown:"Move Down",moveBottom:"Move Bottom",moveToTarget:"Move to Target",moveToSource:"Move to Source",moveAllToTarget:"Move All to Target",moveAllToSource:"Move All to Source",pageLabel:"Page {page}",firstPageLabel:"First Page",lastPageLabel:"Last Page",nextPageLabel:"Next Page",prevPageLabel:"Previous Page",rowsPerPageLabel:"Rows per page",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",selectRow:"Row Selected",unselectRow:"Row Unselected",expandRow:"Row Expanded",collapseRow:"Row Collapsed",showFilterMenu:"Show Filter Menu",hideFilterMenu:"Hide Filter Menu",filterOperator:"Filter Operator",filterConstraint:"Filter Constraint",editRow:"Row Edit",saveEdit:"Save Edit",cancelEdit:"Cancel Edit",listView:"List View",gridView:"Grid View",slide:"Slide",slideNumber:"{slideNumber}",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out",rotateRight:"Rotate Right",rotateLeft:"Rotate Left",listLabel:"Option List"}},filterMatchModeOptions:{text:[f.STARTS_WITH,f.CONTAINS,f.NOT_CONTAINS,f.ENDS_WITH,f.EQUALS,f.NOT_EQUALS],numeric:[f.EQUALS,f.NOT_EQUALS,f.LESS_THAN,f.LESS_THAN_OR_EQUAL_TO,f.GREATER_THAN,f.GREATER_THAN_OR_EQUAL_TO],date:[f.DATE_IS,f.DATE_IS_NOT,f.DATE_BEFORE,f.DATE_AFTER]},zIndex:{modal:1100,overlay:1e3,menu:1e3,tooltip:1100},theme:void 0,unstyled:!1,pt:void 0,ptOptions:{mergeSections:!0,mergeProps:!1},csp:{nonce:void 0}},Vt=Symbol();function Gt(i,t){var e={config:Bt(t)};return i.config.globalProperties.$primevue=e,i.provide(Vt,e),Yt(),Jt(i,e),e}var $=[];function Yt(){rt.clear(),$.forEach(function(i){return i?.()}),$=[]}function Jt(i,t){var e=at(!1),o=function(){var a;if(((a=t.config)===null||a===void 0?void 0:a.theme)!=="none"&&!H.isStyleNameLoaded("common")){var d,p,S=((d=O.getCommonTheme)===null||d===void 0?void 0:d.call(O))||{},T=S.primitive,h=S.semantic,m=S.global,b=S.style,w={nonce:(p=t.config)===null||p===void 0||(p=p.csp)===null||p===void 0?void 0:p.nonce};O.load(T?.css,L({name:"primitive-variables"},w)),O.load(h?.css,L({name:"semantic-variables"},w)),O.load(m?.css,L({name:"global-variables"},w)),O.loadStyle(L({name:"global-style"},w),b),H.setLoadedStyleName("common")}};rt.on("theme:change",function(u){e.value||(i.config.globalProperties.$primevue.config.theme=u,e.value=!0)});var r=Q(t.config,function(u,a){C.emit("config:change",{newValue:u,oldValue:a})},{immediate:!0,deep:!0}),n=Q(function(){return t.config.ripple},function(u,a){C.emit("config:ripple:change",{newValue:u,oldValue:a})},{immediate:!0,deep:!0}),s=Q(function(){return t.config.theme},function(u,a){e.value||H.setTheme(u),t.config.unstyled||o(),e.value=!1,C.emit("config:theme:change",{newValue:u,oldValue:a})},{immediate:!0,deep:!1}),c=Q(function(){return t.config.unstyled},function(u,a){!u&&t.config.theme&&o(),C.emit("config:unstyled:change",{newValue:u,oldValue:a})},{immediate:!0,deep:!0});$.push(r),$.push(n),$.push(s),$.push(c)}var Zt={install:function(t,e){var o=mt(zt,e);Gt(t,o)}},Xt={install:function(t){var e={add:function(r){P.emit("add",r)},remove:function(r){P.emit("remove",r)},removeGroup:function(r){P.emit("remove-group",r)},removeAllGroups:function(){P.emit("remove-all-groups")}};t.config.globalProperties.$toast=e,t.provide(ht,e)}},te={install:function(t){var e={require:function(r){q.emit("confirm",r)},close:function(){q.emit("close")}};t.config.globalProperties.$confirm=e,t.provide(vt,e)}},ee=`
    .p-tooltip {
        position: absolute;
        display: none;
        max-width: dt('tooltip.max.width');
    }

    .p-tooltip-right,
    .p-tooltip-left {
        padding: 0 dt('tooltip.gutter');
    }

    .p-tooltip-top,
    .p-tooltip-bottom {
        padding: dt('tooltip.gutter') 0;
    }

    .p-tooltip-text {
        white-space: pre-line;
        word-break: break-word;
        background: dt('tooltip.background');
        color: dt('tooltip.color');
        padding: dt('tooltip.padding');
        box-shadow: dt('tooltip.shadow');
        border-radius: dt('tooltip.border.radius');
    }

    .p-tooltip-arrow {
        position: absolute;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
    }

    .p-tooltip-right .p-tooltip-arrow {
        margin-top: calc(-1 * dt('tooltip.gutter'));
        border-width: dt('tooltip.gutter') dt('tooltip.gutter') dt('tooltip.gutter') 0;
        border-right-color: dt('tooltip.background');
    }

    .p-tooltip-left .p-tooltip-arrow {
        margin-top: calc(-1 * dt('tooltip.gutter'));
        border-width: dt('tooltip.gutter') 0 dt('tooltip.gutter') dt('tooltip.gutter');
        border-left-color: dt('tooltip.background');
    }

    .p-tooltip-top .p-tooltip-arrow {
        margin-left: calc(-1 * dt('tooltip.gutter'));
        border-width: dt('tooltip.gutter') dt('tooltip.gutter') 0 dt('tooltip.gutter');
        border-top-color: dt('tooltip.background');
        border-bottom-color: dt('tooltip.background');
    }

    .p-tooltip-bottom .p-tooltip-arrow {
        margin-left: calc(-1 * dt('tooltip.gutter'));
        border-width: 0 dt('tooltip.gutter') dt('tooltip.gutter') dt('tooltip.gutter');
        border-top-color: dt('tooltip.background');
        border-bottom-color: dt('tooltip.background');
    }
`,ie={root:"p-tooltip p-component",arrow:"p-tooltip-arrow",text:"p-tooltip-text"},oe=O.extend({name:"tooltip-directive",style:ee,classes:ie}),re=Tt.extend({style:oe});function ne(i,t){return le(i)||ue(i,t)||ae(i,t)||se()}function se(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ae(i,t){if(i){if(typeof i=="string")return X(i,t);var e={}.toString.call(i).slice(8,-1);return e==="Object"&&i.constructor&&(e=i.constructor.name),e==="Map"||e==="Set"?Array.from(i):e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?X(i,t):void 0}}function X(i,t){(t==null||t>i.length)&&(t=i.length);for(var e=0,o=Array(t);e<t;e++)o[e]=i[e];return o}function ue(i,t){var e=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(e!=null){var o,r,n,s,c=[],u=!0,a=!1;try{if(n=(e=e.call(i)).next,t!==0)for(;!(u=(o=n.call(e)).done)&&(c.push(o.value),c.length!==t);u=!0);}catch(d){a=!0,r=d}finally{try{if(!u&&e.return!=null&&(s=e.return(),Object(s)!==s))return}finally{if(a)throw r}}return c}}function le(i){if(Array.isArray(i))return i}function tt(i,t,e){return(t=ce(t))in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function ce(i){var t=de(i,"string");return y(t)=="symbol"?t:t+""}function de(i,t){if(y(i)!="object"||!i)return i;var e=i[Symbol.toPrimitive];if(e!==void 0){var o=e.call(i,t);if(y(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(i)}function y(i){"@babel/helpers - typeof";return y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(i)}var pe=re.extend("tooltip",{beforeMount:function(t,e){var o,r=this.getTarget(t);if(r.$_ptooltipModifiers=this.getModifiers(e),e.value){if(typeof e.value=="string")r.$_ptooltipValue=e.value,r.$_ptooltipDisabled=!1,r.$_ptooltipEscape=!0,r.$_ptooltipClass=null,r.$_ptooltipFitContent=!0,r.$_ptooltipIdAttr=D("pv_id")+"_tooltip",r.$_ptooltipShowDelay=0,r.$_ptooltipHideDelay=0,r.$_ptooltipAutoHide=!0;else if(y(e.value)==="object"&&e.value){if(W(e.value.value)||e.value.value.trim()==="")return;r.$_ptooltipValue=e.value.value,r.$_ptooltipDisabled=!!e.value.disabled===e.value.disabled?e.value.disabled:!1,r.$_ptooltipEscape=!!e.value.escape===e.value.escape?e.value.escape:!0,r.$_ptooltipClass=e.value.class||"",r.$_ptooltipFitContent=!!e.value.fitContent===e.value.fitContent?e.value.fitContent:!0,r.$_ptooltipIdAttr=e.value.id||D("pv_id")+"_tooltip",r.$_ptooltipShowDelay=e.value.showDelay||0,r.$_ptooltipHideDelay=e.value.hideDelay||0,r.$_ptooltipAutoHide=!!e.value.autoHide===e.value.autoHide?e.value.autoHide:!0}}else return;r.$_ptooltipZIndex=(o=e.instance.$primevue)===null||o===void 0||(o=o.config)===null||o===void 0||(o=o.zIndex)===null||o===void 0?void 0:o.tooltip,this.bindEvents(r,e),t.setAttribute("data-pd-tooltip",!0)},updated:function(t,e){var o=this.getTarget(t);if(o.$_ptooltipModifiers=this.getModifiers(e),this.unbindEvents(o),!!e.value){if(typeof e.value=="string")o.$_ptooltipValue=e.value,o.$_ptooltipDisabled=!1,o.$_ptooltipEscape=!0,o.$_ptooltipClass=null,o.$_ptooltipIdAttr=o.$_ptooltipIdAttr||D("pv_id")+"_tooltip",o.$_ptooltipShowDelay=0,o.$_ptooltipHideDelay=0,o.$_ptooltipAutoHide=!0,this.bindEvents(o,e);else if(y(e.value)==="object"&&e.value)if(W(e.value.value)||e.value.value.trim()===""){this.unbindEvents(o,e);return}else o.$_ptooltipValue=e.value.value,o.$_ptooltipDisabled=!!e.value.disabled===e.value.disabled?e.value.disabled:!1,o.$_ptooltipEscape=!!e.value.escape===e.value.escape?e.value.escape:!0,o.$_ptooltipClass=e.value.class||"",o.$_ptooltipFitContent=!!e.value.fitContent===e.value.fitContent?e.value.fitContent:!0,o.$_ptooltipIdAttr=e.value.id||o.$_ptooltipIdAttr||D("pv_id")+"_tooltip",o.$_ptooltipShowDelay=e.value.showDelay||0,o.$_ptooltipHideDelay=e.value.hideDelay||0,o.$_ptooltipAutoHide=!!e.value.autoHide===e.value.autoHide?e.value.autoHide:!0,this.bindEvents(o,e)}},unmounted:function(t,e){var o=this.getTarget(t);this.hide(t,0),this.remove(o),this.unbindEvents(o,e),o.$_ptooltipScrollHandler&&(o.$_ptooltipScrollHandler.destroy(),o.$_ptooltipScrollHandler=null)},timer:void 0,methods:{bindEvents:function(t,e){var o=this,r=t.$_ptooltipModifiers;r.focus?(t.$_ptooltipFocusEvent=function(n){return o.onFocus(n,e)},t.$_ptooltipBlurEvent=this.onBlur.bind(this),t.addEventListener("focus",t.$_ptooltipFocusEvent),t.addEventListener("blur",t.$_ptooltipBlurEvent)):(t.$_ptooltipMouseEnterEvent=function(n){return o.onMouseEnter(n,e)},t.$_ptooltipMouseLeaveEvent=this.onMouseLeave.bind(this),t.$_ptooltipClickEvent=this.onClick.bind(this),t.addEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),t.addEventListener("mouseleave",t.$_ptooltipMouseLeaveEvent),t.addEventListener("click",t.$_ptooltipClickEvent)),t.$_ptooltipKeydownEvent=this.onKeydown.bind(this),t.addEventListener("keydown",t.$_ptooltipKeydownEvent),t.$_pWindowResizeEvent=this.onWindowResize.bind(this,t)},unbindEvents:function(t){var e=t.$_ptooltipModifiers;e.focus?(t.removeEventListener("focus",t.$_ptooltipFocusEvent),t.$_ptooltipFocusEvent=null,t.removeEventListener("blur",t.$_ptooltipBlurEvent),t.$_ptooltipBlurEvent=null):(t.removeEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),t.$_ptooltipMouseEnterEvent=null,t.removeEventListener("mouseleave",t.$_ptooltipMouseLeaveEvent),t.$_ptooltipMouseLeaveEvent=null,t.removeEventListener("click",t.$_ptooltipClickEvent),t.$_ptooltipClickEvent=null),t.removeEventListener("keydown",t.$_ptooltipKeydownEvent),window.removeEventListener("resize",t.$_pWindowResizeEvent),t.$_ptooltipId&&this.remove(t)},bindScrollListener:function(t){var e=this;t.$_ptooltipScrollHandler||(t.$_ptooltipScrollHandler=new Mt(t,function(){e.hide(t)})),t.$_ptooltipScrollHandler.bindScrollListener()},unbindScrollListener:function(t){t.$_ptooltipScrollHandler&&t.$_ptooltipScrollHandler.unbindScrollListener()},onMouseEnter:function(t,e){var o=t.currentTarget,r=o.$_ptooltipShowDelay;this.show(o,e,r)},onMouseLeave:function(t){var e=t.currentTarget,o=e.$_ptooltipHideDelay,r=e.$_ptooltipAutoHide;if(r)this.hide(e,o);else{var n=M(t.target,"data-pc-name")==="tooltip"||M(t.target,"data-pc-section")==="arrow"||M(t.target,"data-pc-section")==="text"||M(t.relatedTarget,"data-pc-name")==="tooltip"||M(t.relatedTarget,"data-pc-section")==="arrow"||M(t.relatedTarget,"data-pc-section")==="text";!n&&this.hide(e,o)}},onFocus:function(t,e){var o=t.currentTarget,r=o.$_ptooltipShowDelay;this.show(o,e,r)},onBlur:function(t){var e=t.currentTarget,o=e.$_ptooltipHideDelay;this.hide(e,o)},onClick:function(t){var e=t.currentTarget,o=e.$_ptooltipHideDelay;this.hide(e,o)},onKeydown:function(t){var e=t.currentTarget,o=e.$_ptooltipHideDelay;t.code==="Escape"&&this.hide(t.currentTarget,o)},onWindowResize:function(t){St()||this.hide(t),window.removeEventListener("resize",t.$_pWindowResizeEvent)},tooltipActions:function(t,e){if(!(t.$_ptooltipDisabled||!_t(t))){var o=this.create(t,e);this.align(t),!this.isUnstyled()&&Ot(o,250);var r=this;window.addEventListener("resize",t.$_pWindowResizeEvent),o.addEventListener("mouseleave",function n(){r.hide(t),o.removeEventListener("mouseleave",n),t.removeEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),setTimeout(function(){return t.addEventListener("mouseenter",t.$_ptooltipMouseEnterEvent)},50)}),this.bindScrollListener(t),U.set("tooltip",o,t.$_ptooltipZIndex)}},show:function(t,e,o){var r=this;o!==void 0?this.timer=setTimeout(function(){return r.tooltipActions(t,e)},o):this.tooltipActions(t,e)},tooltipRemoval:function(t){this.remove(t),this.unbindScrollListener(t),window.removeEventListener("resize",t.$_pWindowResizeEvent)},hide:function(t,e){var o=this;clearTimeout(this.timer),e!==void 0?setTimeout(function(){return o.tooltipRemoval(t)},e):this.tooltipRemoval(t)},getTooltipElement:function(t){return document.getElementById(t.$_ptooltipId)},getArrowElement:function(t){var e=this.getTooltipElement(t);return j(e,'[data-pc-section="arrow"]')},create:function(t){var e=t.$_ptooltipModifiers,o=B("div",{class:!this.isUnstyled()&&this.cx("arrow"),"p-bind":this.ptm("arrow",{context:e})}),r=B("div",{class:!this.isUnstyled()&&this.cx("text"),"p-bind":this.ptm("text",{context:e})});t.$_ptooltipEscape?(r.innerHTML="",r.appendChild(document.createTextNode(t.$_ptooltipValue))):r.innerHTML=t.$_ptooltipValue;var n=B("div",tt(tt({id:t.$_ptooltipIdAttr,role:"tooltip",style:{display:"inline-block",width:t.$_ptooltipFitContent?"fit-content":void 0,pointerEvents:!this.isUnstyled()&&t.$_ptooltipAutoHide&&"none"},class:[!this.isUnstyled()&&this.cx("root"),t.$_ptooltipClass]},this.$attrSelector,""),"p-bind",this.ptm("root",{context:e})),o,r);return document.body.appendChild(n),t.$_ptooltipId=n.id,this.$el=n,n},remove:function(t){if(t){var e=this.getTooltipElement(t);e&&e.parentElement&&(U.clear(e),document.body.removeChild(e)),t.$_ptooltipId=null}},align:function(t){var e=t.$_ptooltipModifiers;e.top?(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignTop(t))):e.left?(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignLeft(t))))):e.bottom?(this.alignBottom(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&this.alignBottom(t))):(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignRight(t)))))},getHostOffset:function(t){var e=t.getBoundingClientRect(),o=e.left+wt(),r=e.top+Et();return{left:o,top:r}},alignRight:function(t){this.preAlign(t,"right");var e=this.getTooltipElement(t),o=this.getArrowElement(t),r=this.getHostOffset(t),n=r.left+E(t),s=r.top+(_(t)-_(e))/2;e.style.left=n+"px",e.style.top=s+"px",o.style.top="50%",o.style.right=null,o.style.bottom=null,o.style.left="0"},alignLeft:function(t){this.preAlign(t,"left");var e=this.getTooltipElement(t),o=this.getArrowElement(t),r=this.getHostOffset(t),n=r.left-E(e),s=r.top+(_(t)-_(e))/2;e.style.left=n+"px",e.style.top=s+"px",o.style.top="50%",o.style.right="0",o.style.bottom=null,o.style.left=null},alignTop:function(t){this.preAlign(t,"top");var e=this.getTooltipElement(t),o=this.getArrowElement(t),r=E(e),n=E(t),s=F(),c=s.width,u=this.getHostOffset(t),a=u.left+(n-r)/2,d=u.top-_(e);a<0?a=0:a+r>c&&(a=Math.floor(u.left+n-r)),e.style.left=a+"px",e.style.top=d+"px";var p=u.left-this.getHostOffset(e).left+n/2;o.style.top=null,o.style.right=null,o.style.bottom="0",o.style.left=p+"px"},alignBottom:function(t){this.preAlign(t,"bottom");var e=this.getTooltipElement(t),o=this.getArrowElement(t),r=E(e),n=E(t),s=F(),c=s.width,u=this.getHostOffset(t),a=u.left+(n-r)/2,d=u.top+_(t);a<0?a=0:a+r>c&&(a=Math.floor(u.left+n-r)),e.style.left=a+"px",e.style.top=d+"px";var p=u.left-this.getHostOffset(e).left+n/2;o.style.top="0",o.style.right=null,o.style.bottom=null,o.style.left=p+"px"},preAlign:function(t,e){var o=this.getTooltipElement(t);o.style.left="-999px",o.style.top="-999px",yt(o,"p-tooltip-".concat(o.$_ptooltipPosition)),!this.isUnstyled()&&bt(o,"p-tooltip-".concat(e)),o.$_ptooltipPosition=e,o.setAttribute("data-p-position",e)},isOutOfBounds:function(t){var e=this.getTooltipElement(t),o=e.getBoundingClientRect(),r=o.top,n=o.left,s=E(e),c=_(e),u=F();return n+s>u.width||n<0||r<0||r+c>u.height},getTarget:function(t){var e;return gt(t,"p-inputwrapper")&&(e=j(t,"input"))!==null&&e!==void 0?e:t},getModifiers:function(t){return t.modifiers&&Object.keys(t.modifiers).length?t.modifiers:t.arg&&y(t.arg)==="object"?Object.entries(t.arg).reduce(function(e,o){var r=ne(o,2),n=r[0],s=r[1];return(n==="event"||n==="position")&&(e[s]=!0),e},{}):{}}}}),ut=class extends nt{constructor(t={}){super(),this.config=t,this.#t=new Map}#t;build(t,e,o){const r=e.queryKey,n=e.queryHash??st(r,e);let s=this.get(n);return s||(s=new $t({client:t,queryKey:r,queryHash:n,options:t.defaultQueryOptions(e),state:o,defaultOptions:t.getQueryDefaults(r)}),this.add(s)),s}add(t){this.#t.has(t.queryHash)||(this.#t.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=this.#t.get(t.queryHash);e&&(t.destroy(),e===t&&this.#t.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){v.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#t.get(t)}getAll(){return[...this.#t.values()]}find(t){const e={exact:!0,...t};return this.getAll().find(o=>K(e,o))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter(o=>K(t,o)):e}notify(t){v.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){v.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){v.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},fe=class extends At{#t;#e;#i;constructor(i){super(),this.mutationId=i.mutationId,this.#e=i.mutationCache,this.#t=[],this.state=i.state||he(),this.setOptions(i.options),this.scheduleGc()}setOptions(i){this.options=i,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(i){this.#t.includes(i)||(this.#t.push(i),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:i}))}removeObserver(i){this.#t=this.#t.filter(t=>t!==i),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:i})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(i){const t=()=>{this.#o({type:"continue"})};this.#i=Pt({fn:()=>this.options.mutationFn?this.options.mutationFn(i):Promise.reject(new Error("No mutationFn found")),onFail:(r,n)=>{this.#o({type:"failed",failureCount:r,error:n})},onPause:()=>{this.#o({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const e=this.state.status==="pending",o=!this.#i.canStart();try{if(e)t();else{this.#o({type:"pending",variables:i,isPaused:o}),await this.#e.config.onMutate?.(i,this);const n=await this.options.onMutate?.(i);n!==this.state.context&&this.#o({type:"pending",context:n,variables:i,isPaused:o})}const r=await this.#i.start();return await this.#e.config.onSuccess?.(r,i,this.state.context,this),await this.options.onSuccess?.(r,i,this.state.context),await this.#e.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,i,this.state.context),this.#o({type:"success",data:r}),r}catch(r){try{throw await this.#e.config.onError?.(r,i,this.state.context,this),await this.options.onError?.(r,i,this.state.context),await this.#e.config.onSettled?.(void 0,r,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,r,i,this.state.context),r}finally{this.#o({type:"error",error:r})}}finally{this.#e.runNext(this)}}#o(i){const t=e=>{switch(i.type){case"failed":return{...e,failureCount:i.failureCount,failureReason:i.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:i.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:i.isPaused,status:"pending",variables:i.variables,submittedAt:Date.now()};case"success":return{...e,data:i.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:i.error,failureCount:e.failureCount+1,failureReason:i.error,isPaused:!1,status:"error"}}};this.state=t(this.state),v.batch(()=>{this.#t.forEach(e=>{e.onMutationUpdate(i)}),this.#e.notify({mutation:this,type:"updated",action:i})})}};function he(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var lt=class extends nt{constructor(t={}){super(),this.config=t,this.#t=new Set,this.#e=new Map,this.#i=0}#t;#e;#i;build(t,e,o){const r=new fe({mutationCache:this,mutationId:++this.#i,options:t.defaultMutationOptions(e),state:o});return this.add(r),r}add(t){this.#t.add(t);const e=x(t);if(typeof e=="string"){const o=this.#e.get(e);o?o.push(t):this.#e.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#t.delete(t)){const e=x(t);if(typeof e=="string"){const o=this.#e.get(e);if(o)if(o.length>1){const r=o.indexOf(t);r!==-1&&o.splice(r,1)}else o[0]===t&&this.#e.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=x(t);if(typeof e=="string"){const r=this.#e.get(e)?.find(n=>n.state.status==="pending");return!r||r===t}else return!0}runNext(t){const e=x(t);return typeof e=="string"?this.#e.get(e)?.find(r=>r!==t&&r.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){v.batch(()=>{this.#t.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(t){const e={exact:!0,...t};return this.getAll().find(o=>z(e,o))}findAll(t={}){return this.getAll().filter(e=>z(t,e))}notify(t){v.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){const t=this.getAll().filter(e=>e.state.isPaused);return v.batch(()=>Promise.all(t.map(e=>e.continue().catch(g))))}};function x(i){return i.options.scope?.id}function et(i){return{onFetch:(t,e)=>{const o=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],s=t.state.data?.pageParams||[];let c={pages:[],pageParams:[]},u=0;const a=async()=>{let d=!1;const p=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(t.signal.aborted?d=!0:t.signal.addEventListener("abort",()=>{d=!0}),t.signal)})},S=Ct(t.options,t.fetchOptions),T=async(h,m,b)=>{if(d)return Promise.reject();if(m==null&&h.pages.length)return Promise.resolve(h);const pt=(()=>{const k={client:t.client,queryKey:t.queryKey,pageParam:m,direction:b?"backward":"forward",meta:t.options.meta};return p(k),k})(),ft=await S(pt),{maxPages:N}=t.options,I=b?Dt:Qt;return{pages:I(h.pages,ft,N),pageParams:I(h.pageParams,m,N)}};if(r&&n.length){const h=r==="backward",m=h?ve:it,b={pages:n,pageParams:s},w=m(o,b);c=await T(b,w,h)}else{const h=i??n.length;do{const m=u===0?s[0]??o.initialPageParam:it(o,c);if(u>0&&m==null)break;c=await T(c,m),u++}while(u<h)}return c};t.options.persister?t.fetchFn=()=>t.options.persister?.(a,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},e):t.fetchFn=a}}}function it(i,{pages:t,pageParams:e}){const o=t.length-1;return t.length>0?i.getNextPageParam(t[o],t,e[o],e):void 0}function ve(i,{pages:t,pageParams:e}){return t.length>0?i.getPreviousPageParam?.(t[0],t,e[0],e):void 0}var me=class{#t;#e;#i;#o;#n;#r;#s;#a;constructor(t={}){this.#t=t.queryCache||new ut,this.#e=t.mutationCache||new lt,this.#i=t.defaultOptions||{},this.#o=new Map,this.#n=new Map,this.#r=0}mount(){this.#r++,this.#r===1&&(this.#s=Lt.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#a=V.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#r--,this.#r===0&&(this.#s?.(),this.#s=void 0,this.#a?.(),this.#a=void 0)}isFetching(t){return this.#t.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return this.#t.get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),o=this.#t.build(this,e),r=o.state.data;return r===void 0?this.fetchQuery(t):(t.revalidateIfStale&&o.isStaleByTime(G(e.staleTime,o))&&this.prefetchQuery(e),Promise.resolve(r))}getQueriesData(t){return this.#t.findAll(t).map(({queryKey:e,state:o})=>{const r=o.data;return[e,r]})}setQueryData(t,e,o){const r=this.defaultQueryOptions({queryKey:t}),s=this.#t.get(r.queryHash)?.state.data,c=xt(e,s);if(c!==void 0)return this.#t.build(this,r).setData(c,{...o,manual:!0})}setQueriesData(t,e,o){return v.batch(()=>this.#t.findAll(t).map(({queryKey:r})=>[r,this.setQueryData(r,e,o)]))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return this.#t.get(e.queryHash)?.state}removeQueries(t){const e=this.#t;v.batch(()=>{e.findAll(t).forEach(o=>{e.remove(o)})})}resetQueries(t,e){const o=this.#t;return v.batch(()=>(o.findAll(t).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){const o={revert:!0,...e},r=v.batch(()=>this.#t.findAll(t).map(n=>n.cancel(o)));return Promise.all(r).then(g).catch(g)}invalidateQueries(t,e={}){return v.batch(()=>(this.#t.findAll(t).forEach(o=>{o.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e)))}refetchQueries(t,e={}){const o={...e,cancelRefetch:e.cancelRefetch??!0},r=v.batch(()=>this.#t.findAll(t).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let s=n.fetch(void 0,o);return o.throwOnError||(s=s.catch(g)),n.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(g)}fetchQuery(t){const e=this.defaultQueryOptions(t);e.retry===void 0&&(e.retry=!1);const o=this.#t.build(this,e);return o.isStaleByTime(G(e.staleTime,o))?o.fetch(e):Promise.resolve(o.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(g).catch(g)}fetchInfiniteQuery(t){return t.behavior=et(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(g).catch(g)}ensureInfiniteQueryData(t){return t.behavior=et(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return V.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#i}setDefaultOptions(t){this.#i=t}setQueryDefaults(t,e){this.#o.set(Y(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...this.#o.values()],o={};return e.forEach(r=>{J(t,r.queryKey)&&Object.assign(o,r.defaultOptions)}),o}setMutationDefaults(t,e){this.#n.set(Y(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...this.#n.values()],o={};return e.forEach(r=>{J(t,r.mutationKey)&&Object.assign(o,r.defaultOptions)}),o}defaultQueryOptions(t){if(t._defaulted)return t;const e={...this.#i.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=st(e.queryKey,e)),e.refetchOnReconnect===void 0&&(e.refetchOnReconnect=e.networkMode!=="always"),e.throwOnError===void 0&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===Rt&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#i.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}},ge=class extends ut{find(i){return super.find(l(i))}findAll(i={}){return super.findAll(l(i))}},ye=class extends lt{find(i){return super.find(l(i))}findAll(i={}){return super.findAll(l(i))}},ct=class extends me{constructor(i={}){const t={defaultOptions:i.defaultOptions,queryCache:i.queryCache||new ge,mutationCache:i.mutationCache||new ye};super(t),this.isRestoring=at(!1)}isFetching(i={}){return super.isFetching(l(i))}isMutating(i={}){return super.isMutating(l(i))}getQueryData(i){return super.getQueryData(l(i))}ensureQueryData(i){return super.ensureQueryData(l(i))}getQueriesData(i){return super.getQueriesData(l(i))}setQueryData(i,t,e={}){return super.setQueryData(l(i),t,l(e))}setQueriesData(i,t,e={}){return super.setQueriesData(l(i),t,l(e))}getQueryState(i){return super.getQueryState(l(i))}removeQueries(i={}){return super.removeQueries(l(i))}resetQueries(i={},t={}){return super.resetQueries(l(i),l(t))}cancelQueries(i={},t={}){return super.cancelQueries(l(i),l(t))}invalidateQueries(i={},t={}){const e=l(i),o=l(t);if(super.invalidateQueries({...e,refetchType:"none"},o),e.refetchType==="none")return Promise.resolve();const r={...e,type:e.refetchType??e.type??"active"};return Nt().then(()=>super.refetchQueries(r,o))}refetchQueries(i={},t={}){return super.refetchQueries(l(i),l(t))}fetchQuery(i){return super.fetchQuery(l(i))}prefetchQuery(i){return super.prefetchQuery(l(i))}fetchInfiniteQuery(i){return super.fetchInfiniteQuery(l(i))}prefetchInfiniteQuery(i){return super.prefetchInfiniteQuery(l(i))}setDefaultOptions(i){super.setDefaultOptions(l(i))}setQueryDefaults(i,t){super.setQueryDefaults(l(i),l(t))}getQueryDefaults(i){return super.getQueryDefaults(l(i))}setMutationDefaults(i,t){super.setMutationDefaults(l(i),l(t))}getMutationDefaults(i){return super.getMutationDefaults(l(i))}},be={install:(i,t={})=>{const e=Ht(t.queryClientKey);let o;if("queryClient"in t&&t.queryClient)o=t.queryClient;else{const s="queryClientConfig"in t?t.queryClientConfig:void 0;o=new ct(s)}Ft||o.mount();let r=()=>{};if(t.clientPersister){o.isRestoring&&(o.isRestoring.value=!0);const[s,c]=t.clientPersister(o);r=s,c.then(()=>{o.isRestoring&&(o.isRestoring.value=!1),t.clientPersisterOnSuccess?.(o)})}const n=()=>{o.unmount(),r()};if(i.onUnmount)i.onUnmount(n);else{const s=i.unmount;i.unmount=function(){n(),s()}}i.provide(e,o)}};const dt=i=>{i.use(Zt,{unstyled:!0});const t=new ct({defaultOptions:{queries:{staleTime:3e4,gcTime:5*6e4,retry:1,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}});i.use(be,{queryClient:t}),i.use(Xt),i.use(te),i.directive("tooltip",pe)},we=Object.freeze(Object.defineProperty({__proto__:null,default:dt},Symbol.toStringTag,{value:"Module"})),Ee=async i=>{"default"in we&&await dt(i)},_e=It({props:{value:String,name:String,hydrate:{type:Boolean,default:!0}},setup({name:i,value:t,hydrate:e}){if(!t)return()=>null;let o=e?"astro-slot":"astro-static-slot";return()=>R(o,{name:i,innerHTML:t})}});var Oe=_e;let ot=new WeakMap;var Ie=i=>async(t,e,o,{client:r})=>{if(!i.hasAttribute("ssr"))return;const n=t.name?`${t.name} Host`:void 0,s={};for(const[d,p]of Object.entries(o))s[d]=()=>R(Oe,{value:p,name:d==="default"?void 0:d});const c=r!=="only",u=c?qt:jt;let a=ot.get(i);if(a)a.props=e,a.slots=s,a.component.$forceUpdate();else{a={props:e,slots:s};const d=u({name:n,render(){let p=R(t,a.props,a.slots);return a.component=this,Se(t.setup)&&(p=R(kt,null,p)),p}});d.config.idPrefix=i.getAttribute("prefix")??void 0,await Ee(d),d.mount(i,c),ot.set(i,a),i.addEventListener("astro:unmount",()=>d.unmount(),{once:!0})}};function Se(i){const t=i?.constructor;return t&&t.name==="AsyncFunction"}export{Ie as default};

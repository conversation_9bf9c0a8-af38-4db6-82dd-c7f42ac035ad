<template>
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div
      v-for="(meteor, index) in meteors"
      :key="index"
      class="absolute"
      :style="{
        left: `${meteor.left}%`,
        top: `${meteor.top}%`,
        animationDelay: `${meteor.delay}s`,
        animationDuration: `${meteor.duration}s`
      }"
    >
      <div
        class="meteor"
        :style="{
          width: `${meteor.size}px`,
          height: `${meteor.size}px`,
          background: `linear-gradient(45deg, ${meteor.color}, transparent)`,
          transform: `rotate(${meteor.angle}deg)`
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Meteor {
  left: number
  top: number
  size: number
  delay: number
  duration: number
  angle: number
  color: string
}

const meteors = ref<Meteor[]>([])

const colors = [
  'rgba(59, 130, 246, 0.8)', // blue
  'rgba(147, 51, 234, 0.8)', // purple
  'rgba(236, 72, 153, 0.8)', // pink
  'rgba(34, 197, 94, 0.8)',  // green
]

const generateMeteor = (): Meteor => ({
  left: Math.random() * 100,
  top: Math.random() * 100,
  size: Math.random() * 3 + 1,
  delay: Math.random() * 10,
  duration: Math.random() * 3 + 2,
  angle: Math.random() * 360,
  color: colors[Math.floor(Math.random() * colors.length)]
})

onMounted(() => {
  // Generate meteors
  for (let i = 0; i < 20; i++) {
    meteors.value.push(generateMeteor())
  }
})
</script>

<style scoped>
.meteor {
  border-radius: 50%;
  animation: meteor-fall infinite linear;
  box-shadow: 0 0 10px currentColor;
}

@keyframes meteor-fall {
  0% {
    opacity: 1;
    transform: translateY(-100vh) translateX(-100px) rotate(var(--angle, 45deg));
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) translateX(100px) rotate(var(--angle, 45deg));
  }
}
</style>
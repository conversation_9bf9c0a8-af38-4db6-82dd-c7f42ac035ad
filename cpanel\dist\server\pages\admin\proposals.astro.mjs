import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { I as Icon, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { defineComponent, useSSRContext, ref, computed, watch, onMounted, resolveDirective, mergeProps, withCtx, createVNode, createBlock, openBlock, Fragment, renderList, toDisplayString, createCommentVNode, withDirectives, createTextVNode } from 'vue';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import { T as Tag } from '../../chunks/Tag_B6nH2bAR.mjs';
import { V as VToggleSwitch } from '../../chunks/ToggleSwitch_DRN4BNJ9.mjs';
import { M as MatchingDetailsGrid, u as useMatchingLabels } from '../../chunks/MatchingDetailsGrid_qP1_u1_F.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_CjmFMz0m.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderAttr, ssrGetDirectiveProps, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { _ as _export_sfc } from '../../chunks/ClientRouter_avhRMbqw.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ProposalsList",
  setup(__props, { expose: __expose }) {
    __expose();
    const { matching, loading } = useTrpc();
    const { getAccuracyLabel, getAccuracySeverity } = useMatchingLabels();
    const status = ref("PENDING");
    const statusOptions = [
      { label: "\u041E\u0436\u0438\u0434\u0430\u044E\u0442", value: "PENDING" },
      { label: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u043E", value: "APPROVED" },
      { label: "\u041E\u0442\u043A\u043B\u043E\u043D\u0435\u043D\u043E", value: "REJECTED" },
      { label: "\u0418\u043D\u0432\u0430\u043B\u0438\u0434\u0438\u0440\u043E\u0432\u0430\u043D\u043E", value: "INVALIDATED" }
    ];
    const items = ref([]);
    const total = ref(0);
    const take = ref(20);
    const takeOptions = [
      { label: "10", value: 10 },
      { label: "20", value: 20 },
      { label: "50", value: 50 }
    ];
    const skip = ref(0);
    const expandAll = ref(false);
    const pageStart = computed(() => total.value === 0 ? 0 : skip.value + 1);
    const pageEnd = computed(() => Math.min(skip.value + take.value, total.value));
    const load = async () => {
      const res = await matching.listProposals({ status: status.value, skip: skip.value, take: take.value });
      if (res) {
        items.value = res.items;
        total.value = res.total;
      }
    };
    watch(status, async () => {
      skip.value = 0;
      await load();
    });
    watch(take, async () => {
      skip.value = 0;
      await load();
    });
    const next = async () => {
      if (skip.value + take.value < total.value) {
        skip.value += take.value;
        await load();
      }
    };
    const prev = async () => {
      if (skip.value > 0) {
        skip.value = Math.max(0, skip.value - take.value);
        await load();
      }
    };
    const approve = async (id, accuracy, notes) => {
      const res = await matching.approveProposal({ id, override: { accuracy, notes } });
      if (res) await load();
    };
    const reject = async (id) => {
      const res = await matching.rejectProposal({ id });
      if (res) await load();
    };
    const generate = async () => {
      const res = await matching.generateProposals({ take: 50 });
      if (res) await load();
    };
    onMounted(load);
    const summarizeDetails = (details = []) => {
      let exact = 0, near = 0, legacy = 0, tol = 0;
      for (const d of details) {
        const kind = String(d?.kind || "");
        if (kind.includes("EXACT")) exact++;
        if (kind.includes("NEAR")) near++;
        if (kind.includes("LEGACY")) legacy++;
        if (kind.includes("WITHIN_TOLERANCE")) tol++;
      }
      return { total: details.length, exact, near, legacy, tol };
    };
    const __returned__ = { matching, loading, getAccuracyLabel, getAccuracySeverity, status, statusOptions, items, total, take, takeOptions, skip, expandAll, pageStart, pageEnd, load, next, prev, approve, reject, generate, summarizeDetails, VButton: Button, VCard: Card, VSelect: Select, VTag: Tag, VToggleSwitch, MatchingDetailsGrid, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _directive_tooltip = resolveDirective("tooltip");
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-4" }, _attrs))}><div class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between"><div class="flex items-center gap-2">`);
  _push(ssrRenderComponent($setup["VSelect"], {
    modelValue: $setup.status,
    "onUpdate:modelValue": ($event) => $setup.status = $event,
    options: $setup.statusOptions,
    optionLabel: "label",
    optionValue: "value",
    class: "w-56"
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    loading: $setup.loading,
    label: "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",
    onClick: $setup.load
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-refresh",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-refresh",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`<div class="hidden md:block text-sm text-surface-500"> \u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E ${ssrInterpolate($setup.pageStart)}\u2013${ssrInterpolate($setup.pageEnd)} \u0438\u0437 ${ssrInterpolate($setup.total)}</div></div><div class="flex items-center gap-3"><div class="flex items-center gap-2"><span class="text-xs text-surface-500">\u041D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0435</span>`);
  _push(ssrRenderComponent($setup["VSelect"], {
    modelValue: $setup.take,
    "onUpdate:modelValue": ($event) => $setup.take = $event,
    options: $setup.takeOptions,
    optionLabel: "label",
    optionValue: "value",
    class: "w-28"
  }, null, _parent));
  _push(`</div><div class="flex items-center gap-2"><span class="text-xs text-surface-500">\u0420\u0430\u0441\u043A\u0440\u044B\u0442\u044C \u0434\u0435\u0442\u0430\u043B\u0438</span>`);
  _push(ssrRenderComponent($setup["VToggleSwitch"], {
    modelValue: $setup.expandAll,
    "onUpdate:modelValue": ($event) => $setup.expandAll = $event
  }, null, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["VButton"], {
    label: "\u0421\u0433\u0435\u043D\u0435\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
    severity: "secondary",
    onClick: $setup.generate
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-cog",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-cog",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.loading) {
          _push2(`<div class="py-10 text-center text-surface-500"${_scopeId}>\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...</div>`);
        } else if ($setup.items.length === 0) {
          _push2(`<div class="py-10 text-center text-surface-500"${_scopeId}>\u041D\u0435\u0442 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439</div>`);
        } else {
          _push2(`<div class="divide-y divide-surface-border"${_scopeId}><!--[-->`);
          ssrRenderList($setup.items, (p) => {
            _push2(`<div class="py-3 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"${_scopeId}><div class="md:col-span-1"${_scopeId}><div class="text-sm text-surface-500"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F</div><div class="font-mono font-semibold"${_scopeId}>${ssrInterpolate(p.catalogItem.sku)} \u2014 ${ssrInterpolate(p.catalogItem.brand?.name)}</div><a${ssrRenderAttr("href", `/admin/parts/${p.part.id}`)} class="text-xs text-surface-500"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u0430: ${ssrInterpolate(p.part.name || "#" + p.part.id)}</a></div><div class="md:col-span-1"${_scopeId}><div class="text-sm text-surface-500 mb-1"${_scopeId}>\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435</div>`);
            _push2(ssrRenderComponent($setup["VTag"], {
              value: $setup.getAccuracyLabel(p.accuracySuggestion),
              severity: $setup.getAccuracySeverity(p.accuracySuggestion)
            }, null, _parent2, _scopeId));
            if (p.notesSuggestion) {
              _push2(`<div class="text-xs text-surface-500 mt-1"${_scopeId}>${ssrInterpolate(p.notesSuggestion)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div class="md:col-span-1 flex items-center justify-end gap-2"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VButton"], mergeProps({
              size: "small",
              label: "\u041E\u0442\u043A\u043B\u043E\u043D\u0438\u0442\u044C",
              severity: "danger",
              outlined: "",
              onClick: ($event) => $setup.reject(p.id)
            }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u041E\u0442\u043A\u043B\u043E\u043D\u0438\u0442\u044C \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435")), {
              icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Icon"], {
                    name: "pi pi-times",
                    class: "w-5 h-5"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Icon"], {
                      name: "pi pi-times",
                      class: "w-5 h-5"
                    })
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent($setup["VButton"], mergeProps({
              size: "small",
              label: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C",
              onClick: ($event) => $setup.approve(p.id, p.accuracySuggestion, p.notesSuggestion)
            }, ssrGetDirectiveProps(_ctx, _directive_tooltip, "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C \u0438 \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u0442\u044C")), {
              icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Icon"], {
                    name: "pi pi-check",
                    class: "w-5 h-5"
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Icon"], {
                      name: "pi pi-check",
                      class: "w-5 h-5"
                    })
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
            _push2(`</div><div class="md:col-span-3"${_scopeId}><details${ssrIncludeBooleanAttr($setup.expandAll) ? " open" : ""}${_scopeId}><summary class="text-xs text-surface-500 cursor-pointer flex items-center gap-2 select-none"${_scopeId}> \u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F <span class="text-surface-400"${_scopeId}>(${ssrInterpolate($setup.summarizeDetails(p.details).total)})</span><span class="hidden md:inline-flex items-center gap-1"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["VTag"], {
              size: "small",
              value: `EXACT ${$setup.summarizeDetails(p.details).exact}`,
              severity: "success"
            }, null, _parent2, _scopeId));
            _push2(ssrRenderComponent($setup["VTag"], {
              size: "small",
              value: `NEAR ${$setup.summarizeDetails(p.details).near}`,
              severity: "info"
            }, null, _parent2, _scopeId));
            _push2(ssrRenderComponent($setup["VTag"], {
              size: "small",
              value: `TOL ${$setup.summarizeDetails(p.details).tol}`,
              severity: "info"
            }, null, _parent2, _scopeId));
            _push2(ssrRenderComponent($setup["VTag"], {
              size: "small",
              value: `LEGACY ${$setup.summarizeDetails(p.details).legacy}`,
              severity: "warning"
            }, null, _parent2, _scopeId));
            _push2(`</span></summary><div class="mt-2"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["MatchingDetailsGrid"], {
              details: p.details || []
            }, null, _parent2, _scopeId));
            _push2(`</div></details></div></div>`);
          });
          _push2(`<!--]--></div>`);
        }
      } else {
        return [
          $setup.loading ? (openBlock(), createBlock("div", {
            key: 0,
            class: "py-10 text-center text-surface-500"
          }, "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...")) : $setup.items.length === 0 ? (openBlock(), createBlock("div", {
            key: 1,
            class: "py-10 text-center text-surface-500"
          }, "\u041D\u0435\u0442 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439")) : (openBlock(), createBlock("div", {
            key: 2,
            class: "divide-y divide-surface-border"
          }, [
            (openBlock(true), createBlock(Fragment, null, renderList($setup.items, (p) => {
              return openBlock(), createBlock("div", {
                key: p.id,
                class: "py-3 grid grid-cols-1 md:grid-cols-3 gap-3 items-start"
              }, [
                createVNode("div", { class: "md:col-span-1" }, [
                  createVNode("div", { class: "text-sm text-surface-500" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F"),
                  createVNode("div", { class: "font-mono font-semibold" }, toDisplayString(p.catalogItem.sku) + " \u2014 " + toDisplayString(p.catalogItem.brand?.name), 1),
                  createVNode("a", {
                    href: `/admin/parts/${p.part.id}`,
                    class: "text-xs text-surface-500"
                  }, "\u0413\u0440\u0443\u043F\u043F\u0430: " + toDisplayString(p.part.name || "#" + p.part.id), 9, ["href"])
                ]),
                createVNode("div", { class: "md:col-span-1" }, [
                  createVNode("div", { class: "text-sm text-surface-500 mb-1" }, "\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435"),
                  createVNode($setup["VTag"], {
                    value: $setup.getAccuracyLabel(p.accuracySuggestion),
                    severity: $setup.getAccuracySeverity(p.accuracySuggestion)
                  }, null, 8, ["value", "severity"]),
                  p.notesSuggestion ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-xs text-surface-500 mt-1"
                  }, toDisplayString(p.notesSuggestion), 1)) : createCommentVNode("", true)
                ]),
                createVNode("div", { class: "md:col-span-1 flex items-center justify-end gap-2" }, [
                  withDirectives((openBlock(), createBlock($setup["VButton"], {
                    size: "small",
                    label: "\u041E\u0442\u043A\u043B\u043E\u043D\u0438\u0442\u044C",
                    severity: "danger",
                    outlined: "",
                    onClick: ($event) => $setup.reject(p.id)
                  }, {
                    icon: withCtx(() => [
                      createVNode($setup["Icon"], {
                        name: "pi pi-times",
                        class: "w-5 h-5"
                      })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])), [
                    [_directive_tooltip, "\u041E\u0442\u043A\u043B\u043E\u043D\u0438\u0442\u044C \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435"]
                  ]),
                  withDirectives((openBlock(), createBlock($setup["VButton"], {
                    size: "small",
                    label: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C",
                    onClick: ($event) => $setup.approve(p.id, p.accuracySuggestion, p.notesSuggestion)
                  }, {
                    icon: withCtx(() => [
                      createVNode($setup["Icon"], {
                        name: "pi pi-check",
                        class: "w-5 h-5"
                      })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])), [
                    [_directive_tooltip, "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C \u0438 \u043F\u0440\u0438\u043C\u0435\u043D\u0438\u0442\u044C"]
                  ])
                ]),
                createVNode("div", { class: "md:col-span-3" }, [
                  createVNode("details", { open: $setup.expandAll }, [
                    createVNode("summary", { class: "text-xs text-surface-500 cursor-pointer flex items-center gap-2 select-none" }, [
                      createTextVNode(" \u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F "),
                      createVNode("span", { class: "text-surface-400" }, "(" + toDisplayString($setup.summarizeDetails(p.details).total) + ")", 1),
                      createVNode("span", { class: "hidden md:inline-flex items-center gap-1" }, [
                        createVNode($setup["VTag"], {
                          size: "small",
                          value: `EXACT ${$setup.summarizeDetails(p.details).exact}`,
                          severity: "success"
                        }, null, 8, ["value"]),
                        createVNode($setup["VTag"], {
                          size: "small",
                          value: `NEAR ${$setup.summarizeDetails(p.details).near}`,
                          severity: "info"
                        }, null, 8, ["value"]),
                        createVNode($setup["VTag"], {
                          size: "small",
                          value: `TOL ${$setup.summarizeDetails(p.details).tol}`,
                          severity: "info"
                        }, null, 8, ["value"]),
                        createVNode($setup["VTag"], {
                          size: "small",
                          value: `LEGACY ${$setup.summarizeDetails(p.details).legacy}`,
                          severity: "warning"
                        }, null, 8, ["value"])
                      ])
                    ]),
                    createVNode("div", { class: "mt-2" }, [
                      createVNode($setup["MatchingDetailsGrid"], {
                        details: p.details || []
                      }, null, 8, ["details"])
                    ])
                  ], 8, ["open"])
                ])
              ]);
            }), 128))
          ]))
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`<div class="flex items-center justify-between gap-2"><div class="text-sm text-surface-500">\u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E ${ssrInterpolate($setup.pageStart)}\u2013${ssrInterpolate($setup.pageEnd)} \u0438\u0437 ${ssrInterpolate($setup.total)}</div><div class="flex justify-end gap-2">`);
  _push(ssrRenderComponent($setup["VButton"], {
    disabled: $setup.skip === 0,
    label: "\u041D\u0430\u0437\u0430\u0434",
    severity: "secondary",
    outlined: "",
    onClick: $setup.prev
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    disabled: $setup.skip + $setup.take >= $setup.total,
    label: "\u0412\u043F\u0435\u0440\u0451\u0434",
    severity: "secondary",
    outlined: "",
    onClick: $setup.next
  }, null, _parent));
  _push(`</div></div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/catalogitems/ProposalsList.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ProposalsList = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u041F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u044F \u044D\u043A\u0432\u0438\u0432\u0430\u043B\u0435\u043D\u0442\u043E\u0432", "description": "\u041E\u0447\u0435\u0440\u0435\u0434\u044C \u0430\u0432\u0442\u043E-\u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0439 \u0434\u043B\u044F \u0438\u043D\u0436\u0435\u043D\u0435\u0440\u0430" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="w-full max-w-6xl"> <div class="flex items-center justify-between mb-4"> <h1 class="text-xl font-semibold">Предложения эквивалентов</h1> </div> ${renderComponent($$result2, "ProposalsList", ProposalsList, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/catalogitems/ProposalsList.vue", "client:component-export": "default" })} </div> ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/proposals/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/proposals/index.astro";
const $$url = "/admin/proposals";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

import{w as A}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{u as E}from"./useTheme.DwNY0gjL.js";import{_ as j}from"./_plugin-vue_export-helper.DlAUqK2U.js";/* empty css                                */import{c as y}from"./createLucideIcon.NtN1-Ts2.js";import{C as q}from"./check.B3pubfVf.js";import{d as O,c as n,o as t,b as u,g as l,M as i,a as c,F as w,r as M,h as P,i as V,A as U}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{n as d,t as m,r as Z}from"./reactivity.esm-bundler.BQ12LWmY.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=y("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=y("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=y("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),G=O({__name:"ThemeToggle",props:{mode:{default:"toggle"},showLabel:{type:Boolean,default:!1},buttonClass:{default:""}},setup(f,{expose:s}){s();const r=f,{currentTheme:e,activeTheme:_,isDark:p,isLight:o,isSystem:h,themeIcon:z,themeName:B,systemPrefersDark:C,setTheme:x,toggleTheme:F}=E(),a=Z(!1),N=P(()=>{switch(e.value){case"light":return b;case"dark":return v;case"system":return C.value?v:b;default:return L}}),D=[{value:"light",label:"Светлая",icon:v},{value:"dark",label:"Темная",icon:b},{value:"system",label:"Системная",icon:L}],I=()=>{a.value=!a.value},S=k=>{x(k),a.value=!1},g=k=>{k.target.closest(".theme-toggle")||(a.value=!1)};V(()=>{document.addEventListener("click",g)}),U(()=>{document.removeEventListener("click",g)});const T={props:r,currentTheme:e,activeTheme:_,isDark:p,isLight:o,isSystem:h,themeIcon:z,themeName:B,systemPrefersDark:C,setTheme:x,toggleTheme:F,showMenu:a,themeIconComponent:N,themes:D,toggleMenu:I,selectTheme:S,handleClickOutside:g,get Check(){return q}};return Object.defineProperty(T,"__isScriptSetup",{enumerable:!1,value:!0}),T}}),H={class:"theme-toggle"},J=["title"],K={key:0,class:"ml-2"},Q={key:1,class:"relative"},R=["title"],W={key:0,class:"ml-2"},X={class:"py-1"},Y=["onClick"],$={key:2,class:"flex rounded-[--radius-md] border border-[--color-border] overflow-hidden"},ee=["onClick","title"],oe={key:0,class:"ml-2"};function te(f,s,r,e,_,p){return t(),n("div",H,[r.mode==="toggle"?(t(),n("button",{key:0,onClick:s[0]||(s[0]=(...o)=>e.toggleTheme&&e.toggleTheme(...o)),class:d([r.buttonClass,"p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors"]),title:`Переключить тему (текущая: ${e.themeName})`},[(t(),l(i(e.themeIconComponent),{size:20})),r.showLabel?(t(),n("span",K,m(e.themeName),1)):u("",!0)],10,J)):r.mode==="menu"?(t(),n("div",Q,[c("button",{onClick:e.toggleMenu,class:d([r.buttonClass,"p-2 rounded-[--radius-md] text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors flex items-center"]),title:`Выбрать тему (текущая: ${e.themeName})`},[(t(),l(i(e.themeIconComponent),{size:20})),r.showLabel?(t(),n("span",W,m(e.themeName),1)):u("",!0)],10,R),e.showMenu?(t(),n("div",{key:0,class:"absolute right-0 mt-2 w-48 bg-[--color-card] rounded-[--radius-md] [box-shadow:var(--shadow-lg)] border border-[--color-border] z-50",onClick:s[1]||(s[1]=A(()=>{},["stop"]))},[c("div",X,[(t(),n(w,null,M(e.themes,o=>c("button",{key:o.value,onClick:h=>e.selectTheme(o.value),class:d(["flex items-center w-full px-4 py-2 text-sm text-[--color-foreground] hover:bg-[--p-content-hover-background] transition-colors",{"bg-[--p-highlight-background] text-[--p-primary-color]":e.currentTheme===o.value}])},[(t(),l(i(o.icon),{size:16,class:"mr-3"})),c("span",null,m(o.label),1),e.currentTheme===o.value?(t(),l(e.Check,{key:0,size:16,class:"ml-auto text-[--p-primary-color]"})):u("",!0)],10,Y)),64))])])):u("",!0)])):r.mode==="buttons"?(t(),n("div",$,[(t(),n(w,null,M(e.themes,o=>c("button",{key:o.value,onClick:h=>e.selectTheme(o.value),class:d(["flex items-center px-3 py-2 text-sm transition-colors border-r border-[--color-border] last:border-r-0",{"bg-[--color-primary] text-[--color-primary-foreground]":e.currentTheme===o.value,"bg-[--color-card] text-[--color-foreground] hover:bg-[--p-content-hover-background]":e.currentTheme!==o.value}]),title:`Выбрать ${o.label.toLowerCase()} тему`},[(t(),l(i(o.icon),{size:16})),r.showLabel?(t(),n("span",oe,m(o.label),1)):u("",!0)],10,ee)),64))])):u("",!0)])}const me=j(G,[["render",te],["__scopeId","data-v-94d58d16"]]);export{me as T};

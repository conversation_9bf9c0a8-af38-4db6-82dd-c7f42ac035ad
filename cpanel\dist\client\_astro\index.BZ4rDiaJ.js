import{t as r}from"./index.BaVCXmir.js";import{p as t,g as d,b as s,o as l,T as i}from"./runtime-core.esm-bundler.CRb7Pg8a.js";var p={name:"Portal",props:{appendTo:{type:[String,Object],default:"body"},disabled:{type:Boolean,default:!1}},data:function(){return{mounted:!1}},mounted:function(){this.mounted=r()},computed:{inline:function(){return this.disabled||this.appendTo==="self"}}};function u(e,f,o,m,n,a){return a.inline?t(e.$slots,"default",{key:0}):n.mounted?(l(),d(i,{key:1,to:o.appendTo},[t(e.$slots,"default")],8,["to"])):s("",!0)}p.render=u;export{p as s};

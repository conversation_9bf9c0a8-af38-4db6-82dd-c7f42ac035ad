import { watch, onScopeDispose } from 'vue';
import { a as use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './ClientRouter_avhRMbqw.mjs';
import { t as trpc } from './trpc_DApR3DD7.mjs';

function useQueryToastErrors(errorRef) {
  const { handleError } = useErrorHandler();
  const shownErrors = /* @__PURE__ */ new Set();
  const stopWatcher = watch(errorRef, (err) => {
    if (err) {
      const errorKey = err.message + err.stack;
      if (!shownErrors.has(errorKey)) {
        shownErrors.add(errorKey);
        handleError(err);
      }
    }
  });
  onScopeDispose(() => {
    stopWatcher();
    shownErrors.clear();
  });
}

const qk = {
  parts: {
    list: (input) => ["trpc", "crud.part.findMany", input ?? {}],
    detail: (input) => ["trpc", "crud.part.findUnique", input],
    count: (input) => ["trpc", "crud.part.count", input ?? {}]
  },
  brands: {
    list: (input) => ["trpc", "crud.brand.findMany", input ?? {}]
  },
  categories: {
    list: (input) => ["trpc", "crud.partCategory.findMany", input ?? {}]
  },
  search: {
    parts: (input) => ["trpc", "search.searchParts", input ?? {}]
  },
  attributeTemplates: {
    list: (input) => ["trpc", "crud.attributeTemplate.findMany", input ?? {}]
  },
  stats: {
    parts: () => ["trpc", "crud.part.count"],
    brands: () => ["trpc", "crud.brand.count"],
    categories: () => ["trpc", "crud.partCategory.count"]
  }
};

const fetchers = {
  parts: {
    list: async (input) => trpc.crud.part.findMany.query(input),
    detail: async (input) => trpc.crud.part.findUnique.query(input),
    count: async (input) => trpc.crud.part.count.query(input)
  },
  brands: {
    list: async (input) => trpc.crud.brand.findMany.query(input)
  },
  categories: {
    list: async (input) => trpc.crud.partCategory.findMany.query(input)
  },
  search: {
    parts: async (input) => trpc.search.searchParts.query(input)
  },
  attributeTemplates: {
    list: async (input) => trpc.crud.attributeTemplate.findMany.query(input)
  },
  stats: {
    parts: async () => trpc.crud.part.count.query(),
    brands: async () => trpc.crud.brand.count.query(),
    categories: async () => trpc.crud.partCategory.count.query()
  }
};

export { fetchers as f, qk as q, useQueryToastErrors as u };

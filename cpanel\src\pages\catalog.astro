---
import Layout from '@/layouts/Layout.astro';
import CatalogBrowserBoundary from '@/components/catalog/CatalogBrowserBoundary.vue';
import { trpc } from '@/lib/trpc';
import { queryToFilters, filtersToQuery } from '@/utils/filters';
import type { PartListQuery, CategoryListQuery, BrandListQuery, PartListItem, PartCategoryLite, BrandLite } from '@/types/catalog';

// Готовим initialData для острова
const sp = Astro.url.searchParams;
const raw = Object.fromEntries(sp.entries());
const filters = queryToFilters(raw);

const baseQuery: PartListQuery = {
  ...filtersToQuery(filters),
  take: 20,
  skip: 0,
  orderBy: { name: 'asc' },
  include: {
    partCategory: true,
    image: true,
    attributes: { include: { template: true } },
  },
};

const categoriesQuery: CategoryListQuery = { where: { level: 0 }, orderBy: { name: 'asc' } };
const brandsQuery: BrandListQuery = {};

let initialParts: PartListItem[] = [];
let initialCategories: PartCategoryLite[] = [];
let initialBrands: BrandLite[] = [];

try {
  const [parts, categories, brands] = await Promise.all([
    trpc.catalog.listParts.query(baseQuery as any),
    trpc.catalog.listCategories.query(categoriesQuery as any),
    trpc.catalog.listBrands.query(brandsQuery as any),
  ]);
  initialParts = parts as unknown as PartListItem[];
  initialCategories = categories as unknown as PartCategoryLite[];
  initialBrands = brands as unknown as BrandLite[];
} catch (error) {
  console.error('Error loading initial data:', error);
}
---

<Layout title="Каталог запчастей">
  <main class="container mx-auto px-4 py-8">
    <CatalogBrowserBoundary
      client:load
      initialParts={initialParts}
      initialCategories={initialCategories}
      initialBrands={initialBrands}
      categoriesQuery={categoriesQuery}
      brandsQuery={brandsQuery}
    />
  </main>
</Layout>

<style>
  /* Дополнительные стили для каталога */
  .container {
    max-width: 1200px;
  }
</style>
import { z } from 'zod'
import { ApplicabilityAccuracySchema } from '../generated/zod/enums/ApplicabilityAccuracy.schema'
import { ProposalStatusSchema } from '../generated/zod/enums/ProposalStatus.schema'

export const FindMatchingPartsInput = z.object({
  catalogItemId: z.number()
})

export const FindMatchingCatalogItemsInput = z.object({
  partId: z.number()
})

export const ProposeLinkInput = z.object({
  catalogItemId: z.number(),
  partId: z.number(),
  accuracySuggestion: ApplicabilityAccuracySchema.default('EXACT_MATCH'),
  notesSuggestion: z.string().optional(),
  details: z.any().optional()
})

export const ListProposalsInput = z.object({
  status: ProposalStatusSchema.default('PENDING'),
  take: z.number().int().min(1).max(100).default(20),
  skip: z.number().int().min(0).default(0),
  catalogItemId: z.number().optional(),
  partId: z.number().optional()
})

export const RejectProposalInput = z.object({ id: z.number(), reason: z.string().optional() })

export const ApproveProposalInput = z.object({
  id: z.number(),
  override: z.object({
    accuracy: z.enum(['EXACT_MATCH','MATCH_WITH_NOTES','REQUIRES_MODIFICATION','PARTIAL_MATCH']).optional(),
    notes: z.string().optional()
  }).optional()
})

export const CreatePartFromItemsInput = z.object({
  name: z.string().min(1).optional(),
  partCategoryId: z.number(),
  itemIds: z.array(z.number()).min(1),
  accuracy: ApplicabilityAccuracySchema.default('EXACT_MATCH'),
  notes: z.string().optional()
})

export const GenerateProposalsInput = z.object({
  catalogItemIds: z.array(z.number()).optional(),
  brandId: z.number().optional(),
  take: z.number().int().min(1).max(500).default(100),
  skip: z.number().int().min(0).default(0)
})

export type MatchKind = 'NUMBER_EXACT' | 'NUMBER_WITHIN_TOLERANCE' | 'STRING_EXACT' | 'STRING_SYNONYM_EXACT' | 'STRING_SYNONYM_NEAR' | 'STRING_SYNONYM_LEGACY'



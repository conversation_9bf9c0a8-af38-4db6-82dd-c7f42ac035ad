import{E as V}from"./ErrorBoundary.B0AhELGe.js";import{u as _,a as p,f as x,q as g}from"./fetchers.B4Ycwiwp.js";import j from"./Card.C4y0_bWr.js";import{S as w}from"./SecondaryButton.DkELYl7Q.js";/* empty css                       */import{_ as P}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as L,c as D,o as l,a,b as K,e as o,w as s,f as k,h as n,g as F}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as c,r as O}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./createLucideIcon.NtN1-Ts2.js";import"./triangle-alert.CP-lXbmj.js";import"./utils.is9Ib0FR.js";import"./useErrorHandler.DVDazL16.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";import"./trpc.BpyaUO08.js";import"./utils.BUKUcbtE.js";import"./index.BaVCXmir.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.6ykohhwZ.js";import"./index.BH7IgUdp.js";import"./index.CDQpPXyE.js";const Q=L({__name:"StatsPageComponent",props:{initialStats:{default:()=>({parts:0,brands:0,categories:0})}},setup(i,{expose:e}){e();const r=i,{data:t,error:u,refetch:d,isFetching:y}=_({queryKey:g.stats.parts(),queryFn:()=>x.stats.parts(),initialData:r.initialStats?.parts,staleTime:6e4});p(u);const{data:v,error:m,refetch:C,isFetching:b}=_({queryKey:g.stats.brands(),queryFn:()=>x.stats.brands(),initialData:r.initialStats?.brands,staleTime:6e4});p(m);const{data:E,error:f,refetch:S,isFetching:h}=_({queryKey:g.stats.categories(),queryFn:()=>x.stats.categories(),initialData:r.initialStats?.categories,staleTime:6e4});p(f);const q=n(()=>t.value??0),A=n(()=>v.value??0),T=n(()=>E.value??0),N=n(()=>y.value||b.value||h.value),R=n(()=>u.value||m.value||f.value),B={props:r,partsCountData:t,partsError:u,refetchParts:d,isPartsLoading:y,brandsCountData:v,brandsError:m,refetchBrands:C,isBrandsLoading:b,categoriesCountData:E,categoriesError:f,refetchCategories:S,isCategoriesLoading:h,partsCount:q,brandsCount:A,categoriesCount:T,isLoading:N,hasErrors:R,refreshStats:async()=>{await Promise.all([d(),C(),S()])},Card:j,SecondaryButton:w};return Object.defineProperty(B,"__isScriptSetup",{enumerable:!1,value:!0}),B}}),I={class:"stats-page"},z={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"},G={class:"text-center"},H={class:"text-3xl font-bold text-primary mb-2"},J={class:"text-center"},M={class:"text-3xl font-bold text-primary mb-2"},U={class:"text-center"},W={class:"text-3xl font-bold text-primary mb-2"},X={class:"flex justify-center"},Y={key:0,class:"mt-6"},Z={class:"text-center text-red-500"};function $(i,e,r,t,u,d){return l(),D("div",I,[e[5]||(e[5]=a("div",{class:"mb-8"},[a("h1",{class:"text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2"}," Статистика системы "),a("p",{class:"text-surface-600 dark:text-surface-400"}," Общая информация о данных в системе ")],-1)),a("div",z,[o(t.Card,null,{content:s(()=>[a("div",G,[a("div",H,c(t.isLoading?"...":t.partsCount),1),e[0]||(e[0]=a("div",{class:"text-surface-600 dark:text-surface-400"}," Запчастей в каталоге ",-1))])]),_:1}),o(t.Card,null,{content:s(()=>[a("div",J,[a("div",M,c(t.isLoading?"...":t.brandsCount),1),e[1]||(e[1]=a("div",{class:"text-surface-600 dark:text-surface-400"}," Брендов ",-1))])]),_:1}),o(t.Card,null,{content:s(()=>[a("div",U,[a("div",W,c(t.isLoading?"...":t.categoriesCount),1),e[2]||(e[2]=a("div",{class:"text-surface-600 dark:text-surface-400"}," Категорий ",-1))])]),_:1})]),a("div",X,[o(t.SecondaryButton,{onClick:t.refreshStats,disabled:t.isLoading},{default:s(()=>[k(c(t.isLoading?"Обновление...":"Обновить статистику"),1)]),_:1},8,["disabled"])]),t.hasErrors?(l(),D("div",Y,[o(t.Card,null,{content:s(()=>[a("div",Z,[e[4]||(e[4]=a("p",{class:"mb-4"},"Ошибка при загрузке некоторых данных",-1)),o(t.SecondaryButton,{onClick:t.refreshStats},{default:s(()=>e[3]||(e[3]=[k(" Повторить ")])),_:1,__:[3]})])]),_:1})])):K("",!0)])}const tt=P(Q,[["render",$],["__scopeId","data-v-3e452373"]]),et=L({__name:"StatsPageBoundary",props:{initialStats:{}},setup(i,{expose:e}){e();const r=O(0),u={key:r,onRetry:()=>{r.value++},ErrorBoundary:V,StatsPageComponent:tt};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}});function at(i,e,r,t,u,d){return l(),F(t.ErrorBoundary,{variant:"detailed",onRetry:t.onRetry},{default:s(()=>[(l(),F(t.StatsPageComponent,{key:t.key,"initial-stats":r.initialStats},null,8,["initial-stats"]))]),_:1})}const Bt=P(et,[["render",at]]);export{Bt as default};

import"./router.DKcY2uv6.js";import{r as L}from"./reactivity.esm-bundler.BQ12LWmY.js";import{h as l,j as M,i as x}from"./runtime-core.esm-bundler.CRb7Pg8a.js";const q=(o,d={})=>{const{prefix:f="",arrayParams:c=[],numberParams:h=[],booleanParams:w=[],debounceMs:F=300,replaceHistory:P=!0}=d,i=L({...o});let m=null;const S=(t,r)=>{if(!(r==null||r===""))return c.includes(t)?Array.isArray(r)?r.map(e=>String(e)).filter(e=>e!==""):[String(r)].filter(e=>e!==""):String(r)},y=(t,r)=>{if(!r||Array.isArray(r)&&r.length===0)return c.includes(t)?[]:null;if(c.includes(t)){const e=Array.isArray(r)?r:[r];return h.includes(t)?e.map(s=>{const n=Number(s);return isNaN(n)?null:n}).filter(s=>s!==null):w.includes(t)?e.map(s=>s==="true"||s==="1"):e}if(h.includes(t)){const e=Number(Array.isArray(r)?r[0]:r);return isNaN(e)?null:e}if(w.includes(t)){const e=Array.isArray(r)?r[0]:r;return e==="true"||e==="1"}return Array.isArray(r)?r[0]:r},g=()=>typeof window>"u"?new URLSearchParams:new URLSearchParams(window.location.search),p=()=>{const t=g(),r={...o};Object.keys(r).forEach(e=>{const s=f+e,n=t.get(s),a=t.getAll(s);(n!==null||a.length>0)&&(c.includes(e)&&a.length>0?r[e]=y(e,a):n!==null&&(r[e]=y(e,n)))}),i.value=r},A=()=>{typeof window>"u"||(m&&clearTimeout(m),m=setTimeout(()=>{const t=g(),r=[];t.forEach((n,a)=>{a.startsWith(f)&&r.push(a)}),r.forEach(n=>t.delete(n)),Object.entries(i.value).forEach(([n,a])=>{const b=f+n,u=S(n,a);u!==void 0&&(Array.isArray(u)?u.forEach(V=>t.append(b,V)):t.set(b,u))});const e=t.toString(),s=typeof window<"u"&&window.location.search?window.location.search.replace(/^\?/,""):"";if(e!==s){const n=new URL(window.location.href);n.search=e,P?window.history.replaceState(null,"",n):window.history.pushState(null,"",n)}},F))},E=(t,r)=>{i.value[t]=r},N=t=>{Object.assign(i.value,t)},T=()=>{i.value={...o}},j=t=>{i.value[t]=o[t]},I=l(()=>Object.entries(i.value).some(([t,r])=>{const e=o[t];return Array.isArray(r)&&Array.isArray(e)?r.length!==e.length||!r.every((s,n)=>s===e[n]):r!==e})),O=l(()=>Object.entries(i.value).reduce((t,[r,e])=>{const s=o[r];return Array.isArray(e)?t+(e.length>0?1:0):e!==s&&e!==null&&e!==void 0&&e!==""?t+1:t},0)),U=l(()=>JSON.stringify(i.value,null,2));return M(i,A,{deep:!0}),x(()=>{if(p(),typeof window<"u"){const t=()=>{p()};return window.addEventListener("popstate",t),()=>{window.removeEventListener("popstate",t)}}}),{filters:l(()=>i.value),hasActiveFilters:I,activeFiltersCount:O,filtersString:U,updateFilter:E,updateFilters:N,resetFilters:T,resetFilter:j,loadFromUrl:p,saveToUrl:A}},_=(o={})=>{const d={query:"",categoryIds:[],brandIds:[],attributes:{},equipmentModelIds:[],page:1,limit:20,...o};return q(d,{prefix:"search_",arrayParams:["categoryIds","brandIds","equipmentModelIds"],numberParams:["page","limit"],debounceMs:300})};export{q as a,_ as u};

import{u as Q,q as z,a as N,f as R}from"./fetchers.B4Ycwiwp.js";import{p as ge}from"./utils.BL5HZsed.js";import{a as fe}from"./useUrlParams.D0jSWJSf.js";import{h as v,d as F,c as n,o as a,a as i,F as D,r as B,g as k,w as f,e as g,f as P,b as _,n as pe,p as _e,q as ye,j as U,i as he}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as K,t as o,b as be,n as G}from"./reactivity.esm-bundler.BQ12LWmY.js";import{u as ve,S as xe,C as ke}from"./Spinner.nnVZHfjN.js";import J from"./Card.C4y0_bWr.js";import{I as Ce}from"./InputText.DOJMNEP-.js";import{M as Ie}from"./MultiSelect.B9h41NVw.js";import{S as O}from"./SecondaryButton.DkELYl7Q.js";import{w as H}from"./runtime-dom.esm-bundler.DXo4nCak.js";/* empty css                         */import{_ as E}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{b as we}from"./index.6ykohhwZ.js";import{p as Pe}from"./utils.BUKUcbtE.js";import{D as Be,s as Fe}from"./index.BWD5ZO4k.js";import"./utils.is9Ib0FR.js";import"./useErrorHandler.DVDazL16.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";import"./trpc.BpyaUO08.js";import"./bundle-mjs.D6B6e0vX.js";import"./router.DKcY2uv6.js";import"./Select.CQBzSu6y.js";import"./index.DPMtieGJ.js";import"./index.BaVCXmir.js";import"./index.CLs7nh7g.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.BH7IgUdp.js";import"./index.CDQpPXyE.js";import"./index.By2TJOuX.js";import"./index.COq_zjeV.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";/* empty css                         */import"./index.CBuVmUry.js";import"./index.D4QD70nN.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";const Ee=u=>{const r=[];if(u.search&&r.push({name:{contains:u.search,mode:"insensitive"}}),u.categoryIds?.length&&r.push({partCategoryId:{in:u.categoryIds}}),u.brandIds?.length&&r.push({applicabilities:{some:{catalogItem:{brandId:{in:u.brandIds}}}}}),u.equipmentModelIds?.length&&r.push({equipmentApplicabilities:{some:{equipmentModelId:{in:u.equipmentModelIds}}}}),u.attributes?.length){const e=u.attributes.filter(d=>!!d?.templateId).map(d=>{const y={templateId:d.templateId};if(d.value&&(y.value=d.value),d.minValue!=null||d.maxValue!=null){const t={};d.minValue!=null&&(t.gte=d.minValue),d.maxValue!=null&&(t.lte=d.maxValue),y.numericValue=t}return y});e.length&&r.push({attributes:{some:{AND:e}}})}return{where:r.length?{AND:r}:{}}},Ae=u=>!!(u.search||u.categoryIds?.length||u.brandIds?.length||u.equipmentModelIds?.length||u.priceRange||u.attributes?.length);function Me(u){const r=fe({search:u?.search??"",categoryIds:u?.categoryIds??[],brandIds:u?.brandIds??[],equipmentModelIds:u?.equipmentModelIds??[],priceMin:u?.priceRange?.[0],priceMax:u?.priceRange?.[1]},{prefix:"catalog_",arrayParams:["categoryIds","brandIds","equipmentModelIds"],numberParams:["priceMin","priceMax"],debounceMs:300}),l=v(()=>{const s=r.filters.value,x=s.priceMin!=null||s.priceMax!=null?[s.priceMin??0,s.priceMax??1/0]:void 0;return{search:s.search||void 0,categoryIds:Array.isArray(s.categoryIds)&&s.categoryIds.length?s.categoryIds.map(Number):void 0,brandIds:Array.isArray(s.brandIds)&&s.brandIds.length?s.brandIds.map(Number):void 0,equipmentModelIds:Array.isArray(s.equipmentModelIds)&&s.equipmentModelIds.length?s.equipmentModelIds:void 0,priceRange:x}}),e=K({mode:"grid",itemsPerPage:20,sortBy:"name",sortOrder:"asc"}),d=v(()=>Ae(l.value)),y=v(()=>Ee(l.value)),t=s=>{const x={};if("search"in s&&(x.search=s.search||void 0),"categoryIds"in s&&(x.categoryIds=s.categoryIds&&s.categoryIds.length?s.categoryIds:void 0),"brandIds"in s&&(x.brandIds=s.brandIds&&s.brandIds.length?s.brandIds:void 0),"equipmentModelIds"in s&&(x.equipmentModelIds=s.equipmentModelIds&&s.equipmentModelIds.length?s.equipmentModelIds:void 0),"priceRange"in s){const M=s.priceRange;x.priceMin=M?.[0],x.priceMax=M?.[1]}r.updateFilters(x)},c=s=>{t({search:s||void 0})},b=s=>{t({categoryIds:s.length?s:void 0})},h=s=>{t({brandIds:s.length?s:void 0})},m=s=>{t({equipmentModelIds:s.length?s:void 0})},C=s=>{t({priceRange:s})},I=s=>{t({attributes:s.length?s:void 0})},A=()=>{r.updateFilters({search:void 0,categoryIds:void 0,brandIds:void 0,equipmentModelIds:void 0,priceMin:void 0,priceMax:void 0})},V=s=>{e.value.mode=s},j=s=>{e.value.itemsPerPage=s},T=(s,x="asc")=>{e.value.sortBy=s,e.value.sortOrder=x};return{filters:v(()=>l.value),viewPreferences:v(()=>e.value),hasFilters:d,queryParams:y,updateFilters:t,setSearch:c,setCategoryIds:b,setBrandIds:h,setEquipmentModelIds:m,setPriceRange:C,setAttributes:I,resetFilters:A,setViewMode:V,setItemsPerPage:j,setSorting:T}}const Se=F({__name:"CatalogGrid",props:{parts:{}},emits:["partClick"],setup(u,{expose:r}){r();const l=()=>"🖼️",e=u,d=h=>h.attributes?.length?h.attributes.filter(m=>m.template&&m.value).slice(0,3):[],b={ImageIcon:l,props:e,getMainAttributes:d,getPartMainAttributes:h=>d(h),formatAttributeValue:h=>{if(!h.value)return"";const m=h.value,C=h.template.unit;return C?`${m} ${C.symbol||C.name}`:m},handleImageError:h=>{const m=h.target;m.style.display="none",console.warn("Ошибка загрузки изображения:",m.src)},Card:J,SecondaryButton:O};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}}),qe={class:"catalog-grid"},De={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Ve={class:"relative aspect-square bg-surface-100 dark:bg-surface-800 rounded-t-xl overflow-hidden"},je=["src","alt"],Qe={key:1,class:"w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"},ze={key:2,class:"absolute top-2 left-2 bg-primary text-primary-contrast px-2 py-1 rounded-md text-xs font-medium"},Re={class:"space-y-3"},Oe={class:"font-medium text-surface-900 dark:text-surface-0 line-clamp-2"},Te={key:0,class:"text-sm text-surface-500 dark:text-surface-400"},Le={key:1,class:"space-y-1"},Ne={class:"text-surface-600 dark:text-surface-300"},Ue={class:"text-surface-900 dark:text-surface-0 font-medium"},Ge={key:2,class:"text-sm text-surface-500 dark:text-surface-400"},Ke={class:"flex items-center justify-between pt-3 border-t border-surface-200 dark:border-surface-700"},Je={class:"text-xs text-surface-500 dark:text-surface-400"};function He(u,r,l,e,d,y){return a(),n("div",qe,[i("div",De,[(a(!0),n(D,null,B(l.parts,t=>(a(),k(e.Card,{key:t.id,class:"part-card cursor-pointer hover:shadow-lg transition-shadow duration-200",onClick:c=>u.$emit("partClick",t)},{header:f(()=>[i("div",Ve,[t.image?.url?(a(),n("img",{key:0,src:t.image.url,alt:t.name||"Запчасть",class:"w-full h-full object-cover",loading:"lazy",onError:e.handleImageError},null,40,je)):(a(),n("div",Qe,[g(e.ImageIcon,{class:"w-12 h-12"})])),t.partCategory?(a(),n("div",ze,o(t.partCategory.name),1)):_("",!0)])]),content:f(()=>[i("div",Re,[i("h3",Oe,o(t.name||`Запчасть #${t.id}`),1),t.path?(a(),n("div",Te," Путь: "+o(t.path),1)):_("",!0),e.getPartMainAttributes(t).length?(a(),n("div",Le,[(a(!0),n(D,null,B(e.getPartMainAttributes(t),c=>(a(),n("div",{key:c.id,class:"flex justify-between text-sm"},[i("span",Ne,o(c.template.title)+": ",1),i("span",Ue,o(e.formatAttributeValue(c)),1)]))),128))])):_("",!0),t.applicabilities?.length?(a(),n("div",Ge,o(t.applicabilities.length)+" "+o(u.pluralize(t.applicabilities.length,["позиция","позиции","позиций"])),1)):_("",!0)])]),footer:f(()=>[i("div",Ke,[i("div",Je," ID: "+o(t.id),1),g(e.SecondaryButton,{size:"small",outlined:"",onClick:H(c=>u.$emit("partClick",t),["stop"])},{default:f(()=>r[0]||(r[0]=[P(" Подробнее ")])),_:2,__:[0]},1032,["onClick"])])]),_:2},1032,["onClick"]))),128))])])}const We=E(Se,[["render",He],["__scopeId","data-v-80e3f4e4"]]),Xe=F({__name:"Badge",setup(u,{expose:r}){r();const e={theme:K({root:`inline-flex items-center justify-center rounded-md
        py-0 px-2 text-xs font-bold min-w-6 h-6
        bg-primary text-primary-contrast
        p-empty:min-w-2 p-empty:h-2 p-empty:rounded-full p-empty:p-0
        p-circle:p-0 p-circle:rounded-full
        p-secondary:bg-surface-100 dark:p-secondary:bg-surface-800 p-secondary:text-surface-600 dark:p-secondary:text-surface-300
        p-success:bg-green-500 dark:p-success:bg-green-400 p-success:text-white dark:p-success:text-green-950
        p-info:bg-sky-500 dark:p-info:bg-sky-400 p-info:text-white dark:p-info:text-sky-950
        p-warn:bg-orange-500 dark:p-warn:bg-orange-400 p-warn:text-white dark:p-warn:text-orange-950
        p-danger:bg-red-500 dark:p-danger:bg-red-400 p-danger:text-white dark:p-danger:text-red-950
        p-contrast:bg-surface-950 dark:p-contrast:bg-white p-contrast:text-white dark:p-contrast:text-surface-950
        p-small:text-[0.625rem] p-small:min-w-5 p-small:h-5
        p-large:text-sm p-large:min-w-7 p-large:h-7
        p-xlarge:text-base p-xlarge:min-w-8 p-xlarge:h-8`}),get Badge(){return we},get ptViewMerge(){return Pe}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}});function Ye(u,r,l,e,d,y){return a(),k(e.Badge,{unstyled:"",pt:e.theme,ptOptions:{mergeProps:e.ptViewMerge}},pe({_:2},[B(u.$slots,(t,c)=>({name:c,fn:f(b=>[_e(u.$slots,c,be(ye(b??{})))])}))]),1032,["pt","ptOptions"])}const te=E(Xe,[["render",Ye]]),Ze=F({__name:"CatalogList",props:{parts:{}},emits:["partClick"],setup(u,{expose:r}){r();const y={ImageIcon:()=>"🖼️",formatAttributeValue:t=>{if(!t.value)return"";const c=t.value,b=t.template.unit;return b?`${c} ${b.symbol||b.name}`:c},formatDate:t=>t?new Date(t).toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric"}):"",Card:J,Badge:te,SecondaryButton:O};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}}),$e={class:"catalog-list"},et={class:"space-y-4"},tt={class:"flex items-start gap-4"},rt={class:"flex-shrink-0 w-20 h-20 bg-surface-100 dark:bg-surface-800 rounded-lg overflow-hidden"},st=["src","alt"],at={key:1,class:"w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"},ut={class:"flex-1 min-w-0"},it={class:"flex items-start justify-between"},nt={class:"flex-1 min-w-0"},ot={class:"flex items-center gap-2 mb-2"},lt={class:"font-medium text-surface-900 dark:text-surface-0 truncate"},ct={key:0,class:"text-sm text-surface-500 dark:text-surface-400 mb-2"},dt={key:1,class:"flex flex-wrap gap-x-4 gap-y-1 text-sm"},mt={class:"font-medium"},gt={class:"ml-1 text-surface-900 dark:text-surface-0"},ft={key:0,class:"text-surface-500 dark:text-surface-400 text-xs"},pt={class:"flex-shrink-0 ml-4"},_t={class:"flex items-center justify-between mt-3 pt-3 border-t border-surface-200 dark:border-surface-700"},yt={class:"flex items-center gap-4 text-sm text-surface-500 dark:text-surface-400"},ht={key:0},bt={key:1},vt={class:"text-xs text-surface-400 dark:text-surface-500"};function xt(u,r,l,e,d,y){return a(),n("div",$e,[i("div",et,[(a(!0),n(D,null,B(l.parts,t=>(a(),k(e.Card,{key:t.id,class:"part-item cursor-pointer hover:shadow-md transition-shadow duration-200",onClick:c=>u.$emit("partClick",t)},{content:f(()=>[i("div",tt,[i("div",rt,[t.image?.url?(a(),n("img",{key:0,src:t.image.url,alt:t.name||"Запчасть",class:"w-full h-full object-cover",loading:"lazy"},null,8,st)):(a(),n("div",at,[g(e.ImageIcon,{class:"w-8 h-8"})]))]),i("div",ut,[i("div",it,[i("div",nt,[i("div",ot,[i("h3",lt,o(t.name||`Запчасть #${t.id}`),1),t.partCategory?(a(),k(e.Badge,{key:0,value:t.partCategory.name,severity:"info",class:"text-xs"},null,8,["value"])):_("",!0)]),t.path?(a(),n("div",ct," Путь: "+o(t.path),1)):_("",!0),t.attributes?.length?(a(),n("div",dt,[(a(!0),n(D,null,B(t.attributes.slice(0,5),c=>(a(),n("div",{key:c.id,class:"text-surface-600 dark:text-surface-300"},[i("span",mt,o(c.template.title)+":",1),i("span",gt,o(e.formatAttributeValue(c)),1)]))),128)),t.attributes.length>5?(a(),n("div",ft," +"+o(t.attributes.length-5)+" еще ",1)):_("",!0)])):_("",!0)]),i("div",pt,[g(e.SecondaryButton,{size:"small",outlined:"",onClick:H(c=>u.$emit("partClick",t),["stop"])},{default:f(()=>r[0]||(r[0]=[P(" Подробнее ")])),_:2,__:[0]},1032,["onClick"])])]),i("div",_t,[i("div",yt,[i("span",null,"ID: "+o(t.id),1),t.applicabilities?.length?(a(),n("span",ht,o(t.applicabilities.length)+" "+o(u.pluralize(t.applicabilities.length,["позиция","позиции","позиций"])),1)):_("",!0),t.equipmentApplicabilities?.length?(a(),n("span",bt,o(t.equipmentApplicabilities.length)+" "+o(u.pluralize(t.equipmentApplicabilities.length,["модель","модели","моделей"]))+" техники ",1)):_("",!0)]),i("div",vt,o(e.formatDate(t.updatedAt)),1)])])])]),_:2},1032,["onClick"]))),128))])])}const kt=E(Ze,[["render",xt],["__scopeId","data-v-8c03748a"]]),Ct=F({__name:"CatalogTable",props:{parts:{},loading:{type:Boolean,default:!1},totalRecords:{default:0},itemsPerPage:{default:20}},emits:["partClick"],setup(u,{expose:r,emit:l}){r();const e=()=>"🖼️",d=u,y=l,h={ImageIcon:e,props:d,emit:y,onRowClick:m=>{y("partClick",m.data)},formatAttributeValue:m=>{if(!m.value)return"";const C=m.value,I=m.template.unit;return I?`${C} ${I.symbol||I.name}`:C},formatDate:m=>m?new Date(m).toLocaleDateString("ru-RU",{day:"2-digit",month:"2-digit",year:"numeric"}):"",DataTable:Be,get Column(){return Fe},Badge:te,SecondaryButton:O};return Object.defineProperty(h,"__isScriptSetup",{enumerable:!1,value:!0}),h}}),It={class:"catalog-table"},wt={class:"w-12 h-12 bg-surface-100 dark:bg-surface-800 rounded overflow-hidden"},Pt=["src","alt"],Bt={key:1,class:"w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"},Ft={class:"font-mono text-sm"},Et={class:"font-medium text-surface-900 dark:text-surface-0"},At={key:0,class:"text-sm text-surface-500 dark:text-surface-400"},Mt={key:1,class:"text-surface-400 dark:text-surface-500"},St={key:0,class:"space-y-1"},qt={class:"text-surface-600 dark:text-surface-300"},Dt={class:"ml-1 text-surface-900 dark:text-surface-0 font-medium"},Vt={key:0,class:"text-xs text-surface-500 dark:text-surface-400"},jt={key:1,class:"text-surface-400 dark:text-surface-500"},Qt={class:"space-y-1 text-sm"},zt={key:0,class:"text-surface-600 dark:text-surface-300"},Rt={key:1,class:"text-surface-600 dark:text-surface-300"},Ot={key:2,class:"text-surface-400 dark:text-surface-500"},Tt={class:"text-sm text-surface-500 dark:text-surface-400"};function Lt(u,r,l,e,d,y){return a(),n("div",It,[g(e.DataTable,{value:l.parts,loading:l.loading,"striped-rows":"",hover:"",onRowClick:e.onRowClick,class:"cursor-pointer"},{default:f(()=>[g(e.Column,{header:"Фото",style:{width:"80px"}},{body:f(({data:t})=>[i("div",wt,[t.image?.url?(a(),n("img",{key:0,src:t.image.url,alt:t.name||"Запчасть",class:"w-full h-full object-cover",loading:"lazy"},null,8,Pt)):(a(),n("div",Bt,[g(e.ImageIcon,{class:"w-6 h-6"})]))])]),_:1}),g(e.Column,{field:"id",header:"ID",sortable:"",style:{width:"80px"}},{body:f(({data:t})=>[i("span",Ft,o(t.id),1)]),_:1}),g(e.Column,{field:"name",header:"Название",sortable:""},{body:f(({data:t})=>[i("div",null,[i("div",Et,o(t.name||`Запчасть #${t.id}`),1),t.path?(a(),n("div",At,o(t.path),1)):_("",!0)])]),_:1}),g(e.Column,{field:"partCategory.name",header:"Категория",sortable:""},{body:f(({data:t})=>[t.partCategory?(a(),k(e.Badge,{key:0,value:t.partCategory.name,severity:"info"},null,8,["value"])):(a(),n("span",Mt,"—"))]),_:1}),g(e.Column,{header:"Атрибуты"},{body:f(({data:t})=>[t.attributes?.length?(a(),n("div",St,[(a(!0),n(D,null,B(t.attributes.slice(0,3),c=>(a(),n("div",{key:c.id,class:"text-sm"},[i("span",qt,o(c.template.title)+": ",1),i("span",Dt,o(e.formatAttributeValue(c)),1)]))),128)),t.attributes.length>3?(a(),n("div",Vt," +"+o(t.attributes.length-3)+" еще ",1)):_("",!0)])):(a(),n("span",jt,"—"))]),_:1}),g(e.Column,{header:"Совместимость"},{body:f(({data:t})=>[i("div",Qt,[t.applicabilities?.length?(a(),n("div",zt,o(t.applicabilities.length)+" "+o(u.pluralize(t.applicabilities.length,["позиция","позиции","позиций"])),1)):_("",!0),t.equipmentApplicabilities?.length?(a(),n("div",Rt,o(t.equipmentApplicabilities.length)+" "+o(u.pluralize(t.equipmentApplicabilities.length,["модель","модели","моделей"])),1)):_("",!0),!t.applicabilities?.length&&!t.equipmentApplicabilities?.length?(a(),n("span",Ot,"—")):_("",!0)])]),_:1}),g(e.Column,{field:"updatedAt",header:"Обновлено",sortable:"",style:{width:"120px"}},{body:f(({data:t})=>[i("span",Tt,o(e.formatDate(t.updatedAt)),1)]),_:1}),g(e.Column,{header:"Действия",style:{width:"120px"}},{body:f(({data:t})=>[g(e.SecondaryButton,{size:"small",outlined:"",onClick:H(c=>u.$emit("partClick",t),["stop"])},{default:f(()=>r[0]||(r[0]=[P(" Подробнее ")])),_:2,__:[0]},1032,["onClick"])]),_:1})]),_:1},8,["value","loading"])])}const Nt=E(Ct,[["render",Lt],["__scopeId","data-v-d4823d7d"]]),Ut=F({__name:"CatalogBrowser",props:{initialFilters:{},showFilters:{type:Boolean,default:!0},initialParts:{},initialCategories:{},initialBrands:{},categoriesQuery:{},brandsQuery:{}},emits:["partClick"],setup(u,{expose:r,emit:l}){r();const e=()=>"⊞",d=()=>"☰",y=()=>"⊟",t=u,c=l,b=K(1),{filters:h,viewPreferences:m,hasFilters:C,queryParams:I,setSearch:A,setCategoryIds:V,setBrandIds:j,resetFilters:T,setViewMode:s,setItemsPerPage:x}=Me(t.initialFilters),M=v({get:()=>h.value.search||"",set:p=>A(p||"")}),re=v({get:()=>h.value.categoryIds||[],set:p=>V(p||[])}),se=v({get:()=>h.value.brandIds||[],set:p=>j(p||[])}),W=ve(M,300),S=v(()=>({...I.value,take:m.value.itemsPerPage,skip:(b.value-1)*m.value.itemsPerPage,orderBy:m.value.sortBy?{[m.value.sortBy]:m.value.sortOrder}:{name:"asc"},include:{partCategory:!0,image:!0,attributes:{include:{template:!0}}}})),{data:ae,error:X}=Q({queryKey:z.categories.list(t.categoriesQuery),queryFn:()=>R.categories.list(t.categoriesQuery),initialData:t.initialCategories});N(X);const{data:ue,error:Y}=Q({queryKey:z.brands.list(t.brandsQuery),queryFn:()=>R.brands.list(t.brandsQuery),initialData:t.initialBrands});N(Y);const w=Q({queryKey:v(()=>z.parts.list(S.value)),queryFn:()=>R.parts.list(S.value),initialData:t.initialParts,placeholderData:p=>p});N(w.error);const L=v(()=>w.data?.value||[]),ie=v(()=>!!w.isFetching.value),ne=v(()=>w.error.value?w.error.value?.message||"Ошибка":null),Z=Q({queryKey:v(()=>z.parts.count({where:S.value.where})),queryFn:()=>R.parts.count({where:S.value.where}),initialData:Array.isArray(t.initialParts)?t.initialParts.length:0,placeholderData:p=>p}),$=v(()=>{const p=Z.data?.value??void 0;return typeof p=="number"?p:Array.isArray(L.value)?L.value.length:0}),oe=()=>{},le=p=>c("partClick",p),ce=p=>{b.value=p},de=()=>w.refetch();U(W,(p,q)=>{p!==q&&(A(String(p)),b.value=1)}),U(I,(p,q)=>{JSON.stringify(p)!==JSON.stringify(q)&&(b.value=1)},{deep:!0}),U(()=>m.value.itemsPerPage,(p,q)=>{p!==q&&(b.value=1)});const me=v(()=>ge($.value,["запчасть","запчасти","запчастей"]));he(()=>{});const ee={GridIcon:e,ListIcon:d,TableIcon:y,props:t,emit:c,currentPage:b,filters:h,viewPreferences:m,hasFilters:C,queryParams:I,setSearch:A,setCategoryIds:V,setBrandIds:j,resetFilters:T,setViewMode:s,setItemsPerPage:x,searchQuery:M,selectedCategoryIds:re,selectedBrandIds:se,debouncedSearch:W,baseListInput:S,categories:ae,categoriesError:X,brands:ue,brandsError:Y,partsQuery:w,parts:L,loading:ie,error:ne,countQuery:Z,totalCount:$,onSearchInput:oe,onPartClick:le,onPageChange:ce,refetch:de,partsLabel:me,Card:J,InputText:Ce,MultiSelect:Ie,SecondaryButton:O,CatalogGrid:We,CatalogList:kt,CatalogTable:Nt,CatalogPagination:ke,Spinner:xe};return Object.defineProperty(ee,"__isScriptSetup",{enumerable:!1,value:!0}),ee}}),Gt={class:"catalog-browser"},Kt={class:"mb-6 flex items-center justify-between"},Jt={class:"flex items-center gap-4"},Ht={key:0,class:"text-surface-500 dark:text-surface-400"},Wt={class:"flex items-center gap-2"},Xt={key:0,class:"mb-6"},Yt={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},Zt={class:"flex items-end"},$t={class:"catalog-content"},er={key:0,class:"flex justify-center py-12"},tr={key:1,class:"py-12 text-center"},rr={class:"mb-4 text-red-500"},sr={key:2,class:"py-12 text-center"},ar={class:"text-surface-500 dark:text-surface-400 mb-4"},ur={key:3};function ir(u,r,l,e,d,y){return a(),n("div",Gt,[i("div",Kt,[i("div",Jt,[r[8]||(r[8]=i("h1",{class:"text-surface-900 dark:text-surface-0 text-2xl font-semibold"},"Каталог запчастей",-1)),e.totalCount!==null?(a(),n("div",Ht,o(e.totalCount)+" "+o(e.partsLabel),1)):_("",!0)]),i("div",Wt,[g(e.SecondaryButton,{class:G({"bg-primary text-primary-contrast":e.viewPreferences&&e.viewPreferences.mode==="grid"}),onClick:r[0]||(r[0]=t=>e.setViewMode("grid")),"icon-only":"",title:"Сетка"},{icon:f(()=>[g(e.GridIcon)]),_:1},8,["class"]),g(e.SecondaryButton,{class:G({"bg-primary text-primary-contrast":e.viewPreferences&&e.viewPreferences.mode==="list"}),onClick:r[1]||(r[1]=t=>e.setViewMode("list")),"icon-only":"",title:"Список"},{icon:f(()=>[g(e.ListIcon)]),_:1},8,["class"]),g(e.SecondaryButton,{class:G({"bg-primary text-primary-contrast":e.viewPreferences&&e.viewPreferences.mode==="table"}),onClick:r[2]||(r[2]=t=>e.setViewMode("table")),"icon-only":"",title:"Таблица"},{icon:f(()=>[g(e.TableIcon)]),_:1},8,["class"])])]),l.showFilters?(a(),n("div",Xt,[g(e.Card,null,{content:f(()=>[i("div",Yt,[i("div",null,[r[9]||(r[9]=i("label",{class:"mb-2 block text-sm font-medium"},"Поиск",-1)),g(e.InputText,{modelValue:e.searchQuery,"onUpdate:modelValue":r[3]||(r[3]=t=>e.searchQuery=t),placeholder:"Поиск запчастей...",onInput:e.onSearchInput},null,8,["modelValue"])]),i("div",null,[r[10]||(r[10]=i("label",{class:"mb-2 block text-sm font-medium"},"Категории",-1)),g(e.MultiSelect,{modelValue:e.selectedCategoryIds,"onUpdate:modelValue":r[4]||(r[4]=t=>e.selectedCategoryIds=t),options:e.categories,"option-label":"name","option-value":"id",placeholder:"Выберите категории",onChange:r[5]||(r[5]=t=>e.setCategoryIds(t.value||[]))},null,8,["modelValue","options"])]),i("div",null,[r[11]||(r[11]=i("label",{class:"mb-2 block text-sm font-medium"},"Бренды",-1)),g(e.MultiSelect,{modelValue:e.selectedBrandIds,"onUpdate:modelValue":r[6]||(r[6]=t=>e.selectedBrandIds=t),options:e.brands,"option-label":"name","option-value":"id",placeholder:"Выберите бренды",onChange:r[7]||(r[7]=t=>e.setBrandIds(t.value||[]))},null,8,["modelValue","options"])]),i("div",Zt,[e.hasFilters?(a(),k(e.SecondaryButton,{key:0,onClick:e.resetFilters,outlined:""},{default:f(()=>r[12]||(r[12]=[P(" Сбросить фильтры ")])),_:1,__:[12]},8,["onClick"])):_("",!0)])])]),_:1})])):_("",!0),i("div",$t,[e.loading?(a(),n("div",er,[g(e.Spinner,{size:"lg",variant:"primary",label:"Загрузка...",centered:!0})])):e.error?(a(),n("div",tr,[i("div",rr,o(e.error),1),g(e.SecondaryButton,{onClick:e.refetch},{default:f(()=>r[13]||(r[13]=[P("Повторить")])),_:1,__:[13]})])):e.parts?.length?(a(),n("div",ur,[e.viewPreferences&&e.viewPreferences.mode==="grid"?(a(),k(e.CatalogGrid,{key:0,parts:e.parts,onPartClick:e.onPartClick},null,8,["parts"])):e.viewPreferences&&e.viewPreferences.mode==="list"?(a(),k(e.CatalogList,{key:1,parts:e.parts,onPartClick:e.onPartClick},null,8,["parts"])):e.viewPreferences&&e.viewPreferences.mode==="table"?(a(),k(e.CatalogTable,{key:2,parts:e.parts,onPartClick:e.onPartClick},null,8,["parts"])):_("",!0),e.totalCount&&e.viewPreferences&&e.totalCount>e.viewPreferences.itemsPerPage?(a(),k(e.CatalogPagination,{key:3,"current-page":e.currentPage,"total-items":e.totalCount,"items-per-page":e.viewPreferences.itemsPerPage,onPageChange:e.onPageChange,onItemsPerPageChange:e.setItemsPerPage},null,8,["current-page","total-items","items-per-page","onItemsPerPageChange"])):_("",!0)])):(a(),n("div",sr,[i("div",ar,o(e.hasFilters?"Запчасти не найдены":"Каталог пуст"),1),e.hasFilters?(a(),k(e.SecondaryButton,{key:0,onClick:e.resetFilters},{default:f(()=>r[14]||(r[14]=[P(" Сбросить фильтры ")])),_:1,__:[14]},8,["onClick"])):_("",!0)]))])])}const nr=E(Ut,[["render",ir],["__scopeId","data-v-662d9b80"]]),or=F({__name:"CatalogBrowserBoundary",props:{initialParts:{},initialCategories:{},initialBrands:{},categoriesQuery:{},brandsQuery:{}},setup(u,{expose:r}){r();const l={CatalogBrowser:nr};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}});function lr(u,r,l,e,d,y){return a(),k(e.CatalogBrowser,{"initial-parts":l.initialParts,"initial-categories":l.initialCategories,"initial-brands":l.initialBrands,"categories-query":l.categoriesQuery,"brands-query":l.brandsQuery},null,8,["initial-parts","initial-categories","initial-brands","categories-query","brands-query"])}const Zr=E(or,[["render",lr]]);export{Zr as default};

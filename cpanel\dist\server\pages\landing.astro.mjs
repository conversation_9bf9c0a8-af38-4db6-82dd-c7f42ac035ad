import { e as createComponent, k as renderComponent, r as renderTemplate } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_AEsVEWgS.mjs';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Landing = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "PartTec - \u041F\u0440\u043E\u0444\u0435\u0441\u0441\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u0430\u044F \u0431\u0430\u0437\u0430 \u0434\u0430\u043D\u043D\u044B\u0445 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "LandingPage", null, { "client:only": "vue", "client:component-hydration": "only", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/landing/LandingPage.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/landing.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/landing.astro";
const $$url = "/landing";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Landing,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

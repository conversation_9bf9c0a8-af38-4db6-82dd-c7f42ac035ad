import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { T as Toast, I as Icon, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { defineComponent, useSSRContext, ref, onMounted, mergeProps, withCtx, createTextVNode, createVNode, toDisplayString, createBlock, openBlock, Fragment, renderList, createCommentVNode } from 'vue';
import { u as useTrpc } from '../../chunks/useTrpc_CjmFMz0m.mjs';
import { u as useConfirm, C as ConfirmDialog } from '../../chunks/ConfirmDialog_D9uxkFze.mjs';
import { u as useToast, _ as _export_sfc } from '../../chunks/ClientRouter_avhRMbqw.mjs';
import { M as MatchingDetailsGrid, u as useMatchingLabels } from '../../chunks/MatchingDetailsGrid_qP1_u1_F.mjs';
import { t as trpc } from '../../chunks/trpc_DApR3DD7.mjs';
import 'clsx';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { D as DataTable, s as script } from '../../chunks/index_CxapvcaX.mjs';
import { T as Tag } from '../../chunks/Tag_B6nH2bAR.mjs';
import { M as Message } from '../../chunks/Message_acgdACvd.mjs';
import { D as Dialog } from '../../chunks/Dialog_DqmfICId.mjs';
import { P as PartWizard } from '../../chunks/PartWizard_DfzK7ldw.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_BeMdq3W3.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import { V as VTextarea } from '../../chunks/Textarea_nBNQZgaf.mjs';
import { M as MatchingLoadingState, a as MatchingEmptyState } from '../../chunks/MatchingLoadingState_CU-YySWT.mjs';
import { PencilIcon, TrashIcon, LinkIcon, PlusIcon, RefreshCcwIcon } from 'lucide-vue-next';
import { S as SecondaryButton } from '../../chunks/SecondaryButton_B0hmlm1n.mjs';
import { D as DangerButton } from '../../chunks/DangerButton_BHpYZbTv.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrRenderList } from 'vue/server-renderer';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "PartsTable",
  setup(__props, { expose: __expose }) {
    __expose();
    const { loading, error, clearError, parts: partsApi, partCategories, matching, partApplicability } = useTrpc();
    const confirm = useConfirm();
    const toast = useToast();
    const { getAccuracyLabel, getAccuracySeverity } = useMatchingLabels();
    const parts = ref([]);
    const categories = ref([]);
    const totalCount = ref(0);
    const pageSize = ref(25);
    const currentPage = ref(0);
    const expandedRows = ref([]);
    const partAttributes = ref({});
    const searchQuery = ref("");
    const selectedCategory = ref(null);
    const categorySuggestions = ref([]);
    const editDialogVisible = ref(false);
    const selectedPartForEdit = ref(null);
    const matchingDialogVisible = ref(false);
    const selectedPartForMatching = ref(null);
    const matchingLoading = ref(false);
    const matchingResults = ref(null);
    const showLinkConfirmDialog = ref(false);
    const selectedLinkCandidate = ref(null);
    const linking = ref(false);
    const linkConfirmForm = ref({
      accuracy: "EXACT_MATCH",
      notes: ""
    });
    const accuracyOptions = [
      { label: "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435", value: "EXACT_MATCH" },
      { label: "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u0441 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438", value: "MATCH_WITH_NOTES" },
      { label: "\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043C\u043E\u0434\u0438\u0444\u0438\u043A\u0430\u0446\u0438\u044F", value: "REQUIRES_MODIFICATION" },
      { label: "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435", value: "PARTIAL_MATCH" }
    ];
    const sortField = ref("createdAt");
    const sortOrder = ref(-1);
    const loadParts = async () => {
      try {
        clearError();
        const filters = {
          skip: currentPage.value * pageSize.value,
          take: pageSize.value,
          orderBy: {
            [sortField.value]: sortOrder.value === 1 ? "asc" : "desc"
          },
          include: {
            image: true,
            partCategory: true,
            attributes: true,
            applicabilities: {
              include: {
                catalogItem: {
                  include: {
                    brand: true
                  }
                }
              }
            },
            equipmentApplicabilities: {
              include: {
                equipmentModel: {
                  include: {
                    brand: true
                  }
                }
              }
            }
          }
        };
        if (searchQuery.value.trim()) {
          filters.where = {
            ...filters.where,
            name: {
              contains: searchQuery.value.trim(),
              mode: "insensitive"
            }
          };
        }
        if (selectedCategory.value) {
          const categoryId = typeof selectedCategory.value === "object" ? selectedCategory.value.id : selectedCategory.value;
          filters.where = {
            ...filters.where,
            partCategoryId: categoryId
          };
        }
        const result = await partsApi.findMany(filters);
        if (result) {
          parts.value = result;
        }
        const countResult = await partsApi.findMany({
          where: filters.where,
          select: { id: true }
        });
        if (countResult) {
          totalCount.value = countResult.length;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439:", err);
      }
    };
    const loadCategories = async () => {
      try {
        const result = await partCategories.findMany({
          orderBy: { name: "asc" }
        });
        if (result) {
          categories.value = result;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439:", err);
      }
    };
    const loadPartAttributes = async (partId) => {
      try {
        const result = await trpc.partAttributes.findByPartId.query({ partId });
        if (result) {
          partAttributes.value[partId] = result;
        }
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438:", err);
      }
    };
    const onPageChange = (event) => {
      currentPage.value = event.page;
      pageSize.value = event.rows;
      loadParts();
    };
    const onSort = (event) => {
      sortField.value = event.sortField;
      sortOrder.value = event.sortOrder;
      loadParts();
    };
    const onRowExpand = (event) => {
      const partId = event.data.id;
      if (!partAttributes.value[partId]) {
        loadPartAttributes(partId);
      }
    };
    const refreshData = () => {
      loadParts();
    };
    const applyFilters = () => {
      currentPage.value = 0;
      loadParts();
    };
    let searchTimeout;
    const debouncedSearch = () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        currentPage.value = 0;
        loadParts();
      }, 500);
    };
    const editPart = (part) => {
      selectedPartForEdit.value = part;
      editDialogVisible.value = true;
    };
    const deletePart = (part) => {
      confirm.confirmDelete(
        "\u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
        async () => {
          try {
            await partsApi.delete({ where: { id: part.id } });
            loadParts();
          } catch (err) {
            toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C", 5e3);
          }
        }
      );
    };
    const onPartSaved = (part) => {
      loadParts();
      selectedPartForEdit.value = null;
      editDialogVisible.value = false;
    };
    const openMatching = (part) => {
      selectedPartForMatching.value = part;
      matchingDialogVisible.value = true;
      runPartMatching();
    };
    const runPartMatching = async () => {
      if (!selectedPartForMatching.value) return;
      matchingLoading.value = true;
      matchingResults.value = null;
      try {
        const res = await matching.findMatchingCatalogItems({ partId: selectedPartForMatching.value.id });
        matchingResults.value = res ? res.candidates || [] : [];
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0434\u0431\u043E\u0440\u0430 \u0434\u043B\u044F Part:", err);
        matchingResults.value = [];
      } finally {
        matchingLoading.value = false;
      }
    };
    const openLinkConfirmDialog = (candidate) => {
      selectedLinkCandidate.value = candidate;
      linkConfirmForm.value.accuracy = candidate.accuracySuggestion;
      linkConfirmForm.value.notes = "";
      const withNotes = (candidate.details || []).find((d) => String(d.kind).includes("NEAR") || String(d.kind).includes("LEGACY"));
      if (withNotes?.notes) {
        linkConfirmForm.value.notes = withNotes.notes;
      }
      const tol = (candidate.details || []).find((d) => d.kind === "NUMBER_WITHIN_TOLERANCE");
      if (tol && !linkConfirmForm.value.notes) {
        linkConfirmForm.value.notes = "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u043F\u043E \u0434\u043E\u043F\u0443\u0441\u043A\u0443";
      }
      showLinkConfirmDialog.value = true;
    };
    const closeLinkConfirmDialog = () => {
      showLinkConfirmDialog.value = false;
      selectedLinkCandidate.value = null;
      linkConfirmForm.value.accuracy = "EXACT_MATCH";
      linkConfirmForm.value.notes = "";
    };
    const confirmLinkItem = async () => {
      if (!selectedPartForMatching.value || !selectedLinkCandidate.value) return;
      linking.value = true;
      try {
        await partApplicability.upsert({
          where: {
            partId_catalogItemId: {
              partId: selectedPartForMatching.value.id,
              catalogItemId: selectedLinkCandidate.value.catalogItem.id
            }
          },
          create: {
            partId: selectedPartForMatching.value.id,
            catalogItemId: selectedLinkCandidate.value.catalogItem.id,
            accuracy: linkConfirmForm.value.accuracy,
            notes: linkConfirmForm.value.notes || void 0
          },
          update: {
            accuracy: linkConfirmForm.value.accuracy,
            notes: linkConfirmForm.value.notes || void 0
          }
        });
        matchingDialogVisible.value = false;
        showLinkConfirmDialog.value = false;
        toast.success("\u0423\u0441\u043F\u0435\u0448\u043D\u043E", "\u041F\u043E\u0437\u0438\u0446\u0438\u044F \u043F\u0440\u0438\u0432\u044F\u0437\u0430\u043D\u0430 \u043A \u0433\u0440\u0443\u043F\u043F\u0435");
        loadParts();
        closeLinkConfirmDialog();
      } catch (err) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438\u0432\u044F\u0437\u043A\u0438:", err);
        toast.error("\u041E\u0448\u0438\u0431\u043A\u0430", "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C \u043F\u043E\u0437\u0438\u0446\u0438\u044E");
      } finally {
        linking.value = false;
      }
    };
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    };
    const getUnitLabel = (unit) => {
      const labels = {
        "MM": "\u043C\u043C",
        "INCH": "\u0434\u044E\u0439\u043C\u044B",
        "FT": "\u0444\u0443\u0442\u044B",
        "G": "\u0433",
        "KG": "\u043A\u0433",
        "T": "\u0442",
        "LB": "\u0444\u0443\u043D\u0442\u044B",
        "ML": "\u043C\u043B",
        "L": "\u043B",
        "GAL": "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        "PCS": "\u0448\u0442",
        "SET": "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        "PAIR": "\u043F\u0430\u0440\u0430",
        "BAR": "\u0431\u0430\u0440",
        "PSI": "PSI",
        "KW": "\u043A\u0412\u0442",
        "HP": "\u043B.\u0441.",
        "NM": "\u041D\u22C5\u043C",
        "RPM": "\u043E\u0431/\u043C\u0438\u043D",
        "C": "\xB0C",
        "F": "\xB0F",
        "PERCENT": "%"
      };
      return labels[unit] || unit;
    };
    const filterCategories = (event) => {
      const query = event.query.toLowerCase();
      if (!query) {
        categorySuggestions.value = categories.value;
      } else {
        categorySuggestions.value = categories.value.filter((category) => category.name.toLowerCase().includes(query));
      }
    };
    const initializeCategorySuggestions = () => {
      categorySuggestions.value = categories.value;
    };
    onMounted(() => {
      loadCategories();
      loadParts();
      initializeCategorySuggestions();
    });
    const __returned__ = { loading, error, clearError, partsApi, partCategories, matching, partApplicability, confirm, toast, getAccuracyLabel, getAccuracySeverity, parts, categories, totalCount, pageSize, currentPage, expandedRows, partAttributes, searchQuery, selectedCategory, categorySuggestions, editDialogVisible, selectedPartForEdit, matchingDialogVisible, selectedPartForMatching, matchingLoading, matchingResults, showLinkConfirmDialog, selectedLinkCandidate, linking, linkConfirmForm, accuracyOptions, sortField, sortOrder, loadParts, loadCategories, loadPartAttributes, onPageChange, onSort, onRowExpand, refreshData, applyFilters, get searchTimeout() {
      return searchTimeout;
    }, set searchTimeout(v) {
      searchTimeout = v;
    }, debouncedSearch, editPart, deletePart, onPartSaved, openMatching, runPartMatching, openLinkConfirmDialog, closeLinkConfirmDialog, confirmLinkItem, formatDate, getUnitLabel, filterCategories, initializeCategorySuggestions, VCard: Card, VButton: Button, VInputText: InputText, VDataTable: DataTable, get VColumn() {
      return script;
    }, VTag: Tag, VMessage: Message, VDialog: Dialog, PartWizard, VConfirmDialog: ConfirmDialog, VAutoComplete, VSelect: Select, VTextarea, Icon, MatchingDetailsGrid, MatchingEmptyState, MatchingLoadingState, get RefreshCcwIcon() {
      return RefreshCcwIcon;
    }, get PlusIcon() {
      return PlusIcon;
    }, SecondaryButton, get LinkIcon() {
      return LinkIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, DangerButton, get PencilIcon() {
      return PencilIcon;
    }, Toast };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-6" }, _attrs))}><div class="flex items-center justify-between"><div><h2 class="text-surface-900 dark:text-surface-0 text-xl font-semibold">\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438</h2><p class="text-surface-600 dark:text-surface-400 mt-1 text-sm">\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u0430\u043C\u0438 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438</p></div><div class="flex gap-3">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    onClick: $setup.refreshData,
    disabled: $setup.loading,
    outlined: ""
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(` \u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C `);
        _push2(ssrRenderComponent($setup["RefreshCcwIcon"], { class: "h-4 w-4" }, null, _parent2, _scopeId));
      } else {
        return [
          createTextVNode(" \u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C "),
          createVNode($setup["RefreshCcwIcon"], { class: "h-4 w-4" })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`<a href="/admin/parts/create">`);
  _push(ssrRenderComponent($setup["VButton"], { outlined: "" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C `);
        _push2(ssrRenderComponent($setup["PlusIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C "),
          createVNode($setup["PlusIcon"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</a></div></div>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-6"${_scopeId}><div class="grid grid-cols-1 gap-4 md:grid-cols-3"${_scopeId}><div${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E </label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.searchQuery,
          "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
          placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438...",
          class: "w-full",
          onInput: $setup.debouncedSearch
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex-1"${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u0424\u0438\u043B\u044C\u0442\u0440 \u043F\u043E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 </label>`);
        _push2(ssrRenderComponent($setup["VAutoComplete"], {
          modelValue: $setup.selectedCategory,
          "onUpdate:modelValue": ($event) => $setup.selectedCategory = $event,
          suggestions: $setup.categorySuggestions,
          onComplete: $setup.filterCategories,
          "option-label": "name",
          "option-value": "id",
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438...",
          class: "w-full",
          dropdown: "",
          "show-clear": "",
          onChange: $setup.applyFilters
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex items-end"${_scopeId}><div class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId}><div${_scopeId}> \u0412\u0441\u0435\u0433\u043E \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439: <span class="text-surface-900 dark:text-surface-100 font-medium"${_scopeId}>${ssrInterpolate($setup.totalCount)}</span></div><div${_scopeId}> \u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E: <span class="text-surface-900 dark:text-surface-100 font-medium"${_scopeId}>${ssrInterpolate($setup.parts.length)}</span></div></div></div></div></div>`);
      } else {
        return [
          createVNode("div", { class: "p-6" }, [
            createVNode("div", { class: "grid grid-cols-1 gap-4 md:grid-cols-3" }, [
              createVNode("div", null, [
                createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E "),
                createVNode($setup["VInputText"], {
                  modelValue: $setup.searchQuery,
                  "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                  placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438...",
                  class: "w-full",
                  onInput: $setup.debouncedSearch
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", { class: "flex-1" }, [
                createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u0424\u0438\u043B\u044C\u0442\u0440 \u043F\u043E \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438 "),
                createVNode($setup["VAutoComplete"], {
                  modelValue: $setup.selectedCategory,
                  "onUpdate:modelValue": ($event) => $setup.selectedCategory = $event,
                  suggestions: $setup.categorySuggestions,
                  onComplete: $setup.filterCategories,
                  "option-label": "name",
                  "option-value": "id",
                  placeholder: "\u041F\u043E\u0438\u0441\u043A \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438...",
                  class: "w-full",
                  dropdown: "",
                  "show-clear": "",
                  onChange: $setup.applyFilters
                }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
              ]),
              createVNode("div", { class: "flex items-end" }, [
                createVNode("div", { class: "text-surface-600 dark:text-surface-400 text-sm" }, [
                  createVNode("div", null, [
                    createTextVNode(" \u0412\u0441\u0435\u0433\u043E \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439: "),
                    createVNode("span", { class: "text-surface-900 dark:text-surface-100 font-medium" }, toDisplayString($setup.totalCount), 1)
                  ]),
                  createVNode("div", null, [
                    createTextVNode(" \u041F\u043E\u043A\u0430\u0437\u0430\u043D\u043E: "),
                    createVNode("span", { class: "text-surface-900 dark:text-surface-100 font-medium" }, toDisplayString($setup.parts.length), 1)
                  ])
                ])
              ])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VDataTable"], {
          value: $setup.parts,
          loading: $setup.loading,
          paginator: "",
          rows: $setup.pageSize,
          "total-records": $setup.totalCount,
          "rows-per-page-options": [10, 25, 50],
          lazy: "",
          onPage: $setup.onPageChange,
          onSort: $setup.onSort,
          "table-style": "min-width: 50rem",
          class: "p-datatable-sm",
          "striped-rows": "",
          expandedRows: $setup.expandedRows,
          "onUpdate:expandedRows": ($event) => $setup.expandedRows = $event,
          onRowExpand: $setup.onRowExpand
        }, {
          expansion: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4"${_scopeId2}><div class="grid grid-cols-1 gap-6 lg:grid-cols-2"${_scopeId2}><div${_scopeId2}><h4 class="text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-list",
                class: "h-4 w-4 text-blue-600"
              }, null, _parent3, _scopeId2));
              _push3(` \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 </h4>`);
              if ($setup.partAttributes[data.id]?.length > 0) {
                _push3(`<div class="space-y-2"${_scopeId2}><!--[-->`);
                ssrRenderList($setup.partAttributes[data.id], (attr) => {
                  _push3(`<div class="bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"${_scopeId2}><div class="flex-1"${_scopeId2}><div class="text-surface-900 dark:text-surface-100 text-sm font-medium"${_scopeId2}>${ssrInterpolate(attr.template?.title || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F")}</div>`);
                  if (attr.description) {
                    _push3(`<div class="text-surface-500 dark:text-surface-400 mt-1 text-sm"${_scopeId2}>${ssrInterpolate(attr.description)}</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div><div class="ml-3 text-right"${_scopeId2}><div class="text-surface-700 dark:text-surface-300 font-medium"${_scopeId2}>${ssrInterpolate(attr.value || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E")}</div>`);
                  if (attr.unit) {
                    _push3(`<div class="text-surface-500 dark:text-surface-400 text-sm"${_scopeId2}>${ssrInterpolate($setup.getUnitLabel(attr.unit))}</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div></div>`);
                });
                _push3(`<!--]--></div>`);
              } else {
                _push3(`<div class="text-surface-500 dark:text-surface-400 py-6 text-center"${_scopeId2}>`);
                _push3(ssrRenderComponent($setup["Icon"], {
                  name: "pi pi-info-circle",
                  class: "mb-2 inline-block text-2xl"
                }, null, _parent3, _scopeId2));
                _push3(` \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043D\u0435 \u0437\u0430\u0434\u0430\u043D\u044B </div>`);
              }
              _push3(`</div><div${_scopeId2}><h4 class="text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-box",
                class: "h-4 w-4 text-green-600"
              }, null, _parent3, _scopeId2));
              _push3(` \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 </h4>`);
              if (data.applicabilities?.length > 0) {
                _push3(`<div class="space-y-2"${_scopeId2}><!--[-->`);
                ssrRenderList(data.applicabilities, (applicability) => {
                  _push3(`<div class="bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"${_scopeId2}><div class="flex items-start justify-between"${_scopeId2}><div class="flex-1"${_scopeId2}><div class="text-surface-900 dark:text-surface-100 font-medium"${_scopeId2}>${ssrInterpolate(applicability.catalogItem?.sku || "N/A")}</div><div class="text-surface-600 dark:text-surface-400 mt-1 text-sm"${_scopeId2}>${ssrInterpolate(applicability.catalogItem?.brand?.name || "\u041D\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043D\u044B\u0439 \u0431\u0440\u0435\u043D\u0434")}</div>`);
                  if (applicability.catalogItem?.description) {
                    _push3(`<div class="text-surface-500 dark:text-surface-400 mt-1 text-sm"${_scopeId2}>${ssrInterpolate(applicability.catalogItem.description)}</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div><div class="ml-3"${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["VTag"], {
                    severity: $setup.getAccuracySeverity(applicability.accuracy),
                    class: "text-sm"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate($setup.getAccuracyLabel(applicability.accuracy))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString($setup.getAccuracyLabel(applicability.accuracy)), 1)
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                  if (applicability.notes) {
                    _push3(`<div class="bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"${_scopeId2}>`);
                    _push3(ssrRenderComponent($setup["Icon"], {
                      name: "pi pi-info-circle",
                      class: "mr-1 inline-block h-4 w-4"
                    }, null, _parent3, _scopeId2));
                    _push3(` ${ssrInterpolate(applicability.notes)}</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div>`);
                });
                _push3(`<!--]--></div>`);
              } else {
                _push3(`<div class="text-surface-500 dark:text-surface-400 py-6 text-center"${_scopeId2}>`);
                _push3(ssrRenderComponent($setup["Icon"], {
                  name: "pi pi-info-circle",
                  class: "mb-2 inline-block text-2xl"
                }, null, _parent3, _scopeId2));
                _push3(` \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043D\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u044B </div>`);
              }
              _push3(`</div><div class="lg:col-span-2"${_scopeId2}><h4 class="text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium"${_scopeId2}>\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435</h4>`);
              if (data.equipmentApplicabilities?.length > 0) {
                _push3(`<div class="grid grid-cols-1 gap-3 md:grid-cols-2"${_scopeId2}><!--[-->`);
                ssrRenderList(data.equipmentApplicabilities, (equipmentApplicability) => {
                  _push3(`<div class="bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"${_scopeId2}><div class="flex items-start justify-between"${_scopeId2}><div class="flex-1"${_scopeId2}><div class="text-surface-900 dark:text-surface-100 font-medium"${_scopeId2}>${ssrInterpolate(equipmentApplicability.equipmentModel?.name || "N/A")}</div>`);
                  if (equipmentApplicability.equipmentModel?.brand) {
                    _push3(`<div class="text-surface-600 dark:text-surface-400 mt-1 text-sm"${_scopeId2}>${ssrInterpolate(equipmentApplicability.equipmentModel.brand.name)}</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div><div class="ml-3"${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["VTag"], {
                    severity: "info",
                    class: "text-sm"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(` \u0422\u0435\u0445\u043D\u0438\u043A\u0430 `);
                      } else {
                        return [
                          createTextVNode(" \u0422\u0435\u0445\u043D\u0438\u043A\u0430 ")
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                  if (equipmentApplicability.notes) {
                    _push3(`<div class="bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"${_scopeId2}>`);
                    _push3(ssrRenderComponent($setup["Icon"], {
                      name: "pi pi-info-circle",
                      class: "mr-1 inline-block h-4 w-4"
                    }, null, _parent3, _scopeId2));
                    _push3(` ${ssrInterpolate(equipmentApplicability.notes)}</div>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div>`);
                });
                _push3(`<!--]--></div>`);
              } else {
                _push3(`<div class="text-surface-500 dark:text-surface-400 py-6 text-center"${_scopeId2}>`);
                _push3(ssrRenderComponent($setup["Icon"], {
                  name: "pi pi-info-circle",
                  class: "mb-2 inline-block text-2xl"
                }, null, _parent3, _scopeId2));
                _push3(` \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435 \u043D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430 </div>`);
              }
              _push3(`</div></div></div>`);
            } else {
              return [
                createVNode("div", { class: "bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4" }, [
                  createVNode("div", { class: "grid grid-cols-1 gap-6 lg:grid-cols-2" }, [
                    createVNode("div", null, [
                      createVNode("h4", { class: "text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium" }, [
                        createVNode($setup["Icon"], {
                          name: "pi pi-list",
                          class: "h-4 w-4 text-blue-600"
                        }),
                        createTextVNode(" \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 ")
                      ]),
                      $setup.partAttributes[data.id]?.length > 0 ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "space-y-2"
                      }, [
                        (openBlock(true), createBlock(Fragment, null, renderList($setup.partAttributes[data.id], (attr) => {
                          return openBlock(), createBlock("div", {
                            key: attr.id,
                            class: "bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"
                          }, [
                            createVNode("div", { class: "flex-1" }, [
                              createVNode("div", { class: "text-surface-900 dark:text-surface-100 text-sm font-medium" }, toDisplayString(attr.template?.title || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"), 1),
                              attr.description ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "text-surface-500 dark:text-surface-400 mt-1 text-sm"
                              }, toDisplayString(attr.description), 1)) : createCommentVNode("", true)
                            ]),
                            createVNode("div", { class: "ml-3 text-right" }, [
                              createVNode("div", { class: "text-surface-700 dark:text-surface-300 font-medium" }, toDisplayString(attr.value || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E"), 1),
                              attr.unit ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "text-surface-500 dark:text-surface-400 text-sm"
                              }, toDisplayString($setup.getUnitLabel(attr.unit)), 1)) : createCommentVNode("", true)
                            ])
                          ]);
                        }), 128))
                      ])) : (openBlock(), createBlock("div", {
                        key: 1,
                        class: "text-surface-500 dark:text-surface-400 py-6 text-center"
                      }, [
                        createVNode($setup["Icon"], {
                          name: "pi pi-info-circle",
                          class: "mb-2 inline-block text-2xl"
                        }),
                        createTextVNode(" \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043D\u0435 \u0437\u0430\u0434\u0430\u043D\u044B ")
                      ]))
                    ]),
                    createVNode("div", null, [
                      createVNode("h4", { class: "text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium" }, [
                        createVNode($setup["Icon"], {
                          name: "pi pi-box",
                          class: "h-4 w-4 text-green-600"
                        }),
                        createTextVNode(" \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 ")
                      ]),
                      data.applicabilities?.length > 0 ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "space-y-2"
                      }, [
                        (openBlock(true), createBlock(Fragment, null, renderList(data.applicabilities, (applicability) => {
                          return openBlock(), createBlock("div", {
                            key: applicability.id,
                            class: "bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"
                          }, [
                            createVNode("div", { class: "flex items-start justify-between" }, [
                              createVNode("div", { class: "flex-1" }, [
                                createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, toDisplayString(applicability.catalogItem?.sku || "N/A"), 1),
                                createVNode("div", { class: "text-surface-600 dark:text-surface-400 mt-1 text-sm" }, toDisplayString(applicability.catalogItem?.brand?.name || "\u041D\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043D\u044B\u0439 \u0431\u0440\u0435\u043D\u0434"), 1),
                                applicability.catalogItem?.description ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "text-surface-500 dark:text-surface-400 mt-1 text-sm"
                                }, toDisplayString(applicability.catalogItem.description), 1)) : createCommentVNode("", true)
                              ]),
                              createVNode("div", { class: "ml-3" }, [
                                createVNode($setup["VTag"], {
                                  severity: $setup.getAccuracySeverity(applicability.accuracy),
                                  class: "text-sm"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(toDisplayString($setup.getAccuracyLabel(applicability.accuracy)), 1)
                                  ]),
                                  _: 2
                                }, 1032, ["severity"])
                              ])
                            ]),
                            applicability.notes ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"
                            }, [
                              createVNode($setup["Icon"], {
                                name: "pi pi-info-circle",
                                class: "mr-1 inline-block h-4 w-4"
                              }),
                              createTextVNode(" " + toDisplayString(applicability.notes), 1)
                            ])) : createCommentVNode("", true)
                          ]);
                        }), 128))
                      ])) : (openBlock(), createBlock("div", {
                        key: 1,
                        class: "text-surface-500 dark:text-surface-400 py-6 text-center"
                      }, [
                        createVNode($setup["Icon"], {
                          name: "pi pi-info-circle",
                          class: "mb-2 inline-block text-2xl"
                        }),
                        createTextVNode(" \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043D\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u044B ")
                      ]))
                    ]),
                    createVNode("div", { class: "lg:col-span-2" }, [
                      createVNode("h4", { class: "text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium" }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435"),
                      data.equipmentApplicabilities?.length > 0 ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "grid grid-cols-1 gap-3 md:grid-cols-2"
                      }, [
                        (openBlock(true), createBlock(Fragment, null, renderList(data.equipmentApplicabilities, (equipmentApplicability) => {
                          return openBlock(), createBlock("div", {
                            key: equipmentApplicability.id,
                            class: "bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"
                          }, [
                            createVNode("div", { class: "flex items-start justify-between" }, [
                              createVNode("div", { class: "flex-1" }, [
                                createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, toDisplayString(equipmentApplicability.equipmentModel?.name || "N/A"), 1),
                                equipmentApplicability.equipmentModel?.brand ? (openBlock(), createBlock("div", {
                                  key: 0,
                                  class: "text-surface-600 dark:text-surface-400 mt-1 text-sm"
                                }, toDisplayString(equipmentApplicability.equipmentModel.brand.name), 1)) : createCommentVNode("", true)
                              ]),
                              createVNode("div", { class: "ml-3" }, [
                                createVNode($setup["VTag"], {
                                  severity: "info",
                                  class: "text-sm"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(" \u0422\u0435\u0445\u043D\u0438\u043A\u0430 ")
                                  ]),
                                  _: 1
                                })
                              ])
                            ]),
                            equipmentApplicability.notes ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"
                            }, [
                              createVNode($setup["Icon"], {
                                name: "pi pi-info-circle",
                                class: "mr-1 inline-block h-4 w-4"
                              }),
                              createTextVNode(" " + toDisplayString(equipmentApplicability.notes), 1)
                            ])) : createCommentVNode("", true)
                          ]);
                        }), 128))
                      ])) : (openBlock(), createBlock("div", {
                        key: 1,
                        class: "text-surface-500 dark:text-surface-400 py-6 text-center"
                      }, [
                        createVNode($setup["Icon"], {
                          name: "pi pi-info-circle",
                          class: "mb-2 inline-block text-2xl"
                        }),
                        createTextVNode(" \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435 \u043D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430 ")
                      ]))
                    ])
                  ])
                ])
              ];
            }
          }),
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VColumn"], {
                expander: "",
                style: { "width": "50px" }
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "id",
                header: "ID",
                sortable: "",
                style: { "width": "80px" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<span class="text-surface-700 dark:text-surface-300 font-mono text-sm"${_scopeId3}>#${ssrInterpolate(data.id)}</span>`);
                  } else {
                    return [
                      createVNode("span", { class: "text-surface-700 dark:text-surface-300 font-mono text-sm" }, "#" + toDisplayString(data.id), 1)
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "name",
                header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                sortable: "",
                style: { "width": "30%" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div${_scopeId3}><div class="text-surface-900 dark:text-surface-100 font-medium"${_scopeId3}><a${ssrRenderAttr("href", `/admin/parts/${data.id}`)} class="hover:underline text-primary"${_scopeId3}>${ssrInterpolate(data.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F")}</a></div><div class="text-surface-600 dark:text-surface-400 mt-1 text-sm"${_scopeId3}>\u0423\u0440\u043E\u0432\u0435\u043D\u044C: ${ssrInterpolate(data.level)} | \u041F\u0443\u0442\u044C: ${ssrInterpolate(data.path)}</div></div>`);
                  } else {
                    return [
                      createVNode("div", null, [
                        createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, [
                          createVNode("a", {
                            href: `/admin/parts/${data.id}`,
                            class: "hover:underline text-primary"
                          }, toDisplayString(data.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"), 9, ["href"])
                        ]),
                        createVNode("div", { class: "text-surface-600 dark:text-surface-400 mt-1 text-sm" }, "\u0423\u0440\u043E\u0432\u0435\u043D\u044C: " + toDisplayString(data.level) + " | \u041F\u0443\u0442\u044C: " + toDisplayString(data.path), 1)
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "partCategory.name",
                header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
                style: { "width": "20%" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    if (data.partCategory) {
                      _push4(ssrRenderComponent($setup["VTag"], {
                        severity: "info",
                        class: "text-sm"
                      }, {
                        default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                          if (_push5) {
                            _push5(`${ssrInterpolate(data.partCategory.name)}`);
                          } else {
                            return [
                              createTextVNode(toDisplayString(data.partCategory.name), 1)
                            ];
                          }
                        }),
                        _: 2
                      }, _parent4, _scopeId3));
                    } else {
                      _push4(`<span class="text-surface-500 dark:text-surface-400 text-sm"${_scopeId3}>\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430</span>`);
                    }
                  } else {
                    return [
                      data.partCategory ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        severity: "info",
                        class: "text-sm"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(data.partCategory.name), 1)
                        ]),
                        _: 2
                      }, 1024)) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-500 dark:text-surface-400 text-sm"
                      }, "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430"))
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                header: "\u0414\u0435\u0442\u0430\u043B\u0438",
                style: { "width": "20%" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex items-center gap-2"${_scopeId3}>`);
                    _push4(ssrRenderComponent($setup["VTag"], {
                      severity: "secondary",
                      class: "text-sm"
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`${ssrInterpolate($setup.partAttributes[data.id]?.length || data.attributes?.length || 0)} \u0430\u0442\u0440. `);
                        } else {
                          return [
                            createTextVNode(toDisplayString($setup.partAttributes[data.id]?.length || data.attributes?.length || 0) + " \u0430\u0442\u0440. ", 1)
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VTag"], {
                      severity: "success",
                      class: "text-sm"
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(`${ssrInterpolate(data.applicabilities?.length || 0)} \u043F\u043E\u0437. `);
                        } else {
                          return [
                            createTextVNode(toDisplayString(data.applicabilities?.length || 0) + " \u043F\u043E\u0437. ", 1)
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex items-center gap-2" }, [
                        createVNode($setup["VTag"], {
                          severity: "secondary",
                          class: "text-sm"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString($setup.partAttributes[data.id]?.length || data.attributes?.length || 0) + " \u0430\u0442\u0440. ", 1)
                          ]),
                          _: 2
                        }, 1024),
                        createVNode($setup["VTag"], {
                          severity: "success",
                          class: "text-sm"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(data.applicabilities?.length || 0) + " \u043F\u043E\u0437. ", 1)
                          ]),
                          _: 2
                        }, 1024)
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                field: "createdAt",
                header: "\u0421\u043E\u0437\u0434\u0430\u043D\u043E",
                sortable: "",
                style: { "width": "120px" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<span class="text-surface-600 dark:text-surface-400 text-sm"${_scopeId3}>${ssrInterpolate($setup.formatDate(data.createdAt))}</span>`);
                  } else {
                    return [
                      createVNode("span", { class: "text-surface-600 dark:text-surface-400 text-sm" }, toDisplayString($setup.formatDate(data.createdAt)), 1)
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["VColumn"], {
                header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                style: { "width": "140px" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex gap-2"${_scopeId3}><a${ssrRenderAttr("href", `/admin/parts/${data.id}`)}${_scopeId3}>`);
                    _push4(ssrRenderComponent($setup["VButton"], {
                      size: "small",
                      outlined: ""
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["Icon"], {
                            name: "pi pi-eye",
                            class: "h-4 w-4"
                          }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["Icon"], {
                              name: "pi pi-eye",
                              class: "h-4 w-4"
                            })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(`</a><a${ssrRenderAttr("href", `/admin/parts/${data.id}/edit`)}${_scopeId3}>`);
                    _push4(ssrRenderComponent($setup["VButton"], { outlined: "" }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["PencilIcon"], { class: "h-4 w-4" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["PencilIcon"], { class: "h-4 w-4" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(`</a>`);
                    _push4(ssrRenderComponent($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: ($event) => $setup.openMatching(data)
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(` \u041F\u043E\u0434\u043E\u0431\u0440\u0430\u0442\u044C `);
                          _push5(ssrRenderComponent($setup["LinkIcon"], { class: "h-4 w-4" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createTextVNode(" \u041F\u043E\u0434\u043E\u0431\u0440\u0430\u0442\u044C "),
                            createVNode($setup["LinkIcon"], { class: "h-4 w-4" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["DangerButton"], {
                      size: "small",
                      outlined: "",
                      onClick: ($event) => $setup.deletePart(data)
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["TrashIcon"], { class: "h-4 w-4" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["TrashIcon"], { class: "h-4 w-4" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex gap-2" }, [
                        createVNode("a", {
                          href: `/admin/parts/${data.id}`
                        }, [
                          createVNode($setup["VButton"], {
                            size: "small",
                            outlined: ""
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["Icon"], {
                                name: "pi pi-eye",
                                class: "h-4 w-4"
                              })
                            ]),
                            _: 1
                          })
                        ], 8, ["href"]),
                        createVNode("a", {
                          href: `/admin/parts/${data.id}/edit`
                        }, [
                          createVNode($setup["VButton"], { outlined: "" }, {
                            default: withCtx(() => [
                              createVNode($setup["PencilIcon"], { class: "h-4 w-4" })
                            ]),
                            _: 1
                          })
                        ], 8, ["href"]),
                        createVNode($setup["VButton"], {
                          size: "small",
                          severity: "secondary",
                          outlined: "",
                          onClick: ($event) => $setup.openMatching(data)
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" \u041F\u043E\u0434\u043E\u0431\u0440\u0430\u0442\u044C "),
                            createVNode($setup["LinkIcon"], { class: "h-4 w-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["DangerButton"], {
                          size: "small",
                          outlined: "",
                          onClick: ($event) => $setup.deletePart(data)
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["TrashIcon"], { class: "h-4 w-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VColumn"], {
                  expander: "",
                  style: { "width": "50px" }
                }),
                createVNode($setup["VColumn"], {
                  field: "id",
                  header: "ID",
                  sortable: "",
                  style: { "width": "80px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("span", { class: "text-surface-700 dark:text-surface-300 font-mono text-sm" }, "#" + toDisplayString(data.id), 1)
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "name",
                  header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                  sortable: "",
                  style: { "width": "30%" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", null, [
                      createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, [
                        createVNode("a", {
                          href: `/admin/parts/${data.id}`,
                          class: "hover:underline text-primary"
                        }, toDisplayString(data.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"), 9, ["href"])
                      ]),
                      createVNode("div", { class: "text-surface-600 dark:text-surface-400 mt-1 text-sm" }, "\u0423\u0440\u043E\u0432\u0435\u043D\u044C: " + toDisplayString(data.level) + " | \u041F\u0443\u0442\u044C: " + toDisplayString(data.path), 1)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "partCategory.name",
                  header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
                  style: { "width": "20%" }
                }, {
                  body: withCtx(({ data }) => [
                    data.partCategory ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      severity: "info",
                      class: "text-sm"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(data.partCategory.name), 1)
                      ]),
                      _: 2
                    }, 1024)) : (openBlock(), createBlock("span", {
                      key: 1,
                      class: "text-surface-500 dark:text-surface-400 text-sm"
                    }, "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430"))
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  header: "\u0414\u0435\u0442\u0430\u043B\u0438",
                  style: { "width": "20%" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex items-center gap-2" }, [
                      createVNode($setup["VTag"], {
                        severity: "secondary",
                        class: "text-sm"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString($setup.partAttributes[data.id]?.length || data.attributes?.length || 0) + " \u0430\u0442\u0440. ", 1)
                        ]),
                        _: 2
                      }, 1024),
                      createVNode($setup["VTag"], {
                        severity: "success",
                        class: "text-sm"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(data.applicabilities?.length || 0) + " \u043F\u043E\u0437. ", 1)
                        ]),
                        _: 2
                      }, 1024)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  field: "createdAt",
                  header: "\u0421\u043E\u0437\u0434\u0430\u043D\u043E",
                  sortable: "",
                  style: { "width": "120px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("span", { class: "text-surface-600 dark:text-surface-400 text-sm" }, toDisplayString($setup.formatDate(data.createdAt)), 1)
                  ]),
                  _: 1
                }),
                createVNode($setup["VColumn"], {
                  header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                  style: { "width": "140px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex gap-2" }, [
                      createVNode("a", {
                        href: `/admin/parts/${data.id}`
                      }, [
                        createVNode($setup["VButton"], {
                          size: "small",
                          outlined: ""
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["Icon"], {
                              name: "pi pi-eye",
                              class: "h-4 w-4"
                            })
                          ]),
                          _: 1
                        })
                      ], 8, ["href"]),
                      createVNode("a", {
                        href: `/admin/parts/${data.id}/edit`
                      }, [
                        createVNode($setup["VButton"], { outlined: "" }, {
                          default: withCtx(() => [
                            createVNode($setup["PencilIcon"], { class: "h-4 w-4" })
                          ]),
                          _: 1
                        })
                      ], 8, ["href"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: ($event) => $setup.openMatching(data)
                      }, {
                        default: withCtx(() => [
                          createTextVNode(" \u041F\u043E\u0434\u043E\u0431\u0440\u0430\u0442\u044C "),
                          createVNode($setup["LinkIcon"], { class: "h-4 w-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["DangerButton"], {
                        size: "small",
                        outlined: "",
                        onClick: ($event) => $setup.deletePart(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "h-4 w-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])
                    ])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VDataTable"], {
            value: $setup.parts,
            loading: $setup.loading,
            paginator: "",
            rows: $setup.pageSize,
            "total-records": $setup.totalCount,
            "rows-per-page-options": [10, 25, 50],
            lazy: "",
            onPage: $setup.onPageChange,
            onSort: $setup.onSort,
            "table-style": "min-width: 50rem",
            class: "p-datatable-sm",
            "striped-rows": "",
            expandedRows: $setup.expandedRows,
            "onUpdate:expandedRows": ($event) => $setup.expandedRows = $event,
            onRowExpand: $setup.onRowExpand
          }, {
            expansion: withCtx(({ data }) => [
              createVNode("div", { class: "bg-surface-50 dark:bg-surface-800 border-surface-200 dark:border-surface-700 border-t p-4" }, [
                createVNode("div", { class: "grid grid-cols-1 gap-6 lg:grid-cols-2" }, [
                  createVNode("div", null, [
                    createVNode("h4", { class: "text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium" }, [
                      createVNode($setup["Icon"], {
                        name: "pi pi-list",
                        class: "h-4 w-4 text-blue-600"
                      }),
                      createTextVNode(" \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438 ")
                    ]),
                    $setup.partAttributes[data.id]?.length > 0 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "space-y-2"
                    }, [
                      (openBlock(true), createBlock(Fragment, null, renderList($setup.partAttributes[data.id], (attr) => {
                        return openBlock(), createBlock("div", {
                          key: attr.id,
                          class: "bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 flex items-start justify-between rounded border p-3"
                        }, [
                          createVNode("div", { class: "flex-1" }, [
                            createVNode("div", { class: "text-surface-900 dark:text-surface-100 text-sm font-medium" }, toDisplayString(attr.template?.title || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"), 1),
                            attr.description ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-surface-500 dark:text-surface-400 mt-1 text-sm"
                            }, toDisplayString(attr.description), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", { class: "ml-3 text-right" }, [
                            createVNode("div", { class: "text-surface-700 dark:text-surface-300 font-medium" }, toDisplayString(attr.value || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E"), 1),
                            attr.unit ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-surface-500 dark:text-surface-400 text-sm"
                            }, toDisplayString($setup.getUnitLabel(attr.unit)), 1)) : createCommentVNode("", true)
                          ])
                        ]);
                      }), 128))
                    ])) : (openBlock(), createBlock("div", {
                      key: 1,
                      class: "text-surface-500 dark:text-surface-400 py-6 text-center"
                    }, [
                      createVNode($setup["Icon"], {
                        name: "pi pi-info-circle",
                        class: "mb-2 inline-block text-2xl"
                      }),
                      createTextVNode(" \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043D\u0435 \u0437\u0430\u0434\u0430\u043D\u044B ")
                    ]))
                  ]),
                  createVNode("div", null, [
                    createVNode("h4", { class: "text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium" }, [
                      createVNode($setup["Icon"], {
                        name: "pi pi-box",
                        class: "h-4 w-4 text-green-600"
                      }),
                      createTextVNode(" \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 ")
                    ]),
                    data.applicabilities?.length > 0 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "space-y-2"
                    }, [
                      (openBlock(true), createBlock(Fragment, null, renderList(data.applicabilities, (applicability) => {
                        return openBlock(), createBlock("div", {
                          key: applicability.id,
                          class: "bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"
                        }, [
                          createVNode("div", { class: "flex items-start justify-between" }, [
                            createVNode("div", { class: "flex-1" }, [
                              createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, toDisplayString(applicability.catalogItem?.sku || "N/A"), 1),
                              createVNode("div", { class: "text-surface-600 dark:text-surface-400 mt-1 text-sm" }, toDisplayString(applicability.catalogItem?.brand?.name || "\u041D\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043D\u044B\u0439 \u0431\u0440\u0435\u043D\u0434"), 1),
                              applicability.catalogItem?.description ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "text-surface-500 dark:text-surface-400 mt-1 text-sm"
                              }, toDisplayString(applicability.catalogItem.description), 1)) : createCommentVNode("", true)
                            ]),
                            createVNode("div", { class: "ml-3" }, [
                              createVNode($setup["VTag"], {
                                severity: $setup.getAccuracySeverity(applicability.accuracy),
                                class: "text-sm"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(toDisplayString($setup.getAccuracyLabel(applicability.accuracy)), 1)
                                ]),
                                _: 2
                              }, 1032, ["severity"])
                            ])
                          ]),
                          applicability.notes ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"
                          }, [
                            createVNode($setup["Icon"], {
                              name: "pi pi-info-circle",
                              class: "mr-1 inline-block h-4 w-4"
                            }),
                            createTextVNode(" " + toDisplayString(applicability.notes), 1)
                          ])) : createCommentVNode("", true)
                        ]);
                      }), 128))
                    ])) : (openBlock(), createBlock("div", {
                      key: 1,
                      class: "text-surface-500 dark:text-surface-400 py-6 text-center"
                    }, [
                      createVNode($setup["Icon"], {
                        name: "pi pi-info-circle",
                        class: "mb-2 inline-block text-2xl"
                      }),
                      createTextVNode(" \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438 \u043D\u0435 \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u044B ")
                    ]))
                  ]),
                  createVNode("div", { class: "lg:col-span-2" }, [
                    createVNode("h4", { class: "text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2 text-lg font-medium" }, "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435"),
                    data.equipmentApplicabilities?.length > 0 ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "grid grid-cols-1 gap-3 md:grid-cols-2"
                    }, [
                      (openBlock(true), createBlock(Fragment, null, renderList(data.equipmentApplicabilities, (equipmentApplicability) => {
                        return openBlock(), createBlock("div", {
                          key: equipmentApplicability.id,
                          class: "bg-surface-0 dark:bg-surface-900 border-surface-200 dark:border-surface-700 rounded border p-3"
                        }, [
                          createVNode("div", { class: "flex items-start justify-between" }, [
                            createVNode("div", { class: "flex-1" }, [
                              createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, toDisplayString(equipmentApplicability.equipmentModel?.name || "N/A"), 1),
                              equipmentApplicability.equipmentModel?.brand ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "text-surface-600 dark:text-surface-400 mt-1 text-sm"
                              }, toDisplayString(equipmentApplicability.equipmentModel.brand.name), 1)) : createCommentVNode("", true)
                            ]),
                            createVNode("div", { class: "ml-3" }, [
                              createVNode($setup["VTag"], {
                                severity: "info",
                                class: "text-sm"
                              }, {
                                default: withCtx(() => [
                                  createTextVNode(" \u0422\u0435\u0445\u043D\u0438\u043A\u0430 ")
                                ]),
                                _: 1
                              })
                            ])
                          ]),
                          equipmentApplicability.notes ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400 mt-2 rounded p-2 text-sm"
                          }, [
                            createVNode($setup["Icon"], {
                              name: "pi pi-info-circle",
                              class: "mr-1 inline-block h-4 w-4"
                            }),
                            createTextVNode(" " + toDisplayString(equipmentApplicability.notes), 1)
                          ])) : createCommentVNode("", true)
                        ]);
                      }), 128))
                    ])) : (openBlock(), createBlock("div", {
                      key: 1,
                      class: "text-surface-500 dark:text-surface-400 py-6 text-center"
                    }, [
                      createVNode($setup["Icon"], {
                        name: "pi pi-info-circle",
                        class: "mb-2 inline-block text-2xl"
                      }),
                      createTextVNode(" \u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u0445\u043D\u0438\u043A\u0435 \u043D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430 ")
                    ]))
                  ])
                ])
              ])
            ]),
            default: withCtx(() => [
              createVNode($setup["VColumn"], {
                expander: "",
                style: { "width": "50px" }
              }),
              createVNode($setup["VColumn"], {
                field: "id",
                header: "ID",
                sortable: "",
                style: { "width": "80px" }
              }, {
                body: withCtx(({ data }) => [
                  createVNode("span", { class: "text-surface-700 dark:text-surface-300 font-mono text-sm" }, "#" + toDisplayString(data.id), 1)
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "name",
                header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                sortable: "",
                style: { "width": "30%" }
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", null, [
                    createVNode("div", { class: "text-surface-900 dark:text-surface-100 font-medium" }, [
                      createVNode("a", {
                        href: `/admin/parts/${data.id}`,
                        class: "hover:underline text-primary"
                      }, toDisplayString(data.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"), 9, ["href"])
                    ]),
                    createVNode("div", { class: "text-surface-600 dark:text-surface-400 mt-1 text-sm" }, "\u0423\u0440\u043E\u0432\u0435\u043D\u044C: " + toDisplayString(data.level) + " | \u041F\u0443\u0442\u044C: " + toDisplayString(data.path), 1)
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "partCategory.name",
                header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
                style: { "width": "20%" }
              }, {
                body: withCtx(({ data }) => [
                  data.partCategory ? (openBlock(), createBlock($setup["VTag"], {
                    key: 0,
                    severity: "info",
                    class: "text-sm"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(data.partCategory.name), 1)
                    ]),
                    _: 2
                  }, 1024)) : (openBlock(), createBlock("span", {
                    key: 1,
                    class: "text-surface-500 dark:text-surface-400 text-sm"
                  }, "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u0430"))
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                header: "\u0414\u0435\u0442\u0430\u043B\u0438",
                style: { "width": "20%" }
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "flex items-center gap-2" }, [
                    createVNode($setup["VTag"], {
                      severity: "secondary",
                      class: "text-sm"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString($setup.partAttributes[data.id]?.length || data.attributes?.length || 0) + " \u0430\u0442\u0440. ", 1)
                      ]),
                      _: 2
                    }, 1024),
                    createVNode($setup["VTag"], {
                      severity: "success",
                      class: "text-sm"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(data.applicabilities?.length || 0) + " \u043F\u043E\u0437. ", 1)
                      ]),
                      _: 2
                    }, 1024)
                  ])
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                field: "createdAt",
                header: "\u0421\u043E\u0437\u0434\u0430\u043D\u043E",
                sortable: "",
                style: { "width": "120px" }
              }, {
                body: withCtx(({ data }) => [
                  createVNode("span", { class: "text-surface-600 dark:text-surface-400 text-sm" }, toDisplayString($setup.formatDate(data.createdAt)), 1)
                ]),
                _: 1
              }),
              createVNode($setup["VColumn"], {
                header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                style: { "width": "140px" }
              }, {
                body: withCtx(({ data }) => [
                  createVNode("div", { class: "flex gap-2" }, [
                    createVNode("a", {
                      href: `/admin/parts/${data.id}`
                    }, [
                      createVNode($setup["VButton"], {
                        size: "small",
                        outlined: ""
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["Icon"], {
                            name: "pi pi-eye",
                            class: "h-4 w-4"
                          })
                        ]),
                        _: 1
                      })
                    ], 8, ["href"]),
                    createVNode("a", {
                      href: `/admin/parts/${data.id}/edit`
                    }, [
                      createVNode($setup["VButton"], { outlined: "" }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "h-4 w-4" })
                        ]),
                        _: 1
                      })
                    ], 8, ["href"]),
                    createVNode($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: ($event) => $setup.openMatching(data)
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" \u041F\u043E\u0434\u043E\u0431\u0440\u0430\u0442\u044C "),
                        createVNode($setup["LinkIcon"], { class: "h-4 w-4" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"]),
                    createVNode($setup["DangerButton"], {
                      size: "small",
                      outlined: "",
                      onClick: ($event) => $setup.deletePart(data)
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["TrashIcon"], { class: "h-4 w-4" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"])
                  ])
                ]),
                _: 1
              })
            ]),
            _: 1
          }, 8, ["value", "loading", "rows", "total-records", "expandedRows", "onUpdate:expandedRows"])
        ];
      }
    }),
    _: 1
  }, _parent));
  if ($setup.error) {
    _push(ssrRenderComponent($setup["VMessage"], {
      severity: "error",
      class: "mt-4"
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`${ssrInterpolate($setup.error)}`);
        } else {
          return [
            createTextVNode(toDisplayString($setup.error), 1)
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.editDialogVisible,
    "onUpdate:visible": ($event) => $setup.editDialogVisible = $event,
    modal: "",
    header: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
    class: ""
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.selectedPartForEdit) {
          _push2(ssrRenderComponent($setup["PartWizard"], {
            part: $setup.selectedPartForEdit,
            mode: "edit",
            onUpdated: $setup.onPartSaved
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $setup.selectedPartForEdit ? (openBlock(), createBlock($setup["PartWizard"], {
            key: 0,
            part: $setup.selectedPartForEdit,
            mode: "edit",
            onUpdated: $setup.onPartSaved
          }, null, 8, ["part"])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.matchingDialogVisible,
    "onUpdate:visible": ($event) => $setup.matchingDialogVisible = $event,
    modal: "",
    header: "\u041F\u043E\u0434\u0431\u043E\u0440 \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439",
    class: "w-full max-w-4xl"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4"${_scopeId}><div class="mb-4 flex items-center justify-between"${_scopeId}><div class="text-surface-500 text-sm"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u0430</div><div class="font-semibold"${_scopeId}>${ssrInterpolate($setup.selectedPartForMatching?.name || "#" + $setup.selectedPartForMatching?.id)}</div>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          severity: "secondary",
          outlined: "",
          size: "small",
          loading: $setup.matchingLoading,
          onClick: $setup.runPartMatching
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["RefreshCcwIcon"], null, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["RefreshCcwIcon"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
        if ($setup.matchingLoading) {
          _push2(ssrRenderComponent($setup["MatchingLoadingState"], { paddingClass: "py-8" }, null, _parent2, _scopeId));
        } else if (!$setup.matchingResults || $setup.matchingResults.length === 0) {
          _push2(ssrRenderComponent($setup["MatchingEmptyState"], null, null, _parent2, _scopeId));
        } else {
          _push2(`<div class="space-y-3"${_scopeId}><!--[-->`);
          ssrRenderList($setup.matchingResults, (c) => {
            _push2(ssrRenderComponent($setup["VCard"], {
              key: c.catalogItem.id,
              class: "border"
            }, {
              content: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3"${_scopeId2}><div class="md:col-span-1"${_scopeId2}><div class="font-mono font-semibold"${_scopeId2}>${ssrInterpolate(c.catalogItem.sku)}</div><div class="text-surface-500"${_scopeId2}>${ssrInterpolate(c.catalogItem.brand?.name)}</div><div class="mt-2"${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["VTag"], {
                    value: $setup.getAccuracyLabel(c.accuracySuggestion),
                    severity: $setup.getAccuracySeverity(c.accuracySuggestion)
                  }, null, _parent3, _scopeId2));
                  _push3(`</div><div class="mt-3"${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["VButton"], {
                    size: "small",
                    label: "\u041F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C",
                    onClick: ($event) => $setup.openLinkConfirmDialog(c)
                  }, {
                    icon: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["Icon"], {
                          name: "pi pi-link",
                          class: "h-5 w-5"
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["Icon"], {
                            name: "pi pi-link",
                            class: "h-5 w-5"
                          })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(`</div></div><div class="md:col-span-2"${_scopeId2}><div class="text-surface-500 mb-2 text-sm"${_scopeId2}>\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F</div>`);
                  _push3(ssrRenderComponent($setup["MatchingDetailsGrid"], {
                    details: c.details
                  }, null, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3" }, [
                      createVNode("div", { class: "md:col-span-1" }, [
                        createVNode("div", { class: "font-mono font-semibold" }, toDisplayString(c.catalogItem.sku), 1),
                        createVNode("div", { class: "text-surface-500" }, toDisplayString(c.catalogItem.brand?.name), 1),
                        createVNode("div", { class: "mt-2" }, [
                          createVNode($setup["VTag"], {
                            value: $setup.getAccuracyLabel(c.accuracySuggestion),
                            severity: $setup.getAccuracySeverity(c.accuracySuggestion)
                          }, null, 8, ["value", "severity"])
                        ]),
                        createVNode("div", { class: "mt-3" }, [
                          createVNode($setup["VButton"], {
                            size: "small",
                            label: "\u041F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C",
                            onClick: ($event) => $setup.openLinkConfirmDialog(c)
                          }, {
                            icon: withCtx(() => [
                              createVNode($setup["Icon"], {
                                name: "pi pi-link",
                                class: "h-5 w-5"
                              })
                            ]),
                            _: 2
                          }, 1032, ["onClick"])
                        ])
                      ]),
                      createVNode("div", { class: "md:col-span-2" }, [
                        createVNode("div", { class: "text-surface-500 mb-2 text-sm" }, "\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F"),
                        createVNode($setup["MatchingDetailsGrid"], {
                          details: c.details
                        }, null, 8, ["details"])
                      ])
                    ])
                  ];
                }
              }),
              _: 2
            }, _parent2, _scopeId));
          });
          _push2(`<!--]--></div>`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4" }, [
            createVNode("div", { class: "mb-4 flex items-center justify-between" }, [
              createVNode("div", { class: "text-surface-500 text-sm" }, "\u0413\u0440\u0443\u043F\u043F\u0430"),
              createVNode("div", { class: "font-semibold" }, toDisplayString($setup.selectedPartForMatching?.name || "#" + $setup.selectedPartForMatching?.id), 1),
              createVNode($setup["VButton"], {
                severity: "secondary",
                outlined: "",
                size: "small",
                loading: $setup.matchingLoading,
                onClick: $setup.runPartMatching
              }, {
                default: withCtx(() => [
                  createVNode($setup["RefreshCcwIcon"])
                ]),
                _: 1
              }, 8, ["loading"])
            ]),
            $setup.matchingLoading ? (openBlock(), createBlock($setup["MatchingLoadingState"], {
              key: 0,
              paddingClass: "py-8"
            })) : !$setup.matchingResults || $setup.matchingResults.length === 0 ? (openBlock(), createBlock($setup["MatchingEmptyState"], { key: 1 })) : (openBlock(), createBlock("div", {
              key: 2,
              class: "space-y-3"
            }, [
              (openBlock(true), createBlock(Fragment, null, renderList($setup.matchingResults, (c) => {
                return openBlock(), createBlock($setup["VCard"], {
                  key: c.catalogItem.id,
                  class: "border"
                }, {
                  content: withCtx(() => [
                    createVNode("div", { class: "grid grid-cols-1 items-start gap-3 p-4 md:grid-cols-3" }, [
                      createVNode("div", { class: "md:col-span-1" }, [
                        createVNode("div", { class: "font-mono font-semibold" }, toDisplayString(c.catalogItem.sku), 1),
                        createVNode("div", { class: "text-surface-500" }, toDisplayString(c.catalogItem.brand?.name), 1),
                        createVNode("div", { class: "mt-2" }, [
                          createVNode($setup["VTag"], {
                            value: $setup.getAccuracyLabel(c.accuracySuggestion),
                            severity: $setup.getAccuracySeverity(c.accuracySuggestion)
                          }, null, 8, ["value", "severity"])
                        ]),
                        createVNode("div", { class: "mt-3" }, [
                          createVNode($setup["VButton"], {
                            size: "small",
                            label: "\u041F\u0440\u0438\u0432\u044F\u0437\u0430\u0442\u044C",
                            onClick: ($event) => $setup.openLinkConfirmDialog(c)
                          }, {
                            icon: withCtx(() => [
                              createVNode($setup["Icon"], {
                                name: "pi pi-link",
                                class: "h-5 w-5"
                              })
                            ]),
                            _: 2
                          }, 1032, ["onClick"])
                        ])
                      ]),
                      createVNode("div", { class: "md:col-span-2" }, [
                        createVNode("div", { class: "text-surface-500 mb-2 text-sm" }, "\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F"),
                        createVNode($setup["MatchingDetailsGrid"], {
                          details: c.details
                        }, null, 8, ["details"])
                      ])
                    ])
                  ]),
                  _: 2
                }, 1024);
              }), 128))
            ]))
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showLinkConfirmDialog,
    "onUpdate:visible": ($event) => $setup.showLinkConfirmDialog = $event,
    modal: "",
    header: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435 \u043F\u0440\u0438\u0432\u044F\u0437\u043A\u0438",
    class: "w-full max-w-3xl"
  }, {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-between"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: $setup.closeLinkConfirmDialog
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0432\u044F\u0437\u044C",
          severity: "success",
          onClick: $setup.confirmLinkItem,
          loading: $setup.linking
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-between" }, [
            createVNode($setup["VButton"], {
              label: "\u041E\u0442\u043C\u0435\u043D\u0430",
              severity: "secondary",
              onClick: $setup.closeLinkConfirmDialog
            }),
            createVNode($setup["VButton"], {
              label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0441\u0432\u044F\u0437\u044C",
              severity: "success",
              onClick: $setup.confirmLinkItem,
              loading: $setup.linking
            }, null, 8, ["loading"])
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.selectedLinkCandidate) {
          _push2(`<div class="space-y-4"${_scopeId}><div class="bg-surface-50 dark:bg-surface-900 grid grid-cols-1 gap-4 rounded p-4 md:grid-cols-2"${_scopeId}><div${_scopeId}><div class="text-surface-500 text-sm"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u0430 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438</div><div class="font-semibold"${_scopeId}>${ssrInterpolate($setup.selectedPartForMatching?.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${$setup.selectedPartForMatching?.id}`)}</div></div><div${_scopeId}><div class="text-surface-500 text-sm"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F</div><div class="font-semibold"${_scopeId}>${ssrInterpolate($setup.selectedLinkCandidate.catalogItem.sku)}</div><div class="text-sm"${_scopeId}>${ssrInterpolate($setup.selectedLinkCandidate.catalogItem.brand?.name)}</div></div></div><div${_scopeId}><h3 class="mb-3 text-lg font-semibold"${_scopeId}>\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F</h3>`);
          _push2(ssrRenderComponent($setup["MatchingDetailsGrid"], {
            details: $setup.selectedLinkCandidate.details
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="space-y-3"${_scopeId}><div${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u044F </label>`);
          _push2(ssrRenderComponent($setup["VSelect"], {
            modelValue: $setup.linkConfirmForm.accuracy,
            "onUpdate:modelValue": ($event) => $setup.linkConfirmForm.accuracy = $event,
            options: $setup.accuracyOptions,
            "option-label": "label",
            "option-value": "value",
            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043D\u043E\u0441\u0442\u044C",
            class: "w-full"
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium"${_scopeId}> \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F </label>`);
          _push2(ssrRenderComponent($setup["VTextarea"], {
            modelValue: $setup.linkConfirmForm.notes,
            "onUpdate:modelValue": ($event) => $setup.linkConfirmForm.notes = $event,
            rows: "3",
            placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438...",
            class: "w-full"
          }, null, _parent2, _scopeId));
          _push2(`<small class="text-surface-500"${_scopeId}> \u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u043E\u0441\u043E\u0431\u0435\u043D\u043D\u043E\u0441\u0442\u0438 \u043F\u0440\u0438\u043C\u0435\u043D\u0435\u043D\u0438\u044F, \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0435\u043D\u0438\u044F \u0438\u043B\u0438 \u0443\u0441\u043B\u043E\u0432\u0438\u044F \u0437\u0430\u043C\u0435\u043D\u044B </small></div></div></div>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          $setup.selectedLinkCandidate ? (openBlock(), createBlock("div", {
            key: 0,
            class: "space-y-4"
          }, [
            createVNode("div", { class: "bg-surface-50 dark:bg-surface-900 grid grid-cols-1 gap-4 rounded p-4 md:grid-cols-2" }, [
              createVNode("div", null, [
                createVNode("div", { class: "text-surface-500 text-sm" }, "\u0413\u0440\u0443\u043F\u043F\u0430 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438"),
                createVNode("div", { class: "font-semibold" }, toDisplayString($setup.selectedPartForMatching?.name || `\u0413\u0440\u0443\u043F\u043F\u0430 #${$setup.selectedPartForMatching?.id}`), 1)
              ]),
              createVNode("div", null, [
                createVNode("div", { class: "text-surface-500 text-sm" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u0430\u044F \u043F\u043E\u0437\u0438\u0446\u0438\u044F"),
                createVNode("div", { class: "font-semibold" }, toDisplayString($setup.selectedLinkCandidate.catalogItem.sku), 1),
                createVNode("div", { class: "text-sm" }, toDisplayString($setup.selectedLinkCandidate.catalogItem.brand?.name), 1)
              ])
            ]),
            createVNode("div", null, [
              createVNode("h3", { class: "mb-3 text-lg font-semibold" }, "\u0414\u0435\u0442\u0430\u043B\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u044F"),
              createVNode($setup["MatchingDetailsGrid"], {
                details: $setup.selectedLinkCandidate.details
              }, null, 8, ["details"])
            ]),
            createVNode("div", { class: "space-y-3" }, [
              createVNode("div", null, [
                createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u0422\u043E\u0447\u043D\u043E\u0441\u0442\u044C \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u044F "),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.linkConfirmForm.accuracy,
                  "onUpdate:modelValue": ($event) => $setup.linkConfirmForm.accuracy = $event,
                  options: $setup.accuracyOptions,
                  "option-label": "label",
                  "option-value": "value",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u043E\u0447\u043D\u043E\u0441\u0442\u044C",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "text-surface-700 dark:text-surface-300 mb-2 block text-sm font-medium" }, " \u041F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F "),
                createVNode($setup["VTextarea"], {
                  modelValue: $setup.linkConfirmForm.notes,
                  "onUpdate:modelValue": ($event) => $setup.linkConfirmForm.notes = $event,
                  rows: "3",
                  placeholder: "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438...",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                createVNode("small", { class: "text-surface-500" }, " \u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u043E\u0441\u043E\u0431\u0435\u043D\u043D\u043E\u0441\u0442\u0438 \u043F\u0440\u0438\u043C\u0435\u043D\u0435\u043D\u0438\u044F, \u043E\u0433\u0440\u0430\u043D\u0438\u0447\u0435\u043D\u0438\u044F \u0438\u043B\u0438 \u0443\u0441\u043B\u043E\u0432\u0438\u044F \u0437\u0430\u043C\u0435\u043D\u044B ")
              ])
            ])
          ])) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VConfirmDialog"], null, null, _parent));
  _push(ssrRenderComponent($setup["Toast"], null, null, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/parts/PartsTable.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const PartsTable = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044F\u043C\u0438 - PartTec" }, { "default": ($$result2) => renderTemplate`  ${renderComponent($$result2, "PartsTable", PartsTable, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/parts/PartsTable.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/index.astro";
const $$url = "/admin/parts";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

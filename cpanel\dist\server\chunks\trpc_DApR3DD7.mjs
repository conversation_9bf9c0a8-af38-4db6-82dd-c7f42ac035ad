import { createTR<PERSON><PERSON>lient, loggerLink, httpBatchLink } from '@trpc/client';
import superjson from 'superjson';

const getBaseUrl = () => {
  if (typeof window !== "undefined") {
    return "http://localhost";
  }
  return process.env.API_URL || "http://localhost";
};
const trpc = createTRPCClient({
  links: [
    loggerLink({ enabled: () => false }),
    httpBatchLink({
      url: `${getBaseUrl()}:3000/trpc`,
      transformer: superjson,
      // Включаем передачу cookies для авторизации
      fetch: (url, options) => {
        return fetch(url, {
          ...options,
          credentials: "include"
          // Важно для передачи cookies
        });
      }
    })
  ]
});

export { trpc as t };

import{u as k}from"./useToast.pIbuf2bs.js";import{r as A}from"./reactivity.esm-bundler.BQ12LWmY.js";import{h as f}from"./runtime-core.esm-bundler.CRb7Pg8a.js";const n=A([]),p=50,x=()=>{const a=k(),l=()=>`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,d=(r,e,t,o)=>({id:l(),code:r,message:e,details:t,field:o,timestamp:new Date}),i=r=>{n.value.unshift(r),n.value.length>p&&(n.value=n.value.slice(0,p))},m=(r,e=!0)=>{console.error("tRPC Error:",r);const t=r?.data?.code||r?.shape?.code||r?.name||"UNKNOWN_ERROR",o=r?.data?.zodError?.fieldErrors||r?.zodError?.fieldErrors;let s=r?.message;if(!s)switch(t){case"UNAUTHORIZED":s="Требуется авторизация";break;case"FORBIDDEN":s="Недостаточно прав для выполнения операции";break;case"NOT_FOUND":s="Ресурс не найден";break;case"BAD_REQUEST":s="Некорректный запрос";break;case"CONFLICT":s="Конфликт данных";break;case"PRECONDITION_FAILED":s="Нарушены условия выполнения операции";break;case"INTERNAL_SERVER_ERROR":s="Внутренняя ошибка сервера";break;case"TIMEOUT":s="Превышено время ожидания";break;default:s="Произошла ошибка при выполнении запроса"}if(o&&typeof o=="object"){const E=Object.entries(o).flatMap(([L,R])=>(Array.isArray(R)?R:[R]).filter(Boolean).map(_=>`${L}: ${_}`)).slice(0,5).join(`
`);E&&(s=`Ошибка валидации:
${E}`)}const c={...d(t,s,r),trpcCode:t,zodErrors:o};return i(c),e&&a.error("Ошибка",s),c},w=(r,e,t=!0)=>{console.error("Network Error:",r);const o=r?.status||r?.response?.status||0,s=r?.message||"Ошибка сети",c=o>=500||o===0||o===408||o===429,E={...d("NETWORK_ERROR",s,r),status:o,retryable:c,url:e};return i(E),t&&(o===0?a.error("Ошибка сети","Проверьте подключение к интернету"):o>=500?a.error("Ошибка сервера","Попробуйте повторить запрос позже"):a.error("Ошибка сети",s)),E},O=(r,e,t,o=!0)=>{const s=`Поле "${r}": ${t}`,c={...d("VALIDATION_ERROR",s,{value:e,constraint:t},r),field:r,value:e,constraint:t};return i(c),o&&a.warn("Ошибка валидации",s),c},h=(r,e,t=!0)=>{console.error("Generic Error:",r);const o=r?.message||"Произошла неизвестная ошибка",s=r?.code||r?.name||"GENERIC_ERROR",c=d(s,e?`${e}: ${o}`:o,r);return i(c),t&&a.error("Ошибка",c.message),c},u=(r,e={})=>{const{context:t,showToast:o=!0,url:s}=e;return r?.data?.code||r?.shape?.code?m(r,o):r?.status||r?.response?.status?w(r,s,o):h(r,t,o)},N=()=>{n.value=[]},T=r=>{const e=n.value.findIndex(t=>t.id===r);e!==-1&&n.value.splice(e,1)},v=r=>n.value.filter(e=>e.code===r),I=(r=10)=>n.value.slice(0,r),b=f(()=>{const r=["INTERNAL_SERVER_ERROR","UNAUTHORIZED","FORBIDDEN"];return n.value.some(e=>r.includes(e.code))}),C=f(()=>{const r={};return n.value.forEach(e=>{r[e.code]=(r[e.code]||0)+1}),r}),D=(r="Запись",e)=>{e?u(e,{context:`Сохранение ${r.toLowerCase()}`}):a.error("Ошибка сохранения",`Не удалось сохранить ${r.toLowerCase()}`)},$=(r="Запись",e)=>{e?u(e,{context:`Удаление ${r.toLowerCase()}`}):a.error("Ошибка удаления",`Не удалось удалить ${r.toLowerCase()}`)},g=(r="Данные",e)=>{e?u(e,{context:`Загрузка ${r.toLowerCase()}`}):a.error("Ошибка загрузки",`Не удалось загрузить ${r.toLowerCase()}`)};return{errors:f(()=>n.value),hasCriticalErrors:b,errorStats:C,handleError:u,handleTRPCError:m,handleNetworkError:w,handleValidationError:O,handleGenericError:h,clearErrors:N,removeError:T,getErrorsByType:v,getRecentErrors:I,showSaveError:D,showDeleteError:$,showLoadError:g}},B=()=>{if(typeof window>"u")return;const{handleError:a}=x();window.addEventListener("unhandledrejection",l=>{console.error("Unhandled promise rejection:",l.reason),a(l.reason,{context:"Необработанная ошибка промиса"})}),window.addEventListener("error",l=>{console.error("JavaScript error:",l.error),a(l.error,{context:"JavaScript ошибка"})})};export{B as s,x as u};

import{S as V,p as k,r as d,s as U,a as S,n as B,i as x,b as j,t as H,f as K,c as N,d as q,e as G,g as J,h as X,j as L,u as _}from"./utils.is9Ib0FR.js";import{R as Y,s as Z,h as $,j as C}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{_ as tt,a as et,a3 as st,d as rt,a5 as it,a6 as z}from"./reactivity.esm-bundler.BQ12LWmY.js";import{u as at}from"./useErrorHandler.DVDazL16.js";import{t as p}from"./trpc.BpyaUO08.js";var nt=class extends V{constructor(t,e){super(),this.options=e,this.#s=t,this.#i=null,this.#r=k(),this.options.experimental_prefetchInRender||this.#r.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#s;#t=void 0;#p=void 0;#e=void 0;#n;#u;#r;#i;#y;#l;#d;#o;#c;#a;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#t.addObserver(this),A(this.#t,this.options)?this.#h():this.updateResult(),this.#g())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return M(this.#t,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return M(this.#t,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#m(),this.#O(),this.#t.removeObserver(this)}setOptions(t){const e=this.options,s=this.#t;if(this.options=this.#s.defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof d(this.options.enabled,this.#t)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#C(),this.#t.setOptions(this.options),e._defaulted&&!U(this.options,e)&&this.#s.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#t,observer:this});const i=this.hasListeners();i&&W(this.#t,s,this.options,e)&&this.#h(),this.updateResult(),i&&(this.#t!==s||d(this.options.enabled,this.#t)!==d(e.enabled,this.#t)||S(this.options.staleTime,this.#t)!==S(e.staleTime,this.#t))&&this.#b();const r=this.#v();i&&(this.#t!==s||d(this.options.enabled,this.#t)!==d(e.enabled,this.#t)||r!==this.#a)&&this.#R(r)}getOptimisticResult(t){const e=this.#s.getQueryCache().build(this.#s,t),s=this.createResult(e,t);return ct(this,s)&&(this.#e=s,this.#u=this.options,this.#n=this.#t.state),s}getCurrentResult(){return this.#e}trackResult(t,e){return new Proxy(t,{get:(s,i)=>(this.trackProp(i),e?.(i),Reflect.get(s,i))})}trackProp(t){this.#f.add(t)}getCurrentQuery(){return this.#t}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#s.defaultQueryOptions(t),s=this.#s.getQueryCache().build(this.#s,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#h({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#e))}#h(t){this.#C();let e=this.#t.fetch(this.options,t);return t?.throwOnError||(e=e.catch(B)),e}#b(){this.#m();const t=S(this.options.staleTime,this.#t);if(x||this.#e.isStale||!j(t))return;const s=H(this.#e.dataUpdatedAt,t)+1;this.#o=setTimeout(()=>{this.#e.isStale||this.updateResult()},s)}#v(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#t):this.options.refetchInterval)??!1}#R(t){this.#O(),this.#a=t,!(x||d(this.options.enabled,this.#t)===!1||!j(this.#a)||this.#a===0)&&(this.#c=setInterval(()=>{(this.options.refetchIntervalInBackground||K.isFocused())&&this.#h()},this.#a))}#g(){this.#b(),this.#R(this.#v())}#m(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}#O(){this.#c&&(clearInterval(this.#c),this.#c=void 0)}createResult(t,e){const s=this.#t,i=this.options,r=this.#e,n=this.#n,h=this.#u,v=t!==s?t.state:this.#p,{state:w}=t;let c={...w},T=!1,o;if(e._optimisticResults){const u=this.hasListeners(),Q=!u&&A(t,e),m=u&&W(t,s,e,i);(Q||m)&&(c={...c,...N(w.data,t.options)}),e._optimisticResults==="isRestoring"&&(c.fetchStatus="idle")}let{error:a,errorUpdatedAt:y,status:l}=c;o=c.data;let O=!1;if(e.placeholderData!==void 0&&o===void 0&&l==="pending"){let u;r?.isPlaceholderData&&e.placeholderData===h?.placeholderData?(u=r.data,O=!0):u=typeof e.placeholderData=="function"?e.placeholderData(this.#d?.state.data,this.#d):e.placeholderData,u!==void 0&&(l="success",o=q(r?.data,u,e),T=!0)}if(e.select&&o!==void 0&&!O)if(r&&o===n?.data&&e.select===this.#y)o=this.#l;else try{this.#y=e.select,o=e.select(o),o=q(r?.data,o,e),this.#l=o,this.#i=null}catch(u){this.#i=u}this.#i&&(a=this.#i,o=this.#l,y=Date.now(),l="error");const R=c.fetchStatus==="fetching",g=l==="pending",I=l==="error",D=g&&R,F=o!==void 0,f={status:l,fetchStatus:c.fetchStatus,isPending:g,isSuccess:l==="success",isError:I,isInitialLoading:D,isLoading:D,data:o,dataUpdatedAt:c.dataUpdatedAt,error:a,errorUpdatedAt:y,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>v.dataUpdateCount||c.errorUpdateCount>v.errorUpdateCount,isFetching:R,isRefetching:R&&!g,isLoadingError:I&&!F,isPaused:c.fetchStatus==="paused",isPlaceholderData:T,isRefetchError:I&&F,isStale:P(t,e),refetch:this.refetch,promise:this.#r,isEnabled:d(e.enabled,t)!==!1};if(this.options.experimental_prefetchInRender){const u=E=>{f.status==="error"?E.reject(f.error):f.data!==void 0&&E.resolve(f.data)},Q=()=>{const E=this.#r=f.promise=k();u(E)},m=this.#r;switch(m.status){case"pending":t.queryHash===s.queryHash&&u(m);break;case"fulfilled":(f.status==="error"||f.data!==m.value)&&Q();break;case"rejected":(f.status!=="error"||f.error!==m.reason)&&Q();break}}return f}updateResult(){const t=this.#e,e=this.createResult(this.#t,this.options);if(this.#n=this.#t.state,this.#u=this.options,this.#n.data!==void 0&&(this.#d=this.#t),U(e,t))return;this.#e=e;const s=()=>{if(!t)return!0;const{notifyOnChangeProps:i}=this.options,r=typeof i=="function"?i():i;if(r==="all"||!r&&!this.#f.size)return!0;const n=new Set(r??this.#f);return this.options.throwOnError&&n.add("error"),Object.keys(this.#e).some(h=>{const b=h;return this.#e[b]!==t[b]&&n.has(b)})};this.#S({listeners:s()})}#C(){const t=this.#s.getQueryCache().build(this.#s,this.options);if(t===this.#t)return;const e=this.#t;this.#t=t,this.#p=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#g()}#S(t){G.batch(()=>{t.listeners&&this.listeners.forEach(e=>{e(this.#e)}),this.#s.getQueryCache().notify({query:this.#t,type:"observerResultsUpdated"})})}};function ot(t,e){return d(e.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&e.retryOnMount===!1)}function A(t,e){return ot(t,e)||t.state.data!==void 0&&M(t,e,e.refetchOnMount)}function M(t,e,s){if(d(e.enabled,t)!==!1&&S(e.staleTime,t)!=="static"){const i=typeof s=="function"?s(t):s;return i==="always"||i!==!1&&P(t,e)}return!1}function W(t,e,s,i){return(t!==e||d(i.enabled,t)===!1)&&(!s.suspense||t.state.status!=="error")&&P(t,s)}function P(t,e){return d(e.enabled,t)!==!1&&t.isStaleByTime(S(e.staleTime,t))}function ct(t,e){return!U(t.getCurrentResult(),e)}function ht(t=""){if(!Y())throw new Error("vue-query hooks can only be used inside setup() function or functions that support injection context.");const e=J(t),s=Z(e);if(!s)throw new Error("No 'queryClient' found in Vue context, use 'VueQueryPlugin' to properly initialize the library.");return s}function ut(t,e,s){const i=ht(),r=$(()=>{const a=X(e);typeof a.enabled=="function"&&(a.enabled=a.enabled());const y=i.defaultQueryOptions(a);return y._optimisticResults=i.isRestoring?.value?"isRestoring":"optimistic",y}),n=new t(i,r.value),h=r.value.shallow?tt(n.getCurrentResult()):et(n.getCurrentResult());let b=()=>{};i.isRestoring&&C(i.isRestoring,a=>{a||(b(),b=n.subscribe(y=>{_(h,y)}))},{immediate:!0});const v=()=>{n.setOptions(r.value),_(h,n.getCurrentResult())};C(r,v),z(()=>{b()});const w=(...a)=>(v(),h.refetch(...a)),c=()=>new Promise((a,y)=>{let l=()=>{};const O=()=>{if(r.value.enabled!==!1){n.setOptions(r.value);const R=n.getOptimisticResult(r.value);R.isStale?(l(),n.fetchOptimistic(r.value).then(a,g=>{L(r.value.throwOnError,[g,n.getCurrentQuery()])?y(g):a(n.getCurrentResult())})):(l(),a(R))}};O(),l=C(r,O)});C(()=>h.error,a=>{if(h.isError&&!h.isFetching&&L(r.value.throwOnError,[a,n.getCurrentQuery()]))throw a});const T=r.value.shallow?st(h):rt(h),o=it(T);for(const a in h)typeof h[a]=="function"&&(o[a]=h[a]);return o.suspense=c,o.refetch=w,o}function vt(t,e){return ut(nt,t)}function Rt(t){const{handleError:e}=at(),s=new Set,i=C(t,r=>{if(r){const n=r.message+r.stack;s.has(n)||(s.add(n),e(r))}});z(()=>{i(),s.clear()})}const gt={parts:{list:t=>["trpc","crud.part.findMany",t??{}],detail:t=>["trpc","crud.part.findUnique",t],count:t=>["trpc","crud.part.count",t??{}]},brands:{list:t=>["trpc","crud.brand.findMany",t??{}]},categories:{list:t=>["trpc","crud.partCategory.findMany",t??{}]},search:{parts:t=>["trpc","search.searchParts",t??{}]},attributeTemplates:{list:t=>["trpc","crud.attributeTemplate.findMany",t??{}]},stats:{parts:()=>["trpc","crud.part.count"],brands:()=>["trpc","crud.brand.count"],categories:()=>["trpc","crud.partCategory.count"]}},mt={parts:{list:async t=>p.crud.part.findMany.query(t),detail:async t=>p.crud.part.findUnique.query(t),count:async t=>p.crud.part.count.query(t)},brands:{list:async t=>p.crud.brand.findMany.query(t)},categories:{list:async t=>p.crud.partCategory.findMany.query(t)},search:{parts:async t=>p.search.searchParts.query(t)},attributeTemplates:{list:async t=>p.crud.attributeTemplate.findMany.query(t)},stats:{parts:async()=>p.crud.part.count.query(),brands:async()=>p.crud.brand.count.query(),categories:async()=>p.crud.partCategory.count.query()}};export{Rt as a,mt as f,gt as q,vt as u};

import{u as X}from"./useTrpc.spLZjt2f.js";import be from"./Card.C4y0_bWr.js";import Y from"./Button.DrThv2lH.js";import{V as De}from"./AutoComplete.rPzROuMW.js";import{T as xe}from"./Tag.DTFTku6q.js";import{D as Z}from"./Dialog.Ct7C9BO5.js";import{A as Ve}from"./AttributeValueInput.BnZ_HprM.js";import{I as _e}from"./Icon.By8t0-Wj.js";import{D as Ae}from"./DangerButton.Du4QYdLH.js";import{_ as $}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{P as Ee,T as Te}from"./trash.4HbnIIsp.js";import{d as ee,c as f,o as d,a as l,e as n,g as F,b as _,w as b,F as L,r as M,f as k,h as G,i as Be,j as R,l as H}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as v,n as h,r as V}from"./reactivity.esm-bundler.BQ12LWmY.js";import{v as W}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{I as he}from"./InputText.DOJMNEP-.js";import{M as ke}from"./Message.BY1UiDHQ.js";const we=ee({__name:"AttributeManager",props:{modelValue:{},entityId:{},title:{default:"Атрибуты запчасти"},description:{default:""},showGroupSelector:{type:Boolean,default:!0},groupByTemplate:{type:Boolean,default:!1},cardMode:{default:"detailed"}},emits:["update:modelValue"],setup(S,{expose:a,emit:E}){a();const e=S,D=E,{client:w,partAttributes:t,attributeTemplates:p}=X(),i=V(null),c=V(null),g=V([]),A=V([]),B=V(!1),C=V(!1),I=V(!1),x=V(null),m=V(""),U=V(null),O=V([]),N=G({get:()=>e.modelValue,set:u=>D("update:modelValue",u)}),ue=G(()=>N.value.filter(u=>u.value&&String(u.value).trim()).length),te=G(()=>e.groupByTemplate?N.value.reduce((u,o)=>{const r=o.template?.group?.name||o.templateGroup||"undefined";return u[r]||(u[r]=[]),u[r].push(o),u},{}):{}),le=G(()=>{if(!c.value&&!x.value)return!1;const u=c.value||x.value?.template;if(!u)return!1;const o=m.value;return u.dataType==="BOOLEAN"?o!=null:!(o==null||o===""||u.dataType==="STRING"&&o.toString().trim()==="")}),ae=(u,o)=>{const r=[...e.modelValue];r[u].value=o,D("update:modelValue",r)},j=async u=>{const o=e.modelValue[u];if(o.id)try{const y=await t.delete({id:o.id});console.log("Атрибут успешно удален из БД:",o.id,y)}catch(y){console.error("Ошибка удаления атрибута:",y);return}const r=[...e.modelValue];r.splice(u,1),D("update:modelValue",r)},oe=u=>u?{id:u.templateId,name:u.template?.name||"",title:u.templateTitle||"",description:u.templateDescription||"",dataType:u.templateDataType||"STRING",unit:u.templateUnit,isRequired:u.template?.isRequired||!1,minValue:u.template?.minValue,maxValue:u.template?.maxValue,allowedValues:u.template?.allowedValues||[],group:u.template?.group}:{id:0,name:"",title:"",description:"",dataType:"STRING",unit:void 0,isRequired:!1,minValue:void 0,maxValue:void 0,allowedValues:[]},se=()=>{if(!c.value)return;const u=c.value,o={templateId:u.id,value:"",template:u,templateTitle:u.title,templateDataType:u.dataType,templateUnit:u.unit,templateGroup:u.group?.name,templateDescription:u.description};D("update:modelValue",[...e.modelValue,o]),c.value=null},re=async u=>{const o=u.query.toLowerCase();try{const r=await p.findAllGroups();if(r&&Array.isArray(r)){const y=r.filter(s=>s.name.toLowerCase().includes(o)).slice(0,10);g.value=y}}catch(r){console.error("Ошибка поиска групп:",r),g.value=[]}},ne=async()=>{if(i.value){B.value=!0;try{const u=i.value.id||i.value,o=await p.findMany({groupId:u});if(o&&typeof o=="object"&&o.templates){const y=o.templates.map(s=>({templateId:s.id,value:"",template:s,templateTitle:s.title,templateDataType:s.dataType,templateUnit:s.unit,templateGroup:s.group?.name,templateDescription:s.description}));D("update:modelValue",[...e.modelValue,...y]),i.value=null}}catch(u){console.error("Ошибка загрузки шаблонов:",u)}finally{B.value=!1}}},ie=async u=>{const o=u.query.toLowerCase();try{const r=await p.findMany({search:o,limit:10});r&&Array.isArray(r.templates)?A.value=r.templates:A.value=[]}catch(r){console.error("Ошибка поиска шаблонов:",r),A.value=[]}},de=u=>({STRING:"pi pi-font",NUMBER:"pi pi-hashtag",BOOLEAN:"pi pi-check-square",DATE:"pi pi-calendar",JSON:"pi pi-code"})[u||""]||"pi pi-question",me=u=>u?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[u]||u:"",P=u=>u?{MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[u]||u:"",ce=u=>{const o=u.templateDataType,r=u.templateUnit;switch(o){case"STRING":return"Введите текст...";case"NUMBER":return r?`Введите число (${P(r)})...`:"Введите число...";case"BOOLEAN":return"Выберите значение...";case"DATE":return"Выберите дату...";default:return"Введите значение..."}},pe=u=>{x.value=u,m.value=u.value||"",C.value=!0},q=()=>{C.value=!1,c.value=null,m.value="",x.value=null},fe=u=>{m.value=u},J=u=>e.modelValue.findIndex(o=>o.id&&u.id?o.id===u.id:o.templateId===u.templateId),ve=async u=>{const o=J(u);o!==-1&&await j(o)},ge=async()=>{const u=c.value||x.value?.template;if(!u)return;const o=m.value;if(u.dataType==="BOOLEAN"){if(o==null)return}else if(o==null||o===""||u.dataType==="STRING"&&o.toString().trim()==="")return;if(!(U.value&&!U.value.validate())){if(x.value){if(x.value.id&&e.entityId)try{await t.update({id:x.value.id,value:m.value})}catch(r){console.error("Ошибка обновления атрибута:",r);return}x.value.value=m.value}else if(c.value){const r=typeof c.value=="object"?c.value.id:c.value,y=N.value.find(s=>s.templateId===r);if(y){if(y.id&&e.entityId)try{await t.update({id:y.id,value:m.value})}catch(s){console.error("Ошибка обновления атрибута:",s);return}y.value=m.value}else{const s={templateId:r,value:m.value,template:c.value};if(e.entityId)try{const T=await t.create({partId:e.entityId,templateId:r,value:m.value});T&&typeof T=="object"&&"id"in T&&(s.id=T.id)}catch(T){console.error("Ошибка создания атрибута:",T);return}D("update:modelValue",[...e.modelValue,s])}}q()}},K=async()=>{try{const u=await p.findAllGroups();u&&Array.isArray(u)&&(g.value=u,O.value=u)}catch(u){console.error("Ошибка загрузки групп шаблонов:",u)}},z=async()=>{if(e.entityId)try{const u=await t.findByPartId({partId:e.entityId});if(u&&Array.isArray(u)){const o=u.map(s=>({id:s.id,templateId:s.templateId,value:s.value,template:s.template,templateTitle:s.template?.title,templateDataType:s.template?.dataType,templateUnit:s.template?.unit,templateGroup:s.template?.group?.name,templateDescription:s.template?.description})),r=new Set(e.modelValue.map(s=>s.templateId)),y=o.filter(s=>!r.has(s.templateId));D("update:modelValue",[...e.modelValue,...y])}}catch(u){console.error("Ошибка загрузки существующих атрибутов:",u)}},ye=async u=>{try{B.value=!0;const o=await p.findMany({groupId:u,limit:100});if(o&&typeof o=="object"){const y=(o.templates||[]).filter(s=>!e.modelValue.some(T=>T.templateId===s.id)).map(s=>({templateId:s.id,value:"",template:s,templateTitle:s.title,templateDataType:s.dataType,templateUnit:s.unit,templateGroup:s.group?.name,templateDescription:s.description}));D("update:modelValue",[...e.modelValue,...y]),console.log(`Загружено ${y.length} новых шаблонов из группы`),I.value=!1}}catch(o){console.error("Ошибка загрузки шаблонов группы:",o)}finally{B.value=!1}};Be(()=>{K(),z()}),R(()=>e.entityId,()=>{e.entityId&&z()}),R(C,u=>{u||(c.value=null,m.value="",x.value=null)}),R(m,u=>{});const Q={props:e,emit:D,client:w,partAttributes:t,attributeTemplates:p,selectedTemplateGroup:i,selectedTemplate:c,groupSuggestions:g,templateSuggestions:A,loadingTemplates:B,showAddDialog:C,showGroupDialog:I,editingAttribute:x,newAttributeValue:m,valueInputRef:U,templateGroups:O,modelValue:N,filledAttributesCount:ue,groupedAttributes:te,canSave:le,updateAttributeValue:ae,removeAttribute:j,getTemplateForInput:oe,addSingleTemplate:se,filterGroups:re,loadSelectedGroupTemplates:ne,filterTemplates:ie,getDataTypeIcon:de,getDataTypeLabel:me,getUnitLabel:P,getPlaceholder:ce,editAttribute:pe,closeAddDialog:q,handleValueChange:fe,getAttributeIndex:J,removeAttributeByObject:ve,saveAttribute:ge,loadTemplateGroups:K,loadExistingAttributes:z,loadTemplatesByGroupId:ye,VCard:be,VButton:Y,VAutoComplete:De,VTag:xe,VDialog:Z,AttributeValueInput:Ve,Icon:_e,DangerButton:Ae,get TrashIcon(){return Te},get PencilIcon(){return Ee}};return Object.defineProperty(Q,"__isScriptSetup",{enumerable:!1,value:!0}),Q}}),Ce={class:"simple-attribute-manager"},Ie={class:"flex items-center justify-between mb-4"},Fe={class:"flex items-center gap-3"},Ge={class:"text-lg font-medium text-surface-900 dark:text-surface-0"},Se={class:"flex gap-2"},Ue={class:"p-4"},Oe={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ne={class:"flex items-center gap-2"},Le={class:"flex-1"},Me={class:"font-medium"},Re={class:"text-sm text-surface-600"},ze={class:"flex items-end gap-2"},je={key:0,class:"space-y-4"},Pe={class:"flex items-center gap-2 mb-3"},qe={class:"font-medium text-surface-900 dark:text-surface-0"},Je={class:"space-y-3 ml-6"},Ke={class:"flex-shrink-0 relative"},Qe=["title"],He={class:"flex-shrink-0 w-48"},We={class:"font-medium text-surface-900 dark:text-surface-0 text-sm"},Xe={class:"flex-1"},Ye={class:"flex-shrink-0 w-16 text-center"},Ze={key:0,class:"text-sm text-surface-500 font-medium"},$e={class:"flex-shrink-0 flex gap-2"},eu={key:1,class:"space-y-3"},uu={class:"flex-shrink-0 relative"},tu=["title"],lu={class:"flex-shrink-0 w-48"},au={class:"font-medium text-surface-900 dark:text-surface-0 text-sm"},ou={class:"flex-1"},su={class:"flex-shrink-0 w-16 text-center"},ru={key:0,class:"text-sm text-surface-500 font-medium"},nu={class:"flex-shrink-0 flex gap-2"},iu={key:1,class:"text-center py-8 text-surface-500 border-2 border-dashed border-surface-200 dark:border-surface-700 rounded-lg"},du={class:"space-y-4"},mu={key:0},cu={class:"flex items-center gap-3"},pu={class:"flex-1"},fu={class:"font-medium"},vu={class:"text-sm text-surface-600 dark:text-surface-400"},gu={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},yu={key:1,class:"p-4 bg-surface-50 dark:bg-surface-900 rounded-lg"},bu={class:"flex items-center gap-2 mb-2"},Du={class:"font-medium"},xu={class:"text-sm text-surface-600 dark:text-surface-400"},Vu={key:2},_u={class:"space-y-4"},Au={class:"flex items-center justify-between"},Eu={class:"font-medium text-surface-900 dark:text-surface-0"},Tu={class:"text-sm text-surface-600 dark:text-surface-400"},Bu={class:"text-surface-500"};function hu(S,a,E,e,D,w){return d(),f("div",Ce,[l("div",Ie,[l("div",Fe,[l("h3",Ge,v(E.title),1),e.modelValue.length>0?(d(),F(e.VTag,{key:0,value:`${e.filledAttributesCount}/${e.modelValue.length} заполнено`,severity:e.filledAttributesCount===e.modelValue.length?"success":"warn",size:"small"},null,8,["value","severity"])):_("",!0)]),l("div",Se,[E.showGroupSelector?(d(),F(e.VButton,{key:0,onClick:a[0]||(a[0]=t=>e.showGroupDialog=!0),severity:"secondary",outlined:"",size:"small",label:"Добавить группу"},{icon:b(()=>[n(e.Icon,{name:"pi pi-tags",class:"w-5 h-5"})]),_:1})):_("",!0),n(e.VButton,{onClick:a[1]||(a[1]=t=>e.showAddDialog=!0),size:"small",outlined:"",label:"Добавить атрибут"})])]),n(e.VCard,{class:"mb-4"},{content:b(()=>[l("div",Ue,[l("div",Oe,[l("div",null,[a[8]||(a[8]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Группа шаблонов ",-1)),n(e.VAutoComplete,{modelValue:e.selectedTemplateGroup,"onUpdate:modelValue":a[2]||(a[2]=t=>e.selectedTemplateGroup=t),suggestions:e.groupSuggestions,onComplete:e.filterGroups,"option-label":"name",placeholder:"Поиск группы...",class:"w-full",dropdown:""},null,8,["modelValue","suggestions"])]),l("div",null,[a[9]||(a[9]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Или отдельный шаблон ",-1)),n(e.VAutoComplete,{modelValue:e.selectedTemplate,"onUpdate:modelValue":a[3]||(a[3]=t=>e.selectedTemplate=t),suggestions:e.templateSuggestions,onComplete:e.filterTemplates,"option-label":"title",placeholder:"Поиск шаблона...",class:"w-full",dropdown:""},{option:b(({option:t})=>[l("div",Ne,[n(e.Icon,{name:e.getDataTypeIcon(t.dataType),class:"text-primary w-4 h-4"},null,8,["name"]),l("div",Le,[l("div",Me,v(t.title),1),l("div",Re,v(t.group?.name)+" • "+v(e.getDataTypeLabel(t.dataType)),1)])])]),_:1},8,["modelValue","suggestions"])]),l("div",ze,[n(e.VButton,{onClick:e.loadSelectedGroupTemplates,size:"small",outlined:"",disabled:!e.selectedTemplateGroup||e.loadingTemplates,loading:e.loadingTemplates,label:"Добавить группу",class:"flex-1"},null,8,["disabled","loading"]),n(e.VButton,{onClick:e.addSingleTemplate,size:"small",outlined:"",disabled:!e.selectedTemplate,label:"Добавить",class:"flex-1"},null,8,["disabled"])])])])]),_:1}),e.modelValue.length>0?(d(),f("div",je,[E.groupByTemplate?(d(!0),f(L,{key:0},M(e.groupedAttributes,(t,p)=>(d(),f("div",{key:p,class:"space-y-3"},[l("div",Pe,[n(e.Icon,{name:"pi pi-folder",class:"text-blue-600 w-4 h-4"}),l("h4",qe,v(p==="undefined"?"Без группы":p),1),n(e.VTag,{value:`${t.length} атр.`,severity:"secondary",size:"small"},null,8,["value"])]),l("div",Je,[(d(!0),f(L,null,M(t,(i,c)=>(d(),f("div",{key:i.id||`new-${c}`},[l("div",{class:h(["flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm",[i.value?"border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10":"border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"]])},[l("div",Ke,[n(e.Icon,{name:e.getDataTypeIcon(i.templateDataType),class:"text-lg text-primary w-5 h-5"},null,8,["name"]),l("div",{class:h(["absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900",i.value?"bg-green-500":"bg-orange-500"]),title:i.value?"Заполнено":"Не заполнено"},null,10,Qe)]),l("div",He,[l("div",We,v(i.templateTitle||"Без названия"),1),i.templateGroup?(d(),F(e.VTag,{key:0,value:i.templateGroup,severity:"secondary",size:"small",class:"mt-1"},null,8,["value"])):_("",!0)]),l("div",Xe,[n(e.AttributeValueInput,{template:e.getTemplateForInput(i),"model-value":i.value,"onUpdate:modelValue":g=>e.updateAttributeValue(e.getAttributeIndex(i),g),class:"w-full",placeholder:e.getPlaceholder(i)},null,8,["template","model-value","onUpdate:modelValue","placeholder"])]),l("div",Ye,[i.templateUnit?(d(),f("span",Ze,v(e.getUnitLabel(i.templateUnit)),1)):_("",!0)]),l("div",$e,[n(e.VButton,{onClick:g=>e.editAttribute(i),size:"small",outlined:""},{default:b(()=>[n(e.PencilIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"]),n(e.DangerButton,{onClick:g=>e.removeAttributeByObject(i),size:"small",outlined:""},{default:b(()=>[n(e.TrashIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])])],2)]))),128))])]))),128)):(d(),f("div",eu,[(d(!0),f(L,null,M(e.modelValue,(t,p)=>(d(),f("div",{key:t.id||`new-${p}`},[l("div",{class:h(["flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm",[t.value?"border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10":"border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"]])},[l("div",uu,[n(e.Icon,{name:e.getDataTypeIcon(t.templateDataType),class:"text-lg text-primary w-5 h-5"},null,8,["name"]),l("div",{class:h(["absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900",t.value?"bg-green-500":"bg-orange-500"]),title:t.value?"Заполнено":"Не заполнено"},null,10,tu)]),l("div",lu,[l("div",au,v(t.templateTitle||"Без названия"),1),t.templateGroup?(d(),F(e.VTag,{key:0,value:t.templateGroup,severity:"secondary",size:"small",class:"mt-1"},null,8,["value"])):_("",!0)]),l("div",ou,[n(e.AttributeValueInput,{template:e.getTemplateForInput(t),"model-value":t.value,"onUpdate:modelValue":i=>e.updateAttributeValue(p,i),class:"w-full",placeholder:e.getPlaceholder(t)},null,8,["template","model-value","onUpdate:modelValue","placeholder"])]),l("div",su,[t.templateUnit?(d(),f("span",ru,v(e.getUnitLabel(t.templateUnit)),1)):_("",!0)]),l("div",nu,[n(e.DangerButton,{onClick:i=>e.removeAttribute(p),severity:"danger",size:"small",outlined:"",class:"hover:bg-red-50 dark:hover:bg-red-900/20"},{default:b(()=>[n(e.TrashIcon,{class:"h-4 w-4"})]),_:2},1032,["onClick"])])],2)]))),128))]))])):(d(),f("div",iu,a[10]||(a[10]=[l("i",{class:"pi pi-plus-circle text-3xl mb-2 block"},null,-1),l("p",null,"Добавьте атрибуты для описания характеристик запчасти",-1),l("p",{class:"text-sm mt-1"},"Выберите группу шаблонов или отдельный шаблон выше",-1)]))),n(e.VDialog,{visible:e.showAddDialog,"onUpdate:visible":a[6]||(a[6]=t=>e.showAddDialog=t),modal:"",header:e.editingAttribute?"Редактировать атрибут":"Добавить атрибут",style:{width:"50rem"},breakpoints:{"1199px":"75vw","575px":"90vw"}},{footer:b(()=>[n(e.VButton,{label:"Отмена",severity:"secondary",onClick:e.closeAddDialog}),n(e.VButton,{label:e.editingAttribute?"Сохранить":"Добавить",onClick:e.saveAttribute,disabled:!e.canSave},null,8,["label","disabled"])]),default:b(()=>[l("div",du,[e.editingAttribute?(d(),f("div",yu,[l("div",bu,[l("i",{class:h([e.getDataTypeIcon(e.editingAttribute.template?.dataType),"text-primary"])},null,2),l("span",Du,v(e.editingAttribute.template?.title),1),n(e.VTag,{value:e.getDataTypeLabel(e.editingAttribute.template?.dataType),severity:"info",size:"small"},null,8,["value"])]),l("div",xu,v(e.editingAttribute.template?.description),1)])):(d(),f("div",mu,[a[11]||(a[11]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Шаблон атрибута * ",-1)),n(e.VAutoComplete,{modelValue:e.selectedTemplate,"onUpdate:modelValue":a[4]||(a[4]=t=>e.selectedTemplate=t),suggestions:e.templateSuggestions,onComplete:e.filterTemplates,"option-label":"title",placeholder:"Поиск шаблона...",class:"w-full",dropdown:""},{option:b(t=>[l("div",cu,[l("i",{class:h([e.getDataTypeIcon(t.option.dataType),"text-primary"])},null,2),l("div",pu,[l("div",fu,v(t.option.title),1),l("div",vu,[k(v(t.option.name)+" ",1),t.option.group?.name?(d(),f("span",gu,v(t.option.group.name),1)):_("",!0)])]),n(e.VTag,{value:e.getDataTypeLabel(t.option.dataType),severity:"info",size:"small"},null,8,["value"])])]),_:1},8,["modelValue","suggestions"])])),e.selectedTemplate||e.editingAttribute?(d(),f("div",Vu,[a[12]||(a[12]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Значение * ",-1)),n(e.AttributeValueInput,{modelValue:e.newAttributeValue,"onUpdate:modelValue":a[5]||(a[5]=t=>e.newAttributeValue=t),template:e.selectedTemplate||e.editingAttribute?.template,ref:"valueInputRef",onChange:e.handleValueChange},null,8,["modelValue","template"])])):_("",!0)])]),_:1},8,["visible","header"]),n(e.VDialog,{visible:e.showGroupDialog,"onUpdate:visible":a[7]||(a[7]=t=>e.showGroupDialog=t),modal:"",header:"Выбор группы шаблонов",style:{width:"40rem"}},{default:b(()=>[l("div",_u,[(d(!0),f(L,null,M(e.templateGroups,t=>(d(),f("div",{key:t.id,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4"},[l("div",Au,[l("div",null,[l("h4",Eu,v(t.name),1),l("p",Tu,v(t.description),1),l("small",Bu,v(t._count?.templates||0)+" шаблонов",1)]),n(e.VButton,{onClick:p=>e.loadTemplatesByGroupId(t.id),size:"small",loading:e.loadingTemplates},{default:b(()=>a[13]||(a[13]=[k(" Добавить все ")])),_:2,__:[13]},1032,["onClick","loading"])])]))),128))])]),_:1},8,["visible"])])}const Zu=$(we,[["render",hu]]),ku=ee({__name:"QuickCreateBrand",props:{visible:{type:Boolean}},emits:["update:visible","created"],setup(S,{expose:a,emit:E}){a();const e=S,D=E,{loading:w,error:t,clearError:p,brands:i}=X(),c=G({get:()=>e.visible,set:m=>D("update:visible",m)}),g=V({name:"",country:"",isOem:!1}),A=V({name:""}),B=G(()=>g.value.name.trim().length>0&&!w.value),C=async()=>{if(p(),A.value={name:""},!g.value.name.trim()){A.value.name="Название обязательно";return}try{const m=g.value.name.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),U={name:g.value.name.trim(),slug:m,country:g.value.country?.trim()||void 0,isOem:g.value.isOem},O=await i.create({data:U});O&&(D("created",O),I(),c.value=!1)}catch(m){console.error("Ошибка создания бренда:",m)}},I=()=>{g.value={name:"",country:"",isOem:!1},A.value={name:""},p()};R(c,m=>{m||I()});const x={props:e,emit:D,loading:w,error:t,clearError:p,brands:i,visible:c,formData:g,errors:A,canCreate:B,createBrand:C,resetForm:I,VDialog:Z,VButton:Y,VInputText:he,VMessage:ke};return Object.defineProperty(x,"__isScriptSetup",{enumerable:!1,value:!0}),x}}),wu={class:"space-y-4"},Cu={key:0,class:"text-red-500"},Iu={class:"flex gap-4"},Fu={class:"flex items-center"},Gu={class:"flex items-center"},Su={class:"flex justify-end gap-3"};function Uu(S,a,E,e,D,w){return d(),F(e.VDialog,{visible:e.visible,"onUpdate:visible":a[5]||(a[5]=t=>e.visible=t),modal:"",header:"Создать новый бренд",class:"w-96"},{footer:b(()=>[l("div",Su,[n(e.VButton,{onClick:a[4]||(a[4]=t=>e.visible=!1),severity:"secondary",outlined:"",disabled:e.loading},{default:b(()=>a[11]||(a[11]=[k(" Отмена ")])),_:1,__:[11]},8,["disabled"]),n(e.VButton,{onClick:e.createBrand,loading:e.loading,disabled:!e.canCreate},{default:b(()=>a[12]||(a[12]=[k(" Создать ")])),_:1,__:[12]},8,["loading","disabled"])])]),default:b(()=>[l("div",wu,[l("div",null,[a[6]||(a[6]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название бренда * ",-1)),n(e.VInputText,{modelValue:e.formData.name,"onUpdate:modelValue":a[0]||(a[0]=t=>e.formData.name=t),placeholder:"Например: SKF",class:h(["w-full",{"p-invalid":e.errors.name}])},null,8,["modelValue","class"]),e.errors.name?(d(),f("small",Cu,v(e.errors.name),1)):_("",!0)]),l("div",null,[a[7]||(a[7]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Страна ",-1)),n(e.VInputText,{modelValue:e.formData.country,"onUpdate:modelValue":a[1]||(a[1]=t=>e.formData.country=t),placeholder:"Например: Швеция",class:"w-full"},null,8,["modelValue"])]),l("div",null,[a[10]||(a[10]=l("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип производителя ",-1)),l("div",Iu,[l("label",Fu,[H(l("input",{type:"radio","onUpdate:modelValue":a[2]||(a[2]=t=>e.formData.isOem=t),value:!1,class:"mr-2"},null,512),[[W,e.formData.isOem]]),a[8]||(a[8]=k(" Aftermarket "))]),l("label",Gu,[H(l("input",{type:"radio","onUpdate:modelValue":a[3]||(a[3]=t=>e.formData.isOem=t),value:!0,class:"mr-2"},null,512),[[W,e.formData.isOem]]),a[9]||(a[9]=k(" OEM "))])])])]),e.error?(d(),F(e.VMessage,{key:0,severity:"error",class:"mt-4"},{default:b(()=>[k(v(e.error),1)]),_:1})):_("",!0)]),_:1},8,["visible"])}const $u=$(ku,[["render",Uu]]);export{Zu as A,$u as Q};

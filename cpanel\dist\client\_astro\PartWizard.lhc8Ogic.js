import{s as we,p as ie}from"./utils.BUKUcbtE.js";import{B as ee,G as Ee,T as kt,z as _e,H as _t,K as oe,r as Ct,aa as qe,k as le,b as Bt,Q as re,a as Me,c as se}from"./index.BaVCXmir.js";import{c as f,o as i,p as L,m as A,d as R,g as w,w as y,k as de,l as Y,b as k,a as o,M as Ce,e as m,f as I,h as H,j as ze,P as wt,F as W,r as U,n as Ue,q as Dt,i as Vt}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{_ as N}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{r as C,n as De,t as h,b as Tt}from"./reactivity.esm-bundler.BQ12LWmY.js";import{s as At}from"./AttributeValueInput.BnZ_HprM.js";import{s as It}from"./index.CUNrRq8E.js";import{R as ce,f as pe}from"./index.BH7IgUdp.js";import{u as be}from"./useTrpc.spLZjt2f.js";import Re from"./Card.C4y0_bWr.js";import fe from"./Button.DrThv2lH.js";import{I as me}from"./InputText.DOJMNEP-.js";import{V as ge}from"./AutoComplete.rPzROuMW.js";import{M as Ne}from"./Message.BY1UiDHQ.js";import{D as St}from"./Dialog.Ct7C9BO5.js";import{V as Ke}from"./Textarea.BLEHJ3ym.js";import{Q as je,A as Ft}from"./QuickCreateBrand.C_-iK7cE.js";import{b as He}from"./index.COq_zjeV.js";import{P as We}from"./plus.CiWMw0wk.js";import{T as Pt}from"./Tag.DTFTku6q.js";import{S as Lt}from"./Select.CQBzSu6y.js";import{I as Ot}from"./Icon.By8t0-Wj.js";import{f as qt,r as Mt}from"./utils.BL5HZsed.js";import"./bundle-mjs.D6B6e0vX.js";import"./InputNumber.vgPO18dj.js";import"./index.CS9OBiV4.js";import"./Checkbox.C7bFmmzc.js";import"./index.By2TJOuX.js";import"./index.D4QD70nN.js";import"./index.CLs7nh7g.js";import"./index.S_9XL1GF.js";import"./index.DPMtieGJ.js";import"./index.BpXFSz0M.js";import"./index.6ykohhwZ.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./index.CDQpPXyE.js";import"./trpc.BpyaUO08.js";import"./useErrorHandler.DVDazL16.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./SecondaryButton.DkELYl7Q.js";import"./DangerButton.Du4QYdLH.js";import"./trash.4HbnIIsp.js";import"./createLucideIcon.NtN1-Ts2.js";/* empty css                       */var zt=`
    .p-tabs {
        display: flex;
        flex-direction: column;
    }

    .p-tablist {
        display: flex;
        position: relative;
    }

    .p-tabs-scrollable > .p-tablist {
        overflow: hidden;
    }

    .p-tablist-viewport {
        overflow-x: auto;
        overflow-y: hidden;
        scroll-behavior: smooth;
        scrollbar-width: none;
        overscroll-behavior: contain auto;
    }

    .p-tablist-viewport::-webkit-scrollbar {
        display: none;
    }

    .p-tablist-tab-list {
        position: relative;
        display: flex;
        background: dt('tabs.tablist.background');
        border-style: solid;
        border-color: dt('tabs.tablist.border.color');
        border-width: dt('tabs.tablist.border.width');
    }

    .p-tablist-content {
        flex-grow: 1;
    }

    .p-tablist-nav-button {
        all: unset;
        position: absolute !important;
        flex-shrink: 0;
        inset-block-start: 0;
        z-index: 2;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: dt('tabs.nav.button.background');
        color: dt('tabs.nav.button.color');
        width: dt('tabs.nav.button.width');
        transition:
            color dt('tabs.transition.duration'),
            outline-color dt('tabs.transition.duration'),
            box-shadow dt('tabs.transition.duration');
        box-shadow: dt('tabs.nav.button.shadow');
        outline-color: transparent;
        cursor: pointer;
    }

    .p-tablist-nav-button:focus-visible {
        z-index: 1;
        box-shadow: dt('tabs.nav.button.focus.ring.shadow');
        outline: dt('tabs.nav.button.focus.ring.width') dt('tabs.nav.button.focus.ring.style') dt('tabs.nav.button.focus.ring.color');
        outline-offset: dt('tabs.nav.button.focus.ring.offset');
    }

    .p-tablist-nav-button:hover {
        color: dt('tabs.nav.button.hover.color');
    }

    .p-tablist-prev-button {
        inset-inline-start: 0;
    }

    .p-tablist-next-button {
        inset-inline-end: 0;
    }

    .p-tablist-prev-button:dir(rtl),
    .p-tablist-next-button:dir(rtl) {
        transform: rotate(180deg);
    }

    .p-tab {
        flex-shrink: 0;
        cursor: pointer;
        user-select: none;
        position: relative;
        border-style: solid;
        white-space: nowrap;
        gap: dt('tabs.tab.gap');
        background: dt('tabs.tab.background');
        border-width: dt('tabs.tab.border.width');
        border-color: dt('tabs.tab.border.color');
        color: dt('tabs.tab.color');
        padding: dt('tabs.tab.padding');
        font-weight: dt('tabs.tab.font.weight');
        transition:
            background dt('tabs.transition.duration'),
            border-color dt('tabs.transition.duration'),
            color dt('tabs.transition.duration'),
            outline-color dt('tabs.transition.duration'),
            box-shadow dt('tabs.transition.duration');
        margin: dt('tabs.tab.margin');
        outline-color: transparent;
    }

    .p-tab:not(.p-disabled):focus-visible {
        z-index: 1;
        box-shadow: dt('tabs.tab.focus.ring.shadow');
        outline: dt('tabs.tab.focus.ring.width') dt('tabs.tab.focus.ring.style') dt('tabs.tab.focus.ring.color');
        outline-offset: dt('tabs.tab.focus.ring.offset');
    }

    .p-tab:not(.p-tab-active):not(.p-disabled):hover {
        background: dt('tabs.tab.hover.background');
        border-color: dt('tabs.tab.hover.border.color');
        color: dt('tabs.tab.hover.color');
    }

    .p-tab-active {
        background: dt('tabs.tab.active.background');
        border-color: dt('tabs.tab.active.border.color');
        color: dt('tabs.tab.active.color');
    }

    .p-tabpanels {
        background: dt('tabs.tabpanel.background');
        color: dt('tabs.tabpanel.color');
        padding: dt('tabs.tabpanel.padding');
        outline: 0 none;
    }

    .p-tabpanel:focus-visible {
        box-shadow: dt('tabs.tabpanel.focus.ring.shadow');
        outline: dt('tabs.tabpanel.focus.ring.width') dt('tabs.tabpanel.focus.ring.style') dt('tabs.tabpanel.focus.ring.color');
        outline-offset: dt('tabs.tabpanel.focus.ring.offset');
    }

    .p-tablist-active-bar {
        z-index: 1;
        display: block;
        position: absolute;
        inset-block-end: dt('tabs.active.bar.bottom');
        height: dt('tabs.active.bar.height');
        background: dt('tabs.active.bar.background');
        transition: 250ms cubic-bezier(0.35, 0, 0.25, 1);
    }
`,Ut={root:function(e){var a=e.props;return["p-tabs p-component",{"p-tabs-scrollable":a.scrollable}]}},Rt=ee.extend({name:"tabs",style:zt,classes:Ut}),Nt={name:"BaseTabs",extends:we,props:{value:{type:[String,Number],default:void 0},lazy:{type:Boolean,default:!1},scrollable:{type:Boolean,default:!1},showNavigators:{type:Boolean,default:!0},tabindex:{type:Number,default:0},selectOnFocus:{type:Boolean,default:!1}},style:Rt,provide:function(){return{$pcTabs:this,$parentInstance:this}}},Qe={name:"Tabs",extends:Nt,inheritAttrs:!1,emits:["update:value"],data:function(){return{d_value:this.value}},watch:{value:function(e){this.d_value=e}},methods:{updateValue:function(e){this.d_value!==e&&(this.d_value=e,this.$emit("update:value",e))},isVertical:function(){return this.orientation==="vertical"}}};function Kt(u,e,a,t,d,r){return i(),f("div",A({class:u.cx("root")},u.ptmi("root")),[L(u.$slots,"default")],16)}Qe.render=Kt;const jt=R({__name:"Tabs",setup(u,{expose:e}){e();const a=u,t=C({root:"flex flex-col"}),d={props:a,theme:t,get Tabs(){return Qe},get ptViewMerge(){return ie}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}});function Ht(u,e,a,t,d,r){return i(),w(t.Tabs,{value:t.props.value,unstyled:"",pt:t.theme,ptOptions:{mergeProps:t.ptViewMerge}},{default:y(()=>[L(u.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const Wt=N(jt,[["render",Ht]]);var Qt={root:"p-tablist",content:function(e){var a=e.instance;return["p-tablist-content",{"p-tablist-viewport":a.$pcTabs.scrollable}]},tabList:"p-tablist-tab-list",activeBar:"p-tablist-active-bar",prevButton:"p-tablist-prev-button p-tablist-nav-button",nextButton:"p-tablist-next-button p-tablist-nav-button"},Gt=ee.extend({name:"tablist",classes:Qt}),Xt={name:"BaseTabList",extends:we,props:{},style:Gt,provide:function(){return{$pcTabList:this,$parentInstance:this}}},Ge={name:"TabList",extends:Xt,inheritAttrs:!1,inject:["$pcTabs"],data:function(){return{isPrevButtonEnabled:!1,isNextButtonEnabled:!0}},resizeObserver:void 0,watch:{showNavigators:function(e){e?this.bindResizeObserver():this.unbindResizeObserver()},activeValue:{flush:"post",handler:function(){this.updateInkBar()}}},mounted:function(){var e=this;setTimeout(function(){e.updateInkBar()},150),this.showNavigators&&(this.updateButtonState(),this.bindResizeObserver())},updated:function(){this.showNavigators&&this.updateButtonState()},beforeUnmount:function(){this.unbindResizeObserver()},methods:{onScroll:function(e){this.showNavigators&&this.updateButtonState(),e.preventDefault()},onPrevButtonClick:function(){var e=this.$refs.content,a=this.getVisibleButtonWidths(),t=Ee(e)-a,d=Math.abs(e.scrollLeft),r=t*.8,n=d-r,s=Math.max(n,0);e.scrollLeft=qe(e)?-1*s:s},onNextButtonClick:function(){var e=this.$refs.content,a=this.getVisibleButtonWidths(),t=Ee(e)-a,d=Math.abs(e.scrollLeft),r=t*.8,n=d+r,s=e.scrollWidth-t,p=Math.min(n,s);e.scrollLeft=qe(e)?-1*p:p},bindResizeObserver:function(){var e=this;this.resizeObserver=new ResizeObserver(function(){return e.updateButtonState()}),this.resizeObserver.observe(this.$refs.list)},unbindResizeObserver:function(){var e;(e=this.resizeObserver)===null||e===void 0||e.unobserve(this.$refs.list),this.resizeObserver=void 0},updateInkBar:function(){var e=this.$refs,a=e.content,t=e.inkbar,d=e.tabs;if(t){var r=_e(a,'[data-pc-name="tab"][data-p-active="true"]');this.$pcTabs.isVertical()?(t.style.height=_t(r)+"px",t.style.top=oe(r).top-oe(d).top+"px"):(t.style.width=Ct(r)+"px",t.style.left=oe(r).left-oe(d).left+"px")}},updateButtonState:function(){var e=this.$refs,a=e.list,t=e.content,d=t.scrollTop,r=t.scrollWidth,n=t.scrollHeight,s=t.offsetWidth,p=t.offsetHeight,c=Math.abs(t.scrollLeft),x=[Ee(t),kt(t)],O=x[0],P=x[1];this.$pcTabs.isVertical()?(this.isPrevButtonEnabled=d!==0,this.isNextButtonEnabled=a.offsetHeight>=p&&parseInt(d)!==n-P):(this.isPrevButtonEnabled=c!==0,this.isNextButtonEnabled=a.offsetWidth>=s&&parseInt(c)!==r-O)},getVisibleButtonWidths:function(){var e=this.$refs,a=e.prevButton,t=e.nextButton,d=0;return this.showNavigators&&(d=(a?.offsetWidth||0)+(t?.offsetWidth||0)),d}},computed:{templates:function(){return this.$pcTabs.$slots},activeValue:function(){return this.$pcTabs.d_value},showNavigators:function(){return this.$pcTabs.scrollable&&this.$pcTabs.showNavigators},prevButtonAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.previous:void 0},nextButtonAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.next:void 0},dataP:function(){return pe({scrollable:this.$pcTabs.scrollable})}},components:{ChevronLeftIcon:At,ChevronRightIcon:It},directives:{ripple:ce}},$t=["data-p"],Jt=["aria-label","tabindex"],Yt=["data-p"],Zt=["aria-orientation"],eu=["aria-label","tabindex"];function tu(u,e,a,t,d,r){var n=de("ripple");return i(),f("div",A({ref:"list",class:u.cx("root"),"data-p":r.dataP},u.ptmi("root")),[r.showNavigators&&d.isPrevButtonEnabled?Y((i(),f("button",A({key:0,ref:"prevButton",type:"button",class:u.cx("prevButton"),"aria-label":r.prevButtonAriaLabel,tabindex:r.$pcTabs.tabindex,onClick:e[0]||(e[0]=function(){return r.onPrevButtonClick&&r.onPrevButtonClick.apply(r,arguments)})},u.ptm("prevButton"),{"data-pc-group-section":"navigator"}),[(i(),w(Ce(r.templates.previcon||"ChevronLeftIcon"),A({"aria-hidden":"true"},u.ptm("prevIcon")),null,16))],16,Jt)),[[n]]):k("",!0),o("div",A({ref:"content",class:u.cx("content"),onScroll:e[1]||(e[1]=function(){return r.onScroll&&r.onScroll.apply(r,arguments)}),"data-p":r.dataP},u.ptm("content")),[o("div",A({ref:"tabs",class:u.cx("tabList"),role:"tablist","aria-orientation":r.$pcTabs.orientation||"horizontal"},u.ptm("tabList")),[L(u.$slots,"default"),o("span",A({ref:"inkbar",class:u.cx("activeBar"),role:"presentation","aria-hidden":"true"},u.ptm("activeBar")),null,16)],16,Zt)],16,Yt),r.showNavigators&&d.isNextButtonEnabled?Y((i(),f("button",A({key:1,ref:"nextButton",type:"button",class:u.cx("nextButton"),"aria-label":r.nextButtonAriaLabel,tabindex:r.$pcTabs.tabindex,onClick:e[2]||(e[2]=function(){return r.onNextButtonClick&&r.onNextButtonClick.apply(r,arguments)})},u.ptm("nextButton"),{"data-pc-group-section":"navigator"}),[(i(),w(Ce(r.templates.nexticon||"ChevronRightIcon"),A({"aria-hidden":"true"},u.ptm("nextIcon")),null,16))],16,eu)),[[n]]):k("",!0)],16,$t)}Ge.render=tu;const ke=`!absolute flex-shrink-0 top-0 z-20 h-full flex items-center justify-center cursor-pointer
        bg-surface-0 dark:bg-surface-900 text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-0 w-10
        shadow-[0px_0px_10px_50px_rgba(255,255,255,0.6)] dark:shadow-[0px_0px_10px_50px] dark:shadow-surface-900/50
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary
        transition-colors duration-200`,uu=R({__name:"TabList",setup(u,{expose:e}){e();const a=C({root:"flex relative",prevButton:ke+" start-0",nextButton:ke+" end-0",content:`flex-grow
        p-scrollable:overflow-x-auto p-scrollable:overflow-y-hidden p-scrollable:overscroll-y-contain p-scrollable:overscroll-x-auto
        scroll-smooth [scrollbar-width:none]`,tabList:`relative flex bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700
        p-scrollable:overflow-hidden`,activeBar:"z-10 block absolute -bottom-px h-px bg-primary transition-[left] duration-200 ease-[cubic-bezier(0.35,0,0.25,1)]"}),t={navButton:ke,theme:a,get TabList(){return Ge},get ptViewMerge(){return ie}};return Object.defineProperty(t,"__isScriptSetup",{enumerable:!1,value:!0}),t}});function au(u,e,a,t,d,r){return i(),w(t.TabList,{unstyled:"",pt:t.theme,ptOptions:{mergeProps:t.ptViewMerge}},{default:y(()=>[L(u.$slots,"default")]),_:3},8,["pt","ptOptions"])}const nu=N(uu,[["render",au]]);var ou={root:function(e){var a=e.instance,t=e.props;return["p-tab",{"p-tab-active":a.active,"p-disabled":t.disabled}]}},ru=ee.extend({name:"tab",classes:ou}),su={name:"BaseTab",extends:we,props:{value:{type:[String,Number],default:void 0},disabled:{type:Boolean,default:!1},as:{type:[String,Object],default:"BUTTON"},asChild:{type:Boolean,default:!1}},style:ru,provide:function(){return{$pcTab:this,$parentInstance:this}}},Xe={name:"Tab",extends:su,inheritAttrs:!1,inject:["$pcTabs","$pcTabList"],methods:{onFocus:function(){this.$pcTabs.selectOnFocus&&this.changeActiveValue()},onClick:function(){this.changeActiveValue()},onKeydown:function(e){switch(e.code){case"ArrowRight":this.onArrowRightKey(e);break;case"ArrowLeft":this.onArrowLeftKey(e);break;case"Home":this.onHomeKey(e);break;case"End":this.onEndKey(e);break;case"PageDown":this.onPageDownKey(e);break;case"PageUp":this.onPageUpKey(e);break;case"Enter":case"NumpadEnter":case"Space":this.onEnterKey(e);break}},onArrowRightKey:function(e){var a=this.findNextTab(e.currentTarget);a?this.changeFocusedTab(e,a):this.onHomeKey(e),e.preventDefault()},onArrowLeftKey:function(e){var a=this.findPrevTab(e.currentTarget);a?this.changeFocusedTab(e,a):this.onEndKey(e),e.preventDefault()},onHomeKey:function(e){var a=this.findFirstTab();this.changeFocusedTab(e,a),e.preventDefault()},onEndKey:function(e){var a=this.findLastTab();this.changeFocusedTab(e,a),e.preventDefault()},onPageDownKey:function(e){this.scrollInView(this.findLastTab()),e.preventDefault()},onPageUpKey:function(e){this.scrollInView(this.findFirstTab()),e.preventDefault()},onEnterKey:function(e){this.changeActiveValue(),e.preventDefault()},findNextTab:function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=a?e:e.nextElementSibling;return t?re(t,"data-p-disabled")||re(t,"data-pc-section")==="activebar"?this.findNextTab(t):_e(t,'[data-pc-name="tab"]'):null},findPrevTab:function(e){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=a?e:e.previousElementSibling;return t?re(t,"data-p-disabled")||re(t,"data-pc-section")==="activebar"?this.findPrevTab(t):_e(t,'[data-pc-name="tab"]'):null},findFirstTab:function(){return this.findNextTab(this.$pcTabList.$refs.tabs.firstElementChild,!0)},findLastTab:function(){return this.findPrevTab(this.$pcTabList.$refs.tabs.lastElementChild,!0)},changeActiveValue:function(){this.$pcTabs.updateValue(this.value)},changeFocusedTab:function(e,a){Bt(a),this.scrollInView(a)},scrollInView:function(e){var a;e==null||(a=e.scrollIntoView)===null||a===void 0||a.call(e,{block:"nearest"})}},computed:{active:function(){var e;return le((e=this.$pcTabs)===null||e===void 0?void 0:e.d_value,this.value)},id:function(){var e;return"".concat((e=this.$pcTabs)===null||e===void 0?void 0:e.$id,"_tab_").concat(this.value)},ariaControls:function(){var e;return"".concat((e=this.$pcTabs)===null||e===void 0?void 0:e.$id,"_tabpanel_").concat(this.value)},attrs:function(){return A(this.asAttrs,this.a11yAttrs,this.ptmi("root",this.ptParams))},asAttrs:function(){return this.as==="BUTTON"?{type:"button",disabled:this.disabled}:void 0},a11yAttrs:function(){return{id:this.id,tabindex:this.active?this.$pcTabs.tabindex:-1,role:"tab","aria-selected":this.active,"aria-controls":this.ariaControls,"data-pc-name":"tab","data-p-disabled":this.disabled,"data-p-active":this.active,onFocus:this.onFocus,onKeydown:this.onKeydown}},ptParams:function(){return{context:{active:this.active}}},dataP:function(){return pe({active:this.active})}},directives:{ripple:ce}};function lu(u,e,a,t,d,r){var n=de("ripple");return u.asChild?L(u.$slots,"default",{key:1,dataP:r.dataP,class:De(u.cx("root")),active:r.active,a11yAttrs:r.a11yAttrs,onClick:r.onClick}):Y((i(),w(Ce(u.as),A({key:0,class:u.cx("root"),"data-p":r.dataP,onClick:r.onClick},r.attrs),{default:y(function(){return[L(u.$slots,"default")]}),_:3},16,["class","data-p","onClick"])),[[n]])}Xe.render=lu;const iu=R({__name:"Tab",setup(u,{expose:e}){e();const a=u,t=C({root:`flex-shrink-0 cursor-pointer select-none relative whitespace-nowrap py-4 px-[1.125rem]
        border-b border-surface-200 dark:border-surface-700 font-semibold
        text-surface-500 dark:text-surface-400
        transition-colors duration-200 -mb-px
        not-p-active:enabled:hover:text-surface-700 dark:not-p-active:enabled:hover:text-surface-0
        p-active:border-primary p-active:text-primary
        disabled:pointer-events-none disabled:opacity-60
        focus-visible:z-10 focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-[-1px] focus-visible:outline-primary`}),d={props:a,theme:t,get Tab(){return Xe},get ptViewMerge(){return ie}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}});function du(u,e,a,t,d,r){return i(),w(t.Tab,{value:t.props.value,unstyled:"",pt:t.theme,ptOptions:{mergeProps:t.ptViewMerge}},{default:y(()=>[L(u.$slots,"default")]),_:3},8,["value","pt","ptOptions"])}const cu=N(iu,[["render",du]]),pu=R({__name:"QuickCreateCategory",props:{visible:{type:Boolean}},emits:["update:visible","created"],setup(u,{expose:e,emit:a}){e();const t=u,d=a,{loading:r,error:n,clearError:s,partCategories:p}=be(),c=H({get:()=>t.visible,set:_=>d("update:visible",_)}),x=C({name:"",description:"",parent:null}),O=C({name:""}),P=C([]),Q=H(()=>x.value.name.trim().length>0&&!r.value),K=async _=>{const F=_.query.toLowerCase(),E=await p.findMany({where:{name:{contains:F,mode:"insensitive"}},take:10});E&&(P.value=E)},M=async()=>{if(s(),O.value={name:""},!x.value.name.trim()){O.value.name="Название обязательно";return}try{const _=x.value.name.toLowerCase().replace(/[^a-zа-я0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),F={name:x.value.name.trim(),slug:_,description:x.value.description?.trim()||void 0,level:x.value.parent?x.value.parent.level+1:0,path:x.value.parent?`${x.value.parent.path}/${_}`:`/${_}`,parentId:x.value.parent?.id||void 0},E=await p.create({data:F});E&&(d("created",E),T(),c.value=!1)}catch(_){console.error("Ошибка создания категории:",_)}},T=()=>{x.value={name:"",description:"",parent:null},O.value={name:""},s()};ze(c,_=>{_||T()});const S={props:t,emit:d,loading:r,error:n,clearError:s,partCategories:p,visible:c,formData:x,errors:O,parentSuggestions:P,canCreate:Q,searchParents:K,createCategory:M,resetForm:T,VDialog:St,VButton:fe,VInputText:me,VTextarea:Ke,VAutoComplete:ge,VMessage:Ne};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}}),bu={class:"space-y-4"},fu={key:0,class:"text-red-500"},mu={class:"flex justify-end gap-3"};function gu(u,e,a,t,d,r){return i(),w(t.VDialog,{visible:t.visible,"onUpdate:visible":e[4]||(e[4]=n=>t.visible=n),modal:"",header:"Создать новую категорию",class:"w-96"},{footer:y(()=>[o("div",mu,[m(t.VButton,{onClick:e[3]||(e[3]=n=>t.visible=!1),severity:"secondary",outlined:"",disabled:t.loading},{default:y(()=>e[8]||(e[8]=[I(" Отмена ")])),_:1,__:[8]},8,["disabled"]),m(t.VButton,{onClick:t.createCategory,loading:t.loading,disabled:!t.canCreate},{default:y(()=>e[9]||(e[9]=[I(" Создать ")])),_:1,__:[9]},8,["loading","disabled"])])]),default:y(()=>[o("div",bu,[o("div",null,[e[5]||(e[5]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название категории * ",-1)),m(t.VInputText,{modelValue:t.formData.name,"onUpdate:modelValue":e[0]||(e[0]=n=>t.formData.name=n),placeholder:"Например: Фильтры масляные",class:De(["w-full",{"p-invalid":t.errors.name}])},null,8,["modelValue","class"]),t.errors.name?(i(),f("small",fu,h(t.errors.name),1)):k("",!0)]),o("div",null,[e[6]||(e[6]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),m(t.VTextarea,{modelValue:t.formData.description,"onUpdate:modelValue":e[1]||(e[1]=n=>t.formData.description=n),placeholder:"Описание категории",rows:"3",class:"w-full"},null,8,["modelValue"])]),o("div",null,[e[7]||(e[7]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Родительская категория ",-1)),m(t.VAutoComplete,{modelValue:t.formData.parent,"onUpdate:modelValue":e[2]||(e[2]=n=>t.formData.parent=n),suggestions:t.parentSuggestions,onComplete:t.searchParents,"option-label":"name",placeholder:"Поиск родительской категории",class:"w-full"},null,8,["modelValue","suggestions"])])]),t.error?(i(),w(t.VMessage,{key:0,severity:"error",class:"mt-4"},{default:y(()=>[I(h(t.error),1)]),_:1})):k("",!0)]),_:1},8,["visible"])}const vu=N(pu,[["render",gu]]);var hu=`
    .p-togglebutton {
        display: inline-flex;
        cursor: pointer;
        user-select: none;
        overflow: hidden;
        position: relative;
        color: dt('togglebutton.color');
        background: dt('togglebutton.background');
        border: 1px solid dt('togglebutton.border.color');
        padding: dt('togglebutton.padding');
        font-size: 1rem;
        font-family: inherit;
        font-feature-settings: inherit;
        transition:
            background dt('togglebutton.transition.duration'),
            color dt('togglebutton.transition.duration'),
            border-color dt('togglebutton.transition.duration'),
            outline-color dt('togglebutton.transition.duration'),
            box-shadow dt('togglebutton.transition.duration');
        border-radius: dt('togglebutton.border.radius');
        outline-color: transparent;
        font-weight: dt('togglebutton.font.weight');
    }

    .p-togglebutton-content {
        display: inline-flex;
        flex: 1 1 auto;
        align-items: center;
        justify-content: center;
        gap: dt('togglebutton.gap');
        padding: dt('togglebutton.content.padding');
        background: transparent;
        border-radius: dt('togglebutton.content.border.radius');
        transition:
            background dt('togglebutton.transition.duration'),
            color dt('togglebutton.transition.duration'),
            border-color dt('togglebutton.transition.duration'),
            outline-color dt('togglebutton.transition.duration'),
            box-shadow dt('togglebutton.transition.duration');
    }

    .p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover {
        background: dt('togglebutton.hover.background');
        color: dt('togglebutton.hover.color');
    }

    .p-togglebutton.p-togglebutton-checked {
        background: dt('togglebutton.checked.background');
        border-color: dt('togglebutton.checked.border.color');
        color: dt('togglebutton.checked.color');
    }

    .p-togglebutton-checked .p-togglebutton-content {
        background: dt('togglebutton.content.checked.background');
        box-shadow: dt('togglebutton.content.checked.shadow');
    }

    .p-togglebutton:focus-visible {
        box-shadow: dt('togglebutton.focus.ring.shadow');
        outline: dt('togglebutton.focus.ring.width') dt('togglebutton.focus.ring.style') dt('togglebutton.focus.ring.color');
        outline-offset: dt('togglebutton.focus.ring.offset');
    }

    .p-togglebutton.p-invalid {
        border-color: dt('togglebutton.invalid.border.color');
    }

    .p-togglebutton:disabled {
        opacity: 1;
        cursor: default;
        background: dt('togglebutton.disabled.background');
        border-color: dt('togglebutton.disabled.border.color');
        color: dt('togglebutton.disabled.color');
    }

    .p-togglebutton-label,
    .p-togglebutton-icon {
        position: relative;
        transition: none;
    }

    .p-togglebutton-icon {
        color: dt('togglebutton.icon.color');
    }

    .p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover .p-togglebutton-icon {
        color: dt('togglebutton.icon.hover.color');
    }

    .p-togglebutton.p-togglebutton-checked .p-togglebutton-icon {
        color: dt('togglebutton.icon.checked.color');
    }

    .p-togglebutton:disabled .p-togglebutton-icon {
        color: dt('togglebutton.icon.disabled.color');
    }

    .p-togglebutton-sm {
        padding: dt('togglebutton.sm.padding');
        font-size: dt('togglebutton.sm.font.size');
    }

    .p-togglebutton-sm .p-togglebutton-content {
        padding: dt('togglebutton.content.sm.padding');
    }

    .p-togglebutton-lg {
        padding: dt('togglebutton.lg.padding');
        font-size: dt('togglebutton.lg.font.size');
    }

    .p-togglebutton-lg .p-togglebutton-content {
        padding: dt('togglebutton.content.lg.padding');
    }

    .p-togglebutton-fluid {
        width: 100%;
    }
`,yu={root:function(e){var a=e.instance,t=e.props;return["p-togglebutton p-component",{"p-togglebutton-checked":a.active,"p-invalid":a.$invalid,"p-togglebutton-fluid":t.fluid,"p-togglebutton-sm p-inputfield-sm":t.size==="small","p-togglebutton-lg p-inputfield-lg":t.size==="large"}]},content:"p-togglebutton-content",icon:"p-togglebutton-icon",label:"p-togglebutton-label"},xu=ee.extend({name:"togglebutton",style:hu,classes:yu}),Eu={name:"BaseToggleButton",extends:He,props:{onIcon:String,offIcon:String,onLabel:{type:String,default:"Yes"},offLabel:{type:String,default:"No"},iconPos:{type:String,default:"left"},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null},size:{type:String,default:null},fluid:{type:Boolean,default:null}},style:xu,provide:function(){return{$pcToggleButton:this,$parentInstance:this}}};function Z(u){"@babel/helpers - typeof";return Z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Z(u)}function ku(u,e,a){return(e=_u(e))in u?Object.defineProperty(u,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):u[e]=a,u}function _u(u){var e=Cu(u,"string");return Z(e)=="symbol"?e:e+""}function Cu(u,e){if(Z(u)!="object"||!u)return u;var a=u[Symbol.toPrimitive];if(a!==void 0){var t=a.call(u,e);if(Z(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(u)}var $e={name:"ToggleButton",extends:Eu,inheritAttrs:!1,emits:["change"],methods:{getPTOptions:function(e){var a=e==="root"?this.ptmi:this.ptm;return a(e,{context:{active:this.active,disabled:this.disabled}})},onChange:function(e){!this.disabled&&!this.readonly&&(this.writeValue(!this.d_value,e),this.$emit("change",e))},onBlur:function(e){var a,t;(a=(t=this.formField).onBlur)===null||a===void 0||a.call(t,e)}},computed:{active:function(){return this.d_value===!0},hasLabel:function(){return Me(this.onLabel)&&Me(this.offLabel)},label:function(){return this.hasLabel?this.d_value?this.onLabel:this.offLabel:" "},dataP:function(){return pe(ku({checked:this.active,invalid:this.$invalid},this.size,this.size))}},directives:{ripple:ce}},Bu=["tabindex","disabled","aria-pressed","aria-label","aria-labelledby","data-p-checked","data-p-disabled","data-p"],wu=["data-p"];function Du(u,e,a,t,d,r){var n=de("ripple");return Y((i(),f("button",A({type:"button",class:u.cx("root"),tabindex:u.tabindex,disabled:u.disabled,"aria-pressed":u.d_value,onClick:e[0]||(e[0]=function(){return r.onChange&&r.onChange.apply(r,arguments)}),onBlur:e[1]||(e[1]=function(){return r.onBlur&&r.onBlur.apply(r,arguments)})},r.getPTOptions("root"),{"aria-label":u.ariaLabel,"aria-labelledby":u.ariaLabelledby,"data-p-checked":r.active,"data-p-disabled":u.disabled,"data-p":r.dataP}),[o("span",A({class:u.cx("content")},r.getPTOptions("content"),{"data-p":r.dataP}),[L(u.$slots,"default",{},function(){return[L(u.$slots,"icon",{value:u.d_value,class:De(u.cx("icon"))},function(){return[u.onIcon||u.offIcon?(i(),f("span",A({key:0,class:[u.cx("icon"),u.d_value?u.onIcon:u.offIcon]},r.getPTOptions("icon")),null,16)):k("",!0)]}),o("span",A({class:u.cx("label")},r.getPTOptions("label")),h(r.label),17)]})],16,wu)],16,Bu)),[[n]])}$e.render=Du;var Vu=`
    .p-selectbutton {
        display: inline-flex;
        user-select: none;
        vertical-align: bottom;
        outline-color: transparent;
        border-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton .p-togglebutton {
        border-radius: 0;
        border-width: 1px 1px 1px 0;
    }

    .p-selectbutton .p-togglebutton:focus-visible {
        position: relative;
        z-index: 1;
    }

    .p-selectbutton .p-togglebutton:first-child {
        border-inline-start-width: 1px;
        border-start-start-radius: dt('selectbutton.border.radius');
        border-end-start-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton .p-togglebutton:last-child {
        border-start-end-radius: dt('selectbutton.border.radius');
        border-end-end-radius: dt('selectbutton.border.radius');
    }

    .p-selectbutton.p-invalid {
        outline: 1px solid dt('selectbutton.invalid.border.color');
        outline-offset: 0;
    }

    .p-selectbutton-fluid {
        width: 100%;
    }
    
    .p-selectbutton-fluid .p-togglebutton {
        flex: 1 1 0;
    }
`,Tu={root:function(e){var a=e.props,t=e.instance;return["p-selectbutton p-component",{"p-invalid":t.$invalid,"p-selectbutton-fluid":a.fluid}]}},Au=ee.extend({name:"selectbutton",style:Vu,classes:Tu}),Iu={name:"BaseSelectButton",extends:He,props:{options:Array,optionLabel:null,optionValue:null,optionDisabled:null,multiple:Boolean,allowEmpty:{type:Boolean,default:!0},dataKey:null,ariaLabelledby:{type:String,default:null},size:{type:String,default:null},fluid:{type:Boolean,default:null}},style:Au,provide:function(){return{$pcSelectButton:this,$parentInstance:this}}};function Su(u,e){var a=typeof Symbol<"u"&&u[Symbol.iterator]||u["@@iterator"];if(!a){if(Array.isArray(u)||(a=Je(u))||e){a&&(u=a);var t=0,d=function(){};return{s:d,n:function(){return t>=u.length?{done:!0}:{done:!1,value:u[t++]}},e:function(c){throw c},f:d}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var r,n=!0,s=!1;return{s:function(){a=a.call(u)},n:function(){var c=a.next();return n=c.done,c},e:function(c){s=!0,r=c},f:function(){try{n||a.return==null||a.return()}finally{if(s)throw r}}}}function Fu(u){return Ou(u)||Lu(u)||Je(u)||Pu()}function Pu(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Je(u,e){if(u){if(typeof u=="string")return Be(u,e);var a={}.toString.call(u).slice(8,-1);return a==="Object"&&u.constructor&&(a=u.constructor.name),a==="Map"||a==="Set"?Array.from(u):a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?Be(u,e):void 0}}function Lu(u){if(typeof Symbol<"u"&&u[Symbol.iterator]!=null||u["@@iterator"]!=null)return Array.from(u)}function Ou(u){if(Array.isArray(u))return Be(u)}function Be(u,e){(e==null||e>u.length)&&(e=u.length);for(var a=0,t=Array(e);a<e;a++)t[a]=u[a];return t}var Ye={name:"SelectButton",extends:Iu,inheritAttrs:!1,emits:["change"],methods:{getOptionLabel:function(e){return this.optionLabel?se(e,this.optionLabel):e},getOptionValue:function(e){return this.optionValue?se(e,this.optionValue):e},getOptionRenderKey:function(e){return this.dataKey?se(e,this.dataKey):this.getOptionLabel(e)},isOptionDisabled:function(e){return this.optionDisabled?se(e,this.optionDisabled):!1},isOptionReadonly:function(e){if(this.allowEmpty)return!1;var a=this.isSelected(e);return this.multiple?a&&this.d_value.length===1:a},onOptionSelect:function(e,a,t){var d=this;if(!(this.disabled||this.isOptionDisabled(a)||this.isOptionReadonly(a))){var r=this.isSelected(a),n=this.getOptionValue(a),s;if(this.multiple)if(r){if(s=this.d_value.filter(function(p){return!le(p,n,d.equalityKey)}),!this.allowEmpty&&s.length===0)return}else s=this.d_value?[].concat(Fu(this.d_value),[n]):[n];else{if(r&&!this.allowEmpty)return;s=r?null:n}this.writeValue(s,e),this.$emit("change",{event:e,value:s})}},isSelected:function(e){var a=!1,t=this.getOptionValue(e);if(this.multiple){if(this.d_value){var d=Su(this.d_value),r;try{for(d.s();!(r=d.n()).done;){var n=r.value;if(le(n,t,this.equalityKey)){a=!0;break}}}catch(s){d.e(s)}finally{d.f()}}}else a=le(this.d_value,t,this.equalityKey);return a}},computed:{equalityKey:function(){return this.optionValue?null:this.dataKey},dataP:function(){return pe({invalid:this.$invalid})}},directives:{ripple:ce},components:{ToggleButton:$e}},qu=["aria-labelledby","data-p"];function Mu(u,e,a,t,d,r){var n=wt("ToggleButton");return i(),f("div",A({class:u.cx("root"),role:"group","aria-labelledby":u.ariaLabelledby},u.ptmi("root"),{"data-p":r.dataP}),[(i(!0),f(W,null,U(u.options,function(s,p){return i(),w(n,{key:r.getOptionRenderKey(s),modelValue:r.isSelected(s),onLabel:r.getOptionLabel(s),offLabel:r.getOptionLabel(s),disabled:u.disabled||r.isOptionDisabled(s),unstyled:u.unstyled,size:u.size,readonly:r.isOptionReadonly(s),onChange:function(x){return r.onOptionSelect(x,s,p)},pt:u.ptm("pcToggleButton")},Ue({_:2},[u.$slots.option?{name:"default",fn:y(function(){return[L(u.$slots,"option",{option:s,index:p},function(){return[o("span",A({ref_for:!0},u.ptm("pcToggleButton").label),h(r.getOptionLabel(s)),17)]})]}),key:"0"}:void 0]),1032,["modelValue","onLabel","offLabel","disabled","unstyled","size","readonly","onChange","pt"])}),128))],16,qu)}Ye.render=Mu;const zu=R({__name:"SelectButton",setup(u,{expose:e}){e();const t={theme:C({root:`inline-flex select-none rounded-md
        p-invalid:outline p-invalid:outline-offset-0 p-invalid:outline-red-400 dark:p-invalid:outline-red-300`,pcToggleButton:{root:`inline-flex items-center justify-center overflow-hidden relative cursor-pointer select-none grow
            border border-surface-100 dark:border-surface-950
            rounded-none first:rounded-s-md last:rounded-e-md
            bg-surface-100 dark:bg-surface-950
            text-surface-500 dark:text-surface-400
            p-checked:text-surface-700 dark:p-checked:text-surface-0
            text-base font-medium
            focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary focus-visible:relative focus-visible:z-10
            disabled:cursor-default
            disabled:bg-surface-200 disabled:border-surface-200 disabled:text-surface-500
            disabled:dark:bg-surface-700 disabled:dark:border-surface-700 disabled:dark:text-surface-400
            p-invalid:border-red-400 dark:p-invalid:border-red-300
            transition-colors duration-200
            p-1 p-small:text-sm p-large:text-lg
        `,content:`relative flex-auto inline-flex items-center justify-center gap-2 py-1 px-3
            rounded-md transition-colors duration-200
            p-checked:bg-surface-0 dark:p-checked:bg-surface-800 p-checked:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.02),0px_1px_2px_0px_rgba(0,0,0,0.04)]`,icon:"",label:""}}),get SelectButton(){return Ye},get ptViewMerge(){return ie}};return Object.defineProperty(t,"__isScriptSetup",{enumerable:!1,value:!0}),t}});function Uu(u,e,a,t,d,r){return i(),w(t.SelectButton,{unstyled:"",pt:t.theme,ptOptions:{mergeProps:t.ptViewMerge}},Ue({_:2},[U(u.$slots,(n,s)=>({name:s,fn:y(p=>[L(u.$slots,s,Tt(Dt(p??{})))])}))]),1032,["pt","ptOptions"])}const Ze=N(zu,[["render",Uu]]),Ru=R({__name:"EquipmentSelector",props:{modelValue:{}},emits:["update:modelValue"],setup(u,{expose:e,emit:a}){e();const t=u,d=a,{equipmentModels:r,brands:n}=be(),s=[{label:"Создать новую модель",value:!1},{label:"Найти существующую",value:!0}],p=C([]),c=C([]),x=H({get:()=>t.modelValue,set:T=>d("update:modelValue",T)}),M={props:t,emit:d,equipmentModels:r,brands:n,equipmentTypeOptions:s,equipmentSuggestions:p,brandSuggestions:c,modelValue:x,searchEquipmentModels:async(T,S)=>{const _=T.query.toLowerCase(),F=await r.findMany({where:{name:{contains:_,mode:"insensitive"}},include:{brand:!0},take:10});F&&Array.isArray(F)&&(p.value=F)},searchBrands:async T=>{const S=T.query.toLowerCase(),_=await n.findMany({where:{name:{contains:S,mode:"insensitive"}},take:10});_&&Array.isArray(_)&&(c.value=_)},addEquipment:()=>{const T={name:"",selectedBrand:null,isExisting:!1,existingEquipmentModel:null,notes:""};x.value=[...x.value,T]},removeEquipment:T=>{const S=[...x.value];S.splice(T,1),x.value=S},VButton:fe,VInputText:me,VAutoComplete:ge,VSelectButton:Ze,get PlusIcon(){return We}};return Object.defineProperty(M,"__isScriptSetup",{enumerable:!1,value:!0}),M}}),Nu={class:"equipment-selector"},Ku={class:"flex items-center justify-between mb-4"},ju={key:0,class:"text-center py-8 text-surface-500"},Hu={class:"flex items-center justify-between mb-4"},Wu={class:"font-medium text-surface-900 dark:text-surface-0"},Qu={class:"mb-4"},Gu={key:0,class:"mb-4"},Xu={class:"flex items-center gap-2"},$u={class:"flex-1"},Ju={class:"font-medium"},Yu={key:0,class:"text-sm text-surface-600"},Zu={key:1,class:"space-y-4"},ea={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ta={class:"mt-4"};function ua(u,e,a,t,d,r){return i(),f("div",Nu,[o("div",Ku,[e[1]||(e[1]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Применимость к технике ",-1)),m(t.VButton,{onClick:t.addEquipment,outlined:"",size:"small"},{default:y(()=>[e[0]||(e[0]=I(" Добавить технику ")),m(t.PlusIcon,{class:"h-4 w-4"})]),_:1,__:[0]})]),t.modelValue.length===0?(i(),f("div",ju," Добавьте модели техники, к которым применима эта запчасть ")):k("",!0),(i(!0),f(W,null,U(t.modelValue,(n,s)=>(i(),f("div",{key:s,class:"border border-surface-200 dark:border-surface-700 rounded-lg p-4 mb-4"},[o("div",Hu,[o("h4",Wu," Техника "+h(s+1),1),m(t.VButton,{onClick:p=>t.removeEquipment(s),severity:"danger",size:"small",text:""},{default:y(()=>e[2]||(e[2]=[I(" Удалить ")])),_:2,__:[2]},1032,["onClick"])]),o("div",Qu,[e[3]||(e[3]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Тип добавления ",-1)),m(t.VSelectButton,{modelValue:n.isExisting,"onUpdate:modelValue":p=>n.isExisting=p,options:t.equipmentTypeOptions,"option-label":"label","option-value":"value",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),n.isExisting?(i(),f("div",Gu,[e[4]||(e[4]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск модели техники * ",-1)),m(t.VAutoComplete,{modelValue:n.existingEquipmentModel,"onUpdate:modelValue":p=>n.existingEquipmentModel=p,suggestions:t.equipmentSuggestions,onComplete:p=>t.searchEquipmentModels(p,s),"option-label":"name",placeholder:"Введите название модели для поиска...",class:"w-full",dropdown:""},{option:y(({option:p})=>[o("div",Xu,[o("div",$u,[o("div",Ju,h(p.name),1),p.brand?(i(),f("div",Yu,h(p.brand.name),1)):k("",!0)])])]),_:2},1032,["modelValue","onUpdate:modelValue","suggestions","onComplete"])])):(i(),f("div",Zu,[o("div",ea,[o("div",null,[e[5]||(e[5]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название модели * ",-1)),m(t.VInputText,{modelValue:n.name,"onUpdate:modelValue":p=>n.name=p,placeholder:"Например: CAT 320D",class:"w-full p-3"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",null,[e[6]||(e[6]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд ",-1)),m(t.VAutoComplete,{modelValue:n.selectedBrand,"onUpdate:modelValue":p=>n.selectedBrand=p,suggestions:t.brandSuggestions,onComplete:t.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:"w-full",dropdown:""},null,8,["modelValue","onUpdate:modelValue","suggestions"])])])])),o("div",ta,[e[7]||(e[7]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),m(t.VInputText,{modelValue:n.notes,"onUpdate:modelValue":p=>n.notes=p,placeholder:"Дополнительная информация о применимости к данной технике",class:"w-full p-3"},null,8,["modelValue","onUpdate:modelValue"])])]))),128))])}const aa=N(Ru,[["render",ua]]),na=R({__name:"CatalogItemWizardEditor",props:{modelValue:{}},emits:["update:modelValue"],setup(u,{expose:e,emit:a}){e();const t=u,d=a,{catalogItems:r,brands:n}=be(),s=C([]),p=C([]),c=C(!1),F={props:t,emit:d,catalogItems:r,brands:n,catalogItemSuggestions:s,brandSuggestions:p,showCreateBrand:c,accuracyOptions:[{label:"Точное совпадение",value:"EXACT_MATCH"},{label:"Совпадение с примечаниями",value:"MATCH_WITH_NOTES"},{label:"Требует доработки",value:"REQUIRES_MODIFICATION"},{label:"Частичное совпадение",value:"PARTIAL_MATCH"}],itemTypeOptions:[{label:"Создать новую позицию",value:!1},{label:"Выбрать существующую",value:!0}],addCatalogItem:()=>{const E={sku:"",brandId:"",selectedBrand:null,description:"",isExisting:!1,existingCatalogItem:null,accuracy:"EXACT_MATCH",notes:""},B=[...t.modelValue,E];d("update:modelValue",B)},removeCatalogItem:E=>{const B=t.modelValue.filter((D,q)=>q!==E);d("update:modelValue",B)},getDisplayLabel:E=>E?typeof E=="object"?`${E.sku} (${E.brand?.name||"Без бренда"})`:E:"",onItemSelect:(E,B)=>{const D=[...t.modelValue];typeof B=="object"&&(D[E].existingCatalogItem=B),d("update:modelValue",D)},searchCatalogItems:async E=>{try{const B=E.query.toLowerCase(),D=await r.findMany({where:{OR:[{sku:{contains:B,mode:"insensitive"}},{brand:{name:{contains:B,mode:"insensitive"}}}]},include:{brand:!0},take:10});D&&(s.value=D.map(q=>({...q,displayLabel:`${q.sku} (${q.brand?.name||"Без бренда"})`})))}catch(B){console.error("Ошибка поиска каталожных позиций:",B)}},searchBrands:async E=>{try{const B=E.query.toLowerCase(),D=await n.findMany({where:{name:{contains:B,mode:"insensitive"}},take:10});D&&(p.value=D)}catch(B){console.error("Ошибка поиска брендов:",B)}},onBrandCreated:E=>{p.value=[E,...p.value]},VCard:Re,VButton:fe,VTag:Pt,VSelectButton:Ze,VAutoComplete:ge,VInputText:me,VTextarea:Ke,VSelect:Lt,QuickCreateBrand:je,Icon:Ot,get PlusIcon(){return We}};return Object.defineProperty(F,"__isScriptSetup",{enumerable:!1,value:!0}),F}}),oa={class:"catalog-item-wizard-editor"},ra={class:"flex items-center justify-between mb-4"},sa={key:0,class:"text-center py-10"},la={key:1,class:"space-y-4"},ia={class:"p-4"},da={class:"flex items-center justify-between mb-4"},ca={class:"flex items-center gap-3"},pa={class:"mb-4"},ba={key:0,class:"mb-4"},fa={class:"flex items-center gap-2"},ma={class:"font-mono font-medium"},ga={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},va={class:"flex gap-2"},ha={class:"md:col-span-2"},ya={class:"grid grid-cols-1 md:grid-cols-2 gap-4"};function xa(u,e,a,t,d,r){const n=de("tooltip");return i(),f("div",oa,[o("div",ra,[e[3]||(e[3]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0"}," Каталожные позиции ",-1)),m(t.VButton,{onClick:t.addCatalogItem,outlined:"",size:"small"},{default:y(()=>[e[2]||(e[2]=I(" Добавить ")),m(t.PlusIcon)]),_:1,__:[2]})]),a.modelValue.length===0?(i(),f("div",sa,[e[5]||(e[5]=o("div",{class:"text-surface-600 dark:text-surface-400 mb-4"}," Вы можете добавить каталожные позиции сейчас или пропустить этот шаг. ",-1)),m(t.VButton,{size:"small",onClick:t.addCatalogItem,outlined:""},{default:y(()=>[e[4]||(e[4]=I(" Добавить ")),m(t.PlusIcon)]),_:1,__:[4]})])):(i(),f("div",la,[(i(!0),f(W,null,U(a.modelValue,(s,p)=>(i(),w(t.VCard,{key:p,class:"border border-surface-200 dark:border-surface-700"},{content:y(()=>[o("div",ia,[o("div",da,[o("div",ca,[m(t.VTag,{value:`Позиция ${p+1}`,severity:"secondary",size:"small"},null,8,["value"]),s.isExisting?(i(),w(t.VTag,{key:0,value:"Существующая",severity:"info",size:"small"})):(i(),w(t.VTag,{key:1,value:"Новая",severity:"success",size:"small"}))]),m(t.VButton,{onClick:c=>t.removeCatalogItem(p),severity:"danger",size:"small",text:""},{default:y(()=>[m(t.Icon,{name:"pi pi-trash",class:"w-5 h-5"})]),_:2},1032,["onClick"])]),o("div",pa,[m(t.VSelectButton,{modelValue:s.isExisting,"onUpdate:modelValue":c=>s.isExisting=c,options:t.itemTypeOptions,optionLabel:"label",optionValue:"value",class:"w-full md:w-auto"},null,8,["modelValue","onUpdate:modelValue"])]),s.isExisting?(i(),f("div",ba,[e[6]||(e[6]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Поиск каталожной позиции * ",-1)),m(t.VAutoComplete,{"model-value":t.getDisplayLabel(s.existingCatalogItem),"onUpdate:modelValue":c=>t.onItemSelect(p,c),suggestions:t.catalogItemSuggestions,onComplete:t.searchCatalogItems,field:"displayLabel",placeholder:"Поиск по артикулу или бренду...",class:"w-full",dropdown:""},{option:y(({option:c})=>[o("div",fa,[o("span",ma,h(c.sku),1),m(t.VTag,{value:c.brand?.name,severity:"secondary",size:"small"},null,8,["value"])])]),_:2},1032,["model-value","onUpdate:modelValue","suggestions"])])):(i(),f("div",ga,[o("div",null,[e[7]||(e[7]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Артикул (SKU) * ",-1)),m(t.VInputText,{modelValue:s.sku,"onUpdate:modelValue":c=>s.sku=c,placeholder:"Например: 12345-ABC",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",null,[e[8]||(e[8]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Бренд * ",-1)),o("div",va,[m(t.VAutoComplete,{modelValue:s.selectedBrand,"onUpdate:modelValue":c=>s.selectedBrand=c,suggestions:t.brandSuggestions,onComplete:t.searchBrands,"option-label":"name",placeholder:"Поиск бренда...",class:"flex-1",dropdown:""},null,8,["modelValue","onUpdate:modelValue","suggestions"]),Y((i(),w(t.VButton,{onClick:e[0]||(e[0]=c=>t.showCreateBrand=!0),severity:"secondary",outlined:"",size:"small"},{default:y(()=>[m(t.Icon,{name:"pi pi-plus",class:"w-5 h-5"})]),_:1})),[[n,"Создать новый бренд"]])])]),o("div",ha,[e[9]||(e[9]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Описание ",-1)),m(t.VInputText,{modelValue:s.description,"onUpdate:modelValue":c=>s.description=c,placeholder:"Описание каталожной позиции...",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])])])),o("div",ya,[o("div",null,[e[10]||(e[10]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Точность применимости * ",-1)),m(t.VSelect,{modelValue:s.accuracy,"onUpdate:modelValue":c=>s.accuracy=c,options:t.accuracyOptions,"option-label":"label","option-value":"value",placeholder:"Выберите точность",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])]),o("div",null,[e[11]||(e[11]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Примечания ",-1)),m(t.VTextarea,{modelValue:s.notes,"onUpdate:modelValue":c=>s.notes=c,"auto-resize":!0,rows:"2",placeholder:"Дополнительные примечания...",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])])])])]),_:2},1024))),128))])),m(t.QuickCreateBrand,{visible:t.showCreateBrand,"onUpdate:visible":e[1]||(e[1]=s=>t.showCreateBrand=s),onCreated:t.onBrandCreated},null,8,["visible"])])}const Ea=N(na,[["render",xa]]),ka=R({__name:"PartWizard",props:{part:{default:null},mode:{default:"create"}},emits:["created","updated"],setup(u,{expose:e,emit:a}){e();const t=u,d=a,r=[{title:"Основная информация",key:"basic"},{title:"Атрибуты",key:"attributes"},{title:"Каталожные позиции",key:"catalog"},{title:"Применимость к технике",key:"equipment"},{title:"Подтверждение",key:"confirm"}],n=r.length,s=C(1),p=H({get:()=>r[s.value-1]?.key,set:b=>{const g=r.findIndex(v=>v.key===b);g>=0&&(s.value=g+1)}}),c=C({name:"",attributes:[],catalogItems:[],equipmentApplicabilities:[]}),{loading:x,error:O,clearError:P,partCategories:Q,parts:K,catalogItems:M,equipmentModels:T,partAttributes:S,client:_,partApplicability:F,equipmentApplicability:E,media:B}=be(),D=C(null),q=C([]),et=C(!1),tt=C(!1),ve=C(!1),te=C(null),ut=H(()=>t.part?.image?.url||null),j=C(null),X=C(!1),at=b=>{const g=b.target;g.files&&g.files[0]&&(j.value=g.files[0])},nt=async()=>{if(!(!t.part?.id||!j.value))try{X.value=!0;const b=await qt(j.value);await B.uploadPartImage({partId:t.part.id,fileName:j.value.name,fileData:b,mimeType:j.value.type||"image/png"}),await J(),j.value=null}finally{X.value=!1}},ot=async()=>{if(t.part?.id)try{X.value=!0,await B.deletePartImage({partId:t.part.id}),await J()}finally{X.value=!1}},rt=b=>{D.value=b,q.value=[b,...q.value]},st=async b=>{const g=b.query.toLowerCase(),v=await Q.findMany({where:{name:{contains:g,mode:"insensitive"}},take:10});v&&Array.isArray(v)&&(q.value=v)},$=H(()=>{switch(s.value){case 1:return c.value.name.trim()&&D.value;case 2:return c.value.attributes.every(b=>{if(!!!(b.template?.isRequired||b.isRequired))return!0;const v=b.template?.dataType||b.templateDataType,G=b.value;return v==="BOOLEAN"?G!=null:String(G??"").trim().length>0});case 3:return c.value.catalogItems.every(b=>b.isExisting?b.existingCatalogItem&&b.accuracy:b.sku.trim()&&b.selectedBrand&&b.accuracy);case 4:return c.value.equipmentApplicabilities.every(b=>b.isExisting?b.existingEquipmentModel:b.name.trim());case 5:return!0;default:return!1}}),lt=H(()=>$.value&&s.value===n),it=()=>{s.value<n&&$.value&&(s.value++,P())},dt=()=>{s.value>1&&(s.value--,P())},ct=b=>{const g=r.findIndex(v=>v.key===b)+1;g!==s.value&&(g>s.value&&!$.value||(s.value=g,P()))},pt=async()=>{if($.value)try{ve.value=!0,te.value=null,P();const b={name:c.value.name,partCategoryId:typeof D.value=="object"?D.value.id:D.value,level:t.part?.level||0,path:t.part?.path||"/"};let g=null;if(t.mode==="edit"&&t.part?g=await K.update({where:{id:t.part.id},data:b}):g=await K.create({data:b}),!g||!g.id)throw new Error(`Не удалось ${t.mode==="edit"?"обновить":"создать"} запчасть`);const v=g.id,G=new Map;for(const l of c.value.attributes)l.templateId&&G.set(l.templateId,l);const he=[],ye=[];G.forEach(l=>{const V=l.value;V==null||String(V).trim()===""||(l.id?ye.push({id:l.id,value:String(V).trim()}):he.push({templateId:l.templateId,value:String(V).trim()}))});const ue=[];he.length>0&&ue.push(S.bulkCreate({partId:v,attributes:he})),ye.length>0&&ue.push(Promise.all(ye.map(l=>S.update({id:l.id,value:l.value})))),ue.length>0&&await Promise.all(ue);const ae=[],xe=[];for(const l of c.value.catalogItems)l.isExisting&&l.existingCatalogItem&&ae.push({id:l.existingCatalogItem.id,accuracy:l.accuracy,notes:l.notes||void 0});const gt=c.value.catalogItems.filter(l=>!l.isExisting).map(async l=>{let V=null;if(typeof l.selectedBrand=="object"&&l.selectedBrand?.id?V=l.selectedBrand.id:typeof l.selectedBrand=="number"?V=l.selectedBrand:typeof l.brandId=="number"&&(V=l.brandId),!V)return xe.push(`Не выбран бренд для каталожной позиции ${l.sku}`),null;const z=await M.create({data:{sku:l.sku.toUpperCase().trim(),brandId:V,description:l.description||void 0,isPublic:!0}});return!z||!z.id?(xe.push(`Не удалось создать каталожную позицию ${l.sku}`),null):{id:z.id,accuracy:l.accuracy,notes:l.notes||void 0}}),vt=await Promise.all(gt);for(const l of vt)l&&ae.push(l);const Fe=[],ht=ae.map(l=>F.upsert({where:{partId_catalogItemId:{partId:v,catalogItemId:l.id}},update:{accuracy:l.accuracy,notes:l.notes},create:{partId:v,catalogItemId:l.id,accuracy:l.accuracy,notes:l.notes}}));(await Promise.all(ht)).forEach((l,V)=>{l||Fe.push(`Связь с каталожной позицией #${ae[V].id} не сохранена`)});const ne=[],Pe=[];for(const l of c.value.equipmentApplicabilities)l.isExisting&&l.existingEquipmentModel&&ne.push({id:l.existingEquipmentModel.id,notes:l.notes||void 0});const yt=c.value.equipmentApplicabilities.filter(l=>!l.isExisting).map(async l=>{const V={name:l.name.trim()};l.selectedBrand&&(V.brandId=typeof l.selectedBrand=="object"?l.selectedBrand.id:l.selectedBrand);const z=await T.create({data:V});return!z||!z.id?(Pe.push(`Не удалось создать модель техники ${l.name}`),null):{id:z.id,notes:l.notes||void 0}}),xt=await Promise.all(yt);for(const l of xt)l&&ne.push(l);const Le=[],Et=ne.map(l=>E.upsert({where:{partId_equipmentModelId:{partId:v,equipmentModelId:l.id}},update:{notes:l.notes},create:{partId:v,equipmentModelId:l.id,notes:l.notes}}));(await Promise.all(Et)).forEach((l,V)=>{l||Le.push(`Связь с техникой #${ne[V].id} не сохранена`)});const Oe=[...xe,...Fe,...Pe,...Le].filter(Boolean);Oe.length>0&&(te.value=Oe.join("; ")),t.mode==="edit"?d("updated",g):d("created",g),t.mode==="create"&&Ve()}catch(b){console.error(`Ошибка ${t.mode==="edit"?"обновления":"создания"} запчасти:`,b),te.value=b?.message||"Произошла ошибка при сохранении запчасти"}finally{ve.value=!1}},Ve=()=>{c.value={name:"",attributes:[],catalogItems:[],equipmentApplicabilities:[]},D.value=null,s.value=1},bt=b=>b?{STRING:"Строка",NUMBER:"Число",BOOLEAN:"Логическое",DATE:"Дата",JSON:"JSON"}[b]||b:"",ft=b=>b?{MM:"мм",INCH:"дюймы",FT:"футы",G:"г",KG:"кг",T:"т",LB:"фунты",ML:"мл",L:"л",GAL:"галлоны",PCS:"шт",SET:"комплект",PAIR:"пара",BAR:"бар",PSI:"PSI",KW:"кВт",HP:"л.с.",NM:"Н⋅м",RPM:"об/мин",C:"°C",F:"°F",PERCENT:"%"}[b]||b:"",mt=b=>({EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"})[b]||b,Te=async b=>{try{const g=await S.findByPartId({partId:b});g&&Array.isArray(g)&&(c.value.attributes=g.map(v=>({id:v.id,templateId:v.templateId,value:v.value,template:v.template,templateTitle:v.template?.title,templateDataType:v.template?.dataType,templateUnit:v.template?.unit,templateGroup:v.template?.group?.name,templateDescription:v.template?.description})))}catch(g){console.warn("Не удалось загрузить атрибуты запчасти:",g)}},Ae=async b=>{try{const g=await _.crud.partApplicability.findMany.query({where:{partId:b},include:{catalogItem:{include:{brand:!0}}}});g&&(c.value.catalogItems=g.map(v=>({isExisting:!0,existingCatalogItem:v.catalogItem,accuracy:v.accuracy,notes:v.notes||"",sku:"",brandId:"",selectedBrand:null,description:""})))}catch(g){console.warn("Не удалось загрузить каталожные позиции:",g)}},Ie=async b=>{try{const g=await _.crud.equipmentApplicability.findMany.query({where:{partId:b},include:{equipmentModel:{include:{brand:!0}}}});g&&(c.value.equipmentApplicabilities=g.map(v=>({isExisting:!0,existingEquipmentModel:v.equipmentModel,notes:v.notes||"",name:"",selectedBrand:null})))}catch(g){console.warn("Не удалось загрузить применимость к технике:",g)}},J=async()=>{t.part&&t.mode==="edit"&&(c.value.name=t.part.name||"",t.part.partCategory&&(D.value=t.part.partCategory),t.part.id&&await Promise.all([Te(t.part.id),Ae(t.part.id),Ie(t.part.id)]))};ze(()=>t.part,async()=>{t.mode==="edit"&&await J()},{immediate:!0}),Vt(async()=>{t.mode==="edit"&&t.part&&await J()});const Se={props:t,emit:d,steps:r,totalSteps:n,currentStep:s,activeStepKey:p,formData:c,loading:x,error:O,clearError:P,partCategories:Q,parts:K,catalogItems:M,equipmentModels:T,partAttributes:S,client:_,partApplicability:F,equipmentApplicability:E,media:B,selectedCategory:D,categorySuggestions:q,showCreateCategory:et,showCreateBrand:tt,saving:ve,saveError:te,partImageUrl:ut,selectedFile:j,uploading:X,onSelectPartImage:at,uploadPartImage:nt,removePartImage:ot,onCategoryCreated:rt,searchCategories:st,canProceed:$,canFinish:lt,nextStep:it,previousStep:dt,onTabChange:ct,savePart:pt,resetForm:Ve,getDataTypeLabel:bt,getUnitLabel:ft,getAccuracyLabel:mt,loadExistingAttributes:Te,loadExistingCatalogItems:Ae,loadExistingEquipmentApplicabilities:Ie,loadPartData:J,VTabs:Wt,VTabList:nu,VTab:cu,VCard:Re,VButton:fe,VInputText:me,VAutoComplete:ge,VMessage:Ne,QuickCreateCategory:vu,QuickCreateBrand:je,AttributeManager:Ft,EquipmentSelector:aa,CatalogItemWizardEditor:Ea,get resolveMediaUrl(){return Mt}};return Object.defineProperty(Se,"__isScriptSetup",{enumerable:!1,value:!0}),Se}}),_a={class:"part-wizard"},Ca={class:"flex items-center justify-between p-6 border-b border-surface-200 dark:border-surface-700"},Ba={class:"flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400"},wa={class:"p-6"},Da={class:"mb-4"},Va={class:"text-sm"},Ta={class:"min-h-96"},Aa={key:0,class:"space-y-6"},Ia={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Sa={class:"flex gap-2"},Fa={key:0,class:"mt-4"},Pa={class:"flex items-center gap-4"},La={class:"w-32 h-32 border rounded bg-surface-50 dark:bg-surface-900 flex items-center justify-center overflow-hidden"},Oa=["src"],qa={key:1,class:"text-surface-500 text-sm"},Ma={class:"flex flex-col gap-2"},za={class:"flex gap-2"},Ua={key:0,class:"text-surface-500 text-xs"},Ra={key:1,class:"space-y-6"},Na={key:2,class:"space-y-6"},Ka={key:3,class:"space-y-6"},ja={key:4,class:"space-y-6"},Ha={class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},Wa={class:"space-y-2"},Qa={class:"flex"},Ga={class:"text-surface-900 dark:text-surface-0"},Xa={class:"flex"},$a={class:"text-surface-900 dark:text-surface-0"},Ja={key:0,class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},Ya={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},Za={class:"space-y-3"},en={class:"flex-1"},tn={class:"font-medium text-surface-900 dark:text-surface-0"},un={key:0,class:"text-red-500 ml-1"},an={class:"text-sm text-surface-600 dark:text-surface-400"},nn={key:0,class:"ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs"},on={class:"text-xs text-surface-500 dark:text-surface-400"},rn={class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},sn={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},ln={class:"space-y-3"},dn={class:"flex-1"},cn={class:"flex items-center gap-2"},pn={class:"font-medium"},bn={class:"text-surface-600 dark:text-surface-400"},fn={key:0,class:"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"},mn={key:1,class:"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"},gn={class:"text-sm text-surface-600 dark:text-surface-400 mt-1"},vn={key:0,class:"ml-2"},hn={key:1,class:"bg-surface-50 dark:bg-surface-900 rounded-lg p-6"},yn={class:"font-medium text-surface-900 dark:text-surface-0 mb-4"},xn={class:"space-y-3"},En={class:"flex-1"},kn={class:"flex items-center gap-2"},_n={class:"font-medium"},Cn={key:0,class:"text-surface-600 dark:text-surface-400"},Bn={key:1,class:"px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded text-xs"},wn={key:2,class:"px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded text-xs"},Dn={key:0,class:"text-sm text-surface-600 dark:text-surface-400 mt-1"},Vn={class:"flex items-center justify-between mt-8 pt-6 border-t border-surface-200 dark:border-surface-700"},Tn={key:1},An={class:"flex gap-3"};function In(u,e,a,t,d,r){return i(),f("div",_a,[m(t.VCard,null,{header:y(()=>[o("div",Ca,[e[8]||(e[8]=o("h2",{class:"text-xl font-semibold text-surface-900 dark:text-surface-0"}," Мастер создания запчасти ",-1)),o("div",Ba," Шаг "+h(t.currentStep)+" из "+h(t.totalSteps),1)])]),content:y(()=>[o("div",wa,[o("div",Da,[m(t.VTabs,{value:t.activeStepKey,"onUpdate:value":t.onTabChange},{default:y(()=>[m(t.VTabList,null,{default:y(()=>[(i(),f(W,null,U(t.steps,(n,s)=>m(t.VTab,{key:n.key,value:n.key},{default:y(()=>[o("span",Va,h(s+1)+". "+h(n.title),1)]),_:2},1032,["value"])),64))]),_:1})]),_:1},8,["value"])]),o("div",Ta,[t.currentStep===1?(i(),f("div",Aa,[e[15]||(e[15]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"}," Основная информация о запчасти ",-1)),o("div",Ia,[o("div",null,[e[9]||(e[9]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Название запчасти * ",-1)),m(t.VInputText,{modelValue:t.formData.name,"onUpdate:modelValue":e[0]||(e[0]=n=>t.formData.name=n),placeholder:"Например: Сальник коленвала передний",class:"w-full p-3"},null,8,["modelValue"])]),o("div",null,[e[11]||(e[11]=o("label",{class:"block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"}," Категория * ",-1)),o("div",Sa,[m(t.VAutoComplete,{modelValue:t.selectedCategory,"onUpdate:modelValue":e[1]||(e[1]=n=>t.selectedCategory=n),suggestions:t.categorySuggestions,onComplete:t.searchCategories,"option-label":"name",placeholder:"Поиск категории...",class:"flex-1",dropdown:""},null,8,["modelValue","suggestions"]),m(t.VButton,{onClick:e[2]||(e[2]=n=>t.showCreateCategory=!0),severity:"secondary",outlined:"",size:"small",label:"Создать новую категорию"},{default:y(()=>e[10]||(e[10]=[I(" + ")])),_:1,__:[10]})])])]),a.mode==="edit"&&t.props.part?(i(),f("div",Fa,[e[14]||(e[14]=o("h4",{class:"text-surface-900 dark:text-surface-0 mb-2 font-medium"},"Изображение запчасти",-1)),o("div",Pa,[o("div",La,[t.partImageUrl?(i(),f("img",{key:0,src:t.resolveMediaUrl(t.partImageUrl),alt:"Изображение запчасти",class:"object-cover w-full h-full"},null,8,Oa)):(i(),f("span",qa,"Нет изображения"))]),o("div",Ma,[o("input",{type:"file",accept:"image/*",onChange:t.onSelectPartImage},null,32),o("div",za,[m(t.VButton,{size:"small",disabled:!t.selectedFile||t.uploading,onClick:t.uploadPartImage},{default:y(()=>e[12]||(e[12]=[I("Загрузить")])),_:1,__:[12]},8,["disabled"]),m(t.VButton,{size:"small",severity:"danger",outlined:"",disabled:!t.partImageUrl||t.uploading,onClick:t.removePartImage},{default:y(()=>e[13]||(e[13]=[I("Удалить")])),_:1,__:[13]},8,["disabled"])]),t.uploading?(i(),f("div",Ua,"Загрузка...")):k("",!0)])])])):k("",!0)])):k("",!0),t.currentStep===2?(i(),f("div",Ra,[m(t.AttributeManager,{modelValue:t.formData.attributes,"onUpdate:modelValue":e[3]||(e[3]=n=>t.formData.attributes=n)},null,8,["modelValue"])])):k("",!0),t.currentStep===3?(i(),f("div",Na,[m(t.CatalogItemWizardEditor,{modelValue:t.formData.catalogItems,"onUpdate:modelValue":e[4]||(e[4]=n=>t.formData.catalogItems=n)},null,8,["modelValue"])])):k("",!0),t.currentStep===4?(i(),f("div",Ka,[m(t.EquipmentSelector,{modelValue:t.formData.equipmentApplicabilities,"onUpdate:modelValue":e[5]||(e[5]=n=>t.formData.equipmentApplicabilities=n)},null,8,["modelValue"])])):k("",!0),t.currentStep===5?(i(),f("div",ja,[e[19]||(e[19]=o("h3",{class:"text-lg font-medium text-surface-900 dark:text-surface-0 mb-4"}," Подтверждение создания ",-1)),o("div",Ha,[e[18]||(e[18]=o("h4",{class:"font-medium text-surface-900 dark:text-surface-0 mb-4"}," Основная информация: ",-1)),o("dl",Wa,[o("div",Qa,[e[16]||(e[16]=o("dt",{class:"w-32 text-surface-600 dark:text-surface-400"}," Название: ",-1)),o("dd",Ga,h(t.formData.name),1)]),o("div",Xa,[e[17]||(e[17]=o("dt",{class:"w-32 text-surface-600 dark:text-surface-400"}," Категория: ",-1)),o("dd",$a,h(t.selectedCategory?.name),1)])])]),t.formData.attributes.length>0?(i(),f("div",Ja,[o("h4",Ya," Атрибуты ("+h(t.formData.attributes.length)+"): ",1),o("div",Za,[(i(!0),f(W,null,U(t.formData.attributes,(n,s)=>(i(),f("div",{key:s,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-800 rounded border"},[o("div",en,[o("div",tn,[I(h(n.template?.title||n.templateTitle)+" ",1),n.template?.isRequired?(i(),f("span",un,"*")):k("",!0)]),o("div",an,[I(h(n.value)+h(n.template?.unit||n.templateUnit?`
                        ${t.getUnitLabel(n.template?.unit||n.templateUnit)}`:"")+" ",1),n.template?.group?.name||n.templateGroup?(i(),f("span",nn,h(n.template?.group?.name||n.templateGroup),1)):k("",!0)])]),o("div",on,h(t.getDataTypeLabel(n.template?.dataType||n.templateDataType)),1)]))),128))])])):k("",!0),o("div",rn,[o("h4",sn," Каталожные позиции ("+h(t.formData.catalogItems.length)+"): ",1),o("div",ln,[(i(!0),f(W,null,U(t.formData.catalogItems,(n,s)=>(i(),f("div",{key:s,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"},[o("div",dn,[o("div",cn,[o("span",pn,h(n.isExisting?n.existingCatalogItem?.sku:n.sku),1),o("span",bn," ("+h(n.isExisting?n.existingCatalogItem?.brand?.name:n.selectedBrand?.name)+") ",1),n.isExisting?(i(),f("span",fn," Существующая ")):(i(),f("span",mn," Новая "))]),o("div",gn,[I(" Точность: "+h(t.getAccuracyLabel(n.accuracy))+" ",1),n.notes?(i(),f("span",vn,"• "+h(n.notes),1)):k("",!0)])])]))),128))])]),t.formData.equipmentApplicabilities.length>0?(i(),f("div",hn,[o("h4",yn," Применимость к технике ("+h(t.formData.equipmentApplicabilities.length)+"): ",1),o("div",xn,[(i(!0),f(W,null,U(t.formData.equipmentApplicabilities,(n,s)=>(i(),f("div",{key:s,class:"flex items-center justify-between p-3 bg-surface-0 dark:bg-surface-950 rounded border"},[o("div",En,[o("div",kn,[o("span",_n,h(n.isExisting?n.existingEquipmentModel?.name:n.name),1),n.selectedBrand||n.existingEquipmentModel?.brand?(i(),f("span",Cn," ("+h(n.selectedBrand?.name||n.existingEquipmentModel?.brand?.name)+") ",1)):k("",!0),n.isExisting?(i(),f("span",Bn," Существующая ")):(i(),f("span",wn," Новая "))]),n.notes?(i(),f("div",Dn,h(n.notes),1)):k("",!0)])]))),128))])])):k("",!0)])):k("",!0)]),o("div",Vn,[t.currentStep>1?(i(),w(t.VButton,{key:0,onClick:t.previousStep,severity:"secondary",outlined:""},{default:y(()=>e[20]||(e[20]=[I(" Назад ")])),_:1,__:[20]})):(i(),f("div",Tn)),o("div",An,[t.currentStep<t.totalSteps?(i(),w(t.VButton,{key:0,onClick:t.nextStep,disabled:!t.canProceed,label:"Далее",outlined:""},null,8,["disabled"])):(i(),w(t.VButton,{key:1,label:a.mode==="edit"?"Сохранить изменения":"Создать запчасть",onClick:t.savePart,loading:t.loading||t.saving,disabled:!t.canFinish||t.saving},null,8,["label","loading","disabled"]))])])])]),_:1}),t.error?(i(),w(t.VMessage,{key:0,severity:"error",class:"mt-4"},{default:y(()=>[I(h(t.error),1)]),_:1})):k("",!0),t.saveError?(i(),w(t.VMessage,{key:1,severity:"error",class:"mt-4"},{default:y(()=>[I(h(t.saveError),1)]),_:1})):k("",!0),m(t.QuickCreateCategory,{visible:t.showCreateCategory,"onUpdate:visible":e[6]||(e[6]=n=>t.showCreateCategory=n),onCreated:t.onCategoryCreated},null,8,["visible"]),m(t.QuickCreateBrand,{visible:t.showCreateBrand,"onUpdate:visible":e[7]||(e[7]=n=>t.showCreateBrand=n)},null,8,["visible"])])}const So=N(ka,[["render",In]]);export{So as default};

import{t as i}from"./trpc.BpyaUO08.js";import{I as b4}from"./InputText.DOJMNEP-.js";import{S as B4}from"./Select.CQBzSu6y.js";import{M as y4}from"./MultiSelect.B9h41NVw.js";import g4 from"./Button.DrThv2lH.js";import{M as D4}from"./Menu.D9nHHLIB.js";import{D as F4,s as w4}from"./index.BWD5ZO4k.js";import{D as p4}from"./Dialog.Ct7C9BO5.js";import{P as V4}from"./Password.CGsZf6xB.js";import{T as k4}from"./Tag.DTFTku6q.js";import{u as A4}from"./useToast.pIbuf2bs.js";import U4 from"./Toast.DmmKUJB6.js";import{_ as S4}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as x4,c as g,a as r,e as a,w as d,j as h4,h as Y,o as D,f as F,b as S}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as n,a as w,t as f}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./index.COq_zjeV.js";import"./utils.BUKUcbtE.js";import"./index.BaVCXmir.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.BH7IgUdp.js";import"./index.CDQpPXyE.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.By2TJOuX.js";import"./index.6ykohhwZ.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./runtime-dom.esm-bundler.DXo4nCak.js";import"./index.D4QD70nN.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./index.CS9OBiV4.js";import"./index.CUNrRq8E.js";import"./SecondaryButton.DkELYl7Q.js";import"./index.PhWaFJhe.js";import"./index.CmzoVUnM.js";const P4=x4({__name:"UsersManager",setup(M,{expose:o}){o();const t=A4(),u=n(1),B=n(20),x=n(0),l=n(!1),c=w({search:"",sortBy:"createdAt",sortDirection:"desc"}),Z=[{label:"Создан",value:"createdAt"},{label:"Email",value:"email"},{label:"Имя",value:"name"}],$=[{label:"По убыванию",value:"desc"},{label:"По возрастанию",value:"asc"}],p=n([]),V=w({}),u4=[{label:"USER",value:"USER"},{label:"SHOP",value:"SHOP"},{label:"ADMIN",value:"ADMIN"}];function e4(e){return e?new Date(e).toLocaleString():""}async function m(){l.value=!0;try{const e=await i.admin.listUsers.query({search:c.search||void 0,sortBy:c.sortBy,sortDirection:c.sortDirection,limit:B.value,offset:(u.value-1)*B.value});p.value=e.users||e.data||[],x.value=e.total||e.count||p.value.length,j()}catch(e){t.error(e?.message||"Не удалось загрузить пользователей")}finally{l.value=!1}}function j(){for(const e of p.value)V[e.id]=e.role}async function l4(e){const s=V[e.id];if(s!==e.role)try{await i.admin.setUserRole.mutate({userId:e.id,role:s}),t.success("Роль обновлена"),await m()}catch(y){t.error(y?.message||"Не удалось обновить роль"),V[e.id]=e.role}}async function T(e){try{e.isBanned?(await i.admin.unbanUser.mutate({userId:e.id}),t.success("Пользователь разблокирован")):(await i.admin.banUser.mutate({userId:e.id}),t.success("Пользователь заблокирован")),await m()}catch(s){t.error(s?.message||"Не удалось изменить статус блокировки")}}async function q(e){try{await i.admin.impersonateUser.mutate({userId:e.id}),t.success("Запущена имперсонация — обновите страницу"),window.location.href="/admin"}catch(s){t.error(s?.message||"Не удалось выполнить имперсонацию")}}async function L(e){try{const s=prompt("Введите новый пароль (мин. 6 символов)");if(!s)return;await i.admin.resetUserPassword.mutate({userId:e.id,newPassword:s}),t.success("Пароль сброшен")}catch(s){t.error(s?.message||"Не удалось сбросить пароль")}}async function N(e){if(confirm("Удалить пользователя безвозвратно?"))try{await i.admin.removeUser.mutate({userId:e.id}),t.success("Пользователь удалён"),await m()}catch(s){t.error(s?.message||"Не удалось удалить пользователя")}}function o4(e){u.value=Math.floor(e.first/e.rows)+1,B.value=e.rows,m()}function a4(){u.value=1,m()}const h=n(!1),P=n(!1),E=w({name:"",email:"",password:"",role:"USER"});function s4(){h.value=!0}async function t4(){if(!E.email||!E.password){t.error("Email и пароль обязательны");return}P.value=!0;try{await i.admin.createUser.mutate({...E,emailVerified:!0}),t.success("Пользователь создан"),h.value=!1,E.name="",E.email="",E.password="",E.role="USER",await m()}catch(e){t.error(e?.message||"Не удалось создать пользователя")}finally{P.value=!1}}h4(()=>[u.value,B.value],m),m(),Q();const _=n(!1),I=n(!1),v=n(null),z=n([]);async function k(e){v.value=e,_.value=!0,I.value=!0;try{const s=await i.admin.listUserSessions.query({userId:e.id});z.value=s?.sessions||s?.data||[]}catch(s){t.error(s?.message||"Не удалось загрузить сессии")}finally{I.value=!1}}const J=n(!1),C=w({json:""}),H=n(null),K=n(null),b=w({}),A=n(null),R=n([]),G=n([]),U=n({}),n4=Y(()=>{const e=A.value;return e?(U.value[e]||[]).map(s=>({label:s,value:s})):[]});async function Q(){try{const e=await i.access.list.query();U.value=e,G.value=Object.keys(U.value).map(s=>({label:s,value:s}))}catch{}}const i4=Y(()=>{if(Object.keys(b).length>0)return!0;try{const e=JSON.parse(C.json||"{}");return e&&typeof e=="object"&&Object.keys(e).length>0}catch{return!1}});async function r4(){try{let e;if(Object.keys(b).length>0)e=b;else{const y=JSON.parse(C.json||"{}");if(!y||Object.keys(y).length===0){t.warn("Добавьте хотя бы один permission");return}e=y}const s=await i.admin.hasPermission.query({permissions:e,userId:C.userId||void 0,role:C.role||void 0});H.value=s,t.success("Проверка выполнена")}catch(e){t.error(e?.message||"Некорректный JSON или ошибка проверки прав")}}function W(e){J.value=!0,K.value=e,C.userId=e.id,C.role=e.role}function d4(){const e=A.value,s=R.value;!e||s.length===0||(b[e]=[...new Set(s)],A.value=null,R.value=[])}function c4(){for(const e of Object.keys(b))delete b[e]}const O=n({});function m4(e){return s=>{s&&(O.value[e]=s)}}function E4(e,s){O.value[s]?.toggle(e)}function f4(e){return[{label:"Имперсонация",icon:"pi pi-user",command:()=>q(e)},{label:e.banned?"Разблокировать":"Заблокировать",icon:e.banned?"pi pi-unlock":"pi pi-lock",command:()=>T(e)},{label:"Сброс пароля",icon:"pi pi-key",command:()=>L(e)},{label:"Сессии",icon:"pi pi-clock",command:()=>k(e)},{label:"Права",icon:"pi pi-shield",command:()=>W(e)},{separator:!0},{label:"Удалить",icon:"pi pi-trash",command:()=>N(e)}]}async function v4(e){try{await i.admin.revokeUserSession.mutate({sessionToken:e.token}),t.success("Сессия отозвана"),v.value&&await k(v.value)}catch(s){t.error(s?.message||"Не удалось отозвать сессию")}}async function C4(){if(v.value)try{await i.admin.revokeAllUserSessions.mutate({userId:v.value.id}),t.success("Все сессии отозваны"),await k(v.value)}catch(e){t.error(e?.message||"Не удалось отозвать все сессии")}}const X={toast:t,page:u,pageSize:B,total:x,isLoading:l,query:c,sortOptions:Z,directionOptions:$,users:p,roleEdits:V,roleOptions:u4,formatDate:e4,fetchUsers:m,roleEditsReset:j,saveRole:l4,toggleBan:T,impersonate:q,resetPassword:L,removeUser:N,onPage:o4,refetch:a4,createVisible:h,actionLoading:P,createForm:E,openCreate:s4,createUser:t4,sessionsVisible:_,sessionsLoading:I,sessionsUser:v,sessions:z,openSessions:k,aclPanelVisible:J,aclForm:C,aclResult:H,aclSelectedUser:K,aclPermissions:b,aclSelectedResource:A,aclSelectedActions:R,resourceOptions:G,actionMap:U,availableActions:n4,loadAccessCatalog:Q,canCheckAcl:i4,checkPermissions:r4,openAclPanel:W,aclAddPermission:d4,aclClearPermissions:c4,rowMenus:O,setRowMenuRef:m4,toggleRowMenu:E4,getRowMenuItems:f4,revokeSession:v4,revokeAllSessions:C4,InputText:b4,Select:B4,MultiSelect:y4,Button:g4,Menu:D4,DataTable:F4,get Column(){return w4},Dialog:p4,Password:V4,Tag:k4,Toast:U4};return Object.defineProperty(X,"__isScriptSetup",{enumerable:!1,value:!0}),X}}),I4={class:"w-full max-w-6xl"},R4={class:"flex items-center justify-between mb-4"},O4={class:"flex gap-2"},M4={class:"relative inline-flex"},j4={key:0,class:"mb-3 text-sm text-surface-600"},T4={class:"flex justify-end gap-2 mt-3"},q4={key:0,class:"text-sm mb-3"},L4={class:"grid grid-cols-2 gap-3 mb-3"},N4={class:"grid grid-cols-2 gap-3 mb-3"},_4={class:"flex gap-2 mb-3"},z4={key:1,class:"font-mono text-xs bg-[--color-muted]/10 p-3 rounded mb-3"},J4={class:"flex justify-end gap-2"},H4={key:2,class:"mt-4 text-sm"},K4={class:"flex flex-col gap-3"},G4={class:"flex justify-end gap-2 mt-2"};function Q4(M,o,t,u,B,x){return D(),g("div",I4,[r("div",R4,[o[25]||(o[25]=r("h2",{class:"text-xl font-semibold"},"Пользователи",-1)),r("div",O4,[a(u.InputText,{modelValue:u.query.search,"onUpdate:modelValue":o[0]||(o[0]=l=>u.query.search=l),placeholder:"Поиск по email/имени",class:"w-64"},null,8,["modelValue"]),a(u.Select,{modelValue:u.query.sortBy,"onUpdate:modelValue":o[1]||(o[1]=l=>u.query.sortBy=l),options:u.sortOptions,optionLabel:"label",optionValue:"value",class:"w-44"},null,8,["modelValue"]),a(u.Select,{modelValue:u.query.sortDirection,"onUpdate:modelValue":o[2]||(o[2]=l=>u.query.sortDirection=l),options:u.directionOptions,optionLabel:"label",optionValue:"value",class:"w-36"},null,8,["modelValue"]),a(u.Button,{label:"Обновить",onClick:o[3]||(o[3]=l=>u.refetch())}),a(u.Button,{label:"Создать",icon:"pi pi-plus",severity:"success",onClick:o[4]||(o[4]=l=>u.openCreate())}),a(u.Button,{label:"Проверка прав",severity:"secondary",onClick:o[5]||(o[5]=l=>M.aclVisible=!0)})])]),a(u.DataTable,{value:u.users,loading:u.isLoading,dataKey:"id",class:"w-full",rows:u.pageSize,paginator:!0,totalRecords:u.total,onPage:u.onPage},{default:d(()=>[a(u.Column,{field:"createdAt",header:"Создан",sortable:!1},{body:d(({data:l})=>[F(f(u.formatDate(l.createdAt)),1)]),_:1}),a(u.Column,{field:"email",header:"Email"}),a(u.Column,{field:"name",header:"Имя"}),a(u.Column,{field:"role",header:"Роль"},{body:d(({data:l})=>[a(u.Select,{modelValue:u.roleEdits[l.id],"onUpdate:modelValue":c=>u.roleEdits[l.id]=c,options:u.roleOptions,class:"w-36",onChange:c=>u.saveRole(l)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(u.Column,{header:"Статус"},{body:d(({data:l})=>[a(u.Tag,{value:l.banned?"Заблокирован":"Активен",severity:l.banned?"danger":"success"},null,8,["value","severity"])]),_:1}),a(u.Column,{header:"Действия"},{body:d(({data:l})=>[r("div",M4,[a(u.Button,{size:"small",label:"Действия",icon:"pi pi-ellipsis-v",onClick:c=>u.toggleRowMenu(c,l.id)},null,8,["onClick"]),a(u.Menu,{model:u.getRowMenuItems(l),ref:u.setRowMenuRef(l.id),popup:!0},null,8,["model"])])]),_:1})]),_:1},8,["value","loading","rows","totalRecords"]),a(u.Dialog,{visible:u.sessionsVisible,"onUpdate:visible":o[8]||(o[8]=l=>u.sessionsVisible=l),header:"Сессии пользователя",modal:!0,style:{width:"720px"}},{default:d(()=>[u.sessionsUser?(D(),g("div",j4,f(u.sessionsUser.email),1)):S("",!0),a(u.DataTable,{value:u.sessions,loading:u.sessionsLoading,dataKey:"token",rows:10,paginator:!0},{default:d(()=>[a(u.Column,{field:"createdAt",header:"Создана"},{body:d(({data:l})=>[F(f(u.formatDate(l.createdAt)),1)]),_:1}),a(u.Column,{field:"expiresAt",header:"Истекает"},{body:d(({data:l})=>[F(f(u.formatDate(l.expiresAt)),1)]),_:1}),a(u.Column,{field:"ipAddress",header:"IP"}),a(u.Column,{field:"userAgent",header:"UA"}),a(u.Column,{header:"Действия"},{body:d(({data:l})=>[a(u.Button,{size:"small",label:"Отозвать",severity:"warning",onClick:c=>u.revokeSession(l)},null,8,["onClick"])]),_:1})]),_:1},8,["value","loading"]),r("div",T4,[a(u.Button,{label:"Отозвать все",severity:"warning",onClick:o[6]||(o[6]=l=>u.revokeAllSessions())}),a(u.Button,{label:"Закрыть",severity:"secondary",onClick:o[7]||(o[7]=l=>u.sessionsVisible=!1)})])]),_:1},8,["visible"]),a(u.Dialog,{visible:u.aclPanelVisible,"onUpdate:visible":o[17]||(o[17]=l=>u.aclPanelVisible=l),header:"Контроль доступа",modal:!0,style:{width:"720px"}},{default:d(()=>[u.aclSelectedUser?(D(),g("div",q4,[o[26]||(o[26]=F(" Пользователь: ")),r("b",null,f(u.aclSelectedUser.email),1),F(" ("+f(u.aclSelectedUser.name||"без имени")+") ",1)])):S("",!0),r("div",L4,[a(u.InputText,{modelValue:u.aclForm.userId,"onUpdate:modelValue":o[9]||(o[9]=l=>u.aclForm.userId=l),placeholder:"User ID (опционально)"},null,8,["modelValue"]),a(u.InputText,{modelValue:u.aclForm.role,"onUpdate:modelValue":o[10]||(o[10]=l=>u.aclForm.role=l),placeholder:"Role (опционально)"},null,8,["modelValue"])]),r("div",N4,[a(u.Select,{modelValue:u.aclSelectedResource,"onUpdate:modelValue":o[11]||(o[11]=l=>u.aclSelectedResource=l),options:u.resourceOptions,optionLabel:"label",optionValue:"value",placeholder:"Ресурс",class:"w-full"},null,8,["modelValue","options"]),a(u.MultiSelect,{modelValue:u.aclSelectedActions,"onUpdate:modelValue":o[12]||(o[12]=l=>u.aclSelectedActions=l),options:u.availableActions,optionLabel:"label",optionValue:"value",placeholder:"Действия",class:"w-full"},null,8,["modelValue","options"])]),r("div",_4,[a(u.Button,{label:"Добавить permission",onClick:o[13]||(o[13]=l=>u.aclAddPermission())}),a(u.Button,{label:"Очистить",severity:"secondary",onClick:o[14]||(o[14]=l=>u.aclClearPermissions())})]),Object.keys(u.aclPermissions).length?(D(),g("div",z4,f(u.aclPermissions),1)):S("",!0),r("div",J4,[a(u.Button,{label:"Проверить",onClick:o[15]||(o[15]=l=>u.checkPermissions()),disabled:!u.canCheckAcl},null,8,["disabled"]),a(u.Button,{label:"Закрыть",severity:"secondary",onClick:o[16]||(o[16]=l=>u.aclPanelVisible=!1)})]),u.aclResult?(D(),g("div",H4,[r("pre",null,f(JSON.stringify(u.aclResult,null,2)),1)])):S("",!0)]),_:1},8,["visible"]),a(u.Dialog,{visible:u.createVisible,"onUpdate:visible":o[24]||(o[24]=l=>u.createVisible=l),header:"Создать пользователя",modal:!0,style:{width:"480px"}},{default:d(()=>[r("div",K4,[a(u.InputText,{modelValue:u.createForm.name,"onUpdate:modelValue":o[18]||(o[18]=l=>u.createForm.name=l),placeholder:"Имя"},null,8,["modelValue"]),a(u.InputText,{modelValue:u.createForm.email,"onUpdate:modelValue":o[19]||(o[19]=l=>u.createForm.email=l),placeholder:"Email"},null,8,["modelValue"]),a(u.Password,{modelValue:u.createForm.password,"onUpdate:modelValue":o[20]||(o[20]=l=>u.createForm.password=l),placeholder:"Пароль",toggleMask:""},null,8,["modelValue"]),a(u.Select,{modelValue:u.createForm.role,"onUpdate:modelValue":o[21]||(o[21]=l=>u.createForm.role=l),options:u.roleOptions,class:"w-36"},null,8,["modelValue"]),r("div",G4,[a(u.Button,{label:"Отмена",severity:"secondary",onClick:o[22]||(o[22]=l=>u.createVisible=!1)}),a(u.Button,{label:"Создать",severity:"success",onClick:o[23]||(o[23]=l=>u.createUser()),loading:u.actionLoading},null,8,["loading"])])])]),_:1},8,["visible"]),a(u.Toast)])}const Iu=S4(P4,[["render",Q4]]);export{Iu as default};

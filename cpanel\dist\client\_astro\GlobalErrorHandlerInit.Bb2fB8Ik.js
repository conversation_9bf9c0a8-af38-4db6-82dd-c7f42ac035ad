import{s as t}from"./useErrorHandler.DVDazL16.js";import{_ as n}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as s,c as a,i as p,o as _}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";import"./reactivity.esm-bundler.BQ12LWmY.js";const i=s({__name:"GlobalErrorHandlerInit",setup(o,{expose:r}){r(),p(()=>{t()});const e={};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),l={style:{display:"none"}};function c(o,r,e,d,m,u){return _(),a("span",l)}const G=n(i,[["render",c]]);export{G as default};

<template>
  <div
    ref="sectionRef"
    :class="className"
  >
    <Motion
      :initial="{ opacity: 0, y: 60 }"
      :animate="isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }"
      :transition="{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }"
    >
      <slot />
    </Motion>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Motion } from 'motion-v'

interface Props {
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  className: ''
})

const sectionRef = ref<HTMLElement>()
const isInView = ref(false)

let observer: IntersectionObserver | null = null

onMounted(() => {
  // Use nextTick to ensure the DOM element is available
  nextTick(() => {
    if (sectionRef.value && sectionRef.value instanceof Element) {
      try {
        observer = new IntersectionObserver(
          ([entry]) => {
            if (entry.isIntersecting) {
              isInView.value = true
            }
          },
          {
            threshold: 0.1,
            rootMargin: '-100px'
          }
        )
        
        observer.observe(sectionRef.value)
      } catch (error) {
        console.warn('IntersectionObserver failed, showing content immediately:', error)
        // Fallback: show content immediately if observer fails
        isInView.value = true
      }
    } else {
      // Fallback: show content immediately if element is not available
      setTimeout(() => {
        isInView.value = true
      }, 100)
    }
  })
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>
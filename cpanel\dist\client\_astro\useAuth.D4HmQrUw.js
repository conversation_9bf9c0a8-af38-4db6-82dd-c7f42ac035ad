import{U as P,u as T,i as C,a as F,c as j,b as l,h as x,g as t}from"./auth-client.CMFsScx1.js";import{o as D,b as p,s as u}from"./types.FgRm47Sn.js";import{a as L,d as N}from"./reactivity.esm-bundler.BQ12LWmY.js";import{h as a}from"./runtime-core.esm-bundler.CRb7Pg8a.js";const g=D({email:u().email("Введите корректный email"),password:u().min(6,"Пароль должен содержать минимум 6 символов"),rememberMe:p().optional().default(!1)}),d=P.extend({password:u().min(6,"Пароль должен содержать минимум 6 символов"),confirmPassword:u(),acceptTerms:p().refine(o=>o===!0,"Необходимо принять условия использования")}).refine(o=>o.password===o.confirmPassword,{message:"Пароли не совпадают",path:["confirmPassword"]}),n=L({signIn:!1,signUp:!1,signOut:!1,session:!1});function G(){const o=T(),c=a(()=>o.value?.data||null),s=a(()=>c.value?.user),v=a(()=>!!c.value?.user),h=a(()=>Object.values(n).some(e=>e)),m=async()=>{try{const e=await l.getSession();if(e.data)return e.data}catch(e){console.error("Error refetching session:",e)}},w=a(()=>s.value?.role||null),y=a(()=>s.value?C(s.value):!1),S=a(()=>s.value?F(s.value):!1),U=a(()=>s.value?.role==="USER"),R=a(()=>s.value?j(s.value):!1),A=async e=>{const i=g.safeParse(e);if(!i.success)return{error:{message:"Ошибка валидации",details:i.error.issues}};n.signIn=!0;try{const r=await l.signIn.email({email:e.email,password:e.password,rememberMe:e.rememberMe});return r.error?{error:{message:t(r.error)}}:(await m(),{data:r.data})}catch(r){return{error:{message:t(r,"Произошла ошибка при входе")}}}finally{n.signIn=!1}},b=async e=>{const i=d.safeParse(e);if(!i.success)return{error:{message:"Ошибка валидации",details:i.error.issues}};n.signUp=!0;try{const r=await l.signUp.email({email:e.email,password:e.password,name:e.name||""});if(r.error)return{error:{message:t(r.error)}};if(e.role&&e.role!=="USER")try{const f=e.role==="ADMIN"?"admin":"user";await l.admin.setRole({userId:r.data?.user?.id,role:f})}catch(f){console.warn("Failed to set user role:",f)}return{data:r.data}}catch(r){return{error:{message:t(r,"Произошла ошибка при регистрации")}}}finally{n.signUp=!1}},O=async()=>{n.signOut=!0;try{const e=await l.signOut();return e.error?{error:{message:t(e.error)}}:(await m(),{data:e.data})}catch(e){return{error:{message:t(e,"Произошла ошибка при выходе")}}}finally{n.signOut=!1}},I=e=>s.value?x(s.value,e):!1,E=a(()=>s.value?s.value.name||s.value.email:null),M=a(()=>s.value?.image||null);return{user:s,session:c,isAuthenticated:v,isLoading:h,userRole:w,isUserAdmin:y,isUserShopOwner:S,isUserRegular:U,userCanAccessAdmin:R,displayName:E,userAvatar:M,signIn:A,signUp:b,signOut:O,checkRole:I,refetchSession:m,loadingState:N(n),LoginFormSchema:g,RegisterFormSchema:d}}export{G as u};

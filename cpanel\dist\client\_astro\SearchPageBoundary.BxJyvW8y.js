import{u as E,a as w,q as D,f as T}from"./fetchers.B4Ycwiwp.js";import{a as ie}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{u as le,C as oe,S as ne}from"./Spinner.nnVZHfjN.js";import{u as de}from"./useUrlParams.D0jSWJSf.js";import ce from"./Card.C4y0_bWr.js";import{I as me}from"./InputText.DOJMNEP-.js";import{M as ge}from"./MultiSelect.B9h41NVw.js";import{S as he}from"./SecondaryButton.DkELYl7Q.js";import{T as pe}from"./Tag.DTFTku6q.js";import{I as ve}from"./InputNumber.vgPO18dj.js";import{S as fe}from"./Select.CQBzSu6y.js";/* empty css                        */import{_ as O}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as z,c as o,o as s,a as r,b as h,e as n,w as v,f as I,g as y,F as M,r as K,h as d,j as q}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as f,r as R}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./utils.is9Ib0FR.js";import"./useErrorHandler.DVDazL16.js";import"./useToast.pIbuf2bs.js";import"./index.PhWaFJhe.js";import"./trpc.BpyaUO08.js";/* empty css                         */import"./index.CBuVmUry.js";import"./router.DKcY2uv6.js";import"./utils.BUKUcbtE.js";import"./index.BaVCXmir.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.COq_zjeV.js";import"./index.BH7IgUdp.js";import"./index.CDQpPXyE.js";import"./index.DPMtieGJ.js";import"./index.CLs7nh7g.js";import"./index.BpXFSz0M.js";import"./index.S_9XL1GF.js";import"./index.By2TJOuX.js";import"./index.6ykohhwZ.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./index.D4QD70nN.js";import"./index.uDWUdklz.js";import"./index.CwqAtb_i.js";import"./index.CS9OBiV4.js";const ye=z({__name:"SearchPageComponent",props:{initialQuery:{default:""},initialSearchResults:{},initialBrands:{},initialCategories:{},initialAttributeTemplates:{}},setup(x,{expose:a}){a();const i=x,{filters:e,updateFilter:c,updateFilters:_}=de({query:i.initialQuery||"",page:1,limit:20}),t=R({}),m=d(()=>{const u=[];for(const[g,l]of Object.entries(t.value)){const b=Number(g);!b||!l||(l.matchType==="range"?(l.min!=null||l.max!=null)&&u.push({templateId:b,matchType:"range",minValue:l.min??void 0,maxValue:l.max??void 0}):l.value&&String(l.value).trim()&&u.push({templateId:b,matchType:l.matchType||"contains",value:String(l.value)}))}return u});function L(u,g,l){const b=t.value[u]||{matchType:"range",min:null,max:null};t.value[u]={...b,matchType:"range",[g]:l}}function G(u,g){const l=t.value[u]||{};t.value[u]={...l,matchType:g,...g==="range"?{min:null,max:null}:{}}}function H(u,g){const l=t.value[u]||{matchType:"contains"};t.value[u]={...l,value:g}}const S=d({get:()=>e.value.query||"",set:u=>c("query",u||"")}),C=d({get:()=>e.value.categoryIds||[],set:u=>c("categoryIds",u||[])}),k=d({get:()=>e.value.brandIds||[],set:u=>c("brandIds",u||[])}),Q=d({get:()=>e.value.page||1,set:u=>c("page",u||1)}),V=d({get:()=>e.value.limit||20,set:u=>_({limit:u||20,page:1})}),J=R(!0),F=R(!!i.initialQuery),B=le(S,500),W=d(()=>C.value.length>0||k.value.length>0),A=d(()=>({name:B.value?.trim()||void 0,categoryIds:C.value.length?C.value:void 0,brandIds:k.value.length?k.value:void 0,attributeFilters:m.value,limit:V.value,offset:(Q.value-1)*V.value,orderBy:"name",orderDir:"asc"})),{data:X,error:U}=E({queryKey:D.categories.list({where:{level:0},orderBy:{name:"asc"}}),queryFn:()=>T.categories.list({where:{level:0},orderBy:{name:"asc"}}),initialData:i.initialCategories}),{data:Y}=E({queryKey:D.attributeTemplates.list({take:100}),queryFn:()=>T.attributeTemplates.list({take:100})});w(U);const{data:Z,error:N}=E({queryKey:D.brands.list(),queryFn:()=>T.brands.list(),initialData:i.initialBrands});w(N);const P=d(()=>!!B.value?.trim()),p=E({queryKey:d(()=>D.search.parts(A.value)),queryFn:()=>T.search.parts(A.value),initialData:i.initialSearchResults,enabled:P,placeholderData:u=>u});w(p.error);const $=d(()=>p.data),ee=d(()=>p.isFetching&&P.value),te=d(()=>p.error?p.error?.message||"Ошибка поиска":null),ae=()=>{S.value.trim()&&(F.value=!0,p.refetch())},ue=()=>{p.refetch()},re=()=>{c("page",1),F.value&&S.value.trim()},se=()=>{_({categoryIds:[],brandIds:[],page:1})};q(B,(u,g)=>{u!==g&&(c("page",1),String(u).trim()&&(F.value=!0))}),q(()=>e.value.categoryIds,()=>c("page",1),{deep:!0}),q(()=>e.value.brandIds,()=>c("page",1),{deep:!0});const j={props:i,filters:e,updateFilter:c,updateFilters:_,attrState:t,attributeFilters:m,updateAttrRange:L,updateAttrMatchType:G,updateAttrValue:H,searchQuery:S,selectedCategoryIds:C,selectedBrandIds:k,currentPage:Q,itemsPerPage:V,showFilters:J,hasSearched:F,debouncedSearchQuery:B,hasFilters:W,searchFilters:A,categories:X,categoriesError:U,attributeTemplates:Y,brands:Z,brandsError:N,searchQuery_enabled:P,searchQueryObject:p,searchResults:$,isSearching:ee,searchError:te,performSearch:ae,retrySearch:ue,onFiltersChange:re,resetFilters:se,Card:ce,InputText:me,MultiSelect:ge,SecondaryButton:he,Spinner:ne,Tag:pe,InputNumber:ve,Select:fe,CatalogPagination:oe};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}}),_e={class:"search-page"},be={class:"mb-8"},xe={class:"flex gap-4"},Se={class:"flex-1"},Ce={key:0,class:"mb-6"},ke={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Fe={class:"flex items-end"},Be={class:"md:col-span-3"},Ee={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},De={class:"text-sm font-medium mb-2"},Te={key:0,class:"flex items-center gap-2"},Ie={key:0,class:"text-xs text-surface-500"},Ve={key:1,class:"flex items-center gap-2"},Ae={class:"search-results"},Pe={key:0,class:"flex justify-center py-12"},we={key:1,class:"text-center py-12"},qe={class:"text-red-500 mb-4"},Re={key:2,class:"text-center py-12"},Qe={class:"text-surface-500 dark:text-surface-400 mb-4"},Ue={key:3},Ne={class:"mb-4"},je={class:"text-surface-600 dark:text-surface-400"},Me={class:"grid gap-4"},Ke={class:"flex items-start gap-4"},Oe={key:0,class:"w-16 h-16 bg-surface-100 rounded-lg overflow-hidden flex-shrink-0"},ze=["src","alt"],Le={class:"flex-1"},Ge={class:"font-semibold text-surface-900 dark:text-surface-0 mb-1"},He={key:0,class:"text-surface-600 dark:text-surface-400 text-sm mb-2"},Je={class:"flex flex-wrap gap-2"},We={class:"mt-6"},Xe={key:4,class:"text-center py-12"};function Ye(x,a,i,e,c,_){return s(),o("div",_e,[r("div",be,[n(e.Card,null,{content:v(()=>[r("div",xe,[r("div",Se,[n(e.InputText,{modelValue:e.searchQuery,"onUpdate:modelValue":a[0]||(a[0]=t=>e.searchQuery=t),placeholder:"Поиск запчастей...",class:"w-full",onKeyup:ie(e.performSearch,["enter"])},null,8,["modelValue"])]),n(e.SecondaryButton,{onClick:e.performSearch,disabled:!e.searchQuery.trim()},{default:v(()=>a[5]||(a[5]=[I(" Найти ")])),_:1,__:[5]},8,["disabled"])])]),_:1})]),e.showFilters?(s(),o("div",Ce,[n(e.Card,null,{content:v(()=>[r("div",ke,[r("div",null,[a[6]||(a[6]=r("label",{class:"block text-sm font-medium mb-2"},"Категории",-1)),n(e.MultiSelect,{modelValue:e.selectedCategoryIds,"onUpdate:modelValue":a[1]||(a[1]=t=>e.selectedCategoryIds=t),options:e.categories||[],"option-label":"name","option-value":"id",placeholder:"Выберите категории",onChange:e.onFiltersChange},null,8,["modelValue","options"])]),r("div",null,[a[7]||(a[7]=r("label",{class:"block text-sm font-medium mb-2"},"Бренды",-1)),n(e.MultiSelect,{modelValue:e.selectedBrandIds,"onUpdate:modelValue":a[2]||(a[2]=t=>e.selectedBrandIds=t),options:e.brands||[],"option-label":"name","option-value":"id",placeholder:"Выберите бренды",onChange:e.onFiltersChange},null,8,["modelValue","options"])]),r("div",Fe,[e.hasFilters?(s(),y(e.SecondaryButton,{key:0,onClick:e.resetFilters,outlined:""},{default:v(()=>a[8]||(a[8]=[I(" Сбросить фильтры ")])),_:1,__:[8]})):h("",!0)]),r("div",Be,[a[10]||(a[10]=r("label",{class:"block text-sm font-medium mb-2"},"Характеристики",-1)),r("div",Ee,[(s(!0),o(M,null,K(e.attributeTemplates,t=>(s(),o("div",{key:t.id,class:"p-3 rounded border border-surface-200 dark:border-surface-700"},[r("div",De,f(t.title||t.name),1),t.dataType==="NUMBER"?(s(),o("div",Te,[n(e.InputNumber,{"model-value":e.attrState[t.id]?.min??null,"onUpdate:modelValue":m=>e.updateAttrRange(t.id,"min",m),placeholder:"Мин",class:"w-full"},null,8,["model-value","onUpdate:modelValue"]),a[9]||(a[9]=r("span",{class:"text-surface-400"},"—",-1)),n(e.InputNumber,{"model-value":e.attrState[t.id]?.max??null,"onUpdate:modelValue":m=>e.updateAttrRange(t.id,"max",m),placeholder:"Макс",class:"w-full"},null,8,["model-value","onUpdate:modelValue"]),t.unit?(s(),o("span",Ie,f(t.unit),1)):h("",!0)])):(s(),o("div",Ve,[n(e.Select,{"model-value":e.attrState[t.id]?.matchType||"contains",options:[{label:"Содержит",value:"contains"},{label:"Точно",value:"exact"}],class:"w-32","onUpdate:modelValue":m=>e.updateAttrMatchType(t.id,m)},null,8,["model-value","onUpdate:modelValue"]),n(e.InputText,{"model-value":e.attrState[t.id]?.value??"","onUpdate:modelValue":m=>e.updateAttrValue(t.id,m),placeholder:"Значение",class:"flex-1"},null,8,["model-value","onUpdate:modelValue"])]))]))),128))])])])]),_:1})])):h("",!0),r("div",Ae,[e.isSearching?(s(),o("div",Pe,[n(e.Spinner,{size:"lg",variant:"primary",label:"Поиск...",centered:!0})])):e.searchError?(s(),o("div",we,[r("div",qe,f(e.searchError),1),n(e.SecondaryButton,{onClick:e.retrySearch},{default:v(()=>a[11]||(a[11]=[I("Повторить")])),_:1,__:[11]})])):e.hasSearched&&!e.searchResults?.items?.length?(s(),o("div",Re,[r("div",Qe,f(e.hasFilters?"Ничего не найдено по заданным критериям":"Ничего не найдено"),1),e.hasFilters?(s(),y(e.SecondaryButton,{key:0,onClick:e.resetFilters},{default:v(()=>a[12]||(a[12]=[I(" Сбросить фильтры ")])),_:1,__:[12]})):h("",!0)])):e.searchResults?.items?.length?(s(),o("div",Ue,[r("div",Ne,[r("p",je," Найдено "+f(e.searchResults.total||e.searchResults.items.length)+" результатов ",1)]),r("div",Me,[(s(!0),o(M,null,K(e.searchResults.items,t=>(s(),y(e.Card,{key:t.id,class:"hover:shadow-md transition-shadow"},{content:v(()=>[r("div",Ke,[t.image?(s(),o("div",Oe,[r("img",{src:t.image.url,alt:t.name,class:"w-full h-full object-cover"},null,8,ze)])):h("",!0),r("div",Le,[r("h3",Ge,f(t.name),1),t.description?(s(),o("p",He,f(t.description),1)):h("",!0),r("div",Je,[t.partCategory?(s(),y(e.Tag,{key:0,severity:"info",value:t.partCategory.name},null,8,["value"])):h("",!0),t.brand?(s(),y(e.Tag,{key:1,severity:"secondary",value:t.brand.name},null,8,["value"])):h("",!0)])])])]),_:2},1024))),128))]),r("div",We,[n(e.CatalogPagination,{"current-page":e.currentPage,"items-per-page":e.itemsPerPage,"total-items":e.searchResults.total||0,onPageChange:a[3]||(a[3]=t=>e.currentPage=t),onItemsPerPageChange:a[4]||(a[4]=t=>e.itemsPerPage=t)},null,8,["current-page","items-per-page","total-items"])])])):e.hasSearched?h("",!0):(s(),o("div",Xe,a[13]||(a[13]=[r("div",{class:"text-surface-500 dark:text-surface-400"}," Введите запрос для поиска запчастей ",-1)])))])])}const Ze=O(ye,[["render",Ye],["__scopeId","data-v-1bf673bf"]]),$e=z({__name:"SearchPageBoundary",props:{initialQuery:{},initialSearchResults:{},initialBrands:{},initialCategories:{},initialAttributeTemplates:{}},setup(x,{expose:a}){a();const i={SearchPageComponent:Ze};return Object.defineProperty(i,"__isScriptSetup",{enumerable:!1,value:!0}),i}});function et(x,a,i,e,c,_){return s(),y(e.SearchPageComponent,{"initial-query":i.initialQuery,"initial-search-results":i.initialSearchResults,"initial-brands":i.initialBrands,"initial-categories":i.initialCategories,"initial-attribute-templates":i.initialAttributeTemplates},null,8,["initial-query","initial-search-results","initial-brands","initial-categories","initial-attribute-templates"])}const Kt=O($e,[["render",et]]);export{Kt as default};

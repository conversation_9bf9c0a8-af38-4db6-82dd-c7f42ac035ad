import { z } from 'zod'
import { router, procedure, expertProcedure } from '../trpc'
import { AttributeTemplatesService } from '../services/attribute-templates.service'
import { AttributeDataTypeSchema } from '../generated/zod/enums/AttributeDataType.schema'
import { AttributeUnitSchema } from '../generated/zod/enums/AttributeUnit.schema'

// Схемы для работы с шаблонами атрибутов
const CreateAttributeTemplateInputSchema = z.object({
  name: z.string().min(1).max(100).regex(/^[a-z0-9_]+$/, 'Имя должно содержать только строчные буквы, цифры и подчеркивания'),
  title: z.string().min(1).max(200),
  description: z.string().optional().nullable(),
  dataType: AttributeDataTypeSchema.default('STRING'),
  unit: AttributeUnitSchema.optional().nullable(),
  isRequired: z.boolean().default(false),
  minValue: z.number().optional().nullable(),
  maxValue: z.number().optional().nullable(),
  allowedValues: z.array(z.string()).optional().default([]),
  tolerance: z.number().optional().nullable(),
  groupId: z.number().optional().nullable()
});

const UpdateAttributeTemplateInputSchema = z.object({
  id: z.number(),
  name: z.string().min(1).max(100).regex(/^[a-z0-9_]+$/, 'Имя должно содержать только строчные буквы, цифры и подчеркивания').optional(),
  title: z.string().min(1).max(200).optional(),
  description: z.string().optional().nullable(),
  dataType: AttributeDataTypeSchema.optional(),
  unit: AttributeUnitSchema.optional().nullable(),
  isRequired: z.boolean().optional(),
  minValue: z.number().optional().nullable(),
  maxValue: z.number().optional().nullable(),
  allowedValues: z.array(z.string()).optional(),
  tolerance: z.number().optional().nullable(),
  groupId: z.number().optional().nullable()
});

const CreateAttributeGroupInputSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional().nullable()
});

const UpdateAttributeGroupInputSchema = z.object({
  id: z.number(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional().nullable()
});

/**
 * Роутер для управления шаблонами атрибутов
 * Доступен только администраторам
 */
export const attributeTemplatesRouter = router({
  
  // =====================================================
  // УПРАВЛЕНИЕ ШАБЛОНАМИ АТРИБУТОВ
  // =====================================================
  
  /**
   * Получение всех шаблонов атрибутов с группировкой
   */
  findMany: procedure
    .input(z.object({
      groupId: z.number().optional(),
      search: z.string().optional(),
      dataType: AttributeDataTypeSchema.optional(),
      limit: z.number().min(1).max(100).default(50),
      offset: z.number().min(0).default(0)
    }))
    .query(async ({ input }) => AttributeTemplatesService.findMany(input)),

  /**
   * Получение шаблона по ID
   */
  findById: procedure
    .input(z.object({
      id: z.number()
    }))
    .query(async ({ input }) => AttributeTemplatesService.findById(input)),

  /**
   * Создание нового шаблона атрибута
   */
  create: expertProcedure
    .input(CreateAttributeTemplateInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof CreateAttributeTemplateInputSchema> }) => AttributeTemplatesService.create(input)),

  /**
   * Обновление шаблона атрибута
   */
  update: expertProcedure
    .input(UpdateAttributeTemplateInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof UpdateAttributeTemplateInputSchema> }) => AttributeTemplatesService.update(input)),

  /**
   * Удаление шаблона атрибута
   */
  delete: expertProcedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input }: { input: { id: number } }) => AttributeTemplatesService.delete(input)),

  // =====================================================
  // УПРАВЛЕНИЕ ГРУППАМИ АТРИБУТОВ
  // =====================================================

  /**
   * Получение всех групп атрибутов
   */
  findAllGroups: procedure
    .query(async () => AttributeTemplatesService.findAllGroups()),

  /**
   * Поиск групп атрибутов
   */
  searchGroups: procedure
    .input(z.object({
      query: z.string().optional(),
      limit: z.number().min(1).max(50).default(20)
    }))
    .query(async ({ input }: { input: { query?: string; limit: number } }) => AttributeTemplatesService.searchGroups(input)),

  /**
   * Создание группы атрибутов
   */
  createGroup: expertProcedure
    .input(CreateAttributeGroupInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof CreateAttributeGroupInputSchema> }) => AttributeTemplatesService.createGroup(input)),

  /**
   * Обновление группы атрибутов
   */
  updateGroup: expertProcedure
    .input(UpdateAttributeGroupInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof UpdateAttributeGroupInputSchema> }) => AttributeTemplatesService.updateGroup(input)),

  /**
   * Удаление группы атрибутов
   */
  deleteGroup: expertProcedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input }: { input: { id: number } }) => AttributeTemplatesService.deleteGroup(input))
});

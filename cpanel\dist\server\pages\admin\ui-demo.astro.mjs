import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { T as Toast, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { mergeProps, createElementBlock, createCommentVNode, openBlock, createBlock, resolveDynamicComponent, normalizeClass, toDisplayString, resolveComponent, createElementVNode, Fragment, renderList, createVNode, renderSlot, defineComponent, useSSRContext, ref, createSlots, withCtx } from 'vue';
import { _ as _export_sfc, u as useToast } from '../../chunks/ClientRouter_avhRMbqw.mjs';
import { u as useConfirm, C as ConfirmDialog } from '../../chunks/ConfirmDialog_D9uxkFze.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { p as ptViewMerge, S as SecondaryButton } from '../../chunks/SecondaryButton_B0hmlm1n.mjs';
import { D as DataTable, s as script$3 } from '../../chunks/index_CxapvcaX.mjs';
import { D as Dialog } from '../../chunks/Dialog_DqmfICId.mjs';
import ChevronRightIcon from '@primevue/icons/chevronright';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/breadcrumb';
import BaseStyle from '@primevue/core/base/style';
import { ssrRenderComponent, ssrRenderSlot, ssrRenderAttrs, ssrRenderClass, ssrInterpolate } from 'vue/server-renderer';
import { T as ThemeToggle } from '../../chunks/ThemeToggle_BLNtK__-.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

var classes = {
  root: 'p-breadcrumb p-component',
  list: 'p-breadcrumb-list',
  homeItem: 'p-breadcrumb-home-item',
  separator: 'p-breadcrumb-separator',
  separatorIcon: 'p-breadcrumb-separator-icon',
  item: function item(_ref) {
    var instance = _ref.instance;
    return ['p-breadcrumb-item', {
      'p-disabled': instance.disabled()
    }];
  },
  itemLink: 'p-breadcrumb-item-link',
  itemIcon: 'p-breadcrumb-item-icon',
  itemLabel: 'p-breadcrumb-item-label'
};
var BreadcrumbStyle = BaseStyle.extend({
  name: 'breadcrumb',
  style: style,
  classes: classes
});

var script$2 = {
  name: 'BaseBreadcrumb',
  "extends": BaseComponent,
  props: {
    model: {
      type: Array,
      "default": null
    },
    home: {
      type: null,
      "default": null
    }
  },
  style: BreadcrumbStyle,
  provide: function provide() {
    return {
      $pcBreadcrumb: this,
      $parentInstance: this
    };
  }
};

var script$1 = {
  name: 'BreadcrumbItem',
  hostName: 'Breadcrumb',
  "extends": BaseComponent,
  props: {
    item: null,
    templates: null,
    index: null
  },
  methods: {
    onClick: function onClick(event) {
      if (this.item.command) {
        this.item.command({
          originalEvent: event,
          item: this.item
        });
      }
    },
    visible: function visible() {
      return typeof this.item.visible === 'function' ? this.item.visible() : this.item.visible !== false;
    },
    disabled: function disabled() {
      return typeof this.item.disabled === 'function' ? this.item.disabled() : this.item.disabled;
    },
    label: function label() {
      return typeof this.item.label === 'function' ? this.item.label() : this.item.label;
    },
    isCurrentUrl: function isCurrentUrl() {
      var _this$item = this.item,
        to = _this$item.to,
        url = _this$item.url;
      var lastPath = typeof window !== 'undefined' ? window.location.pathname : '';
      return to === lastPath || url === lastPath ? 'page' : undefined;
    }
  },
  computed: {
    ptmOptions: function ptmOptions() {
      return {
        context: {
          item: this.item,
          index: this.index
        }
      };
    },
    getMenuItemProps: function getMenuItemProps() {
      var _this = this;
      return {
        action: mergeProps({
          "class": this.cx('itemLink'),
          'aria-current': this.isCurrentUrl(),
          onClick: function onClick($event) {
            return _this.onClick($event);
          }
        }, this.ptm('itemLink', this.ptmOptions)),
        icon: mergeProps({
          "class": [this.cx('icon'), this.item.icon]
        }, this.ptm('icon', this.ptmOptions)),
        label: mergeProps({
          "class": this.cx('label')
        }, this.ptm('label', this.ptmOptions))
      };
    }
  }
};

var _hoisted_1 = ["href", "target", "aria-current"];
function render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return $options.visible() ? (openBlock(), createElementBlock("li", mergeProps({
    key: 0,
    "class": [_ctx.cx('item'), $props.item["class"]]
  }, _ctx.ptm('item', $options.ptmOptions)), [!$props.templates.item ? (openBlock(), createElementBlock("a", mergeProps({
    key: 0,
    href: $props.item.url || '#',
    "class": _ctx.cx('itemLink'),
    target: $props.item.target,
    "aria-current": $options.isCurrentUrl(),
    onClick: _cache[0] || (_cache[0] = function () {
      return $options.onClick && $options.onClick.apply($options, arguments);
    })
  }, _ctx.ptm('itemLink', $options.ptmOptions)), [$props.templates && $props.templates.itemicon ? (openBlock(), createBlock(resolveDynamicComponent($props.templates.itemicon), {
    key: 0,
    item: $props.item,
    "class": normalizeClass(_ctx.cx('itemIcon', $options.ptmOptions))
  }, null, 8, ["item", "class"])) : $props.item.icon ? (openBlock(), createElementBlock("span", mergeProps({
    key: 1,
    "class": [_ctx.cx('itemIcon'), $props.item.icon]
  }, _ctx.ptm('itemIcon', $options.ptmOptions)), null, 16)) : createCommentVNode("", true), $props.item.label ? (openBlock(), createElementBlock("span", mergeProps({
    key: 2,
    "class": _ctx.cx('itemLabel')
  }, _ctx.ptm('itemLabel', $options.ptmOptions)), toDisplayString($options.label()), 17)) : createCommentVNode("", true)], 16, _hoisted_1)) : (openBlock(), createBlock(resolveDynamicComponent($props.templates.item), {
    key: 1,
    item: $props.item,
    label: $options.label(),
    props: $options.getMenuItemProps
  }, null, 8, ["item", "label", "props"]))], 16)) : createCommentVNode("", true);
}

script$1.render = render$1;

var script = {
  name: 'Breadcrumb',
  "extends": script$2,
  inheritAttrs: false,
  components: {
    BreadcrumbItem: script$1,
    ChevronRightIcon: ChevronRightIcon
  }
};

function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_BreadcrumbItem = resolveComponent("BreadcrumbItem");
  var _component_ChevronRightIcon = resolveComponent("ChevronRightIcon");
  return openBlock(), createElementBlock("nav", mergeProps({
    "class": _ctx.cx('root')
  }, _ctx.ptmi('root')), [createElementVNode("ol", mergeProps({
    "class": _ctx.cx('list')
  }, _ctx.ptm('list')), [_ctx.home ? (openBlock(), createBlock(_component_BreadcrumbItem, mergeProps({
    key: 0,
    item: _ctx.home,
    "class": _ctx.cx('homeItem'),
    templates: _ctx.$slots,
    pt: _ctx.pt,
    unstyled: _ctx.unstyled
  }, _ctx.ptm('homeItem')), null, 16, ["item", "class", "templates", "pt", "unstyled"])) : createCommentVNode("", true), (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.model, function (item, i) {
    return openBlock(), createElementBlock(Fragment, {
      key: item.label + '_' + i
    }, [_ctx.home || i !== 0 ? (openBlock(), createElementBlock("li", mergeProps({
      key: 0,
      "class": _ctx.cx('separator')
    }, {
      ref_for: true
    }, _ctx.ptm('separator')), [renderSlot(_ctx.$slots, "separator", {}, function () {
      return [createVNode(_component_ChevronRightIcon, mergeProps({
        "aria-hidden": "true",
        "class": _ctx.cx('separatorIcon')
      }, {
        ref_for: true
      }, _ctx.ptm('separatorIcon')), null, 16, ["class"])];
    })], 16)) : createCommentVNode("", true), createVNode(_component_BreadcrumbItem, {
      item: item,
      index: i,
      templates: _ctx.$slots,
      pt: _ctx.pt,
      unstyled: _ctx.unstyled
    }, null, 8, ["item", "index", "templates", "pt", "unstyled"])], 64);
  }), 128))], 16)], 16);
}

script.render = render;

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "Breadcrumb",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `bg-surface-0 dark:bg-surface-900 p-4 overflow-x-auto`,
      list: `m-0 p-0 list-none flex items-center flex-nowrap gap-2`,
      item: ``,
      itemLink: `no-underline flex items-center gap-2 transition-colors duration-200 rounded-md
        text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-0
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary`,
      itemIcon: ``,
      itemLabel: ``,
      separator: `flex items-center text-surface-400 dark:text-surface-500`,
      separatorIcon: ``
    });
    const __returned__ = { theme, get Breadcrumb() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Breadcrumb"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Breadcrumb.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const Breadcrumb = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "UIDemo",
  setup(__props, { expose: __expose }) {
    __expose();
    const toast = useToast();
    const confirm = useConfirm();
    const dialogVisible = ref(false);
    const breadcrumbItems = ref([
      { label: "\u0410\u0434\u043C\u0438\u043D \u043F\u0430\u043D\u0435\u043B\u044C", url: "/admin" },
      { label: "UI \u041A\u043E\u043C\u043F\u043E\u043D\u0435\u043D\u0442\u044B" }
    ]);
    const tableData = ref([
      { id: 1, name: "\u0421\u0430\u043B\u044C\u043D\u0438\u043A \u043A\u043E\u043B\u0435\u043D\u0432\u0430\u043B\u0430", category: "\u0414\u0432\u0438\u0433\u0430\u0442\u0435\u043B\u044C", status: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D" },
      { id: 2, name: "\u0424\u0438\u043B\u044C\u0442\u0440 \u043C\u0430\u0441\u043B\u044F\u043D\u044B\u0439", category: "\u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u0441\u043C\u0430\u0437\u043A\u0438", status: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D" },
      { id: 3, name: "\u041F\u0440\u043E\u043A\u043B\u0430\u0434\u043A\u0430 \u0413\u0411\u0426", category: "\u0414\u0432\u0438\u0433\u0430\u0442\u0435\u043B\u044C", status: "\u041D\u0435\u0430\u043A\u0442\u0438\u0432\u0435\u043D" },
      { id: 4, name: "\u0422\u043E\u0440\u043C\u043E\u0437\u043D\u044B\u0435 \u043A\u043E\u043B\u043E\u0434\u043A\u0438", category: "\u0422\u043E\u0440\u043C\u043E\u0437\u043D\u0430\u044F \u0441\u0438\u0441\u0442\u0435\u043C\u0430", status: "\u041E\u0436\u0438\u0434\u0430\u043D\u0438\u0435" },
      { id: 5, name: "\u0410\u043C\u043E\u0440\u0442\u0438\u0437\u0430\u0442\u043E\u0440 \u043F\u0435\u0440\u0435\u0434\u043D\u0438\u0439", category: "\u041F\u043E\u0434\u0432\u0435\u0441\u043A\u0430", status: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D" },
      { id: 6, name: "\u0421\u0432\u0435\u0447\u0430 \u0437\u0430\u0436\u0438\u0433\u0430\u043D\u0438\u044F", category: "\u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u0437\u0430\u0436\u0438\u0433\u0430\u043D\u0438\u044F", status: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D" },
      { id: 7, name: "\u0420\u0435\u043C\u0435\u043D\u044C \u0413\u0420\u041C", category: "\u0414\u0432\u0438\u0433\u0430\u0442\u0435\u043B\u044C", status: "\u041D\u0435\u0430\u043A\u0442\u0438\u0432\u0435\u043D" },
      { id: 8, name: "\u0420\u0430\u0434\u0438\u0430\u0442\u043E\u0440 \u043E\u0445\u043B\u0430\u0436\u0434\u0435\u043D\u0438\u044F", category: "\u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u043E\u0445\u043B\u0430\u0436\u0434\u0435\u043D\u0438\u044F", status: "\u0410\u043A\u0442\u0438\u0432\u0435\u043D" }
    ]);
    const showSuccessToast = () => {
      toast.success("\u0423\u0441\u043F\u0435\u0448\u043D\u043E!", "\u041E\u043F\u0435\u0440\u0430\u0446\u0438\u044F \u0432\u044B\u043F\u043E\u043B\u043D\u0435\u043D\u0430 \u0443\u0441\u043F\u0435\u0448\u043D\u043E");
    };
    const showInfoToast = () => {
      toast.info("\u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F", "\u042D\u0442\u043E \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u043E\u043D\u043D\u043E\u0435 \u0441\u043E\u043E\u0431\u0449\u0435\u043D\u0438\u0435");
    };
    const showWarnToast = () => {
      toast.warn("\u0412\u043D\u0438\u043C\u0430\u043D\u0438\u0435!", "\u042D\u0442\u043E \u043F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435");
    };
    const showErrorToast = () => {
      toast.error("\u041E\u0448\u0438\u0431\u043A\u0430!", "\u041F\u0440\u043E\u0438\u0437\u043E\u0448\u043B\u0430 \u043E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438 \u0432\u044B\u043F\u043E\u043B\u043D\u0435\u043D\u0438\u0438 \u043E\u043F\u0435\u0440\u0430\u0446\u0438\u0438");
    };
    const showDialog = () => {
      dialogVisible.value = true;
    };
    const showDeleteConfirm = () => {
      confirm.confirmDelete("\u0437\u0430\u043F\u0438\u0441\u044C", () => {
        toast.success("\u0423\u0434\u0430\u043B\u0435\u043D\u043E", "\u0417\u0430\u043F\u0438\u0441\u044C \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0443\u0434\u0430\u043B\u0435\u043D\u0430");
      });
    };
    const showSaveConfirm = () => {
      confirm.confirmSave("\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F?", () => {
        toast.success("\u0421\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u043E", "\u0418\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u044B");
      });
    };
    const editItem = (item) => {
      toast.info("\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435", `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043F\u0438\u0441\u0438: ${item.name}`);
    };
    const deleteItem = (item) => {
      confirm.confirmDelete(`\u0437\u0430\u043F\u0438\u0441\u044C "${item.name}"`, () => {
        toast.success("\u0423\u0434\u0430\u043B\u0435\u043D\u043E", `\u0417\u0430\u043F\u0438\u0441\u044C "${item.name}" \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0443\u0434\u0430\u043B\u0435\u043D\u0430`);
      });
    };
    const __returned__ = { toast, confirm, dialogVisible, breadcrumbItems, tableData, showSuccessToast, showInfoToast, showWarnToast, showErrorToast, showDialog, showDeleteConfirm, showSaveConfirm, editItem, deleteItem, Button, SecondaryButton, DataTable, get Column() {
      return script$3;
    }, Dialog, Toast, ConfirmDialog, Breadcrumb, ThemeToggle };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-8" }, _attrs))}><div class="mb-8"><h1 class="text-3xl font-bold text-[--color-foreground] mb-2">UI \u041A\u043E\u043C\u043F\u043E\u043D\u0435\u043D\u0442\u044B</h1><p class="text-[--color-muted]"> \u0414\u0435\u043C\u043E\u043D\u0441\u0442\u0440\u0430\u0446\u0438\u044F \u0432\u0441\u0435\u0445 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u044B\u0445 UI \u043A\u043E\u043C\u043F\u043E\u043D\u0435\u043D\u0442\u043E\u0432 \u0441 \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u043E\u0439 \u0442\u0435\u043C </p></div><div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"><h2 class="text-xl font-semibold text-[--color-foreground] mb-4">Breadcrumb</h2>`);
  _push(ssrRenderComponent($setup["Breadcrumb"], { model: $setup.breadcrumbItems }, null, _parent));
  _push(`</div><div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"><h2 class="text-xl font-semibold text-[--color-foreground] mb-4">\u041A\u043D\u043E\u043F\u043A\u0438 \u0438 \u043F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0430\u0442\u0435\u043B\u044C \u0442\u0435\u043C</h2><div class="flex flex-wrap gap-4 items-center">`);
  _push(ssrRenderComponent($setup["Button"], { label: "\u041E\u0441\u043D\u043E\u0432\u043D\u0430\u044F \u043A\u043D\u043E\u043F\u043A\u0430" }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], { label: "\u0412\u0442\u043E\u0440\u0438\u0447\u043D\u0430\u044F \u043A\u043D\u043E\u043F\u043A\u0430" }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0421 \u0438\u043A\u043E\u043D\u043A\u043E\u0439",
    icon: "pi pi-plus"
  }, null, _parent));
  _push(ssrRenderComponent($setup["ThemeToggle"], {
    mode: "buttons",
    "show-label": ""
  }, null, _parent));
  _push(`</div></div><div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"><h2 class="text-xl font-semibold text-[--color-foreground] mb-4">Toast \u0443\u0432\u0435\u0434\u043E\u043C\u043B\u0435\u043D\u0438\u044F</h2><div class="flex flex-wrap gap-4">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0423\u0441\u043F\u0435\u0445",
    onClick: $setup.showSuccessToast,
    class: "bg-[--p-success-color] text-[--p-success-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-success-color),black_10%)]"
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u0418\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F",
    onClick: $setup.showInfoToast,
    class: "bg-[--p-primary-color] text-[--p-primary-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-primary-color),black_10%)]"
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435",
    onClick: $setup.showWarnToast,
    class: "bg-[--p-warning-color] text-[--p-warning-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-warning-color),black_10%)]"
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041E\u0448\u0438\u0431\u043A\u0430",
    onClick: $setup.showErrorToast,
    class: "bg-[--p-danger-color] text-[--p-danger-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-danger-color),black_10%)]"
  }, null, _parent));
  _push(`</div></div><div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"><h2 class="text-xl font-semibold text-[--color-foreground] mb-4">\u0414\u0438\u0430\u043B\u043E\u0433\u0438</h2><div class="flex flex-wrap gap-4">`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041E\u0431\u044B\u0447\u043D\u044B\u0439 \u0434\u0438\u0430\u043B\u043E\u0433",
    onClick: $setup.showDialog
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F",
    onClick: $setup.showDeleteConfirm,
    class: "bg-[--p-danger-color] text-[--p-danger-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-danger-color),black_10%)]"
  }, null, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    label: "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u0435 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F",
    onClick: $setup.showSaveConfirm,
    class: "bg-[--p-success-color] text-[--p-success-contrast-color] hover:bg-[color-mix(in_srgb,var(--p-success-color),black_10%)]"
  }, null, _parent));
  _push(`</div></div><div class="bg-[--color-card] rounded-[--radius-lg] p-6 border border-[--color-border]"><h2 class="text-xl font-semibold text-[--color-foreground] mb-4">DataTable</h2>`);
  _push(ssrRenderComponent($setup["DataTable"], {
    value: $setup.tableData,
    paginator: "",
    rows: 5,
    rowsPerPageOptions: [5, 10, 20],
    tableStyle: "min-width: 50rem"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          field: "id",
          header: "ID",
          sortable: "",
          style: { "width": "10%" }
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "name",
          header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
          sortable: "",
          style: { "width": "30%" }
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "category",
          header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
          sortable: "",
          style: { "width": "25%" }
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "status",
          header: "\u0421\u0442\u0430\u0442\u0443\u0441",
          style: { "width": "15%" }
        }, {
          body: withCtx((slotProps, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<span class="${ssrRenderClass({
                "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-success-color),transparent_85%)] text-[--p-success-color]": slotProps.data.status === "\u0410\u043A\u0442\u0438\u0432\u0435\u043D",
                "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-danger-color),transparent_85%)] text-[--p-danger-color]": slotProps.data.status === "\u041D\u0435\u0430\u043A\u0442\u0438\u0432\u0435\u043D",
                "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-warning-color),transparent_85%)] text-[--p-warning-color]": slotProps.data.status === "\u041E\u0436\u0438\u0434\u0430\u043D\u0438\u0435"
              })}"${_scopeId2}>${ssrInterpolate(slotProps.data.status)}</span>`);
            } else {
              return [
                createVNode("span", {
                  class: {
                    "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-success-color),transparent_85%)] text-[--p-success-color]": slotProps.data.status === "\u0410\u043A\u0442\u0438\u0432\u0435\u043D",
                    "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-danger-color),transparent_85%)] text-[--p-danger-color]": slotProps.data.status === "\u041D\u0435\u0430\u043A\u0442\u0438\u0432\u0435\u043D",
                    "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-warning-color),transparent_85%)] text-[--p-warning-color]": slotProps.data.status === "\u041E\u0436\u0438\u0434\u0430\u043D\u0438\u0435"
                  }
                }, toDisplayString(slotProps.data.status), 3)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
          style: { "width": "20%" }
        }, {
          body: withCtx((slotProps, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["SecondaryButton"], {
                icon: "pi pi-pencil",
                text: "",
                size: "small",
                onClick: ($event) => $setup.editItem(slotProps.data)
              }, null, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["SecondaryButton"], {
                icon: "pi pi-trash",
                text: "",
                size: "small",
                class: "text-red-500 hover:text-red-700",
                onClick: ($event) => $setup.deleteItem(slotProps.data)
              }, null, _parent3, _scopeId2));
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "flex gap-2" }, [
                  createVNode($setup["SecondaryButton"], {
                    icon: "pi pi-pencil",
                    text: "",
                    size: "small",
                    onClick: ($event) => $setup.editItem(slotProps.data)
                  }, null, 8, ["onClick"]),
                  createVNode($setup["SecondaryButton"], {
                    icon: "pi pi-trash",
                    text: "",
                    size: "small",
                    class: "text-red-500 hover:text-red-700",
                    onClick: ($event) => $setup.deleteItem(slotProps.data)
                  }, null, 8, ["onClick"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            field: "id",
            header: "ID",
            sortable: "",
            style: { "width": "10%" }
          }),
          createVNode($setup["Column"], {
            field: "name",
            header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
            sortable: "",
            style: { "width": "30%" }
          }),
          createVNode($setup["Column"], {
            field: "category",
            header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
            sortable: "",
            style: { "width": "25%" }
          }),
          createVNode($setup["Column"], {
            field: "status",
            header: "\u0421\u0442\u0430\u0442\u0443\u0441",
            style: { "width": "15%" }
          }, {
            body: withCtx((slotProps) => [
              createVNode("span", {
                class: {
                  "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-success-color),transparent_85%)] text-[--p-success-color]": slotProps.data.status === "\u0410\u043A\u0442\u0438\u0432\u0435\u043D",
                  "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-danger-color),transparent_85%)] text-[--p-danger-color]": slotProps.data.status === "\u041D\u0435\u0430\u043A\u0442\u0438\u0432\u0435\u043D",
                  "px-2 py-1 rounded-full text-xs bg-[color-mix(in_srgb,var(--p-warning-color),transparent_85%)] text-[--p-warning-color]": slotProps.data.status === "\u041E\u0436\u0438\u0434\u0430\u043D\u0438\u0435"
                }
              }, toDisplayString(slotProps.data.status), 3)
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
            style: { "width": "20%" }
          }, {
            body: withCtx((slotProps) => [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode($setup["SecondaryButton"], {
                  icon: "pi pi-pencil",
                  text: "",
                  size: "small",
                  onClick: ($event) => $setup.editItem(slotProps.data)
                }, null, 8, ["onClick"]),
                createVNode($setup["SecondaryButton"], {
                  icon: "pi pi-trash",
                  text: "",
                  size: "small",
                  class: "text-red-500 hover:text-red-700",
                  onClick: ($event) => $setup.deleteItem(slotProps.data)
                }, null, 8, ["onClick"])
              ])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["Dialog"], {
    visible: $setup.dialogVisible,
    "onUpdate:visible": ($event) => $setup.dialogVisible = $event,
    modal: "",
    header: "\u041F\u0440\u0438\u043C\u0435\u0440 \u0434\u0438\u0430\u043B\u043E\u0433\u0430",
    style: { "width": "25rem" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<p class="text-[--color-muted] mb-4"${_scopeId}> \u042D\u0442\u043E \u043F\u0440\u0438\u043C\u0435\u0440 \u0434\u0438\u0430\u043B\u043E\u0433\u043E\u0432\u043E\u0433\u043E \u043E\u043A\u043D\u0430 \u0441 \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u043E\u0439 \u0442\u0435\u043C. </p><div class="flex justify-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          onClick: ($event) => $setup.dialogVisible = false
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          label: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
          onClick: ($event) => $setup.dialogVisible = false
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("p", { class: "text-[--color-muted] mb-4" }, " \u042D\u0442\u043E \u043F\u0440\u0438\u043C\u0435\u0440 \u0434\u0438\u0430\u043B\u043E\u0433\u043E\u0432\u043E\u0433\u043E \u043E\u043A\u043D\u0430 \u0441 \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u043E\u0439 \u0442\u0435\u043C. "),
          createVNode("div", { class: "flex justify-end gap-2" }, [
            createVNode($setup["SecondaryButton"], {
              label: "\u041E\u0442\u043C\u0435\u043D\u0430",
              onClick: ($event) => $setup.dialogVisible = false
            }, null, 8, ["onClick"]),
            createVNode($setup["Button"], {
              label: "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
              onClick: ($event) => $setup.dialogVisible = false
            }, null, 8, ["onClick"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["Toast"], null, null, _parent));
  _push(ssrRenderComponent($setup["ConfirmDialog"], null, null, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/UIDemo.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const UIDemo = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$UiDemo = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "UI \u041A\u043E\u043C\u043F\u043E\u043D\u0435\u043D\u0442\u044B - PartTec Admin", "showSidebar": true }, { "default": ($$result2) => renderTemplate`  ${renderComponent($$result2, "UIDemo", UIDemo, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/admin/UIDemo.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/ui-demo.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/ui-demo.astro";
const $$url = "/admin/ui-demo";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$UiDemo,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

import{t as a}from"./trpc.BpyaUO08.js";import{u as o}from"./useErrorHandler.DVDazL16.js";import{r as d}from"./reactivity.esm-bundler.BQ12LWmY.js";import{h as m}from"./runtime-core.esm-bundler.CRb7Pg8a.js";function q(){const u=d(!1),r=d(null),n=o(),c=e=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("app:toast",{detail:e}))},p=e=>{const s=n.handleTRPCError(e,!1);r.value=s.message,c({severity:"error",summary:"Ошибка",detail:s.message})},l=()=>{r.value=null},t=async(e,s)=>{try{u.value=!0,l();const i=await e();return s?.success&&c({severity:"success",summary:s.success.title??"Успешно",detail:s.success.message}),i}catch(i){return p(i),null}finally{u.value=!1}};return{loading:m(()=>u.value),error:m(()=>r.value),clearError:l,execute:t,client:a,parts:{findMany:e=>t(()=>a.crud.part.findMany.query(e)),findUnique:e=>t(()=>a.crud.part.findUnique.query(e)),create:e=>t(()=>a.crud.part.create.mutate(e),{success:{title:"Сохранено",message:"Запчасть создана"}}),update:e=>t(()=>a.crud.part.update.mutate(e),{success:{title:"Сохранено",message:"Запчасть обновлена"}}),delete:e=>t(()=>a.crud.part.delete.mutate(e),{success:{title:"Удалено",message:"Запчасть удалена"}})},catalogItems:{findMany:e=>t(()=>a.crud.catalogItem.findMany.query(e)),findUnique:e=>t(()=>a.crud.catalogItem.findUnique.query(e)),create:e=>t(()=>a.crud.catalogItem.create.mutate(e),{success:{title:"Сохранено",message:"Позиция создана"}}),update:e=>t(()=>a.crud.catalogItem.update.mutate(e),{success:{title:"Сохранено",message:"Позиция обновлена"}}),delete:e=>t(()=>a.crud.catalogItem.delete.mutate(e),{success:{title:"Удалено",message:"Позиция удалена"}})},matching:{findMatchingParts:e=>t(()=>a.matching.findMatchingParts.query(e)),findMatchingCatalogItems:e=>t(()=>a.matching.findMatchingCatalogItems.query(e)),proposeLink:e=>t(()=>a.matching.proposeLink.mutate(e)),listProposals:e=>t(()=>a.matching.listProposals.query(e)),approveProposal:e=>t(()=>a.matching.approveProposal.mutate(e)),rejectProposal:e=>t(()=>a.matching.rejectProposal.mutate(e)),generateProposals:e=>t(()=>a.matching.generateProposals.mutate(e)),createPartFromItems:e=>t(()=>a.matching.createPartFromItems.mutate(e))},brands:{findMany:e=>t(()=>a.crud.brand.findMany.query(e)),create:e=>t(()=>a.crud.brand.create.mutate(e),{success:{title:"Сохранено",message:"Бренд создан"}}),update:e=>t(()=>a.crud.brand.update.mutate(e),{success:{title:"Сохранено",message:"Бренд обновлён"}}),delete:e=>t(()=>a.crud.brand.delete.mutate(e),{success:{title:"Удалено",message:"Бренд удалён"}})},partCategories:{findMany:e=>t(()=>a.crud.partCategory.findMany.query(e)),create:e=>t(()=>a.crud.partCategory.create.mutate(e),{success:{title:"Сохранено",message:"Категория создана"}}),update:e=>t(()=>a.crud.partCategory.update.mutate(e),{success:{title:"Сохранено",message:"Категория обновлена"}}),delete:e=>t(()=>a.crud.partCategory.delete.mutate(e),{success:{title:"Удалено",message:"Категория удалена"}})},media:{uploadPartImage:e=>t(()=>a.upload.uploadPartImage.mutate(e),{success:{title:"Загружено",message:"Изображение запчасти обновлено"}}),deletePartImage:e=>t(()=>a.upload.deletePartImage.mutate(e),{success:{title:"Удалено",message:"Изображение запчасти удалено"}}),uploadPartCategoryImage:e=>t(()=>a.upload.uploadPartCategoryImage.mutate(e),{success:{title:"Загружено",message:"Изображение категории обновлено"}}),deletePartCategoryImage:e=>t(()=>a.upload.deletePartCategoryImage.mutate(e),{success:{title:"Удалено",message:"Изображение категории удалено"}})},equipmentModels:{findMany:e=>t(()=>a.crud.equipmentModel.findMany.query(e)),findUnique:e=>t(()=>a.crud.equipmentModel.findUnique.query(e)),create:e=>t(()=>a.crud.equipmentModel.create.mutate(e)),update:e=>t(()=>a.crud.equipmentModel.update.mutate(e)),delete:e=>t(()=>a.crud.equipmentModel.delete.mutate(e))},partAttributes:{findByPartId:e=>t(()=>a.partAttributes.findByPartId.query(e)),create:e=>t(()=>a.partAttributes.create.mutate(e),{success:{title:"Сохранено",message:"Атрибут добавлен"}}),update:e=>t(()=>a.partAttributes.update.mutate(e),{success:{title:"Сохранено",message:"Атрибут обновлён"}}),delete:e=>t(()=>a.partAttributes.delete.mutate(e),{success:{title:"Удалено",message:"Атрибут удалён"}}),bulkCreate:e=>t(()=>a.partAttributes.bulkCreate.mutate(e))},attributeTemplates:{findMany:e=>t(()=>a.attributeTemplates.findMany.query(e)),findById:e=>t(()=>a.attributeTemplates.findById.query(e)),create:e=>t(()=>a.attributeTemplates.create.mutate(e),{success:{title:"Сохранено",message:"Шаблон создан"}}),update:e=>t(()=>a.attributeTemplates.update.mutate(e),{success:{title:"Сохранено",message:"Шаблон обновлён"}}),delete:e=>t(()=>a.attributeTemplates.delete.mutate(e),{success:{title:"Удалено",message:"Шаблон удалён"}}),findAllGroups:()=>t(()=>a.attributeTemplates.findAllGroups.query({})),createGroup:e=>t(()=>a.attributeTemplates.createGroup.mutate(e),{success:{title:"Сохранено",message:"Группа создана"}}),updateGroup:e=>t(()=>a.attributeTemplates.updateGroup.mutate(e),{success:{title:"Сохранено",message:"Группа обновлена"}}),deleteGroup:e=>t(()=>a.attributeTemplates.deleteGroup.mutate(e),{success:{title:"Удалено",message:"Группа удалена"}})},attributeSynonyms:{groups:{findMany:e=>t(()=>a.attributeSynonyms.groups.findMany.query(e)),create:e=>t(()=>a.attributeSynonyms.groups.create.mutate(e),{success:{title:"Сохранено",message:"Группа синонимов создана"}}),update:e=>t(()=>a.attributeSynonyms.groups.update.mutate(e),{success:{title:"Сохранено",message:"Группа синонимов обновлена"}}),delete:e=>t(()=>a.attributeSynonyms.groups.delete.mutate(e),{success:{title:"Удалено",message:"Группа синонимов удалена"}})},synonyms:{findMany:e=>t(()=>a.attributeSynonyms.synonyms.findMany.query(e)),create:e=>t(()=>a.attributeSynonyms.synonyms.create.mutate(e),{success:{title:"Сохранено",message:"Синоним добавлен"}}),delete:e=>t(()=>a.attributeSynonyms.synonyms.delete.mutate(e),{success:{title:"Удалено",message:"Синоним удалён"}})},utils:{findGroupByValue:e=>t(()=>a.attributeSynonyms.utils.findGroupByValue.query(e))}},partApplicability:{upsert:e=>t(()=>a.crud.partApplicability.upsert.mutate(e),{success:{title:"Сохранено",message:"Применимость обновлена"}}),findMany:e=>t(()=>a.crud.partApplicability.findMany.query(e)),create:e=>t(()=>a.crud.partApplicability.create.mutate(e)),update:e=>t(()=>a.crud.partApplicability.update.mutate(e)),findFirst:e=>t(()=>a.crud.partApplicability.findFirst.query(e)),delete:e=>t(()=>a.crud.partApplicability.delete.mutate(e),{success:{title:"Удалено",message:"Связь с группой удалена"}})},equipmentApplicability:{upsert:e=>t(()=>a.crud.equipmentApplicability.upsert.mutate(e)),findMany:e=>t(()=>a.crud.equipmentApplicability.findMany.query(e)),create:e=>t(()=>a.crud.equipmentApplicability.create.mutate(e),{success:{title:"Сохранено",message:"Применимость техники добавлена"}}),update:e=>t(()=>a.crud.equipmentApplicability.update.mutate(e),{success:{title:"Сохранено",message:"Применимость техники обновлена"}}),findFirst:e=>t(()=>a.crud.equipmentApplicability.findFirst.query(e))}}}export{q as u};

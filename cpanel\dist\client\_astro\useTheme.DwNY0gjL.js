import{r as m}from"./reactivity.esm-bundler.BQ12LWmY.js";import{h as a,i as g,j as i}from"./runtime-core.esm-bundler.CRb7Pg8a.js";const d="parttec-theme",t=m("system"),n=m(!1),o=a(()=>t.value==="system"?n.value?"dark":"light":t.value),k=e=>{if(typeof document<"u"){const r=document.documentElement;e==="dark"?(r.setAttribute("data-theme","dark"),r.classList.add("dark")):(r.removeAttribute("data-theme"),r.classList.remove("dark"))}},p=()=>{typeof window<"u"&&window.matchMedia&&(n.value=window.matchMedia("(prefers-color-scheme: dark)").matches)},T=()=>{if(typeof window<"u"){const e=localStorage.getItem(d);if(e&&["light","dark","system"].includes(e))return e}return"system"},M=e=>{typeof window<"u"&&localStorage.setItem(d,e)},L=()=>{g(()=>{if(t.value=T(),p(),typeof window<"u"&&window.matchMedia){const s=window.matchMedia("(prefers-color-scheme: dark)"),c=w=>{n.value=w.matches};return s.addEventListener("change",c),()=>{s.removeEventListener("change",c)}}}),i(o,s=>{k(s)},{immediate:!0}),i(t,s=>{M(s)});const e=s=>{t.value=s},r=()=>{if(t.value==="system"){const s=n.value?"light":"dark";e(s)}else t.value==="light"?e("dark"):e("light")},u=()=>{e("system")},l=a(()=>o.value==="dark"),h=a(()=>o.value==="light"),f=a(()=>t.value==="system"),v=a(()=>{switch(t.value){case"light":return"Sun";case"dark":return"Moon";case"system":return"Monitor";default:return"Monitor"}}),y=a(()=>{switch(t.value){case"light":return"Светлая";case"dark":return"Темная";case"system":return"Системная";default:return"Системная"}});return{currentTheme:a(()=>t.value),activeTheme:o,systemPrefersDark:a(()=>n.value),isDark:l,isLight:h,isSystem:f,themeIcon:v,themeName:y,setTheme:e,toggleTheme:r,resetToSystem:u}};export{L as u};

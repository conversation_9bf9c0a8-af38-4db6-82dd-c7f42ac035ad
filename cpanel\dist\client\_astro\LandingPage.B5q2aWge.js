import{M as L}from"./index.CBuVmUry.js";import{_ as y}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as k,c as l,o,e as n,w as c,p as X,i as Q,U as g4,A as o4,F as _,r as D,g as F,a as u,b as M,l as m4,f as x,M as K,h as N,W as v4,j as c4,X as z4,P as M4,m as S4}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{n as z,r as v,c as b4,t as p,a5 as R4}from"./reactivity.esm-bundler.BQ12LWmY.js";import{c as _4}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{c as b}from"./createLucideIcon.NtN1-Ts2.js";import{S as y4}from"./search.DFOrFhbU.js";import{I as E4}from"./info.B6miOEHp.js";import{c as D4}from"./utils.BL5HZsed.js";import{T as $4}from"./triangle-alert.CP-lXbmj.js";import"./bundle-mjs.D6B6e0vX.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L4=b("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r4=b("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I4=b("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f4=b("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j4=b("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O4=b("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i4=b("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B4=b("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P4=b("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V4=b("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C4=b("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a4=b("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q4=b("factory",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M16 16h.01",key:"1f9h7w"}],["path",{d:"M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a.5.5 0 0 0-.769-.422l-4.462 2.844A.5.5 0 0 1 15 10.5v-2a.5.5 0 0 0-.769-.422L9.77 10.922A.5.5 0 0 1 9 10.5V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z",key:"1iv0i2"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T4=b("file-search",[["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M4.268 21a2 2 0 0 0 1.727 1H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3",key:"ms7g94"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}],["circle",{cx:"5",cy:"14",r:"3",key:"ufru5t"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H4=b("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p4=b("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h4=b("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N4=b("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x4=b("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n4=b("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s4=b("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G4=b("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w4=b("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l4=b("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z4=b("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U4=b("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W4=b("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F4=b("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),X4=k({__name:"AnimatedSection",props:{className:{default:""}},setup(d,{expose:t}){t();const i=d,e=v(),a=v(!1);let f=null;Q(()=>{g4(()=>{if(e.value&&e.value instanceof Element)try{f=new IntersectionObserver(([r])=>{r.isIntersecting&&(a.value=!0)},{threshold:.1,rootMargin:"-100px"}),f.observe(e.value)}catch(r){console.warn("IntersectionObserver failed, showing content immediately:",r),a.value=!0}else setTimeout(()=>{a.value=!0},100)})}),o4(()=>{f&&f.disconnect()});const s={props:i,sectionRef:e,isInView:a,get observer(){return f},set observer(r){f=r},get Motion(){return L}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}});function Y4(d,t,i,e,a,f){return o(),l("div",{ref:"sectionRef",class:z(i.className)},[n(e.Motion,{initial:{opacity:0,y:60},animate:e.isInView?{opacity:1,y:0}:{opacity:0,y:60},transition:{duration:.8,ease:[.25,.46,.45,.94]}},{default:c(()=>[X(d.$slots,"default")]),_:3},8,["animate"])],2)}const K4=y(X4,[["render",Y4]]),Q4=k({__name:"Ripple",setup(d,{expose:t}){t();const i=v([]);let e=0;const a=()=>({id:e++,x:Math.random()*100,y:Math.random()*100,size:Math.random()*100+50,delay:Math.random()*2,duration:Math.random()*3+2});let f=null;Q(()=>{for(let r=0;r<5;r++)i.value.push(a());f=setInterval(()=>{i.value.length<8||i.value.shift(),i.value.push(a())},2e3)}),o4(()=>{f&&clearInterval(f)});const s={ripples:i,get rippleId(){return e},set rippleId(r){e=r},generateRipple:a,get interval(){return f},set interval(r){f=r},get Motion(){return L}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}}),J4={class:"absolute inset-0 overflow-hidden rounded-2xl"};function u0(d,t,i,e,a,f){return o(),l("div",J4,[(o(!0),l(_,null,D(e.ripples,(s,r)=>(o(),F(e.Motion,{key:`${s.id}-${r}`,initial:{scale:0,opacity:.8},animate:{scale:1,opacity:0},transition:{duration:s.duration,delay:s.delay,ease:"easeOut"},class:"absolute rounded-full bg-blue-500/20",style:b4({left:`${s.x}%`,top:`${s.y}%`,width:`${s.size}px`,height:`${s.size}px`,transform:"translate(-50%, -50%)"})},null,8,["transition","style"]))),128))])}const e0=y(Q4,[["render",u0]]),t0=k({__name:"AISearchDemo",setup(d,{expose:t}){t();const i=v(!1),e=v(""),a=v(""),f=v(0),s=[{query:"Нужен сальник для гидроцилиндра Caterpillar 320D",response:"Анализирую запрос... Найдено 15 совместимых сальников для гидроцилиндра Caterpillar 320D. Показываю варианты с экономией до 45%."},{query:"Масляный фильтр для двигателя Cummins ISX15",response:"Обрабатываю... Найдено 8 аналогов масляного фильтра. Все варианты в наличии, совместимость 98-100%."}],r=[{icon:f4,title:"Понимание контекста",description:"ИИ анализирует техническое описание и находит точные аналоги"},{icon:h4,title:"Диалоговый режим",description:"Задавайте уточняющие вопросы для точного поиска"},{icon:l4,title:"Умная фильтрация",description:"Автоматический подбор по техническим характеристикам"},{icon:F4,title:"Мгновенный результат",description:"Поиск за секунды вместо часов навигации по каталогу"}];let h=null;const g=()=>{i.value=!i.value};Q(()=>{h=setInterval(()=>{const B=s[f.value];e.value=B.query,setTimeout(()=>{a.value=B.response},1500),setTimeout(()=>{f.value=(f.value+1)%s.length,e.value="",a.value=""},4e3)},6e3)}),o4(()=>{h&&clearInterval(h)});const w={isListening:i,searchQuery:e,aiResponse:a,step:f,demoSteps:s,features:r,get interval(){return h},set interval(B){h=B},toggleListening:g,get Motion(){return L},get Search(){return y4},get Brain(){return f4},get Mic(){return N4},get Bot(){return I4},get MessageSquare(){return h4},get Headphones(){return H4},get Wand2(){return W4},Ripple:e0};return Object.defineProperty(w,"__isScriptSetup",{enumerable:!1,value:!0}),w}}),s0={class:"relative"},i0={class:"relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-2xl p-6 shadow-2xl"},n0={class:"grid lg:grid-cols-2 gap-8"},o0={class:"space-y-3"},r0={class:"flex items-center gap-3 mb-3"},a0={class:"relative"},l0={class:"w-12 h-12 bg-zinc-800 rounded-xl flex items-center justify-center"},d0={class:"relative"},c0={class:"relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-lg p-4"},f0={class:"flex items-center gap-3 mb-4"},p0={class:"flex items-start gap-3"},h0={class:"text-base text-gray-100"},x0={class:"grid grid-cols-2 gap-4"},g0={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg p-4"},m0={class:"flex items-center gap-2 mb-2"},v0={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg p-4"},b0={class:"flex items-center gap-2 mb-2"},_0={class:"space-y-3"},y0={class:"text-2xl font-semibold text-white flex items-center gap-2"},E0={class:"space-y-4"},D0={class:"w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center flex-shrink-0"},B0={class:"font-medium text-white mb-1"},C0={class:"text-base text-gray-100"};function w0(d,t,i,e,a,f){return o(),l("div",s0,[t[9]||(t[9]=u("div",{class:"absolute inset-0 bg-zinc-900/5 rounded-2xl"},null,-1)),n(e.Ripple),u("div",i0,[u("div",n0,[u("div",o0,[u("div",r0,[u("div",a0,[u("div",l0,[n(e.Brain,{class:"w-6 h-6 text-white"})]),t[1]||(t[1]=u("div",{class:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse"},null,-1))]),t[2]||(t[2]=u("div",null,[u("h3",{class:"text-2xl font-semibold text-white"},"ИИ-Ассистент поиска запчастей"),u("p",{class:"text-base text-gray-100"},"Умный поиск с голосовым вводом")],-1))]),u("div",d0,[t[3]||(t[3]=u("div",{class:"absolute -inset-0.5 bg-blue-500 rounded-lg blur opacity-30"},null,-1)),u("div",c0,[u("div",f0,[n(e.Search,{class:"w-5 h-5 text-gray-300"}),m4(u("input",{type:"text",placeholder:"Опишите нужную запчасть или технику...","onUpdate:modelValue":t[0]||(t[0]=s=>e.searchQuery=s),class:"flex-1 bg-transparent text-white placeholder-gray-300 focus:outline-none"},null,512),[[_4,e.searchQuery]]),n(e.Motion,{onClick:e.toggleListening,class:z(`p-2 rounded-lg transition-all ${e.isListening?"bg-red-500 text-white animate-pulse":"bg-zinc-800 text-gray-300 hover:bg-zinc-700"}`),whileHover:{scale:1.05},whileTap:{scale:.95}},{default:c(()=>[n(e.Mic,{class:"w-4 h-4"})]),_:1},8,["class"])]),e.aiResponse?(o(),F(e.Motion,{key:0,initial:{opacity:0,y:20},animate:{opacity:1,y:0},class:"bg-zinc-800 border border-zinc-700 rounded-lg p-4"},{default:c(()=>[u("div",p0,[n(e.Bot,{class:"w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0"}),u("div",h0,p(e.aiResponse),1)])]),_:1})):M("",!0)])]),u("div",x0,[u("div",g0,[u("div",m0,[n(e.MessageSquare,{class:"w-4 h-4 text-blue-400"}),t[4]||(t[4]=u("span",{class:"text-base font-medium text-white"},"Текстовый ввод",-1))]),t[5]||(t[5]=u("p",{class:"text-sm text-gray-100"},"Опишите деталь естественным языком",-1))]),u("div",v0,[u("div",b0,[n(e.Headphones,{class:"w-4 h-4 text-blue-400"}),t[6]||(t[6]=u("span",{class:"text-base font-medium text-white"},"Голосовой ввод",-1))]),t[7]||(t[7]=u("p",{class:"text-sm text-gray-100"},"Говорите - ИИ поймет и найдет",-1))])])]),u("div",_0,[u("h4",y0,[n(e.Wand2,{class:"w-5 h-5 text-blue-400"}),t[8]||(t[8]=x(" Возможности ИИ-поиска "))]),u("div",E0,[(o(),l(_,null,D(e.features,(s,r)=>n(e.Motion,{key:r,initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{delay:r*.1},viewport:{once:!0},class:"flex items-start gap-4 p-4 bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg transition-all duration-300"},{default:c(()=>[u("div",D0,[(o(),F(K(s.icon),{class:"w-5 h-5 text-white"}))]),u("div",null,[u("h5",B0,p(s.title),1),u("p",C0,p(s.description),1)])]),_:2},1032,["transition"])),64))])])])])])}const F0=y(t0,[["render",w0]]),A0=k({__name:"TechnicalSchema",setup(d,{expose:t}){t();const i=v(null),e=v(""),a=[{id:1,original:"Corteco 12345-ABC",specs:"25×47×7mm, NBR, -40°C/+120°C",alternatives:["SKF 789-XYZ","Febi 456-DEF","NOK 321-GHI"],category:"Сальники",compatibility:98,savings:35},{id:2,original:"John Deere RE12345",specs:"Передаточное число 1:4.5, крутящий момент 850 Нм",alternatives:["Komatsu 708-1W-00151","Caterpillar 123-4567"],category:"Редукторы",compatibility:95,savings:45}],f=N(()=>a.find(h=>h.id===i.value)),r={activeConnection:i,searchQuery:e,parts:a,selectedPart:f,setActiveConnection:h=>{i.value=i.value===h?null:h},get Motion(){return L},get Search(){return y4},get Database(){return a4},get Target(){return l4},get CheckCircle(){return i4},get CheckCircle2(){return B4},get Info(){return E4},get TrendingUp(){return Z4}};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}}),k0={class:"relative"},z0={class:"relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-2xl p-6 shadow-2xl"},M0={class:"grid lg:grid-cols-2 gap-8"},S0={class:"space-y-3"},R0={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg"},$0={class:"p-6 border-b border-zinc-700"},L0={class:"flex items-center gap-3 mb-3"},I0={class:"w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center"},j0={class:"relative group"},O0={class:"relative"},P0={class:"p-6 space-y-3"},V0={class:"flex items-center justify-between mb-3"},q0={class:"font-mono text-base text-white font-medium"},T0={class:"bg-zinc-700 text-white text-sm border-0 px-2 py-1 rounded"},H0={class:"text-sm text-gray-100 mb-3"},N0={class:"flex items-center gap-4 text-sm"},G0={class:"text-green-400 flex items-center gap-1"},Z0={class:"text-blue-400 flex items-center gap-1"},U0={class:"space-y-3"},W0={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg"},X0={class:"p-6 border-b border-zinc-700"},Y0={class:"flex items-center gap-3 mb-3"},K0={class:"w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center"},Q0={class:"p-6"},J0={key:0,class:"space-y-4"},uu={class:"bg-zinc-800 backdrop-blur-xl rounded-xl p-4 border border-zinc-700"},eu={class:"text-base font-medium text-white mb-2 flex items-center gap-2"},tu={class:"font-mono text-blue-400 text-2xl"},su={class:"text-sm text-gray-100 mt-2"},iu={class:"space-y-3"},nu={class:"text-base font-medium text-white flex items-center gap-2"},ou={class:"flex-1"},ru={class:"font-mono text-base text-white"},au={class:"text-sm text-green-400 font-medium"},lu={class:"bg-zinc-800 border border-zinc-700 rounded-xl p-4 backdrop-blur-xl"},du={class:"flex items-center gap-2 text-blue-400 text-base font-medium mb-2"},cu={key:1,class:"py-12 text-center"};function fu(d,t,i,e,a,f){return o(),l("div",k0,[t[11]||(t[11]=u("div",{class:"absolute inset-0 bg-zinc-900/5 rounded-2xl"},null,-1)),u("div",z0,[u("div",M0,[u("div",S0,[u("div",R0,[u("div",$0,[u("div",L0,[u("div",I0,[n(e.Search,{class:"w-5 h-5 text-white"})]),t[1]||(t[1]=u("h3",{class:"text-2xl font-semibold text-white"},"Поиск взаимозаменяемых деталей",-1))]),u("div",j0,[t[2]||(t[2]=u("div",{class:"absolute -inset-0.5 bg-blue-500 rounded-lg blur opacity-30 group-hover:opacity-60 transition duration-300"},null,-1)),u("div",O0,[n(e.Search,{class:"absolute left-4 top-4 w-5 h-5 text-gray-300"}),m4(u("input",{type:"text",placeholder:"Введите артикул или OEM номер...","onUpdate:modelValue":t[0]||(t[0]=s=>e.searchQuery=s),class:"w-full pl-12 pr-4 py-4 bg-zinc-900/50 backdrop-blur-xl border border-zinc-700 rounded-lg text-white placeholder-gray-300 focus:border-blue-500/50 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all duration-300"},null,512),[[_4,e.searchQuery]])])])]),u("div",P0,[(o(),l(_,null,D(e.parts,s=>n(e.Motion,{key:s.id,class:z(`p-4 rounded-xl cursor-pointer transition-all duration-300 ${e.activeConnection===s.id?"bg-blue-500/20 border border-blue-500/50":"bg-zinc-800 backdrop-blur-xl border border-zinc-700 hover:border-zinc-600"}`),onClick:r=>e.setActiveConnection(s.id),whileHover:{scale:1.02},whileTap:{scale:.98}},{default:c(()=>[u("div",V0,[u("span",q0,p(s.original),1),u("div",T0,p(s.category),1)]),u("div",H0,p(s.specs),1),u("div",N0,[u("span",G0,[t[3]||(t[3]=u("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"},null,-1)),x(" Совместимость: "+p(s.compatibility)+"% ",1)]),u("span",Z0,[n(e.TrendingUp,{class:"w-3 h-3"}),x(" Экономия: до "+p(s.savings)+"% ",1)])])]),_:2},1032,["class","onClick"])),64))])])]),u("div",U0,[u("div",W0,[u("div",X0,[u("div",Y0,[u("div",K0,[n(e.Database,{class:"w-5 h-5 text-white"})]),t[4]||(t[4]=u("h3",{class:"text-2xl font-semibold text-white"},"Результаты анализа",-1))])]),u("div",Q0,[e.activeConnection?(o(),l("div",J0,[n(e.Motion,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},class:"space-y-4"},{default:c(()=>[u("div",uu,[u("div",eu,[n(e.Target,{class:"w-4 h-4 text-blue-400"}),t[5]||(t[5]=x(" Оригинальная деталь: "))]),u("div",tu,p(e.selectedPart?.original),1),u("div",su,p(e.selectedPart?.specs),1)]),u("div",iu,[u("div",nu,[n(e.CheckCircle,{class:"w-4 h-4 text-green-400"}),t[6]||(t[6]=x(" Найденные аналоги: "))]),(o(!0),l(_,null,D(e.selectedPart?.alternatives,(s,r)=>(o(),F(e.Motion,{key:r,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:r*.1},class:"flex items-center gap-3 p-3 bg-zinc-800 rounded-xl border border-zinc-700 backdrop-blur-xl"},{default:c(()=>[n(e.CheckCircle2,{class:"w-4 h-4 text-green-400 flex-shrink-0"}),u("div",ou,[u("div",ru,p(s),1),t[7]||(t[7]=u("div",{class:"text-sm text-gray-100"},"Физическая совместимость подтверждена",-1))]),u("div",au,"-"+p(e.selectedPart?.savings)+"%",1)]),_:2},1032,["transition"]))),128))]),u("div",lu,[u("div",du,[n(e.Info,{class:"w-4 h-4"}),t[8]||(t[8]=x(" Техническое заключение "))]),t[9]||(t[9]=u("div",{class:"text-sm text-gray-100"}," Все найденные аналоги имеют идентичные технические характеристики и могут использоваться как прямая замена без модификаций. ",-1))])]),_:1})])):(o(),l("div",cu,[n(e.Motion,{class:"w-16 h-16 bg-zinc-700 rounded-2xl flex items-center justify-center mx-auto mb-4"},{default:c(()=>[n(e.Database,{class:"w-8 h-8 text-white"})]),_:1}),t[10]||(t[10]=u("div",{class:"text-gray-300"},"Выберите деталь для анализа совместимости",-1))]))])])])])])])}const pu=y(A0,[["render",fu]]),hu=k({__name:"Circle",props:{className:{default:""},label:{default:""}},setup(d,{expose:t}){const i=d,e=v();t({$el:e});const a={props:i,circleRef:e,get cn(){return D4}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}}),xu={class:"flex flex-col items-center gap-2"},gu={key:0,class:"text-center"},mu={class:"text-white text-xs md:text-sm font-medium px-1.5 py-0.5 md:px-2 md:py-1 bg-zinc-800/80 rounded-lg border border-zinc-700"};function vu(d,t,i,e,a,f){return o(),l("div",xu,[u("div",{ref:"circleRef",class:z(e.cn("z-10 flex size-12 items-center justify-center rounded-full border-2 border-border bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]",i.className))},[X(d.$slots,"default")],2),i.label?(o(),l("div",gu,[u("div",mu,p(i.label),1)])):M("",!0)])}const bu=y(hu,[["render",vu]]),_u=k({__name:"AnimatedBeam",props:{className:{},containerRef:{},fromRef:{},toRef:{},curvature:{default:0},reverse:{type:Boolean,default:!1},pathColor:{default:"rgba(96,165,250,0.15)"},pathWidth:{default:2},pathOpacity:{default:.2},gradientStartColor:{default:"#60a5fa"},gradientStopColor:{default:"#a78bfa"},delay:{default:0},duration:{default:6},startXOffset:{default:0},startYOffset:{default:0},endXOffset:{default:0},endYOffset:{default:0}},setup(d,{expose:t}){t();const i=d,e=v(""),a=v({width:0,height:0}),f=v(null);function s(C){return C?typeof C.getBoundingClientRect=="function"?C:C.$el&&typeof C.$el.getBoundingClientRect=="function"?C.$el:C.value!==void 0?s(C.value):null:null}const r=`beam-gradient-${Math.random().toString(36).slice(2)}`,h=N(()=>`url(#${r})`),g={x1:"0%",y1:"0%",x2:"0%",y2:"0%"},w=N(()=>i.reverse?{x1:"90%;-10%",x2:"100%;0%"}:{x1:"10%;110%",x2:"0%;100%"});let B=null;const E=()=>{const C=s(i.containerRef)||f.value?.parentElement,j=s(i.fromRef),O=s(i.toRef);if(!C)return;const R=C.getBoundingClientRect(),G=Math.max(1,Math.round(R.width)),I=Math.max(1,Math.round(R.height));if(a.value={width:G,height:I},!j||!O)return;const P=j.getBoundingClientRect(),$=O.getBoundingClientRect(),V=P.left+P.width/2-R.left+(i.startXOffset||0),q=P.top+P.height/2-R.top+(i.startYOffset||0),Z=$.left+$.width/2-R.left+(i.endXOffset||0),J=$.top+$.height/2-R.top+(i.endYOffset||0),u4=q-(i.curvature||0);e.value=`M ${V},${q} Q ${(V+Z)/2},${u4} ${Z},${J}`};Q(()=>{E();const C=s(i.containerRef)||f.value?.parentElement;C&&(B=new ResizeObserver(()=>E()),B.observe(C));const j=s(i.fromRef),O=s(i.toRef);if(j)try{new ResizeObserver(()=>E()).observe(j)}catch{}if(O)try{new ResizeObserver(()=>E()).observe(O)}catch{}window.addEventListener("resize",E),window.addEventListener("scroll",E,!0),g4(()=>E());let R=20;const G=()=>{E(),!e.value&&R-- >0&&requestAnimationFrame(G)};requestAnimationFrame(G),setTimeout(E,150),setTimeout(E,350),setTimeout(E,700)}),v4(()=>{B&&B.disconnect(),window.removeEventListener("resize",E),window.removeEventListener("scroll",E,!0)}),c4(()=>[s(i.fromRef),s(i.toRef)],()=>E(),{flush:"post"}),c4(()=>s(i.containerRef),()=>E(),{flush:"post"});const Y={props:i,pathD:e,svgDimensions:a,svgRoot:f,resolveElement:s,gradientId:r,gradientUrl:h,gradient:g,gradientAnim:w,get resizeObserver(){return B},set resizeObserver(C){B=C},updatePath:E};return Object.defineProperty(Y,"__isScriptSetup",{enumerable:!1,value:!0}),Y}}),yu=["width","height","viewBox"],Eu=["x1","y1","x2","y2"],Du=["stop-color"],Bu=["stop-color"],Cu=["stop-color"],wu=["stop-color"],Fu=["values","dur","begin"],Au=["values","dur","begin"],ku=["d","stroke","stroke-width","stroke-opacity"],zu=["d","stroke-width","stroke"];function Mu(d,t,i,e,a,f){return o(),l("svg",{ref:"svgRoot",class:"pointer-events-none absolute left-0 top-0 w-full h-full transform-gpu z-20",width:e.svgDimensions.width,height:e.svgDimensions.height,xmlns:"http://www.w3.org/2000/svg",viewBox:`0 0 ${e.svgDimensions.width} ${e.svgDimensions.height}`},[u("defs",null,[u("linearGradient",{id:e.gradientId,gradientUnits:"userSpaceOnUse",x1:e.gradient.x1,y1:e.gradient.y1,x2:e.gradient.x2,y2:e.gradient.y2},[u("stop",{"stop-color":i.gradientStartColor,"stop-opacity":"0"},null,8,Du),u("stop",{"stop-color":i.gradientStartColor},null,8,Bu),u("stop",{offset:"32.5%","stop-color":i.gradientStopColor},null,8,Cu),u("stop",{offset:"100%","stop-color":i.gradientStopColor,"stop-opacity":"0"},null,8,wu),u("animate",{attributeName:"x1",values:e.gradientAnim.x1,dur:`${i.duration}s`,repeatCount:"indefinite",begin:`${i.delay}s`},null,8,Fu),u("animate",{attributeName:"x2",values:e.gradientAnim.x2,dur:`${i.duration}s`,repeatCount:"indefinite",begin:`${i.delay}s`},null,8,Au)],8,Eu)]),e.pathD?(o(),l("path",{key:0,d:e.pathD,stroke:i.pathColor,"stroke-width":i.pathWidth,"stroke-opacity":i.pathOpacity,"stroke-linecap":"round",fill:"none"},null,8,ku)):M("",!0),e.pathD?(o(),l("path",{key:1,d:e.pathD,"stroke-width":i.pathWidth,stroke:e.gradientUrl,"stroke-linecap":"round",fill:"none"},null,8,zu)):M("",!0)],8,yu)}const Su=y(_u,[["render",Mu]]),Ru={},$u={width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function Lu(d,t){return o(),l("svg",$u,t[0]||(t[0]=[u("path",{d:"M12 2L2 7L12 12L22 7L12 2Z",fill:"#FCD34D"},null,-1),u("path",{d:"M2 17L12 22L22 17",stroke:"#FCD34D","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),u("path",{d:"M2 12L12 17L22 12",stroke:"#FCD34D","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))}const Iu=y(Ru,[["render",Lu]]),ju={},Ou={width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function Pu(d,t){return o(),l("svg",Ou,t[0]||(t[0]=[u("path",{d:"M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20Z",fill:"#10B981"},null,-1),u("path",{d:"M12 6V18M6 12H18",stroke:"#10B981","stroke-width":"2","stroke-linecap":"round"},null,-1)]))}const Vu=y(ju,[["render",Pu]]),qu={},Tu={width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function Hu(d,t){return o(),l("svg",Tu,t[0]||(t[0]=[u("path",{d:"M3 3H21V21H3V3Z",fill:"#F59E0B"},null,-1),u("path",{d:"M8 8H16V16H8V8Z",fill:"white"},null,-1)]))}const Nu=y(qu,[["render",Hu]]),Gu={},Zu={width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function Uu(d,t){return o(),l("svg",Zu,t[0]||(t[0]=[u("circle",{cx:"12",cy:"12",r:"10",fill:"#3B82F6"},null,-1),u("path",{d:"M8 12L12 8L16 12L12 16L8 12Z",fill:"white"},null,-1)]))}const Wu=y(Gu,[["render",Uu]]),Xu={},Yu={width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function Ku(d,t){return o(),l("svg",Yu,t[0]||(t[0]=[u("path",{d:"M12 2L22 20H2L12 2Z",fill:"#8B5CF6"},null,-1),u("circle",{cx:"12",cy:"14",r:"3",fill:"white"},null,-1)]))}const Qu=y(Xu,[["render",Ku]]),Ju={},u3={width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function e3(d,t){return o(),l("svg",u3,t[0]||(t[0]=[u("path",{d:"M4 6H20M4 12H20M4 18H20",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),u("circle",{cx:"12",cy:"12",r:"10",stroke:"white","stroke-width":"2",fill:"none"},null,-1)]))}const t3=y(Ju,[["render",e3]]),s3=k({__name:"AnimatedBeamDemo",setup(d,{expose:t}){t();const i=v(null),e=v(null),a=v(null),f=v(null),s=v(null),r=v(null),h=v(null),g=v(null),w={containerRef:i,div1Ref:e,div2Ref:a,div3Ref:f,div4Ref:s,div5Ref:r,div6Ref:h,div7Ref:g,AnimatedBeam:Su,Circle:bu};return Object.defineProperty(w,"__isScriptSetup",{enumerable:!1,value:!0}),w}}),i3={class:"relative flex h-[500px] w-full items-center justify-center overflow-hidden p-10",ref:"containerRef"},n3={class:"relative z-30 flex size-full max-w-3xl flex-row items-stretch justify-between gap-10"},o3={class:"flex flex-col justify-center gap-2"},r3={ref:"div1Ref"},a3={ref:"div2Ref"},l3={ref:"div3Ref"},d3={ref:"div4Ref"},c3={ref:"div5Ref"},f3={class:"flex flex-col justify-center"},p3={ref:"div6Ref"},h3={class:"flex flex-col justify-center"},x3={ref:"div7Ref"};function g3(d,t,i,e,a,f){const s=M4("CatalogIcon");return o(),l(_,null,[u("div",i3,[t[6]||(t[6]=u("div",{class:"absolute inset-0 z-10 pointer-events-none"},null,-1)),u("div",n3,[u("div",o3,[u("div",r3,[n(e.Circle,{className:"bg-blue-100 border-blue-300"},{default:c(()=>[n(s)]),_:1})],512),u("div",a3,[n(e.Circle,null,{default:c(()=>t[0]||(t[0]=[u("svg",{width:"47",height:"65",viewBox:"0 0 47 65",xmlns:"http://www.w3.org/2000/svg"},[u("defs",null,[u("path",{id:"gd-path-1",d:"M29.375,0 L4.40625,0 C1.9828125,0 0,1.99431818 0,4.43181818 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,17.7272727 L29.375,0 Z"}),u("linearGradient",{id:"gd-linear-5",x1:"50.0053945%",y1:"8.58610612%",x2:"50.0053945%",y2:"100.013939%"},[u("stop",{"stop-color":"#1A237E","stop-opacity":"0.2",offset:"0%"}),u("stop",{"stop-color":"#1A237E","stop-opacity":"0.02",offset:"100%"})]),u("radialGradient",{id:"gd-radial-16",cx:"3.16804688%",cy:"2.71744318%",fx:"3.16804688%",fy:"2.71744318%",r:"161.248516%",gradientTransform:"translate(0.031680,0.027174),scale(1.000000,0.723077),translate(-0.031680,-0.027174)"},[u("stop",{"stop-color":"#FFFFFF","stop-opacity":"0.1",offset:"0%"}),u("stop",{"stop-color":"#FFFFFF","stop-opacity":"0",offset:"100%"})])]),u("g",{fill:"none","fill-rule":"evenodd"},[u("mask",{id:"gd-mask-2",fill:"white"},[u("use",{"xlink:href":"#gd-path-1"})]),u("path",{fill:"#4285F4","fill-rule":"nonzero",mask:"url(#gd-mask-2)",d:"M29.375,0 L4.40625,0 C1.9828125,0 0,1.99431818 0,4.43181818 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,17.7272727 L36.71875,10.3409091 L29.375,0 Z"}),u("mask",{id:"gd-mask-4",fill:"white"},[u("use",{"xlink:href":"#gd-path-1"})]),u("polygon",{fill:"url(#gd-linear-5)","fill-rule":"nonzero",mask:"url(#gd-mask-4)",points:"30.6638281 16.4309659 47 32.8582386 47 17.7272727"}),u("mask",{id:"gd-mask-7",fill:"white"},[u("use",{"xlink:href":"#gd-path-1"})]),u("path",{fill:"#F1F1F1","fill-rule":"nonzero",mask:"url(#gd-mask-7)",d:"M11.75,47.2727273 L35.25,47.2727273 L35.25,44.3181818 L11.75,44.3181818 L11.75,47.2727273 Z M11.75,53.1818182 L29.375,53.1818182 L29.375,50.2272727 L11.75,50.2272727 L11.75,53.1818182 Z M11.75,32.5 L11.75,35.4545455 L35.25,35.4545455 L35.25,32.5 L11.75,32.5 Z M11.75,41.3636364 L35.25,41.3636364 L35.25,38.4090909 L11.75,38.4090909 L11.75,41.3636364 Z"}),u("mask",{id:"gd-mask-9",fill:"white"},[u("use",{"xlink:href":"#gd-path-1"})]),u("g",{mask:"url(#gd-mask-9)"},[u("g",{transform:"translate(26.437500, -2.954545)"},[u("path",{fill:"#A1C2FA","fill-rule":"nonzero",d:"M2.9375,2.95454545 L2.9375,16.25 C2.9375,18.6985795 4.90929688,20.6818182 7.34375,20.6818182 L20.5625,20.6818182 L2.9375,2.95454545 Z"})])]),u("mask",{id:"gd-mask-13",fill:"white"},[u("use",{"xlink:href":"#gd-path-1"})]),u("path",{"fill-opacity":"0.2",fill:"#1A237E","fill-rule":"nonzero",mask:"url(#gd-mask-13)",d:"M42.59375,64.6306818 L4.40625,64.6306818 C1.9828125,64.6306818 0,62.6363636 0,60.1988636 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,60.1988636 C47,62.6363636 45.0171875,64.6306818 42.59375,64.6306818 Z"}),u("path",{d:"M29.375,0 L4.40625,0 C1.9828125,0 0,1.99431818 0,4.43181818 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,17.7272727 L29.375,0 Z",fill:"url(#gd-radial-16)","fill-rule":"nonzero"})])],-1)])),_:1,__:[0]})],512),u("div",l3,[n(e.Circle,null,{default:c(()=>t[1]||(t[1]=[u("svg",{width:"100",height:"100",viewBox:"0 0 175.216 175.552",xmlns:"http://www.w3.org/2000/svg"},[u("defs",null,[u("linearGradient",{id:"wa-b",x1:"85.915",x2:"86.535",y1:"32.567",y2:"137.092",gradientUnits:"userSpaceOnUse"},[u("stop",{offset:"0","stop-color":"#57d163"}),u("stop",{offset:"1","stop-color":"#23b33a"})]),u("filter",{id:"wa-a",width:"1.115",height:"1.114",x:"-.057",y:"-.057","color-interpolation-filters":"sRGB"},[u("feGaussianBlur",{stdDeviation:"3.531"})])]),u("path",{d:"m54.532 138.45 2.235 1.324c9.387 5.571 20.15 8.518 31.126 8.523h.023c33.707 0 61.139-27.426 61.153-61.135.006-16.335-6.349-31.696-17.895-43.251A60.75 60.75 0 0 0 87.94 25.983c-33.733 0-61.166 27.423-61.178 61.13a60.98 60.98 0 0 0 9.349 32.535l1.455 2.312-6.179 22.558zm-40.811 23.544L24.16 123.88c-6.438-11.154-9.825-23.808-9.821-36.772.017-40.556 33.021-73.55 73.578-73.55 19.681.01 38.154 7.669 52.047 21.572s21.537 32.383 21.53 52.037c-.018 40.553-33.027 73.553-73.578 73.553h-.032c-12.313-.005-24.412-3.094-35.159-8.954zm0 0",fill:"#b3b3b3",filter:"url(#wa-a)"}),u("path",{d:"m12.966 161.238 10.439-38.114a73.42 73.42 0 0 1-9.821-36.772c.017-40.556 33.021-73.55 73.578-73.55 19.681.01 38.154 7.669 52.047 21.572s21.537 32.383 21.53 52.037c-.018 40.553-33.027 73.553-73.578 73.553h-.032c-12.313-.005-24.412-3.094-35.159-8.954z",fill:"#ffffff"}),u("path",{d:"M87.184 25.227c-33.733 0-61.166 27.423-61.178 61.13a60.98 60.98 0 0 0 9.349 32.535l1.455 2.312-6.179 22.559 23.146-6.069 2.235 1.324c9.387 5.571 20.15 8.518 31.126 8.524h.023c33.707 0 61.14-27.426 61.153-61.135a60.75 60.75 0 0 0-17.895-43.251 60.75 60.75 0 0 0-43.235-17.929z",fill:"url(#wa-b)"}),u("path",{d:"M68.772 55.603c-1.378-3.061-2.828-3.123-4.137-3.176l-3.524-.043c-1.226 0-3.218.46-4.902 2.3s-6.435 6.287-6.435 15.332 6.588 17.785 7.506 19.013 12.718 20.381 31.405 27.75c15.529 6.124 18.689 4.906 22.061 4.6s10.877-4.447 12.408-8.74 1.532-7.971 1.073-8.74-1.685-1.226-3.525-2.146-10.877-5.367-12.562-5.981-2.91-.919-4.137.921-4.746 5.979-5.819 7.206-2.144 1.381-3.984.462-7.76-2.861-14.784-9.124c-5.465-4.873-9.154-10.891-10.228-12.73s-.114-2.835.808-3.751c.825-.824 1.838-2.147 2.759-3.22s1.224-1.84 1.836-3.065.307-2.301-.153-3.22-4.032-10.011-5.666-13.647",fill:"#ffffff","fill-rule":"evenodd"})],-1)])),_:1,__:[1]})],512),u("div",d3,[n(e.Circle,null,{default:c(()=>t[2]||(t[2]=[u("svg",{width:"100",height:"100",viewBox:"0 0 48 48",xmlns:"http://www.w3.org/2000/svg"},[u("radialGradient",{id:"msg-grad",cx:"11.087",cy:"7.022",r:"47.612",gradientTransform:"matrix(1 0 0 -1 0 50)",gradientUnits:"userSpaceOnUse"},[u("stop",{offset:"0","stop-color":"#1292ff"}),u("stop",{offset:".079","stop-color":"#2982ff"}),u("stop",{offset:".23","stop-color":"#4e69ff"}),u("stop",{offset:".351","stop-color":"#6559ff"}),u("stop",{offset:".428","stop-color":"#6d53ff"}),u("stop",{offset:".754","stop-color":"#df47aa"}),u("stop",{offset:".946","stop-color":"#ff6257"})]),u("path",{fill:"url(#msg-grad)",d:"M44,23.5C44,34.27,35.05,43,24,43c-1.651,0-3.25-0.194-4.784-0.564c-0.465-0.112-0.951-0.069-1.379,0.145L13.46,44.77C12.33,45.335,11,44.513,11,43.249v-4.025c0-0.575-0.257-1.111-0.681-1.499C6.425,34.165,4,29.11,4,23.5C4,12.73,12.95,4,24,4S44,12.73,44,23.5z"}),u("path",{fill:"#ffffff",d:"M34.394,18.501l-5.7,4.22c-0.61,0.46-1.44,0.46-2.04,0.01L22.68,19.74c-1.68-1.25-4.06-0.82-5.19,0.94l-1.21,1.89l-4.11,6.68c-0.6,0.94,0.55,2.01,1.44,1.34l5.7-4.22c0.61-0.46,1.44-0.46,2.04-0.01l3.974,2.991c1.68,1.25,4.06,0.82,5.19-0.94l1.21-1.89l4.11-6.68C36.434,18.901,35.284,17.831,34.394,18.501z"})],-1)])),_:1,__:[2]})],512),u("div",c3,[n(e.Circle,null,{default:c(()=>t[3]||(t[3]=[u("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[u("path",{d:"M6.017 4.313l55.333 -4.087c6.797 -0.583 8.543 -0.19 12.817 2.917l17.663 12.443c2.913 2.14 3.883 2.723 3.883 5.053v68.243c0 4.277 -1.553 6.807 -6.99 7.193L24.467 99.967c-4.08 0.193 -6.023 -0.39 -8.16 -3.113L3.3 79.94c-2.333 -3.113 -3.3 -5.443 -3.3 -8.167V11.113c0 -3.497 1.553 -6.413 6.017 -6.8z",fill:"#ffffff"}),u("path",{d:"M61.35 0.227l-55.333 4.087C1.553 4.7 0 7.617 0 11.113v60.66c0 2.723 0.967 5.053 3.3 8.167l13.007 16.913c2.137 2.723 4.08 3.307 8.16 3.113l64.257 -3.89c5.433 -0.387 6.99 -2.917 6.99 -7.193V20.64c0 -2.21 -0.873 -2.847 -3.443 -4.733L74.167 3.143c-4.273 -3.107 -6.02 -3.5 -12.817 -2.917zM25.92 19.523c-5.247 0.353 -6.437 0.433 -9.417 -1.99L8.927 11.507c-0.77 -0.78 -0.383 -1.753 1.557 -1.947l53.193 -3.887c4.467 -0.39 6.793 1.167 8.54 2.527l9.123 6.61c0.39 0.197 1.36 1.36 0.193 1.36l-54.933 3.307 -0.68 0.047zM19.803 88.3V30.367c0 -2.53 0.777 -3.697 3.103 -3.893L86 22.78c2.14 -0.193 3.107 1.167 3.107 3.693v57.547c0 2.53 -0.39 4.67 -3.883 4.863l-60.377 3.5c-3.493 0.193 -5.043 -0.97 -5.043 -4.083zm59.6 -54.827c0.387 1.75 0 3.5 -1.75 3.7l-2.91 0.577v42.773c-2.527 1.36 -4.853 2.137 -6.797 2.137 -3.107 0 -3.883 -0.973 -6.21 -3.887l-19.03 -29.94v28.967l6.02 1.363s0 3.5 -4.857 3.5l-13.39 0.777c-0.39 -0.78 0 -2.723 1.357 -3.11l3.497 -0.97v-38.3L30.48 40.667c-0.39 -1.75 0.58 -4.277 3.3 -4.473l14.367 -0.967 19.8 30.327v-26.83l-5.047 -0.58c-0.39 -2.143 1.163 -3.7 3.103 -3.89l13.4 -0.78z",fill:"#000000","fill-rule":"evenodd","clip-rule":"evenodd"})],-1)])),_:1,__:[3]})],512)]),u("div",f3,[u("div",p3,[n(e.Circle,{className:"size-16"},{default:c(()=>t[4]||(t[4]=[u("svg",{width:"100",height:"100",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[u("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"})],-1)])),_:1,__:[4]})],512)]),u("div",h3,[u("div",x3,[n(e.Circle,null,{default:c(()=>t[5]||(t[5]=[u("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"#000000","stroke-width":"2",xmlns:"http://www.w3.org/2000/svg"},[u("path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"}),u("circle",{cx:"12",cy:"7",r:"4"})],-1)])),_:1,__:[5]})],512)])]),n(e.AnimatedBeam,{containerRef:e.containerRef,fromRef:e.div1Ref,toRef:e.div6Ref,pathColor:"#ff00ff",pathOpacity:1,pathWidth:4,curvature:60},null,8,["containerRef","fromRef","toRef"]),n(e.AnimatedBeam,{containerRef:e.containerRef,fromRef:e.div2Ref,toRef:e.div6Ref,pathColor:"#ff00ff",pathOpacity:1,pathWidth:4,curvature:60},null,8,["containerRef","fromRef","toRef"]),n(e.AnimatedBeam,{containerRef:e.containerRef,fromRef:e.div3Ref,toRef:e.div6Ref,pathColor:"#ff00ff",pathOpacity:1,pathWidth:4,curvature:60},null,8,["containerRef","fromRef","toRef"]),n(e.AnimatedBeam,{containerRef:e.containerRef,fromRef:e.div4Ref,toRef:e.div6Ref,pathColor:"#ff00ff",pathOpacity:1,pathWidth:4,curvature:60},null,8,["containerRef","fromRef","toRef"]),n(e.AnimatedBeam,{containerRef:e.containerRef,fromRef:e.div5Ref,toRef:e.div6Ref,pathColor:"#ff00ff",pathOpacity:1,pathWidth:4,curvature:60},null,8,["containerRef","fromRef","toRef"]),n(e.AnimatedBeam,{containerRef:e.containerRef,fromRef:e.div6Ref,toRef:e.div7Ref,pathColor:"#ff00ff",pathOpacity:1,pathWidth:4,curvature:60},null,8,["containerRef","fromRef","toRef"])],512),t[7]||(t[7]=z4('<div class="absolute bottom-4 left-4 right-4"><div class="bg-zinc-900/80 backdrop-blur-xl border border-zinc-800 rounded-xl p-4 md:p-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6"><div><h4 class="text-white font-semibold mb-1 md:mb-2">Глобальная сеть</h4><p class="text-gray-100 text-xs md:text-sm">Производители, интеграторы и заказчики объединены единой моделью данных и связями совместимости.</p></div><div><h4 class="text-white font-semibold mb-1 md:mb-2">Стандартизированные атрибуты</h4><p class="text-gray-100 text-xs md:text-sm">Единицы измерения, синонимы и классификаторы обеспечивают точный подбор аналогов.</p></div><div><h4 class="text-white font-semibold mb-1 md:mb-2">Прозрачные интеграции</h4><p class="text-gray-100 text-xs md:text-sm">API для ERP/CRM и складских систем. Отслеживание происхождения данных и аудит.</p></div></div></div>',1))],64)}const m3=y(s3,[["render",g3]]),v3=k({__name:"ExamplesSection",setup(d,{expose:t}){t();const e={examplesData:[{title:"Сальники радиальные",original:"Corteco 12345-ABC",originalSpecs:{dimensions:"25×47×7mm",material:"NBR 70 Shore A",temperature:"-40°C до +120°C",speed:"до 15 м/с",standard:"DIN 3760"},compatible:[{part:"SKF 789-XYZ",match:"98%",price:"-35%",availability:"В наличии",specs:"25×47×7mm, NBR 70 Shore A, -40°C/+120°C"},{part:"Febi 456-DEF",match:"96%",price:"-28%",availability:"2-3 дня",specs:"25×47×7mm, NBR 72 Shore A, -35°C/+125°C"},{part:"NOK 321-GHI",match:"99%",price:"-42%",availability:"Под заказ",specs:"25×47×7mm, NBR 70 Shore A, -40°C/+120°C"}],savings:"до 42%",description:"Радиальные сальники для валов с идентичными размерами и материалами. Все аналоги соответствуют стандарту DIN 3760 и имеют подтвержденную взаимозаменяемость.",technicalNote:"Различия в твердости материала (±2 Shore A) не влияют на эксплуатационные характеристики при стандартных условиях работы."},{title:"Редукторы планетарные",original:"John Deere RE12345",originalSpecs:{ratio:"1:4.5",torque:"850 Нм",input:"1800 об/мин",efficiency:"96%",mounting:"Фланец SAE B"},compatible:[{part:"Komatsu 708-1W-00151",match:"95%",price:"-45%",availability:"В наличии",specs:"1:4.5, 850 Нм, 1800 об/мин, 95% КПД"},{part:"Caterpillar 123-4567",match:"93%",price:"-38%",availability:"1-2 дня",specs:"1:4.6, 820 Нм, 1800 об/мин, 94% КПД"}],savings:"до 45%",description:"Планетарные редукторы для мобильной техники с совместимыми характеристиками и креплениями. Физически один агрегат, производимый под разными брендами.",technicalNote:"Незначительные отличия в передаточном числе (±0.1) и крутящем моменте (±30 Нм) находятся в пределах допустимых отклонений."}],get Motion(){return L},get Settings(){return n4},get Target(){return l4},get CheckCircle(){return i4},get Info(){return E4}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),b3={class:"space-y-16"},_3={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl overflow-hidden"},y3={class:"bg-zinc-800 p-6"},E3={class:"flex items-center justify-between"},D3={class:"flex items-center gap-4"},B3={class:"w-12 h-12 bg-zinc-700 rounded-xl flex items-center justify-center"},C3={class:"text-3xl font-bold text-white"},w3={class:"text-gray-100"},F3={class:"bg-green-500 text-white border-0 px-4 py-2 text-lg rounded"},A3={class:"grid lg:grid-cols-2 gap-0"},k3={class:"p-6 bg-zinc-900 backdrop-blur-xl border-r border-zinc-700"},z3={class:"font-semibold text-white mb-4 flex items-center gap-3"},M3={class:"w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center"},S3={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-6 mb-6"},R3={class:"font-mono text-2xl font-bold text-white mb-4"},$3={class:"grid grid-cols-2 gap-4 text-sm"},L3={class:"text-gray-300 capitalize text-sm mb-1"},I3={class:"font-medium text-white"},j3={key:0,class:"bg-zinc-800 border border-zinc-700 rounded-xl p-4 backdrop-blur-xl"},O3={class:"flex items-start gap-3"},P3={class:"text-sm"},V3={class:"text-gray-100"},q3={class:"p-6 bg-zinc-900 backdrop-blur-xl"},T3={class:"font-semibold text-white mb-4 flex items-center gap-3"},H3={class:"w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center"},N3={class:"space-y-4"},G3={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4 hover:border-zinc-600 transition-all duration-300"},Z3={class:"flex items-center justify-between mb-3"},U3={class:"font-mono font-bold text-white"},W3={class:"flex items-center gap-2"},X3={class:"bg-green-500/20 text-green-400 border border-green-500/30 px-2 py-1 rounded text-xs"},Y3={class:"bg-blue-500 text-white border-0 px-2 py-1 rounded text-xs"},K3={class:"text-sm text-gray-100 mb-3"},Q3={class:"flex items-center justify-between text-sm"};function J3(d,t,i,e,a,f){return o(),l("div",b3,[(o(),l(_,null,D(e.examplesData,(s,r)=>n(e.Motion,{key:r,initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:r*.2},viewport:{once:!0}},{default:c(()=>[u("div",_3,[u("div",y3,[u("div",E3,[u("div",D3,[u("div",B3,[n(e.Settings,{class:"w-6 h-6 text-white"})]),u("div",null,[u("h3",C3,p(s.title),1),u("p",w3,p(s.description),1)])]),u("div",F3," Экономия "+p(s.savings),1)])]),u("div",A3,[u("div",k3,[u("h4",z3,[u("div",M3,[n(e.Target,{class:"w-4 h-4 text-white"})]),t[0]||(t[0]=x(" Оригинальная деталь "))]),u("div",S3,[u("div",R3,p(s.original),1),u("div",$3,[(o(!0),l(_,null,D(Object.entries(s.originalSpecs),([h,g],w)=>(o(),l("div",{key:w,class:"bg-zinc-900 rounded-lg p-3"},[u("div",L3,p(h)+":",1),u("div",I3,p(g),1)]))),128))])]),s.technicalNote?(o(),l("div",j3,[u("div",O3,[n(e.Info,{class:"w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0"}),u("div",P3,[t[1]||(t[1]=u("div",{class:"font-medium text-blue-400 mb-2"},"Техническое примечание:",-1)),u("div",V3,p(s.technicalNote),1)])])])):M("",!0)]),u("div",q3,[u("h4",T3,[u("div",H3,[n(e.CheckCircle,{class:"w-4 h-4 text-white"})]),t[2]||(t[2]=x(" Совместимые аналоги "))]),u("div",N3,[(o(!0),l(_,null,D(s.compatible,(h,g)=>(o(),F(e.Motion,{key:g,initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:g*.1},viewport:{once:!0}},{default:c(()=>[u("div",G3,[u("div",Z3,[u("div",U3,p(h.part),1),u("div",W3,[u("div",X3,p(h.match)+" совместимость ",1),u("div",Y3,p(h.price),1)])]),u("div",K3,p(h.specs),1),u("div",Q3,[t[3]||(t[3]=u("span",{class:"text-gray-300"},"Наличие:",-1)),u("span",{class:z(`font-medium ${h.availability==="В наличии"?"text-green-400":h.availability.includes("дня")?"text-yellow-400":"text-gray-400"}`)},p(h.availability),3)])])]),_:2},1032,["transition"]))),128))])])])])]),_:2},1032,["transition"])),64))])}const ue=y(v3,[["render",J3]]),ee=k({__name:"Card",props:{variant:{default:"default"},hover:{type:Boolean,default:!0}},setup(d,{expose:t}){t();const i=d,e=N(()=>{const f="bg-[--color-card] border border-[--color-border] rounded-[--radius-lg] overflow-hidden transition-all duration-300 [box-shadow:var(--shadow-md)]",s={default:"",elevated:"[box-shadow:var(--shadow-lg)]",outlined:"border-2"},r=i.hover?"hover:bg-[--p-content-hover-background] hover:[box-shadow:var(--shadow-xl)]":"";return`${f} ${s[i.variant]} ${r}`}),a={props:i,cardClasses:e,get Motion(){return L}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}}),te={key:0,class:"p-6 border-b border-[--color-border]"},se={class:"p-6 text-[--color-foreground]"},ie={key:1,class:"p-6 border-t border-[--color-border]"};function ne(d,t,i,e,a,f){return o(),F(e.Motion,{whileHover:{y:-5,scale:1.02},transition:{type:"spring",stiffness:300,damping:20},class:z(e.cardClasses)},{default:c(()=>[d.$slots.header?(o(),l("div",te,[X(d.$slots,"header")])):M("",!0),u("div",se,[X(d.$slots,"default")]),d.$slots.footer?(o(),l("div",ie,[X(d.$slots,"footer")])):M("",!0)]),_:3},8,["class"])}const A4=y(ee,[["render",ne]]),oe=k({__name:"Button",props:{variant:{default:"primary"},size:{default:"md"},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["click"],setup(d,{expose:t}){t();const i=d,e=N(()=>{const f="inline-flex items-center justify-center font-medium transition-all duration-300 rounded-[--radius-md] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[--color-ring] focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",s={primary:"bg-[--color-primary] hover:bg-[--color-primary-hover] active:bg-[--color-primary-active] text-[--color-primary-foreground] [box-shadow:var(--shadow-md)] hover:[box-shadow:var(--shadow-lg)]",secondary:"bg-[--color-card] text-[--color-foreground] border border-[--color-border] hover:bg-[--p-content-hover-background]",outline:"bg-transparent border border-[--color-border] text-[--color-foreground] hover:bg-[--p-content-hover-background]",ghost:"bg-transparent text-[--color-foreground] hover:bg-[--p-content-hover-background]"},r={sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"};return`${f} ${s[i.variant]} ${r[i.size]}`}),a={props:i,buttonClasses:e,get Motion(){return L}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}}),re=["disabled"],ae={key:0,class:"mr-2"};function le(d,t,i,e,a,f){return o(),F(e.Motion,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17}},{default:c(()=>[u("button",S4({class:e.buttonClasses,disabled:i.disabled},d.$attrs,{onClick:t[0]||(t[0]=s=>d.$emit("click",s))}),[i.loading?(o(),l("span",ae,t[1]||(t[1]=[u("svg",{class:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24"},[u("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),u("path",{class:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):M("",!0),X(d.$slots,"default")],16,re)]),_:3})}const d4=y(oe,[["render",le]]),de=k({__name:"CatalogSection",setup(d,{expose:t}){t();const e={catalogSectionsData:[{id:"seals",name:"Сальники и уплотнения",icon:n4,active:!0,description:"Комплексная база данных сальников, манжет и уплотнительных элементов",detailedInfo:{totalParts:"52,847",manufacturers:"247",categories:["Радиальные сальники (ГОСТ 8752-79, DIN 3760)","Манжеты гидроцилиндров (ГОСТ 14896-84)","V-образные манжеты (ГОСТ 22704-77)","Уплотнения поршневые и штоковые","O-кольца (ГОСТ 9833-73, ISO 3601)","Грязесъемники и направляющие кольца"],specifications:["Диаметры: от 6мм до 2000мм","Материалы: NBR, FKM, PTFE, PU, EPDM","Температурный диапазон: -60°C до +300°C","Давление: до 700 бар"],applications:["Гидравлические системы","Пневматические системы","Автомобильная промышленность","Сельскохозяйственная техника","Строительная техника","Промышленное оборудование"]}},{id:"filters",name:"Фильтрующие элементы",icon:a4,active:!1,description:"Масляные, воздушные, топливные и гидравлические фильтры",detailedInfo:{totalParts:"Скоро",manufacturers:"150+",categories:["Масляные фильтры двигателей","Воздушные фильтры и элементы","Топливные фильтры и сепараторы","Гидравлические фильтры","Салонные фильтры","Сепараторы масла и воздуха"],specifications:["Степень фильтрации: от 1 до 200 микрон","Рабочее давление: до 350 бар","Температурный режим: -40°C до +150°C","Типы соединений: резьбовые, фланцевые, байонетные"],applications:["Двигатели внутреннего сгорания","Гидравлические системы","Компрессорное оборудование","Системы вентиляции"]}}],get Motion(){return L},get Settings(){return n4},get Clock(){return V4},get Cpu(){return C4},get ChevronRight(){return O4},get Factory(){return q4},get Building2(){return j4},get ArrowRight(){return r4},Card:A4,Button:d4};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),ce={class:"space-y-12"},fe={class:"grid lg:grid-cols-3 gap-0"},pe={class:"p-6 bg-zinc-900"},he={class:"flex items-center gap-4 mb-3"},xe={class:"text-3xl font-bold text-white"},ge={key:0,class:"flex items-center gap-2 mt-2"},me={class:"text-lg text-gray-100 mb-4"},ve={class:"grid grid-cols-2 gap-6"},be={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4"},_e={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4"},ye={key:0,class:"mt-4 p-4 bg-zinc-800 border border-zinc-700 rounded-xl backdrop-blur-xl"},Ee={class:"flex items-center gap-2 text-yellow-400 text-sm font-medium"},De={class:"p-6 bg-zinc-900 border-l border-zinc-700"},Be={class:"font-semibold text-white mb-4 flex items-center gap-3"},Ce={class:"w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center"},we={class:"space-y-3 mb-4"},Fe={class:"bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4"},Ae={class:"font-medium text-white mb-3 flex items-center gap-2"},ke={class:"space-y-2"},ze={class:"p-6 bg-zinc-900 border-l border-zinc-700"},Me={class:"font-semibold text-white mb-4 flex items-center gap-3"},Se={class:"w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center"},Re={class:"space-y-4 mb-4"},$e={class:"flex items-center gap-4 p-4 bg-zinc-800 backdrop-blur-xl rounded-xl border border-zinc-700 hover:border-zinc-600 transition-all duration-300"};function Le(d,t,i,e,a,f){return o(),l("div",ce,[(o(),l(_,null,D(e.catalogSectionsData,(s,r)=>n(e.Motion,{key:s.id,initial:{opacity:0,x:r%2===0?-60:60},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:r*.1},viewport:{once:!0}},{default:c(()=>[n(e.Card,{variant:"elevated",class:z(`${s.active?"":"opacity-75"}`)},{default:c(()=>[u("div",fe,[u("div",pe,[u("div",he,[u("div",{class:z(`w-16 h-16 rounded-2xl flex items-center justify-center ${s.active?"bg-blue-500":"bg-zinc-700"}`)},[(o(),F(K(s.icon),{class:"w-8 h-8 text-white"}))],2),u("div",null,[u("h3",xe,p(s.name),1),s.active?(o(),l("div",ge,t[0]||(t[0]=[u("div",{class:"w-2 h-2 bg-green-400 rounded-full"},null,-1),u("span",{class:"text-green-400 text-sm font-medium"},"Активен",-1)]))):M("",!0)])]),u("p",me,p(s.description),1),u("div",ve,[u("div",be,[u("div",{class:z(`text-3xl font-bold mb-1 ${s.active?"text-blue-400":"text-gray-400"}`)},p(s.detailedInfo.totalParts),3),t[1]||(t[1]=u("div",{class:"text-sm text-gray-100"},"Позиций в каталоге",-1))]),u("div",_e,[u("div",{class:z(`text-3xl font-bold mb-1 ${s.active?"text-green-400":"text-gray-400"}`)},p(s.detailedInfo.manufacturers),3),t[2]||(t[2]=u("div",{class:"text-sm text-gray-100"},"Производителей",-1))])]),s.active?M("",!0):(o(),l("div",ye,[u("div",Ee,[n(e.Clock,{class:"w-4 h-4"}),t[3]||(t[3]=x(" Раздел появится в ближайшее время "))])]))]),u("div",De,[u("h4",Be,[u("div",Ce,[n(e.Cpu,{class:"w-4 h-4 text-white"})]),t[4]||(t[4]=x(" Категории деталей "))]),u("div",we,[(o(!0),l(_,null,D(s.detailedInfo.categories,(h,g)=>(o(),F(e.Motion,{key:g,initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{delay:g*.1},viewport:{once:!0},class:"flex items-center gap-3 text-sm group"},{default:c(()=>[n(e.ChevronRight,{class:"w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors"}),u("span",{class:z(`${s.active?"text-gray-300":"text-gray-500"} group-hover:text-white transition-colors`)},p(h),3)]),_:2},1032,["transition"]))),128))]),u("div",Fe,[u("h5",Ae,[n(e.Settings,{class:"w-4 h-4 text-blue-400"}),t[5]||(t[5]=x(" Технические характеристики: "))]),u("div",ke,[(o(!0),l(_,null,D(s.detailedInfo.specifications,(h,g)=>(o(),l("div",{key:g,class:"text-sm text-gray-100 flex items-center gap-2"},[t[6]||(t[6]=u("div",{class:"w-1 h-1 bg-blue-400 rounded-full"},null,-1)),x(" "+p(h),1)]))),128))])])]),u("div",ze,[u("h4",Me,[u("div",Se,[n(e.Factory,{class:"w-4 h-4 text-white"})]),t[7]||(t[7]=x(" Области применения "))]),u("div",Re,[(o(!0),l(_,null,D(s.detailedInfo.applications,(h,g)=>(o(),F(e.Motion,{key:g,initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:g*.1},viewport:{once:!0}},{default:c(()=>[u("div",$e,[n(e.Building2,{class:"w-5 h-5 text-gray-400"}),u("span",{class:z(`text-sm ${s.active?"text-gray-300":"text-gray-500"}`)},p(h),3)])]),_:2},1032,["transition"]))),128))]),s.active?(o(),F(e.Button,{key:0,variant:"primary",class:"w-full flex items-center justify-center gap-2"},{default:c(()=>[t[8]||(t[8]=x(" Перейти к каталогу ")),n(e.ArrowRight,{class:"w-4 h-4"})]),_:1,__:[8]})):M("",!0)])])]),_:2},1032,["class"])]),_:2},1032,["initial","transition"])),64))])}const Ie=y(de,[["render",Le]]),je=k({__name:"PricingSection",setup(d,{expose:t}){t();const e={pricingPlansData:[{name:"Базовый",price:"4,990",period:"мес",description:"Для небольших мастерских и сервисных центров",features:["До 2,000 запросов в месяц","Доступ к разделу «Сальники»","Базовые технические характеристики","Экспорт результатов в Excel/PDF","Email поддержка (48 часов)","Мобильное приложение"],limitations:["Ограниченный доступ к техническим чертежам","Без API интеграции","Стандартные отчеты"],popular:!1},{name:"Профессиональный",price:"12,990",period:"мес",description:"Для средних предприятий и дилерских центров",features:["До 15,000 запросов в месяц","Все активные разделы каталога","Полные технические характеристики","3D модели и чертежи деталей","REST API для интеграции","Приоритетная поддержка (24 часа)","Расширенная аналитика и отчеты","Персональный кабинет с историей","Уведомления о новых аналогах"],limitations:["Ограничения по количеству API вызовов","Стандартная интеграция"],popular:!0},{name:"Корпоративный",price:"29,990",period:"мес",description:"Для крупных производственных предприятий",features:["Безлимитные запросы и API вызовы","Все разделы + приоритетный доступ к новым","Полная техническая документация","CAD файлы и 3D модели","Кастомная API интеграция","Персональный технический менеджер","SLA 99.9% доступности","Белый лейбл решения","Интеграция с ERP системами","Обучение персонала","Техническая поддержка 24/7"],limitations:[],popular:!1}],get Motion(){return L},get Star(){return w4},get Sparkles(){return G4},get CheckCircle(){return i4},get CheckCircle2(){return B4},get AlertTriangle(){return $4},get ArrowRight(){return r4},Card:A4,Button:d4};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),Oe={class:"grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto"},Pe={class:"relative h-full"},Ve={class:"bg-blue-600 text-white px-6 py-3 rounded-full text-sm font-medium flex items-center gap-2"},qe={class:"text-center mb-8"},Te={class:"text-3xl font-bold text-white mb-3"},He={class:"text-gray-100 mb-6"},Ne={class:"text-center"},Ge={class:"text-5xl font-bold mb-2 text-blue-400"},Ze={class:"text-gray-500"},Ue={class:"flex-1 space-y-6"},We={class:"font-semibold text-white mb-4 flex items-center gap-2"},Xe={class:"space-y-3"},Ye={class:"text-gray-100"},Ke={key:0},Qe={class:"font-semibold text-white mb-4 flex items-center gap-2"},Je={class:"space-y-3"},ut={class:"text-gray-300"};function et(d,t,i,e,a,f){return o(),l("div",Oe,[(o(),l(_,null,D(e.pricingPlansData,(s,r)=>n(e.Motion,{key:r,initial:{opacity:0,y:60},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:r*.1},viewport:{once:!0}},{default:c(()=>[u("div",Pe,[s.popular?(o(),F(e.Motion,{key:0,initial:{opacity:0,y:-20},animate:{opacity:1,y:0},class:"absolute -top-6 left-1/2 transform -translate-x-1/2 z-10"},{default:c(()=>[u("div",Ve,[n(e.Star,{class:"w-4 h-4"}),t[0]||(t[0]=x(" Рекомендуемый ")),n(e.Sparkles,{class:"w-4 h-4 text-blue-400"})])]),_:1})):M("",!0),n(e.Card,{variant:"elevated",class:"h-full flex flex-col p-6"},{default:c(()=>[u("div",qe,[u("h3",Te,p(s.name),1),u("p",He,p(s.description),1),u("div",Ne,[u("div",Ge,p(s.price)+"₽",1),u("div",Ze,"за "+p(s.period),1)])]),u("div",Ue,[u("div",null,[u("h4",We,[n(e.CheckCircle,{class:"w-5 h-5 text-green-400"}),t[1]||(t[1]=x(" Включено в план "))]),u("ul",Xe,[(o(!0),l(_,null,D(s.features,(h,g)=>(o(),F(e.Motion,{key:g,initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{delay:g*.05},viewport:{once:!0},class:"flex items-start gap-3 text-sm"},{default:c(()=>[n(e.CheckCircle2,{class:"w-4 h-4 text-green-400 mt-0.5 flex-shrink-0"}),u("span",Ye,p(h),1)]),_:2},1032,["transition"]))),128))])]),s.limitations.length>0?(o(),l("div",Ke,[u("h4",Qe,[n(e.AlertTriangle,{class:"w-5 h-5 text-yellow-400"}),t[2]||(t[2]=x(" Ограничения "))]),u("ul",Je,[(o(!0),l(_,null,D(s.limitations,(h,g)=>(o(),l("li",{key:g,class:"flex items-start gap-3 text-sm"},[t[3]||(t[3]=u("div",{class:"w-4 h-4 border border-gray-500 rounded mt-0.5 flex-shrink-0"},null,-1)),u("span",ut,p(h),1)]))),128))])])):M("",!0)]),n(e.Button,{size:"lg",variant:(s.popular,"primary"),class:"w-full flex items-center justify-center gap-2"},{default:c(()=>[t[4]||(t[4]=x(" Выбрать план ")),n(e.ArrowRight,{class:"w-4 h-4"})]),_:2,__:[4]},1032,["variant"])]),_:2},1024)])]),_:2},1032,["transition"])),64))])}const tt=y(je,[["render",et]]),st=k({__name:"AuroraBackground",setup(d,{expose:t}){t();const i=v([]),e=["bg-blue-500/30","bg-purple-500/30","bg-pink-500/30","bg-green-500/30","bg-yellow-500/30","bg-indigo-500/30"],a=()=>({x:Math.random()*100,y:Math.random()*100,size:Math.random()*400+200,color:e[Math.floor(Math.random()*e.length)],delay:Math.random()*5,duration:Math.random()*10+5});Q(()=>{for(let s=0;s<6;s++)i.value.push(a())});const f={auroras:i,colors:e,generateAurora:a};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}}),it={class:"relative w-full h-full overflow-hidden"},nt={class:"absolute inset-0 mix-blend-screen opacity-70"},ot={class:"relative z-10"};function rt(d,t,i,e,a,f){return o(),l("div",it,[t[0]||(t[0]=u("div",{class:"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"},null,-1)),u("div",nt,[(o(!0),l(_,null,D(e.auroras,(s,r)=>(o(),l("div",{key:r,class:z(["absolute rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse",s.color]),style:b4({left:`${s.x}%`,top:`${s.y}%`,width:`${s.size}px`,height:`${s.size}px`,animationDelay:`${s.delay}s`,animationDuration:`${s.duration}s`})},null,6))),128))]),u("div",ot,[X(d.$slots,"default")])])}const at=y(st,[["render",rt]]),lt=k({__name:"GridPattern",props:{width:{default:40},height:{default:40},x:{default:-1},y:{default:-1},stroke:{default:"currentColor"},strokeWidth:{default:1},className:{default:""}},setup(d,{expose:t}){t();const i=d,e=N(()=>`grid-pattern-${Math.random().toString(36).substr(2,9)}`),a={props:i,id:e};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}}),dt={class:"absolute inset-0 overflow-hidden pointer-events-none"},ct=["id","width","height"],ft=["d","stroke","stroke-width"],pt=["fill"];function ht(d,t,i,e,a,f){return o(),l("div",dt,[(o(),l("svg",{class:z(["absolute inset-0 h-full w-full",i.className]),xmlns:"http://www.w3.org/2000/svg"},[u("defs",null,[u("pattern",{id:e.id,width:i.width,height:i.height,patternUnits:"userSpaceOnUse"},[u("path",{d:`M.5 ${i.height}V.5H${i.width}`,fill:"none",stroke:i.stroke,"stroke-width":i.strokeWidth},null,8,ft)],8,ct)]),u("rect",{width:"100%",height:"100%",fill:`url(#${e.id})`},null,8,pt)],2))])}const xt=y(lt,[["render",ht]]),gt=k({__name:"FlickeringGrid",props:{squareSize:{default:4},gridGap:{default:6},flickerChance:{default:.3},color:{default:"rgb(0, 0, 0)"},width:{},height:{},class:{},maxOpacity:{default:.3}},setup(d,{expose:t}){t();const i=d,{squareSize:e,gridGap:a,flickerChance:f,color:s,maxOpacity:r,width:h,height:g}=R4(i),w=v(),B=v(),E=v(),Y=v(!1),C=v({width:0,height:0}),j=N(()=>{if(!E.value)return"rgba(255, 0, 0,";const m=s.value.replace(/^#/,""),A=Number.parseInt(m,16),S=A>>16&255,U=A>>8&255,W=A&255;return`rgba(${S}, ${U}, ${W},`});function O(m,A,S){const U=window.devicePixelRatio||1;m.width=A*U,m.height=S*U,m.style.width=`${A}px`,m.style.height=`${S}px`;const W=Math.floor(A/(e.value+a.value)),e4=Math.floor(S/(e.value+a.value)),T=new Float32Array(W*e4);for(let H=0;H<T.length;H++)T[H]=Math.random()*r.value;return{cols:W,rows:e4,squares:T,dpr:U}}function R(m,A){for(let S=0;S<m.length;S++)Math.random()<f.value*A&&(m[S]=Math.random()*r.value)}function G(m,A,S,U,W,e4,T){m.clearRect(0,0,A,S),m.fillStyle="transparent",m.fillRect(0,0,A,S);for(let H=0;H<U;H++)for(let t4=0;t4<W;t4++){const k4=e4[H*W+t4];m.fillStyle=`${j.value}${k4})`,m.fillRect(H*(e.value+a.value)*T,t4*(e.value+a.value)*T,e.value*T,e.value*T)}}const I=v();function P(){const m=h.value||w.value.clientWidth,A=g.value||w.value.clientHeight;C.value={width:m,height:A},I.value=O(B.value,m,A)}let $,V,q,Z=0;function J(m){if(!Y.value)return;const A=(m-Z)/1e3;Z=m,R(I.value.squares,A),G(E.value,B.value.width,B.value.height,I.value.cols,I.value.rows,I.value.squares,I.value.dpr),$=requestAnimationFrame(J)}Q(()=>{!B.value||!w.value||(E.value=B.value.getContext("2d"),E.value&&(P(),V=new ResizeObserver(()=>{P()}),q=new IntersectionObserver(([m])=>{Y.value=m.isIntersecting,$=requestAnimationFrame(J)},{threshold:0}),V.observe(w.value),q.observe(B.value)))}),v4(()=>{$&&cancelAnimationFrame($),V?.disconnect(),q?.disconnect()});const u4={props:i,squareSize:e,gridGap:a,flickerChance:f,color:s,maxOpacity:r,width:h,height:g,containerRef:w,canvasRef:B,context:E,isInView:Y,canvasSize:C,computedColor:j,setupCanvas:O,updateSquares:R,drawGrid:G,gridParams:I,updateCanvasSize:P,get animationFrameId(){return $},set animationFrameId(m){$=m},get resizeObserver(){return V},set resizeObserver(m){V=m},get intersectionObserver(){return q},set intersectionObserver(m){q=m},get lastTime(){return Z},set lastTime(m){Z=m},animate:J,get cn(){return D4}};return Object.defineProperty(u4,"__isScriptSetup",{enumerable:!1,value:!0}),u4}}),mt=["width","height"];function vt(d,t,i,e,a,f){return o(),l("div",{ref:"containerRef",class:z(e.cn("w-full h-full",e.props.class))},[u("canvas",{ref:"canvasRef",class:"pointer-events-none",width:e.canvasSize.width,height:e.canvasSize.height},null,8,mt)],2)}const bt=y(gt,[["render",vt]]),_t=k({__name:"AnimatedCounter",props:{value:{},suffix:{default:""},duration:{default:2e3}},setup(d,{expose:t}){t();const i=d,e=v(0),a=v(!1),f=N(()=>typeof i.value=="string"?i.value:Math.floor(e.value).toLocaleString()),r={props:i,currentValue:e,isAnimating:a,displayValue:f,startCounting:()=>{if(a.value||typeof i.value=="string")return;a.value=!0;const h=typeof i.value=="number"?i.value:parseInt(i.value.replace(/\D/g,"")),g=h/(i.duration/16),w=()=>{e.value<h?(e.value+=g,requestAnimationFrame(w)):e.value=h};w()},get Motion(){return L}};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}});function yt(d,t,i,e,a,f){return o(),F(e.Motion,{initial:{opacity:0,scale:.5},whileInView:{opacity:1,scale:1},transition:{duration:.8,ease:"easeOut"},viewport:{once:!0},onInView:e.startCounting,class:"text-4xl font-bold text-[--p-primary-color]"},{default:c(()=>[x(p(e.displayValue)+p(i.suffix),1)]),_:1})}const Et=y(_t,[["render",yt]]),Dt=k({__name:"LandingPage",setup(d,{expose:t}){t();const g={stats:[{value:"500K+",label:"Запчастей в базе"},{value:"2,847",label:"Производителей"},{value:"до 50%",label:"Экономия на закупках"}],trustedLogos:[Iu,Vu,Nu,Wu,Qu,t3],features:[{icon:T4,title:"Идентичности и атрибуты",description:"Точная модель данных: физическая идентичность, степени совместимости, стандартизированные единицы."},{icon:C4,title:"ИИ‑сопоставление",description:"Автоматический анализ описаний, объединение синонимов и типизация характеристик."},{icon:x4,title:"N‑уровневая архитектура",description:"Bun + Hono + tRPC + PostgreSQL + Prisma + ZenStack. Горизонтальное масштабирование."},{icon:s4,title:"Ролевая модель и аудит",description:"RBAC, детальные журналы действий, контроль версий и трассировка изменений."},{icon:p4,title:"Шифрование и изоляция",description:"TLS, безопасное хранение токенов, сегментация данных и защита периметра."},{icon:L4,title:"Надёжность и SLA",description:"Мониторинг, алёртинг, SLO/SLA, отказоустойчивые конфигурации."}],steps:[{title:"Описание",description:"Задайте запрос голосом или текстом. Укажите ключевые параметры и применение."},{title:"Сопоставление",description:"ИИ сопоставляет характеристики и находит физически идентичные или частично совместимые детали."},{title:"Верификация",description:"Экспертная проверка и прозрачные метки точности: EXACT, PARTIAL, WITH_NOTES, MODIFICATION."}],securityBadges:[{icon:s4,title:"RBAC + Audit",description:"Разграничение доступа и детальные журналы событий"},{icon:p4,title:"Шифрование",description:"TLS в транзите, защищённое хранение секретов"},{icon:x4,title:"Резервирование",description:"Бэкапы, точка восстановления, план DR"},{icon:a4,title:"Целостность данных",description:"Типобезопасность end‑to‑end и схемы валидации"}],ctaFeatures:[{icon:s4,title:"Гарантия качества",description:"Все связи проверены экспертами"},{icon:F4,title:"Быстрая интеграция",description:"API готов к работе за 24 часа"},{icon:U4,title:"Экспертная поддержка",description:"Команда инженеров 24/7"}],faqs:[{q:"Можно ли интегрировать в нашу ERP/CRM?",a:"Да. Доступны tRPC/REST endpoints, вебхуки, ключи доступа и троттлинг. Помогаем с архитектурой интеграции."},{q:"Как обеспечивается точность сопоставления?",a:"Комбинация формализованных атрибутов, единиц измерения и ИИ‑моделей. Каждый матч помечен меткой точности."},{q:"Есть ли тестовый доступ?",a:"Предоставляем sandbox‑окружение с ограниченными датасетами и rate‑limit. Запросите у команды продаж."},{q:"Как устроена безопасность и контроль доступа?",a:"RBAC по ролям (GUEST/USER/SHOP/ADMIN), аудит действий, шифрование и сегментация данных."}],get Motion(){return L},get ArrowRight(){return r4},get CheckCircle(){return i4},get Star(){return w4},get Shield(){return s4},get HelpCircle(){return P4},AnimatedSection:K4,AISearchDemo:F0,TechnicalSchema:pu,AnimatedBeamDemo:m3,ExamplesSection:ue,CatalogSection:Ie,PricingSection:tt,AuroraBackground:at,GridPattern:xt,FlickeringGrid:bt,Button:d4,AnimatedCounter:Et};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}}),Bt={class:"min-h-screen bg-black text-white overflow-hidden"},Ct={class:"relative min-h-[92vh] grid place-items-center"},wt={class:"relative z-30 w-full"},Ft={class:"w-full max-w-5xl mx-auto px-4 md:px-6 text-center"},At={class:"mt-8 flex flex-col sm:flex-row gap-4 justify-center items-center mx-auto"},kt={class:"mt-8 grid grid-cols-2 sm:grid-cols-3 gap-4 max-w-3xl mx-auto"},zt={class:"text-gray-300 text-sm"},Mt={class:"relative container mx-auto px-4"},St={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-6 items-center opacity-90"},Rt={class:"relative container mx-auto px-4"},$t={class:"text-center mb-12"},Lt={class:"grid md:grid-cols-2 lg:grid-cols-3 gap-6"},It={class:"w-12 h-12 rounded-xl bg-zinc-800 flex items-center justify-center mb-4"},jt={class:"text-white font-semibold mb-1"},Ot={class:"text-gray-200 text-sm"},Pt={class:"relative container mx-auto px-4"},Vt={class:"text-center mb-12"},qt={class:"grid md:grid-cols-3 gap-6"},Tt={class:"w-10 h-10 rounded-full bg-zinc-800 flex items-center justify-center mb-3 text-sm text-gray-300"},Ht={class:"text-white font-semibold mb-1"},Nt={class:"text-gray-200 text-sm"},Gt={class:"relative container mx-auto px-4"},Zt={class:"text-center mb-12"},Ut={class:"relative container mx-auto px-4"},Wt={class:"text-center mb-12"},Xt={class:"relative container mx-auto px-4"},Yt={class:"relative container mx-auto px-4"},Kt={class:"text-center mb-12"},Qt={class:"relative container mx-auto px-4"},Jt={class:"relative container mx-auto px-4"},us={class:"text-center mb-10"},es={class:"grid md:grid-cols-2 lg:grid-cols-4 gap-6"},ts={class:"w-12 h-12 mx-auto rounded-xl bg-zinc-800 flex items-center justify-center mb-3"},ss={class:"text-white font-semibold mb-1"},is={class:"text-gray-200 text-sm"},ns={class:"relative container mx-auto px-4"},os={class:"text-center mb-12"},rs={class:"relative container mx-auto px-4 max-w-5xl"},as={class:"text-center mb-10"},ls={class:"space-y-4"},ds={class:"group rounded-xl"},cs={class:"cursor-pointer list-none px-6 py-4 flex items-center justify-between"},fs={class:"text-white font-medium"},ps={class:"px-6 pb-5 text-gray-200 text-sm leading-relaxed"},hs={class:"relative container mx-auto px-4 text-center"},xs={class:"flex flex-col sm:flex-row gap-6 justify-center mb-10"},gs={class:"grid md:grid-cols-3 gap-6 max-w-5xl mx-auto"},ms={class:"w-16 h-16 bg-zinc-800 rounded-2xl flex items-center justify-center mx-auto mb-4"},vs={class:"font-semibold text-white text-lg mb-2"},bs={class:"text-sm text-gray-100"},_s={class:"border-t border-zinc-800/80 bg-black/60"},ys={class:"container mx-auto px-4 py-8 text-sm text-gray-400 flex flex-col sm:flex-row items-center justify-between gap-3"};function Es(d,t,i,e,a,f){return o(),l("div",Bt,[u("section",Ct,[n(e.AuroraBackground,{class:"absolute inset-0 z-0"}),t[3]||(t[3]=u("div",{class:"absolute inset-0 z-10 bg-[radial-gradient(ellipse_at_center,rgba(0,0,0,0)_0%,rgba(0,0,0,0.6)_60%,rgba(0,0,0,0.9)_100%)]"},null,-1)),n(e.FlickeringGrid,{class:"absolute inset-0 z-20 pointer-events-none","square-size":6,"grid-gap":8,"flicker-chance":.35,color:"#60a5fa","max-opacity":.5}),u("div",wt,[u("div",Ft,[n(e.Motion,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.9},class:"w-full"},{default:c(()=>[t[2]||(t[2]=u("div",{class:"bg-zinc-900/70 border border-zinc-800 rounded-xl p-4"},[u("h1",{class:"text-[40px] leading-[1.05] sm:text-6xl lg:text-7xl font-bold mb-6 max-w-4xl mx-auto text-center"},[x(" Профессиональная база данных "),u("span",{class:"bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-cyan-300"},"взаимозаменяемых"),x(" запчастей ")]),u("p",{class:"text-lg sm:text-xl text-gray-200/90 leading-relaxed max-w-3xl mx-auto text-center"}," Идентификация и связывание физически идентичных деталей разных брендов. Надёжные инженерные данные, проверенные экспертами. API‑интеграции уровня Enterprise. ")],-1)),u("div",At,[n(e.Button,{size:"lg",variant:"primary",class:"flex items-center gap-2 px-6 py-5"},{default:c(()=>[t[0]||(t[0]=x(" Запросить доступ ")),n(e.ArrowRight,{class:"w-5 h-5"})]),_:1,__:[0]}),n(e.Button,{size:"lg",variant:"outline",class:"px-6 py-5"},{default:c(()=>t[1]||(t[1]=[x("Смотреть документацию")])),_:1,__:[1]})]),u("div",kt,[(o(),l(_,null,D(e.stats,(s,r)=>u("div",{key:r,class:"bg-zinc-900/70 border border-zinc-800 rounded-xl p-4"},[n(e.AnimatedCounter,{value:s.value,class:"text-3xl font-bold text-blue-400 mb-1"},null,8,["value"]),u("div",zt,p(s.label),1)])),64))])]),_:1,__:[2]})])])]),n(e.AnimatedSection,{class:"py-8 relative"},{default:c(()=>[t[5]||(t[5]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),u("div",Mt,[t[4]||(t[4]=u("div",{class:"text-center text-gray-400 text-sm mb-5"},"Нам доверяют производители и интеграторы",-1)),u("div",St,[(o(),l(_,null,D(e.trustedLogos,(s,r)=>u("div",{key:r,class:"flex items-center justify-center py-3 rounded-lg bg-zinc-900/60 border border-zinc-800"},[(o(),F(K(s),{class:"h-6 sm:h-7 text-white/80"}))])),64))])])]),_:1,__:[5]}),n(e.AnimatedSection,{class:"py-20 relative"},{default:c(()=>[t[9]||(t[9]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),n(e.GridPattern,{class:"opacity-20"}),u("div",Rt,[u("div",$t,[n(e.Motion,{initial:{opacity:0,scale:.95},whileInView:{opacity:1,scale:1},transition:{duration:.6},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-blue-300 px-4 py-2 rounded-full text-sm font-medium mb-4"},{default:c(()=>[n(e.Star,{class:"w-4 h-4"}),t[6]||(t[6]=x(" Ключевые преимущества "))]),_:1,__:[6]}),t[7]||(t[7]=u("h2",{class:"text-4xl lg:text-5xl font-bold mb-4"},"Инженерная точность. Enterprise‑уровень.",-1)),t[8]||(t[8]=u("p",{class:"text-lg text-gray-300 max-w-3xl mx-auto"},"Современная архитектура с упором на надёжность, масштабируемость и скорость интеграции.",-1))]),u("div",Lt,[(o(),l(_,null,D(e.features,(s,r)=>n(e.Motion,{key:r,initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{delay:r*.05},viewport:{once:!0},class:"bg-zinc-900/80 border border-zinc-800 rounded-2xl p-6"},{default:c(()=>[u("div",It,[(o(),F(K(s.icon),{class:"w-6 h-6 text-white"}))]),u("div",jt,p(s.title),1),u("div",Ot,p(s.description),1)]),_:2},1032,["transition"])),64))])])]),_:1,__:[9]}),n(e.AnimatedSection,{class:"py-20 relative"},{default:c(()=>[t[13]||(t[13]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),n(e.GridPattern,{class:"opacity-10"}),u("div",Pt,[u("div",Vt,[n(e.Motion,{initial:{opacity:0,scale:.95},whileInView:{opacity:1,scale:1},transition:{duration:.6},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-300 px-4 py-2 rounded-full text-sm font-medium mb-4"},{default:c(()=>[n(e.CheckCircle,{class:"w-4 h-4"}),t[10]||(t[10]=x(" Как это работает "))]),_:1,__:[10]}),t[11]||(t[11]=u("h2",{class:"text-4xl lg:text-5xl font-bold mb-4"},"Три шага к точному аналогу",-1)),t[12]||(t[12]=u("p",{class:"text-lg text-gray-300 max-w-3xl mx-auto"},"ИИ и стандартизированные атрибуты обеспечивают высокую точность сопоставления.",-1))]),u("div",qt,[(o(),l(_,null,D(e.steps,(s,r)=>u("div",{key:r,class:"bg-zinc-900/80 border border-zinc-800 rounded-2xl p-6"},[u("div",Tt,p(r+1),1),u("div",Ht,p(s.title),1),u("div",Nt,p(s.description),1)])),64))])])]),_:1,__:[13]}),n(e.AnimatedSection,{class:"py-20 relative"},{default:c(()=>[t[17]||(t[17]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),n(e.GridPattern,{class:"opacity-20"}),u("div",Gt,[u("div",Zt,[n(e.Motion,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.8},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-blue-400 px-4 py-2 rounded-full text-base font-medium mb-4"},{default:c(()=>t[14]||(t[14]=[u("div",{class:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"},null,-1),x(" ИИ-Ассистент поиска ")])),_:1,__:[14]}),t[15]||(t[15]=u("h2",{class:"text-4xl lg:text-5xl font-bold mb-4"},[u("span",{class:"text-gray-300"},"Умный поиск с"),x(),u("span",{class:"text-blue-400"},"голосовым вводом")],-1)),t[16]||(t[16]=u("p",{class:"text-xl text-gray-300 max-w-3xl mx-auto"},"ИИ анализирует техническое описание и находит точные аналоги за секунды",-1))]),n(e.AISearchDemo)])]),_:1,__:[17]}),n(e.AnimatedSection,{class:"py-20 relative"},{default:c(()=>[t[21]||(t[21]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),n(e.GridPattern,{class:"opacity-20"}),u("div",Ut,[u("div",Wt,[n(e.Motion,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.8},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-4"},{default:c(()=>t[18]||(t[18]=[u("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"},null,-1),x(" Техническая схема ")])),_:1,__:[18]}),t[19]||(t[19]=u("h2",{class:"text-4xl lg:text-5xl font-bold mb-4"},[u("span",{class:"text-gray-300"},"Поиск взаимозаменяемых"),u("span",{class:"text-green-400"},"деталей")],-1)),t[20]||(t[20]=u("p",{class:"text-xl text-gray-300 max-w-3xl mx-auto"},"Система анализирует технические характеристики и находит совместимые аналоги",-1))]),n(e.TechnicalSchema)])]),_:1,__:[21]}),n(e.AnimatedSection,{class:"py-20 relative mt-4"},{default:c(()=>[t[23]||(t[23]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),n(e.GridPattern,{class:"opacity-20"}),u("div",Xt,[t[22]||(t[22]=u("div",{class:"text-center mb-8"},[u("h2",{class:"text-5xl font-bold mb-3"},[u("span",{class:"text-gray-300"},"Единая экосистема"),x(),u("span",{class:"text-blue-400"},"производителей")])],-1)),n(e.AnimatedBeamDemo,{class:"mt-6"})])]),_:1,__:[23]}),n(e.AnimatedSection,{class:"py-16 relative"},{default:c(()=>[t[27]||(t[27]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),u("div",Yt,[u("div",Kt,[n(e.Motion,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.8},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-3"},{default:c(()=>[n(e.CheckCircle,{class:"w-4 h-4"}),t[24]||(t[24]=x(" Реальные кейсы "))]),_:1,__:[24]}),t[25]||(t[25]=u("h2",{class:"text-5xl font-bold mb-3"},[u("span",{class:"text-gray-300"},"Примеры взаимозаменяемости"),x(),u("span",{class:"text-green-400"},"деталей")],-1)),t[26]||(t[26]=u("p",{class:"text-2xl text-gray-100"},"Реальные кейсы поиска аналогов с техническими характеристиками и экономическим эффектом",-1))]),n(e.ExamplesSection)])]),_:1,__:[27]}),n(e.AnimatedSection,{class:"py-16 relative"},{default:c(()=>[t[29]||(t[29]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),n(e.GridPattern,{class:"opacity-10"}),u("div",Qt,[t[28]||(t[28]=u("div",{class:"text-center mb-12"},[u("h2",{class:"text-5xl font-bold mb-3"},[u("span",{class:"text-gray-300"},"Разделы промышленного"),x(),u("span",{class:"text-blue-400"},"каталога")]),u("p",{class:"text-2xl text-gray-100"},"Комплексная система каталогизации запчастей по техническим категориям")],-1)),n(e.CatalogSection)])]),_:1,__:[29]}),n(e.AnimatedSection,{class:"py-16 relative"},{default:c(()=>[t[33]||(t[33]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),u("div",Jt,[u("div",us,[n(e.Motion,{initial:{opacity:0,scale:.95},whileInView:{opacity:1,scale:1},transition:{duration:.6},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-teal-300 px-4 py-2 rounded-full text-sm font-medium mb-4"},{default:c(()=>[n(e.Shield,{class:"w-4 h-4"}),t[30]||(t[30]=x(" Безопасность и соответствие "))]),_:1,__:[30]}),t[31]||(t[31]=u("h2",{class:"text-4xl lg:text-5xl font-bold mb-4"},"Корпоративные стандарты безопасности",-1)),t[32]||(t[32]=u("p",{class:"text-lg text-gray-300 max-w-3xl mx-auto"},"Шифрование, аудит действий, разграничение ролей и полноценные журналы событий.",-1))]),u("div",es,[(o(),l(_,null,D(e.securityBadges,(s,r)=>u("div",{key:r,class:"bg-zinc-900/80 border border-zinc-800 rounded-2xl p-6 text-center"},[u("div",ts,[(o(),F(K(s.icon),{class:"w-6 h-6 text-white"}))]),u("div",ss,p(s.title),1),u("div",is,p(s.description),1)])),64))])])]),_:1,__:[33]}),n(e.AnimatedSection,{class:"py-16 relative"},{default:c(()=>[t[37]||(t[37]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),u("div",ns,[u("div",os,[n(e.Motion,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.8},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-3"},{default:c(()=>[n(e.Star,{class:"w-4 h-4"}),t[34]||(t[34]=x(" Тарифные планы "))]),_:1,__:[34]}),t[35]||(t[35]=u("h2",{class:"text-5xl font-bold mb-3"},[u("span",{class:"text-gray-300"},"Тарифные планы для"),x(),u("span",{class:"text-green-400"},"профессионалов")],-1)),t[36]||(t[36]=u("p",{class:"text-2xl text-gray-100"},"Выберите подходящий уровень доступа к промышленной базе данных",-1))]),n(e.PricingSection)])]),_:1,__:[37]}),n(e.AnimatedSection,{class:"py-16 relative"},{default:c(()=>[t[41]||(t[41]=u("div",{class:"absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black"},null,-1)),u("div",rs,[u("div",as,[n(e.Motion,{initial:{opacity:0,scale:.95},whileInView:{opacity:1,scale:1},transition:{duration:.6},viewport:{once:!0},class:"inline-flex items-center gap-2 bg-zinc-900/80 border border-zinc-700/50 text-blue-300 px-4 py-2 rounded-full text-sm font-medium mb-4"},{default:c(()=>[n(e.HelpCircle,{class:"w-4 h-4"}),t[38]||(t[38]=x(" Частые вопросы "))]),_:1,__:[38]}),t[39]||(t[39]=u("h2",{class:"text-4xl lg:text-5xl font-bold"},"Ответы для быстрого старта",-1))]),u("div",ls,[(o(),l(_,null,D(e.faqs,(s,r)=>u("div",{key:r,class:"bg-zinc-900/80 border border-zinc-800 rounded-xl"},[u("details",ds,[u("summary",cs,[u("span",fs,p(s.q),1),t[40]||(t[40]=u("svg",{class:"w-5 h-5 text-gray-400 transition-transform group-open:rotate-180",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[u("path",{d:"m6 9 6 6 6-6"})],-1))]),u("div",ps,p(s.a),1)])])),64))])])]),_:1,__:[41]}),n(e.AnimatedSection,{class:"py-16 relative"},{default:c(()=>[t[46]||(t[46]=u("div",{class:"absolute inset-0 bg-gradient-to-br from-zinc-900 via-black to-zinc-950"},null,-1)),u("div",hs,[n(e.Motion,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0}},{default:c(()=>[t[44]||(t[44]=u("h2",{class:"text-5xl lg:text-6xl font-bold mb-4"},[u("span",{class:"text-gray-300"},"Готовы оптимизировать"),x(),u("span",{class:"text-blue-400"},"закупки запчастей?")],-1)),t[45]||(t[45]=u("p",{class:"text-2xl text-gray-100 mb-6 max-w-4xl mx-auto leading-relaxed"},"Присоединяйтесь к предприятиям, экономящим миллионы благодаря нашей системе поиска взаимозаменяемых деталей",-1)),u("div",xs,[n(e.Button,{size:"lg",variant:"primary",class:"flex items-center gap-2 px-10 py-5"},{default:c(()=>[t[42]||(t[42]=x("Запросить демонстрацию ")),n(e.ArrowRight,{class:"w-5 h-5"})]),_:1,__:[42]}),n(e.Button,{size:"lg",variant:"outline",class:"px-10 py-5"},{default:c(()=>t[43]||(t[43]=[x("Техническая консультация")])),_:1,__:[43]})]),u("div",gs,[(o(),l(_,null,D(e.ctaFeatures,(s,r)=>u("div",{key:r,class:"bg-zinc-900/80 backdrop-blur-xl border border-zinc-700/50 rounded-2xl p-6 text-center"},[u("div",ms,[(o(),F(K(s.icon),{class:"w-8 h-8 text-white"}))]),u("div",vs,p(s.title),1),u("div",bs,p(s.description),1)])),64))])]),_:1,__:[44,45]})])]),_:1,__:[46]}),u("footer",_s,[u("div",ys,[u("div",null,"© "+p(new Date().getFullYear())+" PartTec. Все права защищены.",1),t[47]||(t[47]=u("div",{class:"flex items-center gap-4"},[u("a",{href:"/privacy",class:"hover:text-gray-200"},"Политика конфиденциальности"),u("a",{href:"/terms",class:"hover:text-gray-200"},"Условия использования"),u("a",{href:"/security",class:"hover:text-gray-200"},"Безопасность")],-1))])])])}const $s=y(Dt,[["render",Es]]);export{$s as default};

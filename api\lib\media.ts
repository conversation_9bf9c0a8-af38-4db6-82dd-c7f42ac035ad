import { mkdir, writeFile, unlink } from 'fs/promises'
import { existsSync } from 'fs'
import { join, extname } from 'path'

const UPLOADS_ROOT = join(process.cwd(), 'uploads')

const ALLOWED_IMAGE_MIME = new Set([
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/svg+xml',
])

const MIME_TO_EXT: Record<string, string> = {
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/webp': 'webp',
  'image/gif': 'gif',
  'image/svg+xml': 'svg',
}

export type SaveImageInput = {
  fileName: string
  fileDataBase64: string
  mimeType: string
  subdir: string // e.g. 'part-images' | 'category-images'
}

export type SavedImage = {
  fileName: string
  mimeType: string
  fileSize: number
  url: string // /api/uploads/<subdir>/<fileName>
  filePath: string // absolute path
}

export function getUploadsRoot(): string {
  return UPLOADS_ROOT
}

export function ensureDirSync(path: string) {
  if (!existsSync(path)) {
    // Using promises API with recursive to avoid race conditions
    // but caller is async – we keep sync check and async mkdir in save
  }
}

export function resolveUrlToFilePath(url: string): string {
  const prefix = '/api/uploads/'
  if (!url.startsWith(prefix)) {
    throw new Error('Invalid media URL')
  }
  const relative = url.slice(prefix.length)
  return join(UPLOADS_ROOT, relative)
}

function getExtensionFromNameOrMime(name: string, mimeType: string): string {
  const fromMime = MIME_TO_EXT[mimeType]
  if (fromMime) return fromMime
  const ext = extname(name).replace('.', '').toLowerCase()
  return ext || 'png'
}

function toBase64Payload(data: string): Buffer {
  const cleaned = data.replace(/^data:[^;]+;base64,/, '')
  return Buffer.from(cleaned, 'base64')
}

function generateUniqueFileName(proposedExt: string): string {
  const ts = Date.now()
  const rnd = Math.random().toString(36).slice(2)
  const ext = proposedExt.startsWith('.') ? proposedExt.slice(1) : proposedExt
  return `${ts}-${rnd}.${ext}`
}

export async function saveImageToLocalFS(input: SaveImageInput): Promise<SavedImage> {
  if (!ALLOWED_IMAGE_MIME.has(input.mimeType)) {
    throw new Error('Неподдерживаемый MIME-тип изображения')
  }

  const ext = getExtensionFromNameOrMime(input.fileName, input.mimeType)
  const dir = join(UPLOADS_ROOT, input.subdir)
  if (!existsSync(dir)) {
    await mkdir(dir, { recursive: true })
  }

  const uniqueFileName = generateUniqueFileName(ext)
  const filePath = join(dir, uniqueFileName)

  const buffer = toBase64Payload(input.fileDataBase64)
  await writeFile(filePath, buffer)

  const url = `/api/uploads/${input.subdir}/${uniqueFileName}`

  return {
    fileName: uniqueFileName,
    mimeType: input.mimeType,
    fileSize: buffer.byteLength,
    url,
    filePath,
  }
}

export async function deleteFileIfExists(filePath: string): Promise<void> {
  try {
    await unlink(filePath)
  } catch (e: any) {
    // Ignore if already deleted
  }
}




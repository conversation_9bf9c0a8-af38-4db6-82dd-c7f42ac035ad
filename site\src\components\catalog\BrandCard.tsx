import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { BrandItem } from '@/types/catalog';

interface BrandCardProps {
  brand: BrandItem;
  onClick?: () => void;
}

export function BrandCard({ brand, onClick }: BrandCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      window.location.href = `/catalog/brands/${brand.slug}`;
    }
  };

  return (
    <Card 
      className="cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-1">
              {brand.name}
            </CardTitle>
            {brand.country && (
              <CardDescription className="mt-1">
                {brand.country}
              </CardDescription>
            )}
          </div>
          <div className="flex flex-col gap-1">
            <Badge variant={brand.isOem ? "default" : "secondary"}>
              {brand.isOem ? "OEM" : "Aftermarket"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {brand._count && (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Запчасти:</span>
                <span className="font-medium">{brand._count.catalogItems}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Модели:</span>
                <span className="font-medium">{brand._count.equipmentModel}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_AEsVEWgS.mjs';
import { computed, ref, defineComponent, useSSRContext, mergeProps, withCtx, createTextVNode, createVNode, toDisplayString, withModifiers, createBlock, createCommentVNode, openBlock, Fragment, renderList, createSlots, renderSlot, watch, onMounted } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import { f as fetchers, q as qk, u as useQueryToastErrors } from '../chunks/fetchers_CTjsTB2x.mjs';
import { p as pluralize } from '../chunks/utils_C3vjJnJw.mjs';
import { u as useUrlParams } from '../chunks/useUrlParams_CGyErwK-.mjs';
import { u as useDebounce, S as Spinner, C as CatalogPagination } from '../chunks/Spinner_g8zgaE0e.mjs';
import { C as Card } from '../chunks/Card_aE2_b9LT.mjs';
import { I as InputText } from '../chunks/InputText_DNFWprlB.mjs';
import { M as MultiSelect } from '../chunks/MultiSelect_uAHCsge7.mjs';
import { S as SecondaryButton, p as ptViewMerge, a as script } from '../chunks/SecondaryButton_B0hmlm1n.mjs';
import 'clsx';
import { ssrRenderAttrs, ssrRenderList, ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrRenderSlot } from 'vue/server-renderer';
/* empty css                                   */
import { _ as _export_sfc } from '../chunks/ClientRouter_avhRMbqw.mjs';
import { D as DataTable, s as script$1 } from '../chunks/index_CxapvcaX.mjs';
import { t as trpc } from '../chunks/trpc_DApR3DD7.mjs';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const filtersToQuery = (filters) => {
  const and = [];
  if (filters.search) {
    and.push({ name: { contains: filters.search, mode: "insensitive" } });
  }
  if (filters.categoryIds?.length) {
    and.push({ partCategoryId: { in: filters.categoryIds } });
  }
  if (filters.brandIds?.length) {
    and.push({ applicabilities: { some: { catalogItem: { brandId: { in: filters.brandIds } } } } });
  }
  if (filters.equipmentModelIds?.length) {
    and.push({ equipmentApplicabilities: { some: { equipmentModelId: { in: filters.equipmentModelIds } } } });
  }
  if (filters.attributes?.length) {
    const attrConds = filters.attributes.filter((a) => !!a?.templateId).map((a) => {
      const cond = { templateId: a.templateId };
      if (a.value) cond.value = a.value;
      if (a.minValue != null || a.maxValue != null) {
        const range = {};
        if (a.minValue != null) range.gte = a.minValue;
        if (a.maxValue != null) range.lte = a.maxValue;
        cond.numericValue = range;
      }
      return cond;
    });
    if (attrConds.length) {
      and.push({ attributes: { some: { AND: attrConds } } });
    }
  }
  const where = and.length ? { AND: and } : {};
  return { where };
};
const queryToFilters = (query) => {
  const filters = {};
  if (query.search) {
    filters.search = query.search;
  }
  if (query.categoryIds) {
    filters.categoryIds = Array.isArray(query.categoryIds) ? query.categoryIds.map(Number) : [Number(query.categoryIds)];
  }
  if (query.brandIds) {
    filters.brandIds = Array.isArray(query.brandIds) ? query.brandIds.map(Number) : [Number(query.brandIds)];
  }
  if (query.equipmentModelIds) {
    filters.equipmentModelIds = Array.isArray(query.equipmentModelIds) ? query.equipmentModelIds : [query.equipmentModelIds];
  }
  if (query.minPrice !== void 0 || query.maxPrice !== void 0) {
    filters.priceRange = [
      query.minPrice ? Number(query.minPrice) : 0,
      query.maxPrice ? Number(query.maxPrice) : Infinity
    ];
  }
  if (query.attributes) {
    filters.attributes = Array.isArray(query.attributes) ? query.attributes : [query.attributes];
  }
  return filters;
};
const hasActiveFilters = (filters) => {
  return !!(filters.search || filters.categoryIds?.length || filters.brandIds?.length || filters.equipmentModelIds?.length || filters.priceRange || filters.attributes?.length);
};

function useCatalogFilters(initialFilters) {
  const urlSync = useUrlParams({
    search: initialFilters?.search ?? "",
    categoryIds: initialFilters?.categoryIds ?? [],
    brandIds: initialFilters?.brandIds ?? [],
    equipmentModelIds: initialFilters?.equipmentModelIds ?? [],
    priceMin: initialFilters?.priceRange?.[0],
    priceMax: initialFilters?.priceRange?.[1]
  }, {
    prefix: "catalog_",
    arrayParams: ["categoryIds", "brandIds", "equipmentModelIds"],
    numberParams: ["priceMin", "priceMax"],
    debounceMs: 300
  });
  const filtersState = computed(() => {
    const f = urlSync.filters.value;
    const priceRange = f.priceMin != null || f.priceMax != null ? [f.priceMin ?? 0, f.priceMax ?? Infinity] : void 0;
    return {
      search: f.search || void 0,
      categoryIds: Array.isArray(f.categoryIds) && f.categoryIds.length ? f.categoryIds.map(Number) : void 0,
      brandIds: Array.isArray(f.brandIds) && f.brandIds.length ? f.brandIds.map(Number) : void 0,
      equipmentModelIds: Array.isArray(f.equipmentModelIds) && f.equipmentModelIds.length ? f.equipmentModelIds : void 0,
      priceRange
    };
  });
  const viewPreferences = ref({
    mode: "grid",
    itemsPerPage: 20,
    sortBy: "name",
    sortOrder: "asc"
  });
  const hasFilters = computed(() => hasActiveFilters(filtersState.value));
  const queryParams = computed(() => filtersToQuery(filtersState.value));
  const updateFilters = (updates) => {
    const patch = {};
    if ("search" in updates) patch.search = updates.search || void 0;
    if ("categoryIds" in updates) patch.categoryIds = updates.categoryIds && updates.categoryIds.length ? updates.categoryIds : void 0;
    if ("brandIds" in updates) patch.brandIds = updates.brandIds && updates.brandIds.length ? updates.brandIds : void 0;
    if ("equipmentModelIds" in updates) patch.equipmentModelIds = updates.equipmentModelIds && updates.equipmentModelIds.length ? updates.equipmentModelIds : void 0;
    if ("priceRange" in updates) {
      const pr = updates.priceRange;
      patch.priceMin = pr?.[0];
      patch.priceMax = pr?.[1];
    }
    urlSync.updateFilters(patch);
  };
  const setSearch = (search) => {
    updateFilters({ search: search || void 0 });
  };
  const setCategoryIds = (categoryIds) => {
    updateFilters({ categoryIds: categoryIds.length ? categoryIds : void 0 });
  };
  const setBrandIds = (brandIds) => {
    updateFilters({ brandIds: brandIds.length ? brandIds : void 0 });
  };
  const setEquipmentModelIds = (equipmentModelIds) => {
    updateFilters({ equipmentModelIds: equipmentModelIds.length ? equipmentModelIds : void 0 });
  };
  const setPriceRange = (priceRange) => {
    updateFilters({ priceRange });
  };
  const setAttributes = (attributes) => {
    updateFilters({ attributes: attributes.length ? attributes : void 0 });
  };
  const resetFilters = () => {
    urlSync.updateFilters({
      search: void 0,
      categoryIds: void 0,
      brandIds: void 0,
      equipmentModelIds: void 0,
      priceMin: void 0,
      priceMax: void 0
    });
  };
  const setViewMode = (mode) => {
    viewPreferences.value.mode = mode;
  };
  const setItemsPerPage = (count) => {
    viewPreferences.value.itemsPerPage = count;
  };
  const setSorting = (sortBy, sortOrder = "asc") => {
    viewPreferences.value.sortBy = sortBy;
    viewPreferences.value.sortOrder = sortOrder;
  };
  return {
    // Состояние
    filters: computed(() => filtersState.value),
    viewPreferences: computed(() => viewPreferences.value),
    hasFilters,
    queryParams,
    // Методы обновления фильтров
    updateFilters,
    setSearch,
    setCategoryIds,
    setBrandIds,
    setEquipmentModelIds,
    setPriceRange,
    setAttributes,
    resetFilters,
    // Методы настроек отображения
    setViewMode,
    setItemsPerPage,
    setSorting
  };
}

const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "CatalogGrid",
  props: {
    parts: {}
  },
  emits: ["partClick"],
  setup(__props, { expose: __expose }) {
    __expose();
    const ImageIcon = () => "\u{1F5BC}\uFE0F";
    const props = __props;
    const getMainAttributes = (part) => {
      if (!part.attributes?.length) return [];
      return part.attributes.filter((attr) => attr.template && attr.value).slice(0, 3);
    };
    const getPartMainAttributes = (part) => getMainAttributes(part);
    const formatAttributeValue = (attr) => {
      if (!attr.value) return "";
      const value = attr.value;
      const unit = attr.template.unit;
      if (unit) {
        return `${value} ${unit.symbol || unit.name}`;
      }
      return value;
    };
    const handleImageError = (event) => {
      const img = event.target;
      img.style.display = "none";
      console.warn("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F:", img.src);
    };
    const __returned__ = { ImageIcon, props, getMainAttributes, getPartMainAttributes, formatAttributeValue, handleImageError, Card, SecondaryButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$5(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-grid" }, _attrs))} data-v-80e3f4e4><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" data-v-80e3f4e4><!--[-->`);
  ssrRenderList($props.parts, (part) => {
    _push(ssrRenderComponent($setup["Card"], {
      key: part.id,
      class: "part-card cursor-pointer hover:shadow-lg transition-shadow duration-200",
      onClick: ($event) => _ctx.$emit("partClick", part)
    }, {
      header: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="relative aspect-square bg-surface-100 dark:bg-surface-800 rounded-t-xl overflow-hidden" data-v-80e3f4e4${_scopeId}>`);
          if (part.image?.url) {
            _push2(`<img${ssrRenderAttr("src", part.image.url)}${ssrRenderAttr("alt", part.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C")} class="w-full h-full object-cover" loading="lazy" data-v-80e3f4e4${_scopeId}>`);
          } else {
            _push2(`<div class="w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500" data-v-80e3f4e4${_scopeId}>`);
            _push2(ssrRenderComponent($setup["ImageIcon"], { class: "w-12 h-12" }, null, _parent2, _scopeId));
            _push2(`</div>`);
          }
          if (part.partCategory) {
            _push2(`<div class="absolute top-2 left-2 bg-primary text-primary-contrast px-2 py-1 rounded-md text-xs font-medium" data-v-80e3f4e4${_scopeId}>${ssrInterpolate(part.partCategory.name)}</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "relative aspect-square bg-surface-100 dark:bg-surface-800 rounded-t-xl overflow-hidden" }, [
              part.image?.url ? (openBlock(), createBlock("img", {
                key: 0,
                src: part.image.url,
                alt: part.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
                class: "w-full h-full object-cover",
                loading: "lazy",
                onError: $setup.handleImageError
              }, null, 40, ["src", "alt"])) : (openBlock(), createBlock("div", {
                key: 1,
                class: "w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
              }, [
                createVNode($setup["ImageIcon"], { class: "w-12 h-12" })
              ])),
              part.partCategory ? (openBlock(), createBlock("div", {
                key: 2,
                class: "absolute top-2 left-2 bg-primary text-primary-contrast px-2 py-1 rounded-md text-xs font-medium"
              }, toDisplayString(part.partCategory.name), 1)) : createCommentVNode("", true)
            ])
          ];
        }
      }),
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="space-y-3" data-v-80e3f4e4${_scopeId}><h3 class="font-medium text-surface-900 dark:text-surface-0 line-clamp-2" data-v-80e3f4e4${_scopeId}>${ssrInterpolate(part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`)}</h3>`);
          if (part.path) {
            _push2(`<div class="text-sm text-surface-500 dark:text-surface-400" data-v-80e3f4e4${_scopeId}> \u041F\u0443\u0442\u044C: ${ssrInterpolate(part.path)}</div>`);
          } else {
            _push2(`<!---->`);
          }
          if ($setup.getPartMainAttributes(part).length) {
            _push2(`<div class="space-y-1" data-v-80e3f4e4${_scopeId}><!--[-->`);
            ssrRenderList($setup.getPartMainAttributes(part), (attr) => {
              _push2(`<div class="flex justify-between text-sm" data-v-80e3f4e4${_scopeId}><span class="text-surface-600 dark:text-surface-300" data-v-80e3f4e4${_scopeId}>${ssrInterpolate(attr.template.title)}: </span><span class="text-surface-900 dark:text-surface-0 font-medium" data-v-80e3f4e4${_scopeId}>${ssrInterpolate($setup.formatAttributeValue(attr))}</span></div>`);
            });
            _push2(`<!--]--></div>`);
          } else {
            _push2(`<!---->`);
          }
          if (part.applicabilities?.length) {
            _push2(`<div class="text-sm text-surface-500 dark:text-surface-400" data-v-80e3f4e4${_scopeId}>${ssrInterpolate(part.applicabilities.length)} ${ssrInterpolate(_ctx.pluralize(part.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"]))}</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "space-y-3" }, [
              createVNode("h3", { class: "font-medium text-surface-900 dark:text-surface-0 line-clamp-2" }, toDisplayString(part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`), 1),
              part.path ? (openBlock(), createBlock("div", {
                key: 0,
                class: "text-sm text-surface-500 dark:text-surface-400"
              }, " \u041F\u0443\u0442\u044C: " + toDisplayString(part.path), 1)) : createCommentVNode("", true),
              $setup.getPartMainAttributes(part).length ? (openBlock(), createBlock("div", {
                key: 1,
                class: "space-y-1"
              }, [
                (openBlock(true), createBlock(Fragment, null, renderList($setup.getPartMainAttributes(part), (attr) => {
                  return openBlock(), createBlock("div", {
                    key: attr.id,
                    class: "flex justify-between text-sm"
                  }, [
                    createVNode("span", { class: "text-surface-600 dark:text-surface-300" }, toDisplayString(attr.template.title) + ": ", 1),
                    createVNode("span", { class: "text-surface-900 dark:text-surface-0 font-medium" }, toDisplayString($setup.formatAttributeValue(attr)), 1)
                  ]);
                }), 128))
              ])) : createCommentVNode("", true),
              part.applicabilities?.length ? (openBlock(), createBlock("div", {
                key: 2,
                class: "text-sm text-surface-500 dark:text-surface-400"
              }, toDisplayString(part.applicabilities.length) + " " + toDisplayString(_ctx.pluralize(part.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"])), 1)) : createCommentVNode("", true)
            ])
          ];
        }
      }),
      footer: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="flex items-center justify-between pt-3 border-t border-surface-200 dark:border-surface-700" data-v-80e3f4e4${_scopeId}><div class="text-xs text-surface-500 dark:text-surface-400" data-v-80e3f4e4${_scopeId}> ID: ${ssrInterpolate(part.id)}</div>`);
          _push2(ssrRenderComponent($setup["SecondaryButton"], {
            size: "small",
            outlined: "",
            onClick: ($event) => _ctx.$emit("partClick", part)
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 `);
              } else {
                return [
                  createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          return [
            createVNode("div", { class: "flex items-center justify-between pt-3 border-t border-surface-200 dark:border-surface-700" }, [
              createVNode("div", { class: "text-xs text-surface-500 dark:text-surface-400" }, " ID: " + toDisplayString(part.id), 1),
              createVNode($setup["SecondaryButton"], {
                size: "small",
                outlined: "",
                onClick: withModifiers(($event) => _ctx.$emit("partClick", part), ["stop"])
              }, {
                default: withCtx(() => [
                  createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                ]),
                _: 2
              }, 1032, ["onClick"])
            ])
          ];
        }
      }),
      _: 2
    }, _parent));
  });
  _push(`<!--]--></div></div>`);
}
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/catalog/CatalogGrid.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const CatalogGrid = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["ssrRender", _sfc_ssrRender$5], ["__scopeId", "data-v-80e3f4e4"]]);

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "Badge",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `inline-flex items-center justify-center rounded-md
        py-0 px-2 text-xs font-bold min-w-6 h-6
        bg-primary text-primary-contrast
        p-empty:min-w-2 p-empty:h-2 p-empty:rounded-full p-empty:p-0
        p-circle:p-0 p-circle:rounded-full
        p-secondary:bg-surface-100 dark:p-secondary:bg-surface-800 p-secondary:text-surface-600 dark:p-secondary:text-surface-300
        p-success:bg-green-500 dark:p-success:bg-green-400 p-success:text-white dark:p-success:text-green-950
        p-info:bg-sky-500 dark:p-info:bg-sky-400 p-info:text-white dark:p-info:text-sky-950
        p-warn:bg-orange-500 dark:p-warn:bg-orange-400 p-warn:text-white dark:p-warn:text-orange-950
        p-danger:bg-red-500 dark:p-danger:bg-red-400 p-danger:text-white dark:p-danger:text-red-950
        p-contrast:bg-surface-950 dark:p-contrast:bg-white p-contrast:text-white dark:p-contrast:text-surface-950
        p-small:text-[0.625rem] p-small:min-w-5 p-small:h-5
        p-large:text-sm p-large:min-w-7 p-large:h-7
        p-xlarge:text-base p-xlarge:min-w-8 p-xlarge:h-8`
    });
    const __returned__ = { theme, get Badge() {
      return script;
    }, get ptViewMerge() {
      return ptViewMerge;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Badge"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), createSlots({ _: 2 }, [
    renderList(_ctx.$slots, (_, slotName) => {
      return {
        name: slotName,
        fn: withCtx((slotProps, _push2, _parent2, _scopeId) => {
          if (_push2) {
            ssrRenderSlot(_ctx.$slots, slotName, slotProps ?? {}, null, _push2, _parent2, _scopeId);
          } else {
            return [
              renderSlot(_ctx.$slots, slotName, slotProps ?? {})
            ];
          }
        })
      };
    })
  ]), _parent));
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/Badge.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const Badge = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4]]);

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "CatalogList",
  props: {
    parts: {}
  },
  emits: ["partClick"],
  setup(__props, { expose: __expose }) {
    __expose();
    const ImageIcon = () => "\u{1F5BC}\uFE0F";
    const formatAttributeValue = (attr) => {
      if (!attr.value) return "";
      const value = attr.value;
      const unit = attr.template.unit;
      if (unit) {
        return `${value} ${unit.symbol || unit.name}`;
      }
      return value;
    };
    const formatDate = (date) => {
      if (!date) return "";
      const d = new Date(date);
      return d.toLocaleDateString("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    };
    const __returned__ = { ImageIcon, formatAttributeValue, formatDate, Card, Badge, SecondaryButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-list" }, _attrs))} data-v-8c03748a><div class="space-y-4" data-v-8c03748a><!--[-->`);
  ssrRenderList($props.parts, (part) => {
    _push(ssrRenderComponent($setup["Card"], {
      key: part.id,
      class: "part-item cursor-pointer hover:shadow-md transition-shadow duration-200",
      onClick: ($event) => _ctx.$emit("partClick", part)
    }, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="flex items-start gap-4" data-v-8c03748a${_scopeId}><div class="flex-shrink-0 w-20 h-20 bg-surface-100 dark:bg-surface-800 rounded-lg overflow-hidden" data-v-8c03748a${_scopeId}>`);
          if (part.image?.url) {
            _push2(`<img${ssrRenderAttr("src", part.image.url)}${ssrRenderAttr("alt", part.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C")} class="w-full h-full object-cover" loading="lazy" data-v-8c03748a${_scopeId}>`);
          } else {
            _push2(`<div class="w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500" data-v-8c03748a${_scopeId}>`);
            _push2(ssrRenderComponent($setup["ImageIcon"], { class: "w-8 h-8" }, null, _parent2, _scopeId));
            _push2(`</div>`);
          }
          _push2(`</div><div class="flex-1 min-w-0" data-v-8c03748a${_scopeId}><div class="flex items-start justify-between" data-v-8c03748a${_scopeId}><div class="flex-1 min-w-0" data-v-8c03748a${_scopeId}><div class="flex items-center gap-2 mb-2" data-v-8c03748a${_scopeId}><h3 class="font-medium text-surface-900 dark:text-surface-0 truncate" data-v-8c03748a${_scopeId}>${ssrInterpolate(part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`)}</h3>`);
          if (part.partCategory) {
            _push2(ssrRenderComponent($setup["Badge"], {
              value: part.partCategory.name,
              severity: "info",
              class: "text-xs"
            }, null, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
          if (part.path) {
            _push2(`<div class="text-sm text-surface-500 dark:text-surface-400 mb-2" data-v-8c03748a${_scopeId}> \u041F\u0443\u0442\u044C: ${ssrInterpolate(part.path)}</div>`);
          } else {
            _push2(`<!---->`);
          }
          if (part.attributes?.length) {
            _push2(`<div class="flex flex-wrap gap-x-4 gap-y-1 text-sm" data-v-8c03748a${_scopeId}><!--[-->`);
            ssrRenderList(part.attributes.slice(0, 5), (attr) => {
              _push2(`<div class="text-surface-600 dark:text-surface-300" data-v-8c03748a${_scopeId}><span class="font-medium" data-v-8c03748a${_scopeId}>${ssrInterpolate(attr.template.title)}:</span><span class="ml-1 text-surface-900 dark:text-surface-0" data-v-8c03748a${_scopeId}>${ssrInterpolate($setup.formatAttributeValue(attr))}</span></div>`);
            });
            _push2(`<!--]-->`);
            if (part.attributes.length > 5) {
              _push2(`<div class="text-surface-500 dark:text-surface-400 text-xs" data-v-8c03748a${_scopeId}> +${ssrInterpolate(part.attributes.length - 5)} \u0435\u0449\u0435 </div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div><div class="flex-shrink-0 ml-4" data-v-8c03748a${_scopeId}>`);
          _push2(ssrRenderComponent($setup["SecondaryButton"], {
            size: "small",
            outlined: "",
            onClick: ($event) => _ctx.$emit("partClick", part)
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 `);
              } else {
                return [
                  createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
          _push2(`</div></div><div class="flex items-center justify-between mt-3 pt-3 border-t border-surface-200 dark:border-surface-700" data-v-8c03748a${_scopeId}><div class="flex items-center gap-4 text-sm text-surface-500 dark:text-surface-400" data-v-8c03748a${_scopeId}><span data-v-8c03748a${_scopeId}>ID: ${ssrInterpolate(part.id)}</span>`);
          if (part.applicabilities?.length) {
            _push2(`<span data-v-8c03748a${_scopeId}>${ssrInterpolate(part.applicabilities.length)} ${ssrInterpolate(_ctx.pluralize(part.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"]))}</span>`);
          } else {
            _push2(`<!---->`);
          }
          if (part.equipmentApplicabilities?.length) {
            _push2(`<span data-v-8c03748a${_scopeId}>${ssrInterpolate(part.equipmentApplicabilities.length)} ${ssrInterpolate(_ctx.pluralize(part.equipmentApplicabilities.length, ["\u043C\u043E\u0434\u0435\u043B\u044C", "\u043C\u043E\u0434\u0435\u043B\u0438", "\u043C\u043E\u0434\u0435\u043B\u0435\u0439"]))} \u0442\u0435\u0445\u043D\u0438\u043A\u0438 </span>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div><div class="text-xs text-surface-400 dark:text-surface-500" data-v-8c03748a${_scopeId}>${ssrInterpolate($setup.formatDate(part.updatedAt))}</div></div></div></div>`);
        } else {
          return [
            createVNode("div", { class: "flex items-start gap-4" }, [
              createVNode("div", { class: "flex-shrink-0 w-20 h-20 bg-surface-100 dark:bg-surface-800 rounded-lg overflow-hidden" }, [
                part.image?.url ? (openBlock(), createBlock("img", {
                  key: 0,
                  src: part.image.url,
                  alt: part.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
                  class: "w-full h-full object-cover",
                  loading: "lazy"
                }, null, 8, ["src", "alt"])) : (openBlock(), createBlock("div", {
                  key: 1,
                  class: "w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
                }, [
                  createVNode($setup["ImageIcon"], { class: "w-8 h-8" })
                ]))
              ]),
              createVNode("div", { class: "flex-1 min-w-0" }, [
                createVNode("div", { class: "flex items-start justify-between" }, [
                  createVNode("div", { class: "flex-1 min-w-0" }, [
                    createVNode("div", { class: "flex items-center gap-2 mb-2" }, [
                      createVNode("h3", { class: "font-medium text-surface-900 dark:text-surface-0 truncate" }, toDisplayString(part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`), 1),
                      part.partCategory ? (openBlock(), createBlock($setup["Badge"], {
                        key: 0,
                        value: part.partCategory.name,
                        severity: "info",
                        class: "text-xs"
                      }, null, 8, ["value"])) : createCommentVNode("", true)
                    ]),
                    part.path ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "text-sm text-surface-500 dark:text-surface-400 mb-2"
                    }, " \u041F\u0443\u0442\u044C: " + toDisplayString(part.path), 1)) : createCommentVNode("", true),
                    part.attributes?.length ? (openBlock(), createBlock("div", {
                      key: 1,
                      class: "flex flex-wrap gap-x-4 gap-y-1 text-sm"
                    }, [
                      (openBlock(true), createBlock(Fragment, null, renderList(part.attributes.slice(0, 5), (attr) => {
                        return openBlock(), createBlock("div", {
                          key: attr.id,
                          class: "text-surface-600 dark:text-surface-300"
                        }, [
                          createVNode("span", { class: "font-medium" }, toDisplayString(attr.template.title) + ":", 1),
                          createVNode("span", { class: "ml-1 text-surface-900 dark:text-surface-0" }, toDisplayString($setup.formatAttributeValue(attr)), 1)
                        ]);
                      }), 128)),
                      part.attributes.length > 5 ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "text-surface-500 dark:text-surface-400 text-xs"
                      }, " +" + toDisplayString(part.attributes.length - 5) + " \u0435\u0449\u0435 ", 1)) : createCommentVNode("", true)
                    ])) : createCommentVNode("", true)
                  ]),
                  createVNode("div", { class: "flex-shrink-0 ml-4" }, [
                    createVNode($setup["SecondaryButton"], {
                      size: "small",
                      outlined: "",
                      onClick: withModifiers(($event) => _ctx.$emit("partClick", part), ["stop"])
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                      ]),
                      _: 2
                    }, 1032, ["onClick"])
                  ])
                ]),
                createVNode("div", { class: "flex items-center justify-between mt-3 pt-3 border-t border-surface-200 dark:border-surface-700" }, [
                  createVNode("div", { class: "flex items-center gap-4 text-sm text-surface-500 dark:text-surface-400" }, [
                    createVNode("span", null, "ID: " + toDisplayString(part.id), 1),
                    part.applicabilities?.length ? (openBlock(), createBlock("span", { key: 0 }, toDisplayString(part.applicabilities.length) + " " + toDisplayString(_ctx.pluralize(part.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"])), 1)) : createCommentVNode("", true),
                    part.equipmentApplicabilities?.length ? (openBlock(), createBlock("span", { key: 1 }, toDisplayString(part.equipmentApplicabilities.length) + " " + toDisplayString(_ctx.pluralize(part.equipmentApplicabilities.length, ["\u043C\u043E\u0434\u0435\u043B\u044C", "\u043C\u043E\u0434\u0435\u043B\u0438", "\u043C\u043E\u0434\u0435\u043B\u0435\u0439"])) + " \u0442\u0435\u0445\u043D\u0438\u043A\u0438 ", 1)) : createCommentVNode("", true)
                  ]),
                  createVNode("div", { class: "text-xs text-surface-400 dark:text-surface-500" }, toDisplayString($setup.formatDate(part.updatedAt)), 1)
                ])
              ])
            ])
          ];
        }
      }),
      _: 2
    }, _parent));
  });
  _push(`<!--]--></div></div>`);
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/catalog/CatalogList.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const CatalogList = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3], ["__scopeId", "data-v-8c03748a"]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "CatalogTable",
  props: {
    parts: {},
    loading: { type: Boolean, default: false },
    totalRecords: { default: 0 },
    itemsPerPage: { default: 20 }
  },
  emits: ["partClick"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const ImageIcon = () => "\u{1F5BC}\uFE0F";
    const props = __props;
    const emit = __emit;
    const onRowClick = (event) => {
      emit("partClick", event.data);
    };
    const formatAttributeValue = (attr) => {
      if (!attr.value) return "";
      const value = attr.value;
      const unit = attr.template.unit;
      if (unit) {
        return `${value} ${unit.symbol || unit.name}`;
      }
      return value;
    };
    const formatDate = (date) => {
      if (!date) return "";
      const d = new Date(date);
      return d.toLocaleDateString("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    };
    const __returned__ = { ImageIcon, props, emit, onRowClick, formatAttributeValue, formatDate, DataTable, get Column() {
      return script$1;
    }, Badge, SecondaryButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-table" }, _attrs))} data-v-d4823d7d>`);
  _push(ssrRenderComponent($setup["DataTable"], {
    value: $props.parts,
    loading: $props.loading,
    "striped-rows": "",
    hover: "",
    onRowClick: $setup.onRowClick,
    class: "cursor-pointer"
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          header: "\u0424\u043E\u0442\u043E",
          style: { width: "80px" }
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="w-12 h-12 bg-surface-100 dark:bg-surface-800 rounded overflow-hidden" data-v-d4823d7d${_scopeId2}>`);
              if (data.image?.url) {
                _push3(`<img${ssrRenderAttr("src", data.image.url)}${ssrRenderAttr("alt", data.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C")} class="w-full h-full object-cover" loading="lazy" data-v-d4823d7d${_scopeId2}>`);
              } else {
                _push3(`<div class="w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500" data-v-d4823d7d${_scopeId2}>`);
                _push3(ssrRenderComponent($setup["ImageIcon"], { class: "w-6 h-6" }, null, _parent3, _scopeId2));
                _push3(`</div>`);
              }
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "w-12 h-12 bg-surface-100 dark:bg-surface-800 rounded overflow-hidden" }, [
                  data.image?.url ? (openBlock(), createBlock("img", {
                    key: 0,
                    src: data.image.url,
                    alt: data.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
                    class: "w-full h-full object-cover",
                    loading: "lazy"
                  }, null, 8, ["src", "alt"])) : (openBlock(), createBlock("div", {
                    key: 1,
                    class: "w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
                  }, [
                    createVNode($setup["ImageIcon"], { class: "w-6 h-6" })
                  ]))
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "id",
          header: "ID",
          sortable: "",
          style: { width: "80px" }
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<span class="font-mono text-sm" data-v-d4823d7d${_scopeId2}>${ssrInterpolate(data.id)}</span>`);
            } else {
              return [
                createVNode("span", { class: "font-mono text-sm" }, toDisplayString(data.id), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "name",
          header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
          sortable: ""
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div data-v-d4823d7d${_scopeId2}><div class="font-medium text-surface-900 dark:text-surface-0" data-v-d4823d7d${_scopeId2}>${ssrInterpolate(data.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${data.id}`)}</div>`);
              if (data.path) {
                _push3(`<div class="text-sm text-surface-500 dark:text-surface-400" data-v-d4823d7d${_scopeId2}>${ssrInterpolate(data.path)}</div>`);
              } else {
                _push3(`<!---->`);
              }
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", null, [
                  createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(data.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${data.id}`), 1),
                  data.path ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-sm text-surface-500 dark:text-surface-400"
                  }, toDisplayString(data.path), 1)) : createCommentVNode("", true)
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "partCategory.name",
          header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
          sortable: ""
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              if (data.partCategory) {
                _push3(ssrRenderComponent($setup["Badge"], {
                  value: data.partCategory.name,
                  severity: "info"
                }, null, _parent3, _scopeId2));
              } else {
                _push3(`<span class="text-surface-400 dark:text-surface-500" data-v-d4823d7d${_scopeId2}>\u2014</span>`);
              }
            } else {
              return [
                data.partCategory ? (openBlock(), createBlock($setup["Badge"], {
                  key: 0,
                  value: data.partCategory.name,
                  severity: "info"
                }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                  key: 1,
                  class: "text-surface-400 dark:text-surface-500"
                }, "\u2014"))
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              if (data.attributes?.length) {
                _push3(`<div class="space-y-1" data-v-d4823d7d${_scopeId2}><!--[-->`);
                ssrRenderList(data.attributes.slice(0, 3), (attr) => {
                  _push3(`<div class="text-sm" data-v-d4823d7d${_scopeId2}><span class="text-surface-600 dark:text-surface-300" data-v-d4823d7d${_scopeId2}>${ssrInterpolate(attr.template.title)}: </span><span class="ml-1 text-surface-900 dark:text-surface-0 font-medium" data-v-d4823d7d${_scopeId2}>${ssrInterpolate($setup.formatAttributeValue(attr))}</span></div>`);
                });
                _push3(`<!--]-->`);
                if (data.attributes.length > 3) {
                  _push3(`<div class="text-xs text-surface-500 dark:text-surface-400" data-v-d4823d7d${_scopeId2}> +${ssrInterpolate(data.attributes.length - 3)} \u0435\u0449\u0435 </div>`);
                } else {
                  _push3(`<!---->`);
                }
                _push3(`</div>`);
              } else {
                _push3(`<span class="text-surface-400 dark:text-surface-500" data-v-d4823d7d${_scopeId2}>\u2014</span>`);
              }
            } else {
              return [
                data.attributes?.length ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "space-y-1"
                }, [
                  (openBlock(true), createBlock(Fragment, null, renderList(data.attributes.slice(0, 3), (attr) => {
                    return openBlock(), createBlock("div", {
                      key: attr.id,
                      class: "text-sm"
                    }, [
                      createVNode("span", { class: "text-surface-600 dark:text-surface-300" }, toDisplayString(attr.template.title) + ": ", 1),
                      createVNode("span", { class: "ml-1 text-surface-900 dark:text-surface-0 font-medium" }, toDisplayString($setup.formatAttributeValue(attr)), 1)
                    ]);
                  }), 128)),
                  data.attributes.length > 3 ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-xs text-surface-500 dark:text-surface-400"
                  }, " +" + toDisplayString(data.attributes.length - 3) + " \u0435\u0449\u0435 ", 1)) : createCommentVNode("", true)
                ])) : (openBlock(), createBlock("span", {
                  key: 1,
                  class: "text-surface-400 dark:text-surface-500"
                }, "\u2014"))
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0421\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u044C" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="space-y-1 text-sm" data-v-d4823d7d${_scopeId2}>`);
              if (data.applicabilities?.length) {
                _push3(`<div class="text-surface-600 dark:text-surface-300" data-v-d4823d7d${_scopeId2}>${ssrInterpolate(data.applicabilities.length)} ${ssrInterpolate(_ctx.pluralize(data.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"]))}</div>`);
              } else {
                _push3(`<!---->`);
              }
              if (data.equipmentApplicabilities?.length) {
                _push3(`<div class="text-surface-600 dark:text-surface-300" data-v-d4823d7d${_scopeId2}>${ssrInterpolate(data.equipmentApplicabilities.length)} ${ssrInterpolate(_ctx.pluralize(data.equipmentApplicabilities.length, ["\u043C\u043E\u0434\u0435\u043B\u044C", "\u043C\u043E\u0434\u0435\u043B\u0438", "\u043C\u043E\u0434\u0435\u043B\u0435\u0439"]))}</div>`);
              } else {
                _push3(`<!---->`);
              }
              if (!data.applicabilities?.length && !data.equipmentApplicabilities?.length) {
                _push3(`<span class="text-surface-400 dark:text-surface-500" data-v-d4823d7d${_scopeId2}>\u2014</span>`);
              } else {
                _push3(`<!---->`);
              }
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "space-y-1 text-sm" }, [
                  data.applicabilities?.length ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "text-surface-600 dark:text-surface-300"
                  }, toDisplayString(data.applicabilities.length) + " " + toDisplayString(_ctx.pluralize(data.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"])), 1)) : createCommentVNode("", true),
                  data.equipmentApplicabilities?.length ? (openBlock(), createBlock("div", {
                    key: 1,
                    class: "text-surface-600 dark:text-surface-300"
                  }, toDisplayString(data.equipmentApplicabilities.length) + " " + toDisplayString(_ctx.pluralize(data.equipmentApplicabilities.length, ["\u043C\u043E\u0434\u0435\u043B\u044C", "\u043C\u043E\u0434\u0435\u043B\u0438", "\u043C\u043E\u0434\u0435\u043B\u0435\u0439"])), 1)) : createCommentVNode("", true),
                  !data.applicabilities?.length && !data.equipmentApplicabilities?.length ? (openBlock(), createBlock("span", {
                    key: 2,
                    class: "text-surface-400 dark:text-surface-500"
                  }, "\u2014")) : createCommentVNode("", true)
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "updatedAt",
          header: "\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E",
          sortable: "",
          style: { width: "120px" }
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<span class="text-sm text-surface-500 dark:text-surface-400" data-v-d4823d7d${_scopeId2}>${ssrInterpolate($setup.formatDate(data.updatedAt))}</span>`);
            } else {
              return [
                createVNode("span", { class: "text-sm text-surface-500 dark:text-surface-400" }, toDisplayString($setup.formatDate(data.updatedAt)), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
          style: { width: "120px" }
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["SecondaryButton"], {
                size: "small",
                outlined: "",
                onClick: ($event) => _ctx.$emit("partClick", data)
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(` \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 `);
                  } else {
                    return [
                      createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["SecondaryButton"], {
                  size: "small",
                  outlined: "",
                  onClick: withModifiers(($event) => _ctx.$emit("partClick", data), ["stop"])
                }, {
                  default: withCtx(() => [
                    createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                  ]),
                  _: 2
                }, 1032, ["onClick"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            header: "\u0424\u043E\u0442\u043E",
            style: { width: "80px" }
          }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "w-12 h-12 bg-surface-100 dark:bg-surface-800 rounded overflow-hidden" }, [
                data.image?.url ? (openBlock(), createBlock("img", {
                  key: 0,
                  src: data.image.url,
                  alt: data.name || "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C",
                  class: "w-full h-full object-cover",
                  loading: "lazy"
                }, null, 8, ["src", "alt"])) : (openBlock(), createBlock("div", {
                  key: 1,
                  class: "w-full h-full flex items-center justify-center text-surface-400 dark:text-surface-500"
                }, [
                  createVNode($setup["ImageIcon"], { class: "w-6 h-6" })
                ]))
              ])
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "id",
            header: "ID",
            sortable: "",
            style: { width: "80px" }
          }, {
            body: withCtx(({ data }) => [
              createVNode("span", { class: "font-mono text-sm" }, toDisplayString(data.id), 1)
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "name",
            header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
            sortable: ""
          }, {
            body: withCtx(({ data }) => [
              createVNode("div", null, [
                createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(data.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${data.id}`), 1),
                data.path ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "text-sm text-surface-500 dark:text-surface-400"
                }, toDisplayString(data.path), 1)) : createCommentVNode("", true)
              ])
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "partCategory.name",
            header: "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u044F",
            sortable: ""
          }, {
            body: withCtx(({ data }) => [
              data.partCategory ? (openBlock(), createBlock($setup["Badge"], {
                key: 0,
                value: data.partCategory.name,
                severity: "info"
              }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                key: 1,
                class: "text-surface-400 dark:text-surface-500"
              }, "\u2014"))
            ]),
            _: 1
          }),
          createVNode($setup["Column"], { header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B" }, {
            body: withCtx(({ data }) => [
              data.attributes?.length ? (openBlock(), createBlock("div", {
                key: 0,
                class: "space-y-1"
              }, [
                (openBlock(true), createBlock(Fragment, null, renderList(data.attributes.slice(0, 3), (attr) => {
                  return openBlock(), createBlock("div", {
                    key: attr.id,
                    class: "text-sm"
                  }, [
                    createVNode("span", { class: "text-surface-600 dark:text-surface-300" }, toDisplayString(attr.template.title) + ": ", 1),
                    createVNode("span", { class: "ml-1 text-surface-900 dark:text-surface-0 font-medium" }, toDisplayString($setup.formatAttributeValue(attr)), 1)
                  ]);
                }), 128)),
                data.attributes.length > 3 ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "text-xs text-surface-500 dark:text-surface-400"
                }, " +" + toDisplayString(data.attributes.length - 3) + " \u0435\u0449\u0435 ", 1)) : createCommentVNode("", true)
              ])) : (openBlock(), createBlock("span", {
                key: 1,
                class: "text-surface-400 dark:text-surface-500"
              }, "\u2014"))
            ]),
            _: 1
          }),
          createVNode($setup["Column"], { header: "\u0421\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u044C" }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "space-y-1 text-sm" }, [
                data.applicabilities?.length ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "text-surface-600 dark:text-surface-300"
                }, toDisplayString(data.applicabilities.length) + " " + toDisplayString(_ctx.pluralize(data.applicabilities.length, ["\u043F\u043E\u0437\u0438\u0446\u0438\u044F", "\u043F\u043E\u0437\u0438\u0446\u0438\u0438", "\u043F\u043E\u0437\u0438\u0446\u0438\u0439"])), 1)) : createCommentVNode("", true),
                data.equipmentApplicabilities?.length ? (openBlock(), createBlock("div", {
                  key: 1,
                  class: "text-surface-600 dark:text-surface-300"
                }, toDisplayString(data.equipmentApplicabilities.length) + " " + toDisplayString(_ctx.pluralize(data.equipmentApplicabilities.length, ["\u043C\u043E\u0434\u0435\u043B\u044C", "\u043C\u043E\u0434\u0435\u043B\u0438", "\u043C\u043E\u0434\u0435\u043B\u0435\u0439"])), 1)) : createCommentVNode("", true),
                !data.applicabilities?.length && !data.equipmentApplicabilities?.length ? (openBlock(), createBlock("span", {
                  key: 2,
                  class: "text-surface-400 dark:text-surface-500"
                }, "\u2014")) : createCommentVNode("", true)
              ])
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "updatedAt",
            header: "\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E",
            sortable: "",
            style: { width: "120px" }
          }, {
            body: withCtx(({ data }) => [
              createVNode("span", { class: "text-sm text-surface-500 dark:text-surface-400" }, toDisplayString($setup.formatDate(data.updatedAt)), 1)
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
            style: { width: "120px" }
          }, {
            body: withCtx(({ data }) => [
              createVNode($setup["SecondaryButton"], {
                size: "small",
                outlined: "",
                onClick: withModifiers(($event) => _ctx.$emit("partClick", data), ["stop"])
              }, {
                default: withCtx(() => [
                  createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                ]),
                _: 2
              }, 1032, ["onClick"])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/catalog/CatalogTable.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const CatalogTable = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2], ["__scopeId", "data-v-d4823d7d"]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "CatalogBrowser",
  props: {
    initialFilters: {},
    showFilters: { type: Boolean, default: true },
    initialParts: {},
    initialCategories: {},
    initialBrands: {},
    categoriesQuery: {},
    brandsQuery: {}
  },
  emits: ["partClick"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const GridIcon = () => "\u229E";
    const ListIcon = () => "\u2630";
    const TableIcon = () => "\u229F";
    const props = __props;
    const emit = __emit;
    const currentPage = ref(1);
    const { filters, viewPreferences, hasFilters, queryParams, setSearch, setCategoryIds, setBrandIds, resetFilters, setViewMode, setItemsPerPage } = useCatalogFilters(props.initialFilters);
    const searchQuery = computed({
      get: () => filters.value.search || "",
      set: (v) => setSearch(v || "")
    });
    const selectedCategoryIds = computed({
      get: () => filters.value.categoryIds || [],
      set: (v) => setCategoryIds(v || [])
    });
    const selectedBrandIds = computed({
      get: () => filters.value.brandIds || [],
      set: (v) => setBrandIds(v || [])
    });
    const debouncedSearch = useDebounce(searchQuery, 300);
    const baseListInput = computed(() => ({
      ...queryParams.value,
      take: viewPreferences.value.itemsPerPage,
      skip: (currentPage.value - 1) * viewPreferences.value.itemsPerPage,
      orderBy: viewPreferences.value.sortBy ? {
        [viewPreferences.value.sortBy]: viewPreferences.value.sortOrder
      } : { name: "asc" },
      include: {
        partCategory: true,
        image: true,
        attributes: { include: { template: true } }
      }
    }));
    const { data: categories, error: categoriesError } = useQuery({
      queryKey: qk.categories.list(props.categoriesQuery),
      queryFn: () => fetchers.categories.list(props.categoriesQuery),
      initialData: props.initialCategories
    });
    useQueryToastErrors(categoriesError);
    const { data: brands, error: brandsError } = useQuery({
      queryKey: qk.brands.list(props.brandsQuery),
      queryFn: () => fetchers.brands.list(props.brandsQuery),
      initialData: props.initialBrands
    });
    useQueryToastErrors(brandsError);
    const partsQuery = useQuery({
      queryKey: computed(() => qk.parts.list(baseListInput.value)),
      queryFn: () => fetchers.parts.list(baseListInput.value),
      initialData: props.initialParts,
      placeholderData: (previousData) => previousData
    });
    useQueryToastErrors(partsQuery.error);
    const parts = computed(() => partsQuery.data?.value || []);
    const loading = computed(() => !!partsQuery.isFetching.value);
    const error = computed(() => partsQuery.error.value ? partsQuery.error.value?.message || "\u041E\u0448\u0438\u0431\u043A\u0430" : null);
    const countQuery = useQuery({
      queryKey: computed(() => qk.parts.count({ where: baseListInput.value.where })),
      queryFn: () => fetchers.parts.count({ where: baseListInput.value.where }),
      initialData: Array.isArray(props.initialParts) ? props.initialParts.length : 0,
      placeholderData: (prev) => prev
    });
    const totalCount = computed(() => {
      const cnt = countQuery.data?.value ?? void 0;
      return typeof cnt === "number" ? cnt : Array.isArray(parts.value) ? parts.value.length : 0;
    });
    const onSearchInput = () => {
    };
    const onPartClick = (part) => emit("partClick", part);
    const onPageChange = (page) => {
      currentPage.value = page;
    };
    const refetch = () => partsQuery.refetch();
    watch(debouncedSearch, (newValue, oldValue) => {
      if (newValue !== oldValue) {
        setSearch(String(newValue));
        currentPage.value = 1;
      }
    });
    watch(
      queryParams,
      (newValue, oldValue) => {
        if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
          currentPage.value = 1;
        }
      },
      { deep: true }
    );
    watch(
      () => viewPreferences.value.itemsPerPage,
      (newValue, oldValue) => {
        if (newValue !== oldValue) {
          currentPage.value = 1;
        }
      }
    );
    const partsLabel = computed(
      () => pluralize(totalCount.value, ["\u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C", "\u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438", "\u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439"])
    );
    onMounted(() => {
    });
    const __returned__ = { GridIcon, ListIcon, TableIcon, props, emit, currentPage, filters, viewPreferences, hasFilters, queryParams, setSearch, setCategoryIds, setBrandIds, resetFilters, setViewMode, setItemsPerPage, searchQuery, selectedCategoryIds, selectedBrandIds, debouncedSearch, baseListInput, categories, categoriesError, brands, brandsError, partsQuery, parts, loading, error, countQuery, totalCount, onSearchInput, onPartClick, onPageChange, refetch, partsLabel, Card, InputText, MultiSelect, SecondaryButton, CatalogGrid, CatalogList, CatalogTable, CatalogPagination, Spinner };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "catalog-browser" }, _attrs))} data-v-662d9b80><div class="mb-6 flex items-center justify-between" data-v-662d9b80><div class="flex items-center gap-4" data-v-662d9b80><h1 class="text-surface-900 dark:text-surface-0 text-2xl font-semibold" data-v-662d9b80>\u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439</h1>`);
  if ($setup.totalCount !== null) {
    _push(`<div class="text-surface-500 dark:text-surface-400" data-v-662d9b80>${ssrInterpolate($setup.totalCount)} ${ssrInterpolate($setup.partsLabel)}</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="flex items-center gap-2" data-v-662d9b80>`);
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    class: { "bg-primary text-primary-contrast": $setup.viewPreferences && $setup.viewPreferences.mode === "grid" },
    onClick: ($event) => $setup.setViewMode("grid"),
    "icon-only": "",
    title: "\u0421\u0435\u0442\u043A\u0430"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["GridIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["GridIcon"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    class: { "bg-primary text-primary-contrast": $setup.viewPreferences && $setup.viewPreferences.mode === "list" },
    onClick: ($event) => $setup.setViewMode("list"),
    "icon-only": "",
    title: "\u0421\u043F\u0438\u0441\u043E\u043A"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["ListIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["ListIcon"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["SecondaryButton"], {
    class: { "bg-primary text-primary-contrast": $setup.viewPreferences && $setup.viewPreferences.mode === "table" },
    onClick: ($event) => $setup.setViewMode("table"),
    "icon-only": "",
    title: "\u0422\u0430\u0431\u043B\u0438\u0446\u0430"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["TableIcon"], null, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["TableIcon"])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
  if ($props.showFilters) {
    _push(`<div class="mb-6" data-v-662d9b80>`);
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4" data-v-662d9b80${_scopeId}><div data-v-662d9b80${_scopeId}><label class="mb-2 block text-sm font-medium" data-v-662d9b80${_scopeId}>\u041F\u043E\u0438\u0441\u043A</label>`);
          _push2(ssrRenderComponent($setup["InputText"], {
            modelValue: $setup.searchQuery,
            "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439...",
            onInput: $setup.onSearchInput
          }, null, _parent2, _scopeId));
          _push2(`</div><div data-v-662d9b80${_scopeId}><label class="mb-2 block text-sm font-medium" data-v-662d9b80${_scopeId}>\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438</label>`);
          _push2(ssrRenderComponent($setup["MultiSelect"], {
            modelValue: $setup.selectedCategoryIds,
            "onUpdate:modelValue": ($event) => $setup.selectedCategoryIds = $event,
            options: $setup.categories,
            "option-label": "name",
            "option-value": "id",
            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
            onChange: ($event) => $setup.setCategoryIds($event.value || [])
          }, null, _parent2, _scopeId));
          _push2(`</div><div data-v-662d9b80${_scopeId}><label class="mb-2 block text-sm font-medium" data-v-662d9b80${_scopeId}>\u0411\u0440\u0435\u043D\u0434\u044B</label>`);
          _push2(ssrRenderComponent($setup["MultiSelect"], {
            modelValue: $setup.selectedBrandIds,
            "onUpdate:modelValue": ($event) => $setup.selectedBrandIds = $event,
            options: $setup.brands,
            "option-label": "name",
            "option-value": "id",
            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0431\u0440\u0435\u043D\u0434\u044B",
            onChange: ($event) => $setup.setBrandIds($event.value || [])
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="flex items-end" data-v-662d9b80${_scopeId}>`);
          if ($setup.hasFilters) {
            _push2(ssrRenderComponent($setup["SecondaryButton"], {
              onClick: $setup.resetFilters,
              outlined: ""
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B `);
                } else {
                  return [
                    createTextVNode(" \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div></div>`);
        } else {
          return [
            createVNode("div", { class: "grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "mb-2 block text-sm font-medium" }, "\u041F\u043E\u0438\u0441\u043A"),
                createVNode($setup["InputText"], {
                  modelValue: $setup.searchQuery,
                  "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                  placeholder: "\u041F\u043E\u0438\u0441\u043A \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439...",
                  onInput: $setup.onSearchInput
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "mb-2 block text-sm font-medium" }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"),
                createVNode($setup["MultiSelect"], {
                  modelValue: $setup.selectedCategoryIds,
                  "onUpdate:modelValue": ($event) => $setup.selectedCategoryIds = $event,
                  options: $setup.categories,
                  "option-label": "name",
                  "option-value": "id",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
                  onChange: ($event) => $setup.setCategoryIds($event.value || [])
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "onChange"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "mb-2 block text-sm font-medium" }, "\u0411\u0440\u0435\u043D\u0434\u044B"),
                createVNode($setup["MultiSelect"], {
                  modelValue: $setup.selectedBrandIds,
                  "onUpdate:modelValue": ($event) => $setup.selectedBrandIds = $event,
                  options: $setup.brands,
                  "option-label": "name",
                  "option-value": "id",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0431\u0440\u0435\u043D\u0434\u044B",
                  onChange: ($event) => $setup.setBrandIds($event.value || [])
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options", "onChange"])
              ]),
              createVNode("div", { class: "flex items-end" }, [
                $setup.hasFilters ? (openBlock(), createBlock($setup["SecondaryButton"], {
                  key: 0,
                  onClick: $setup.resetFilters,
                  outlined: ""
                }, {
                  default: withCtx(() => [
                    createTextVNode(" \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
                  ]),
                  _: 1
                }, 8, ["onClick"])) : createCommentVNode("", true)
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="catalog-content" data-v-662d9b80>`);
  if ($setup.loading) {
    _push(`<div class="flex justify-center py-12" data-v-662d9b80>`);
    _push(ssrRenderComponent($setup["Spinner"], {
      size: "lg",
      variant: "primary",
      label: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430...",
      centered: true
    }, null, _parent));
    _push(`</div>`);
  } else if ($setup.error) {
    _push(`<div class="py-12 text-center" data-v-662d9b80><div class="mb-4 text-red-500" data-v-662d9b80>${ssrInterpolate($setup.error)}</div>`);
    _push(ssrRenderComponent($setup["SecondaryButton"], { onClick: $setup.refetch }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`\u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C`);
        } else {
          return [
            createTextVNode("\u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C")
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  } else if (!$setup.parts?.length) {
    _push(`<div class="py-12 text-center" data-v-662d9b80><div class="text-surface-500 dark:text-surface-400 mb-4" data-v-662d9b80>${ssrInterpolate($setup.hasFilters ? "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B" : "\u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u043F\u0443\u0441\u0442")}</div>`);
    if ($setup.hasFilters) {
      _push(ssrRenderComponent($setup["SecondaryButton"], { onClick: $setup.resetFilters }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B `);
          } else {
            return [
              createTextVNode(" \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else {
    _push(`<div data-v-662d9b80>`);
    if ($setup.viewPreferences && $setup.viewPreferences.mode === "grid") {
      _push(ssrRenderComponent($setup["CatalogGrid"], {
        parts: $setup.parts,
        onPartClick: $setup.onPartClick
      }, null, _parent));
    } else if ($setup.viewPreferences && $setup.viewPreferences.mode === "list") {
      _push(ssrRenderComponent($setup["CatalogList"], {
        parts: $setup.parts,
        onPartClick: $setup.onPartClick
      }, null, _parent));
    } else if ($setup.viewPreferences && $setup.viewPreferences.mode === "table") {
      _push(ssrRenderComponent($setup["CatalogTable"], {
        parts: $setup.parts,
        onPartClick: $setup.onPartClick
      }, null, _parent));
    } else {
      _push(`<!---->`);
    }
    if ($setup.totalCount && $setup.viewPreferences && $setup.totalCount > $setup.viewPreferences.itemsPerPage) {
      _push(ssrRenderComponent($setup["CatalogPagination"], {
        "current-page": $setup.currentPage,
        "total-items": $setup.totalCount,
        "items-per-page": $setup.viewPreferences.itemsPerPage,
        onPageChange: $setup.onPageChange,
        onItemsPerPageChange: $setup.setItemsPerPage
      }, null, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  }
  _push(`</div></div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/catalog/CatalogBrowser.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const CatalogBrowser = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1], ["__scopeId", "data-v-662d9b80"]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "CatalogBrowserBoundary",
  props: {
    initialParts: {},
    initialCategories: {},
    initialBrands: {},
    categoriesQuery: {},
    brandsQuery: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const __returned__ = { CatalogBrowser };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["CatalogBrowser"], mergeProps({
    "initial-parts": $props.initialParts,
    "initial-categories": $props.initialCategories,
    "initial-brands": $props.initialBrands,
    "categories-query": $props.categoriesQuery,
    "brands-query": $props.brandsQuery
  }, _attrs), null, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/catalog/CatalogBrowserBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const CatalogBrowserBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Catalog = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Catalog;
  const sp = Astro2.url.searchParams;
  const raw = Object.fromEntries(sp.entries());
  const filters = queryToFilters(raw);
  const baseQuery = {
    ...filtersToQuery(filters),
    take: 20,
    skip: 0,
    orderBy: { name: "asc" },
    include: {
      partCategory: true,
      image: true,
      attributes: { include: { template: true } }
    }
  };
  const categoriesQuery = { where: { level: 0 }, orderBy: { name: "asc" } };
  const brandsQuery = {};
  let initialParts, initialCategories, initialBrands;
  try {
    [initialParts, initialCategories, initialBrands] = await Promise.all([
      trpc.crud.part.findMany.query(baseQuery),
      trpc.crud.partCategory.findMany.query(categoriesQuery),
      trpc.crud.brand.findMany.query(brandsQuery)
    ]);
  } catch (error) {
    console.error("Error loading initial data:", error);
    initialParts = [];
    initialCategories = [];
    initialBrands = [];
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "\u041A\u0430\u0442\u0430\u043B\u043E\u0433 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439", "data-astro-cid-6kjp6l6a": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8" data-astro-cid-6kjp6l6a> ${renderComponent($$result2, "CatalogBrowserBoundary", CatalogBrowserBoundary, { "client:load": true, "initialParts": initialParts, "initialCategories": initialCategories, "initialBrands": initialBrands, "categoriesQuery": categoriesQuery, "brandsQuery": brandsQuery, "client:component-hydration": "load", "client:component-path": "@/components/catalog/CatalogBrowserBoundary.vue", "client:component-export": "default", "data-astro-cid-6kjp6l6a": true })} </main> ` })} `;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/catalog.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/catalog.astro";
const $$url = "/catalog";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Catalog,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

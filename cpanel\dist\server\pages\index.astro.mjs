import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_AEsVEWgS.mjs';
import { defineComponent, useSSRContext, ref, computed, mergeProps, withCtx, createVNode, createBlock, createCommentVNode, createTextVNode, toDisplayString, openBlock } from 'vue';
import { B as Button } from '../chunks/Button_0V33JvkC.mjs';
import { C as Card } from '../chunks/Card_aE2_b9LT.mjs';
import { I as InputText } from '../chunks/InputText_DNFWprlB.mjs';
import { u as useTheme, T as ThemeToggle } from '../chunks/ThemeToggle_BLNtK__-.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderClass } from 'vue/server-renderer';
import { _ as _export_sfc } from '../chunks/ClientRouter_avhRMbqw.mjs';
import { Menu } from 'lucide-vue-next';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "HelloVue",
  setup(__props, { expose: __expose }) {
    __expose();
    const name = ref("");
    const clicked = ref(false);
    const clickCount = ref(0);
    const { themeName } = useTheme();
    const buttonLabel = computed(
      () => clicked.value ? "\u041D\u0430\u0436\u0430\u0442\u044C \u0435\u0449\u0435 \u0440\u0430\u0437" : "\u041D\u0430\u0436\u043C\u0438 \u043C\u0435\u043D\u044F!"
    );
    const handleClick = () => {
      clicked.value = true;
      clickCount.value++;
    };
    const __returned__ = { name, clicked, clickCount, themeName, buttonLabel, handleClick, Button, Card, InputText };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "p-6 max-w-md mx-auto" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["Card"], { class: "mb-4" }, {
    title: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<h2 class="text-2xl font-bold text-primary"${_scopeId}>\u0414\u043E\u0431\u0440\u043E \u043F\u043E\u0436\u0430\u043B\u043E\u0432\u0430\u0442\u044C \u0432 PartTec!</h2>`);
      } else {
        return [
          createVNode("h2", { class: "text-2xl font-bold text-primary" }, "\u0414\u043E\u0431\u0440\u043E \u043F\u043E\u0436\u0430\u043B\u043E\u0432\u0430\u0442\u044C \u0432 PartTec!")
        ];
      }
    }),
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<p class="text-surface-700 dark:text-surface-300 mb-4"${_scopeId}> \u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u0443\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u043E\u043C \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 </p><div class="mb-4 p-3 bg-surface-100 dark:bg-surface-200 rounded-md"${_scopeId}><p class="text-sm text-surface-600 dark:text-surface-400 mb-2"${_scopeId}> \u0422\u0435\u043A\u0443\u0449\u0430\u044F \u0442\u0435\u043C\u0430: <span class="font-semibold"${_scopeId}>${ssrInterpolate($setup.themeName)}</span></p><p class="text-xs text-surface-500 dark:text-surface-500"${_scopeId}> \u042D\u0442\u043E\u0442 \u0431\u043B\u043E\u043A \u0434\u0435\u043C\u043E\u043D\u0441\u0442\u0440\u0438\u0440\u0443\u0435\u0442 \u0430\u0434\u0430\u043F\u0442\u0438\u0432\u043D\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u043C\u0430\u043C </p></div><div class="mb-4"${_scopeId}><label for="name" class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0412\u0430\u0448\u0435 \u0438\u043C\u044F: </label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "name",
          modelValue: $setup.name,
          "onUpdate:modelValue": ($event) => $setup.name = $event,
          placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0432\u0430\u0448\u0435 \u0438\u043C\u044F",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
        if ($setup.name) {
          _push2(`<p class="text-surface-600 dark:text-surface-400 mb-4"${_scopeId}> \u041F\u0440\u0438\u0432\u0435\u0442, ${ssrInterpolate($setup.name)}! \u{1F44B} </p>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(ssrRenderComponent($setup["Button"], {
          label: $setup.buttonLabel,
          onClick: $setup.handleClick,
          class: "w-full"
        }, null, _parent2, _scopeId));
        if ($setup.clicked) {
          _push2(`<p class="text-primary-600 dark:text-primary-400 mt-2 text-center"${_scopeId}> \u041A\u043D\u043E\u043F\u043A\u0430 \u0431\u044B\u043B\u0430 \u043D\u0430\u0436\u0430\u0442\u0430 ${ssrInterpolate($setup.clickCount)} \u0440\u0430\u0437! </p>`);
        } else {
          _push2(`<!---->`);
        }
      } else {
        return [
          createVNode("p", { class: "text-surface-700 dark:text-surface-300 mb-4" }, " \u0421\u0438\u0441\u0442\u0435\u043C\u0430 \u0443\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u044F \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u043E\u043C \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 "),
          createVNode("div", { class: "mb-4 p-3 bg-surface-100 dark:bg-surface-200 rounded-md" }, [
            createVNode("p", { class: "text-sm text-surface-600 dark:text-surface-400 mb-2" }, [
              createTextVNode(" \u0422\u0435\u043A\u0443\u0449\u0430\u044F \u0442\u0435\u043C\u0430: "),
              createVNode("span", { class: "font-semibold" }, toDisplayString($setup.themeName), 1)
            ]),
            createVNode("p", { class: "text-xs text-surface-500 dark:text-surface-500" }, " \u042D\u0442\u043E\u0442 \u0431\u043B\u043E\u043A \u0434\u0435\u043C\u043E\u043D\u0441\u0442\u0440\u0438\u0440\u0443\u0435\u0442 \u0430\u0434\u0430\u043F\u0442\u0438\u0432\u043D\u043E\u0441\u0442\u044C \u043A \u0442\u0435\u043C\u0430\u043C ")
          ]),
          createVNode("div", { class: "mb-4" }, [
            createVNode("label", {
              for: "name",
              class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
            }, " \u0412\u0430\u0448\u0435 \u0438\u043C\u044F: "),
            createVNode($setup["InputText"], {
              id: "name",
              modelValue: $setup.name,
              "onUpdate:modelValue": ($event) => $setup.name = $event,
              placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0432\u0430\u0448\u0435 \u0438\u043C\u044F",
              class: "w-full"
            }, null, 8, ["modelValue", "onUpdate:modelValue"])
          ]),
          $setup.name ? (openBlock(), createBlock("p", {
            key: 0,
            class: "text-surface-600 dark:text-surface-400 mb-4"
          }, " \u041F\u0440\u0438\u0432\u0435\u0442, " + toDisplayString($setup.name) + "! \u{1F44B} ", 1)) : createCommentVNode("", true),
          createVNode($setup["Button"], {
            label: $setup.buttonLabel,
            onClick: $setup.handleClick,
            class: "w-full"
          }, null, 8, ["label"]),
          $setup.clicked ? (openBlock(), createBlock("p", {
            key: 1,
            class: "text-primary-600 dark:text-primary-400 mt-2 text-center"
          }, " \u041A\u043D\u043E\u043F\u043A\u0430 \u0431\u044B\u043B\u0430 \u043D\u0430\u0436\u0430\u0442\u0430 " + toDisplayString($setup.clickCount) + " \u0440\u0430\u0437! ", 1)) : createCommentVNode("", true)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/HelloVue.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const HelloVue = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Navbar",
  setup(__props, { expose: __expose }) {
    __expose();
    const showMobileMenu = ref(false);
    const currentPath = computed(() => {
      if (typeof window !== "undefined") {
        return window.location.pathname;
      }
      return "";
    });
    const isActive = (path) => {
      if (path === "/") {
        return currentPath.value === "/";
      }
      return currentPath.value.startsWith(path);
    };
    const toggleMobileMenu = () => {
      showMobileMenu.value = !showMobileMenu.value;
    };
    const closeMobileMenu = () => {
      showMobileMenu.value = false;
    };
    const __returned__ = { showMobileMenu, currentPath, isActive, toggleMobileMenu, closeMobileMenu, ThemeToggle, get Menu() {
      return Menu;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<nav${ssrRenderAttrs(mergeProps({ class: "bg-[--p-surface-card] border-b border-[--color-border] shadow-sm" }, _attrs))}><div class="container mx-auto px-4"><div class="flex justify-between items-center h-16"><div class="flex items-center"><img class="h-8 w-8" src="/favicon.svg" alt="PartTec"><span class="ml-3 text-xl font-semibold text-[--color-foreground]"> PartTec </span></div><div class="hidden md:flex items-center space-x-6"><a href="/" class="${ssrRenderClass([{ "text-[--color-primary] font-medium": $setup.isActive("/") }, "text-[--color-muted] hover:text-[--color-primary] transition-colors"])}"> \u0413\u043B\u0430\u0432\u043D\u0430\u044F </a><a href="/catalog" class="${ssrRenderClass([{ "text-[--color-primary] font-medium": $setup.isActive("/catalog") }, "text-[--color-muted] hover:text-[--color-primary] transition-colors"])}"> \u041A\u0430\u0442\u0430\u043B\u043E\u0433 </a><a href="/search" class="${ssrRenderClass([{ "text-[--color-primary] font-medium": $setup.isActive("/search") }, "text-[--color-muted] hover:text-[--color-primary] transition-colors"])}"> \u041F\u043E\u0438\u0441\u043A </a><a href="/stats" class="${ssrRenderClass([{ "text-[--color-primary] font-medium": $setup.isActive("/stats") }, "text-[--color-muted] hover:text-[--color-primary] transition-colors"])}"> \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 </a><a href="/admin" class="${ssrRenderClass([{ "text-[--color-primary] font-medium": $setup.isActive("/admin") }, "text-[--color-muted] hover:text-[--color-primary] transition-colors"])}"> \u0410\u0434\u043C\u0438\u043D \u043F\u0430\u043D\u0435\u043B\u044C </a></div><div class="flex items-center space-x-4">`);
  _push(ssrRenderComponent($setup["ThemeToggle"], { mode: "toggle" }, null, _parent));
  _push(`<button class="md:hidden p-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors">`);
  _push(ssrRenderComponent($setup["Menu"], { size: 20 }, null, _parent));
  _push(`</button></div></div>`);
  if ($setup.showMobileMenu) {
    _push(`<div class="md:hidden border-t border-[--color-border] py-4"><div class="space-y-2"><a href="/" class="${ssrRenderClass([{ "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.isActive("/") }, "block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"])}"> \u0413\u043B\u0430\u0432\u043D\u0430\u044F </a><a href="/catalog" class="${ssrRenderClass([{ "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.isActive("/catalog") }, "block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"])}"> \u041A\u0430\u0442\u0430\u043B\u043E\u0433 </a><a href="/search" class="${ssrRenderClass([{ "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.isActive("/search") }, "block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"])}"> \u041F\u043E\u0438\u0441\u043A </a><a href="/stats" class="${ssrRenderClass([{ "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.isActive("/stats") }, "block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"])}"> \u0421\u0442\u0430\u0442\u0438\u0441\u0442\u0438\u043A\u0430 </a><a href="/admin" class="${ssrRenderClass([{ "bg-[--p-highlight-background] text-[--p-primary-color]": $setup.isActive("/admin") }, "block px-3 py-2 rounded-md text-[--color-muted] hover:bg-[--p-content-hover-background] transition-colors"])}"> \u0410\u0434\u043C\u0438\u043D \u043F\u0430\u043D\u0435\u043B\u044C </a></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div></nav>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/ui/Navbar.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const Navbar = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Index = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "PartTec - \u0413\u043B\u0430\u0432\u043D\u0430\u044F" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "Navbar", Navbar, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/ui/Navbar.vue", "client:component-export": "default" })} ${maybeRenderHead()}<main class="min-h-screen bg-surface-50 dark:bg-surface-900 py-8"> <div class="container mx-auto px-4"> <div class="text-center mb-8"> <h1 class="text-4xl font-bold text-surface-900 dark:text-surface-50 mb-2">
PartTec
</h1> <p class="text-xl text-surface-600 dark:text-surface-400">
Система управления каталогом взаимозаменяемых запчастей
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"> <div class="bg-surface-0 dark:bg-surface-100 rounded-lg shadow-md p-6 border border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-semibold text-surface-800 dark:text-surface-200 mb-2">🚀 Astro</h3> <p class="text-surface-600 dark:text-surface-400">Современный статический генератор сайтов</p> </div> <div class="bg-surface-0 dark:bg-surface-100 rounded-lg shadow-md p-6 border border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-semibold text-surface-800 dark:text-surface-200 mb-2">⚡ Vue 3</h3> <p class="text-surface-600 dark:text-surface-400">Прогрессивный JavaScript фреймворк</p> </div> <div class="bg-surface-0 dark:bg-surface-100 rounded-lg shadow-md p-6 border border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-semibold text-surface-800 dark:text-surface-200 mb-2">🎨 Tailwind CSS</h3> <p class="text-surface-600 dark:text-surface-400">Utility-first CSS фреймворк</p> </div> <div class="bg-surface-0 dark:bg-surface-100 rounded-lg shadow-md p-6 border border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-semibold text-surface-800 dark:text-surface-200 mb-2">🎯 PrimeVue</h3> <p class="text-surface-600 dark:text-surface-400">Богатый набор UI компонентов</p> </div> <div class="bg-surface-0 dark:bg-surface-100 rounded-lg shadow-md p-6 border border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-semibold text-surface-800 dark:text-surface-200 mb-2">⚡ Volt UI</h3> <p class="text-surface-600 dark:text-surface-400">Современная дизайн система</p> </div> <div class="bg-surface-0 dark:bg-surface-100 rounded-lg shadow-md p-6 border border-surface-200 dark:border-surface-700"> <h3 class="text-lg font-semibold text-surface-800 dark:text-surface-200 mb-2">🔧 TypeScript</h3> <p class="text-surface-600 dark:text-surface-400">Типизированный JavaScript</p> </div> </div> <div class="flex justify-center"> ${renderComponent($$result2, "HelloVue", HelloVue, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/HelloVue.vue", "client:component-export": "default" })} </div> </div> </main> ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

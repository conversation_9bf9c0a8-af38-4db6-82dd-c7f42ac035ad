import { e as createComponent, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { I as Icon, u as useAuth, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { defineComponent, useSSRContext, computed, ref, nextTick, mergeProps, watch, withCtx, createVNode, createBlock, createCommentVNode, openBlock, toDisplayString, onMounted, onUnmounted } from 'vue';
import { E as ErrorBoundary } from '../../chunks/ErrorBoundary_BKC82unE.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_CjmFMz0m.mjs';
import { u as useUrlParams } from '../../chunks/useUrlParams_CGyErwK-.mjs';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { D as DataTable, s as script, a as script$1 } from '../../chunks/index_CxapvcaX.mjs';
import { T as Tag } from '../../chunks/Tag_B6nH2bAR.mjs';
import { D as Dialog } from '../../chunks/Dialog_DqmfICId.mjs';
import { _ as _export_sfc, u as useToast } from '../../chunks/ClientRouter_avhRMbqw.mjs';
import { V as VTextarea } from '../../chunks/Textarea_nBNQZgaf.mjs';
import { I as InputNumber } from '../../chunks/InputNumber_B4WnM2Ea.mjs';
import TimesIcon from '@primevue/icons/times';
import { ssrRenderAttrs, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrRenderComponent, ssrRenderAttr } from 'vue/server-renderer';
import { C as Checkbox } from '../../chunks/Checkbox_Ca7GoCvq.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_BeMdq3W3.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import { TrashIcon, PencilIcon, ListIcon, PlusCircleIcon } from 'lucide-vue-next';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const containerClass = `m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`;
const tokenClass = `py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`;
const labelClass = `leading-none`;
const removeIconClass = `ml-2 w-4 h-4 cursor-pointer`;
const inputClass = `border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500 flex-1 inline-flex`;
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "InputChips",
  props: {
    modelValue: {},
    separator: {},
    addOnBlur: { type: Boolean },
    allowDuplicate: { type: Boolean },
    max: {}
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const model = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const inputValue = ref("");
    const inputRef = ref(null);
    const addValue = () => {
      if (inputValue.value.trim() !== "") {
        if (props.max && model.value.length >= props.max) {
          return;
        }
        if (!props.allowDuplicate && model.value.includes(inputValue.value.trim())) {
          inputValue.value = "";
          return;
        }
        model.value = [...model.value, inputValue.value.trim()];
        inputValue.value = "";
      }
    };
    const removeValue = (index) => {
      model.value = model.value.filter((_, i) => i !== index);
    };
    const handleBackspace = () => {
      if (inputValue.value === "" && model.value.length > 0) {
        removeValue(model.value.length - 1);
      }
    };
    const focusInput = async () => {
      await nextTick();
      inputRef.value?.focus();
    };
    const __returned__ = { props, emit, model, inputValue, inputRef, addValue, removeValue, handleBackspace, focusInput, containerClass, tokenClass, labelClass, removeIconClass, inputClass, get TimesIcon() {
      return TimesIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$6(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: $setup.containerClass }, _attrs))}><!--[-->`);
  ssrRenderList($setup.model, (value, index) => {
    _push(`<div class="${ssrRenderClass($setup.tokenClass)}"><span class="${ssrRenderClass($setup.labelClass)}">${ssrInterpolate(value)}</span><span class="${ssrRenderClass($setup.removeIconClass)}">`);
    _push(ssrRenderComponent($setup["TimesIcon"], null, null, _parent));
    _push(`</span></div>`);
  });
  _push(`<!--]--><input type="text"${ssrRenderAttr("value", $setup.inputValue)} class="${ssrRenderClass($setup.inputClass)}"></div>`);
}
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/InputChips.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const InputChips = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["ssrRender", _sfc_ssrRender$6]]);

const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "EditSynonymGroupDialog",
  props: {
    visible: { type: Boolean },
    templateId: {},
    group: {}
  },
  emits: ["update:visible", "saved"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { attributeSynonyms } = useTrpc();
    const toast = useToast();
    const visible = computed({
      get: () => props.visible,
      set: (v) => emit("update:visible", v)
    });
    const isEdit = computed(() => !!props.group?.id);
    const dialogTitle = computed(() => isEdit.value ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
    const compatibilityOptions = [
      { label: "EXACT", value: "EXACT" },
      { label: "NEAR", value: "NEAR" },
      { label: "LEGACY", value: "LEGACY" }
    ];
    const form = ref({
      name: "",
      description: "",
      compatibilityLevel: "EXACT",
      notes: ""
    });
    const errors = ref({});
    const saving = ref(false);
    watch(() => props.group, (g) => {
      if (g) {
        form.value = {
          name: g.name || "",
          description: g.description || null,
          compatibilityLevel: g.compatibilityLevel || "EXACT",
          notes: g.notes || null
        };
      } else {
        form.value = { name: "", description: null, compatibilityLevel: "EXACT", notes: null };
      }
    }, { immediate: true });
    const validate = () => {
      errors.value = {};
      if (!form.value.name.trim()) errors.value.name = "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435";
      return Object.keys(errors.value).length === 0;
    };
    const close = () => {
      visible.value = false;
    };
    const save = async () => {
      if (!validate()) return;
      saving.value = true;
      try {
        if (isEdit.value) {
          await attributeSynonyms.groups.update({ id: props.group.id, ...form.value });
          toast.success("\u0413\u0440\u0443\u043F\u043F\u0430 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0430");
        } else {
          await attributeSynonyms.groups.create({ templateId: props.templateId, ...form.value });
          toast.success("\u0413\u0440\u0443\u043F\u043F\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0430");
        }
        emit("saved");
        close();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
      } finally {
        saving.value = false;
      }
    };
    const __returned__ = { props, emit, attributeSynonyms, toast, visible, isEdit, dialogTitle, compatibilityOptions, form, errors, saving, validate, close, save, VDialog: Dialog, VInputText: InputText, VTextarea, VButton: Button, VSelect: Select };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$5(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["VDialog"], mergeProps({
    visible: $setup.visible,
    "onUpdate:visible": ($event) => $setup.visible = $event,
    modal: "",
    header: $setup.dialogTitle,
    style: { width: "34rem" }
  }, _attrs), {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: $setup.close
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: $setup.isEdit ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
          loading: $setup.saving,
          onClick: $setup.save
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VButton"], {
            label: "\u041E\u0442\u043C\u0435\u043D\u0430",
            severity: "secondary",
            onClick: $setup.close
          }),
          createVNode($setup["VButton"], {
            label: $setup.isEdit ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
            loading: $setup.saving,
            onClick: $setup.save
          }, null, 8, ["label", "loading"])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 *</label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.form.name,
          "onUpdate:modelValue": ($event) => $setup.form.name = $event,
          placeholder: "\u0421\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u044B\u0435 \u0442\u0438\u043F\u044B \u0443\u043F\u043B\u043E\u0442\u043D\u0435\u043D\u0438\u0439",
          class: ["w-full", { "p-invalid": !!$setup.errors.name }]
        }, null, _parent2, _scopeId));
        if ($setup.errors.name) {
          _push2(`<small class="p-error"${_scopeId}>${ssrInterpolate($setup.errors.name)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435</label>`);
        _push2(ssrRenderComponent($setup["VTextarea"], {
          modelValue: $setup.form.description,
          "onUpdate:modelValue": ($event) => $setup.form.description = $event,
          rows: "2",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0423\u0440\u043E\u0432\u0435\u043D\u044C \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438</label>`);
        _push2(ssrRenderComponent($setup["VSelect"], {
          modelValue: $setup.form.compatibilityLevel,
          "onUpdate:modelValue": ($event) => $setup.form.compatibilityLevel = $event,
          options: $setup.compatibilityOptions,
          "option-label": "label",
          "option-value": "value",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}>\u0417\u0430\u043C\u0435\u0442\u043A\u0438</label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.form.notes,
          "onUpdate:modelValue": ($event) => $setup.form.notes = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0434\u043B\u044F \u0441\u0442\u0430\u0440\u044B\u0445 \u0441\u043F\u0435\u0446\u0438\u0444\u0438\u043A\u0430\u0446\u0438\u0439",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div></div></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 *"),
              createVNode($setup["VInputText"], {
                modelValue: $setup.form.name,
                "onUpdate:modelValue": ($event) => $setup.form.name = $event,
                placeholder: "\u0421\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u044B\u0435 \u0442\u0438\u043F\u044B \u0443\u043F\u043B\u043E\u0442\u043D\u0435\u043D\u0438\u0439",
                class: ["w-full", { "p-invalid": !!$setup.errors.name }]
              }, null, 8, ["modelValue", "onUpdate:modelValue", "class"]),
              $setup.errors.name ? (openBlock(), createBlock("small", {
                key: 0,
                class: "p-error"
              }, toDisplayString($setup.errors.name), 1)) : createCommentVNode("", true)
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.form.description,
                "onUpdate:modelValue": ($event) => $setup.form.description = $event,
                rows: "2",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0423\u0440\u043E\u0432\u0435\u043D\u044C \u0441\u043E\u0432\u043C\u0435\u0441\u0442\u0438\u043C\u043E\u0441\u0442\u0438"),
                createVNode($setup["VSelect"], {
                  modelValue: $setup.form.compatibilityLevel,
                  "onUpdate:modelValue": ($event) => $setup.form.compatibilityLevel = $event,
                  options: $setup.compatibilityOptions,
                  "option-label": "label",
                  "option-value": "value",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, "\u0417\u0430\u043C\u0435\u0442\u043A\u0438"),
                createVNode($setup["VInputText"], {
                  modelValue: $setup.form.notes,
                  "onUpdate:modelValue": ($event) => $setup.form.notes = $event,
                  placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0434\u043B\u044F \u0441\u0442\u0430\u0440\u044B\u0445 \u0441\u043F\u0435\u0446\u0438\u0444\u0438\u043A\u0430\u0446\u0438\u0439",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue"])
              ])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/EditSynonymGroupDialog.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const EditSynonymGroupDialog = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["ssrRender", _sfc_ssrRender$5]]);

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "SynonymValueEditor",
  props: {
    groupId: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const { attributeSynonyms } = useTrpc();
    const toast = useToast();
    const synonyms = ref([]);
    const loading = ref(false);
    const newValue = ref("");
    const canAdd = ref(false);
    watch(newValue, (v) => {
      const trimmed = (v || "").trim();
      canAdd.value = trimmed.length > 0 && !synonyms.value.some((s) => s.value.toLowerCase() === trimmed.toLowerCase());
    });
    const load = async () => {
      loading.value = true;
      try {
        const result = await attributeSynonyms.synonyms.findMany({ groupId: props.groupId });
        if (Array.isArray(result)) synonyms.value = result;
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F");
      } finally {
        loading.value = false;
      }
    };
    const addValue = async () => {
      const value = newValue.value.trim();
      if (!value) return;
      if (synonyms.value.some((s) => s.value.toLowerCase() === value.toLowerCase())) {
        toast.error("\u0414\u0443\u0431\u043B\u0438\u043A\u0430\u0442\u044B \u043D\u0435 \u0434\u043E\u043F\u0443\u0441\u043A\u0430\u044E\u0442\u0441\u044F");
        return;
      }
      try {
        const created = await attributeSynonyms.synonyms.create({ groupId: props.groupId, value });
        if (created && typeof created === "object") {
          synonyms.value.push(created);
          newValue.value = "";
        }
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435");
      }
    };
    const removeValue = async (row) => {
      if (!confirm("\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435?")) return;
      try {
        await attributeSynonyms.synonyms.delete({ id: row.id });
        synonyms.value = synonyms.value.filter((s) => s.id !== row.id);
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435");
      }
    };
    onMounted(load);
    watch(() => props.groupId, load);
    const __returned__ = { props, attributeSynonyms, toast, synonyms, loading, newValue, canAdd, load, addValue, removeValue, VInputText: InputText, VButton: Button, VDataTable: DataTable, get Column() {
      return script;
    }, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "space-y-4" }, _attrs))}><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.newValue,
    "onUpdate:modelValue": ($event) => $setup.newValue = $event,
    placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
    class: "flex-1",
    onKeyup: $setup.addValue
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
    onClick: $setup.addValue,
    disabled: !$setup.canAdd
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-plus",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-plus",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["VDataTable"], {
    value: $setup.synonyms,
    loading: $setup.loading,
    class: "p-datatable-sm",
    "table-style": "min-width: 24rem",
    "striped-rows": ""
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          field: "value",
          header: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          header: "",
          style: { "width": "80px" }
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["VButton"], {
                size: "small",
                severity: "danger",
                outlined: "",
                onClick: ($event) => $setup.removeValue(data)
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["Icon"], {
                      name: "pi pi-trash",
                      class: "w-5 h-5"
                    }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["Icon"], {
                        name: "pi pi-trash",
                        class: "w-5 h-5"
                      })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "danger",
                  outlined: "",
                  onClick: ($event) => $setup.removeValue(data)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["Icon"], {
                      name: "pi pi-trash",
                      class: "w-5 h-5"
                    })
                  ]),
                  _: 2
                }, 1032, ["onClick"])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            field: "value",
            header: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435"
          }),
          createVNode($setup["Column"], {
            header: "",
            style: { "width": "80px" }
          }, {
            body: withCtx(({ data }) => [
              createVNode($setup["VButton"], {
                size: "small",
                severity: "danger",
                outlined: "",
                onClick: ($event) => $setup.removeValue(data)
              }, {
                default: withCtx(() => [
                  createVNode($setup["Icon"], {
                    name: "pi pi-trash",
                    class: "w-5 h-5"
                  })
                ]),
                _: 2
              }, 1032, ["onClick"])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/SynonymValueEditor.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const SynonymValueEditor = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4]]);

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "AttributeSynonymManager",
  props: {
    template: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const toast = useToast();
    const { attributeSynonyms } = useTrpc();
    const groups = ref([]);
    const total = ref(0);
    const loadingGroups = ref(false);
    const search = ref("");
    const selectedGroup = ref(null);
    const showGroupDialog = ref(false);
    const editingGroup = ref(null);
    let reloadTimer;
    const loadGroups = async () => {
      if (!props.template?.id) return;
      loadingGroups.value = true;
      try {
        const result = await attributeSynonyms.groups.findMany({ templateId: props.template.id, search: search.value || void 0, limit: 50, offset: 0 });
        if (result && typeof result === "object") {
          groups.value = result.groups || [];
          total.value = result.total || 0;
          if (selectedGroup.value) {
            const updated = groups.value.find((g) => g.id === selectedGroup.value.id);
            if (updated) selectedGroup.value = updated;
          }
        }
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u044B");
      } finally {
        loadingGroups.value = false;
      }
    };
    const debouncedReload = () => {
      clearTimeout(reloadTimer);
      reloadTimer = setTimeout(() => loadGroups(), 400);
    };
    const compatibilityLabel = (level) => {
      switch (level) {
        case "EXACT":
          return "EXACT";
        case "NEAR":
          return "NEAR";
        case "LEGACY":
          return "LEGACY";
        default:
          return String(level);
      }
    };
    const compatibilitySeverity = (level) => {
      return level === "EXACT" ? "success" : level === "NEAR" ? "warn" : "secondary";
    };
    const selectGroup = (group) => {
      selectedGroup.value = group;
    };
    const openCreateGroup = () => {
      editingGroup.value = null;
      showGroupDialog.value = true;
    };
    const openEditGroup = (group) => {
      editingGroup.value = group;
      showGroupDialog.value = true;
    };
    const deleteGroup = async (group) => {
      if (!confirm(`\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 "${group.name}"?`)) return;
      try {
        await attributeSynonyms.groups.delete({ id: group.id });
        if (selectedGroup.value?.id === group.id) selectedGroup.value = null;
        loadGroups();
      } catch (e) {
        toast.error(e?.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
      }
    };
    const onGroupSaved = () => {
      showGroupDialog.value = false;
      loadGroups();
    };
    onMounted(() => {
      loadGroups();
    });
    const __returned__ = { props, toast, attributeSynonyms, groups, total, loadingGroups, search, selectedGroup, showGroupDialog, editingGroup, get reloadTimer() {
      return reloadTimer;
    }, set reloadTimer(v) {
      reloadTimer = v;
    }, loadGroups, debouncedReload, compatibilityLabel, compatibilitySeverity, selectGroup, openCreateGroup, openEditGroup, deleteGroup, onGroupSaved, VCard: Card, VButton: Button, VInputText: InputText, VDataTable: DataTable, VTag: Tag, get Column() {
      return script;
    }, EditSynonymGroupDialog, SynonymValueEditor, get PlusCircleIcon() {
      return PlusCircleIcon;
    }, get ListIcon() {
      return ListIcon;
    }, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "flex justify-center gap-4" }, _attrs))}>`);
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 space-y-3"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432</h3>`);
        _push2(ssrRenderComponent($setup["VButton"], {
          size: "small",
          onClick: $setup.openCreateGroup
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["PlusCircleIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["PlusCircleIcon"], { class: "w-4 h-4" })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.search,
          "onUpdate:modelValue": ($event) => $setup.search = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E / \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E...",
          onInput: $setup.debouncedReload
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VDataTable"], {
          value: $setup.groups,
          loading: $setup.loadingGroups,
          "table-style": "min-width: 24rem",
          class: "p-datatable-sm",
          "striped-rows": ""
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["Column"], { header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435" }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex flex-col"${_scopeId3}><div class="font-medium"${_scopeId3}>${ssrInterpolate(data.name)}</div>`);
                    if (data.description) {
                      _push4(`<small class="text-surface-500"${_scopeId3}>${ssrInterpolate(data.description)}</small>`);
                    } else {
                      _push4(`<!---->`);
                    }
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex flex-col" }, [
                        createVNode("div", { class: "font-medium" }, toDisplayString(data.name), 1),
                        data.description ? (openBlock(), createBlock("small", {
                          key: 0,
                          class: "text-surface-500"
                        }, toDisplayString(data.description), 1)) : createCommentVNode("", true)
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], {
                header: "\u0423\u0440\u043E\u0432\u0435\u043D\u044C",
                style: { "width": "120px" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["VTag"], {
                      value: $setup.compatibilityLabel(data.compatibilityLevel),
                      severity: $setup.compatibilitySeverity(data.compatibilityLevel)
                    }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["VTag"], {
                        value: $setup.compatibilityLabel(data.compatibilityLevel),
                        severity: $setup.compatibilitySeverity(data.compatibilityLevel)
                      }, null, 8, ["value", "severity"])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], {
                header: "#",
                style: { "width": "64px" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["VTag"], {
                      value: data._count?.synonyms || 0,
                      severity: "secondary"
                    }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["VTag"], {
                        value: data._count?.synonyms || 0,
                        severity: "secondary"
                      }, null, 8, ["value"])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Column"], {
                header: "",
                style: { "width": "150px" }
              }, {
                body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(`<div class="flex gap-2"${_scopeId3}>`);
                    _push4(ssrRenderComponent($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: ($event) => $setup.selectGroup(data)
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["ListIcon"], { class: "w-4 h-4" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["ListIcon"], { class: "w-4 h-4" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VButton"], {
                      size: "small",
                      severity: "secondary",
                      outlined: "",
                      onClick: ($event) => $setup.openEditGroup(data)
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(ssrRenderComponent($setup["VButton"], {
                      size: "small",
                      severity: "danger",
                      outlined: "",
                      onClick: ($event) => $setup.deleteGroup(data)
                    }, {
                      default: withCtx((_3, _push5, _parent5, _scopeId4) => {
                        if (_push5) {
                          _push5(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent5, _scopeId4));
                        } else {
                          return [
                            createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent4, _scopeId3));
                    _push4(`</div>`);
                  } else {
                    return [
                      createVNode("div", { class: "flex gap-2" }, [
                        createVNode($setup["VButton"], {
                          size: "small",
                          severity: "secondary",
                          outlined: "",
                          onClick: ($event) => $setup.selectGroup(data)
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["ListIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["VButton"], {
                          size: "small",
                          severity: "secondary",
                          outlined: "",
                          onClick: ($event) => $setup.openEditGroup(data)
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["VButton"], {
                          size: "small",
                          severity: "danger",
                          outlined: "",
                          onClick: ($event) => $setup.deleteGroup(data)
                        }, {
                          default: withCtx(() => [
                            createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ])
                    ];
                  }
                }),
                _: 1
              }, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["Column"], { header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435" }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex flex-col" }, [
                      createVNode("div", { class: "font-medium" }, toDisplayString(data.name), 1),
                      data.description ? (openBlock(), createBlock("small", {
                        key: 0,
                        class: "text-surface-500"
                      }, toDisplayString(data.description), 1)) : createCommentVNode("", true)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  header: "\u0423\u0440\u043E\u0432\u0435\u043D\u044C",
                  style: { "width": "120px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode($setup["VTag"], {
                      value: $setup.compatibilityLabel(data.compatibilityLevel),
                      severity: $setup.compatibilitySeverity(data.compatibilityLevel)
                    }, null, 8, ["value", "severity"])
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  header: "#",
                  style: { "width": "64px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode($setup["VTag"], {
                      value: data._count?.synonyms || 0,
                      severity: "secondary"
                    }, null, 8, ["value"])
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  header: "",
                  style: { "width": "150px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex gap-2" }, [
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: ($event) => $setup.selectGroup(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["ListIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: ($event) => $setup.openEditGroup(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "danger",
                        outlined: "",
                        onClick: ($event) => $setup.deleteGroup(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])
                    ])
                  ]),
                  _: 1
                })
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 space-y-3" }, [
            createVNode("div", { class: "flex items-center justify-between" }, [
              createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, "\u0413\u0440\u0443\u043F\u043F\u044B \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u043E\u0432"),
              createVNode($setup["VButton"], {
                size: "small",
                onClick: $setup.openCreateGroup
              }, {
                default: withCtx(() => [
                  createVNode($setup["PlusCircleIcon"], { class: "w-4 h-4" })
                ]),
                _: 1
              })
            ]),
            createVNode($setup["VInputText"], {
              modelValue: $setup.search,
              "onUpdate:modelValue": ($event) => $setup.search = $event,
              placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E / \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E...",
              onInput: $setup.debouncedReload
            }, null, 8, ["modelValue", "onUpdate:modelValue"]),
            createVNode($setup["VDataTable"], {
              value: $setup.groups,
              loading: $setup.loadingGroups,
              "table-style": "min-width: 24rem",
              class: "p-datatable-sm",
              "striped-rows": ""
            }, {
              default: withCtx(() => [
                createVNode($setup["Column"], { header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435" }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex flex-col" }, [
                      createVNode("div", { class: "font-medium" }, toDisplayString(data.name), 1),
                      data.description ? (openBlock(), createBlock("small", {
                        key: 0,
                        class: "text-surface-500"
                      }, toDisplayString(data.description), 1)) : createCommentVNode("", true)
                    ])
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  header: "\u0423\u0440\u043E\u0432\u0435\u043D\u044C",
                  style: { "width": "120px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode($setup["VTag"], {
                      value: $setup.compatibilityLabel(data.compatibilityLevel),
                      severity: $setup.compatibilitySeverity(data.compatibilityLevel)
                    }, null, 8, ["value", "severity"])
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  header: "#",
                  style: { "width": "64px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode($setup["VTag"], {
                      value: data._count?.synonyms || 0,
                      severity: "secondary"
                    }, null, 8, ["value"])
                  ]),
                  _: 1
                }),
                createVNode($setup["Column"], {
                  header: "",
                  style: { "width": "150px" }
                }, {
                  body: withCtx(({ data }) => [
                    createVNode("div", { class: "flex gap-2" }, [
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: ($event) => $setup.selectGroup(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["ListIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "secondary",
                        outlined: "",
                        onClick: ($event) => $setup.openEditGroup(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        size: "small",
                        severity: "danger",
                        outlined: "",
                        onClick: ($event) => $setup.deleteGroup(data)
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"])
                    ])
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["value", "loading"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VCard"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 space-y-4"${_scopeId}><div class="flex items-center justify-between"${_scopeId}><div${_scopeId}><h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate($setup.selectedGroup ? $setup.selectedGroup.name : "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443")}</h3>`);
        if ($setup.selectedGroup) {
          _push2(`<div class="flex items-center gap-2 mt-1"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VTag"], {
            value: $setup.compatibilityLabel($setup.selectedGroup.compatibilityLevel),
            severity: $setup.compatibilitySeverity($setup.selectedGroup.compatibilityLevel)
          }, null, _parent2, _scopeId));
          if ($setup.selectedGroup.notes) {
            _push2(`<small class="text-surface-500"${_scopeId}>${ssrInterpolate($setup.selectedGroup.notes)}</small>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if ($setup.selectedGroup) {
          _push2(`<div class="flex gap-2"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["VButton"], {
            size: "small",
            severity: "secondary",
            outlined: "",
            onClick: ($event) => $setup.openEditGroup($setup.selectedGroup)
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              } else {
                return [
                  createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["VButton"], {
            size: "small",
            severity: "danger",
            outlined: "",
            onClick: ($event) => $setup.deleteGroup($setup.selectedGroup)
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              } else {
                return [
                  createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
        if (!$setup.selectedGroup) {
          _push2(`<div class="text-surface-500"${_scopeId}>\u0421\u043B\u0435\u0432\u0430 \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443, \u0447\u0442\u043E\u0431\u044B \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F.</div>`);
        } else {
          _push2(`<div${_scopeId}>`);
          _push2(ssrRenderComponent($setup["SynonymValueEditor"], {
            "group-id": $setup.selectedGroup.id
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 space-y-4" }, [
            createVNode("div", { class: "flex items-center justify-between" }, [
              createVNode("div", null, [
                createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString($setup.selectedGroup ? $setup.selectedGroup.name : "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443"), 1),
                $setup.selectedGroup ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "flex items-center gap-2 mt-1"
                }, [
                  createVNode($setup["VTag"], {
                    value: $setup.compatibilityLabel($setup.selectedGroup.compatibilityLevel),
                    severity: $setup.compatibilitySeverity($setup.selectedGroup.compatibilityLevel)
                  }, null, 8, ["value", "severity"]),
                  $setup.selectedGroup.notes ? (openBlock(), createBlock("small", {
                    key: 0,
                    class: "text-surface-500"
                  }, toDisplayString($setup.selectedGroup.notes), 1)) : createCommentVNode("", true)
                ])) : createCommentVNode("", true)
              ]),
              $setup.selectedGroup ? (openBlock(), createBlock("div", {
                key: 0,
                class: "flex gap-2"
              }, [
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "secondary",
                  outlined: "",
                  onClick: ($event) => $setup.openEditGroup($setup.selectedGroup)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode($setup["VButton"], {
                  size: "small",
                  severity: "danger",
                  outlined: "",
                  onClick: ($event) => $setup.deleteGroup($setup.selectedGroup)
                }, {
                  default: withCtx(() => [
                    createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                  ]),
                  _: 1
                }, 8, ["onClick"])
              ])) : createCommentVNode("", true)
            ]),
            !$setup.selectedGroup ? (openBlock(), createBlock("div", {
              key: 0,
              class: "text-surface-500"
            }, "\u0421\u043B\u0435\u0432\u0430 \u0432\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443, \u0447\u0442\u043E\u0431\u044B \u0440\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F.")) : (openBlock(), createBlock("div", { key: 1 }, [
              createVNode($setup["SynonymValueEditor"], {
                "group-id": $setup.selectedGroup.id
              }, null, 8, ["group-id"])
            ]))
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["EditSynonymGroupDialog"], {
    visible: $setup.showGroupDialog,
    "onUpdate:visible": ($event) => $setup.showGroupDialog = $event,
    "template-id": $props.template.id,
    group: $setup.editingGroup,
    onSaved: $setup.onGroupSaved
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeSynonymManager.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const AttributeSynonymManager = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "TemplateForm",
  props: {
    modelValue: {},
    groups: {},
    loading: { type: Boolean, default: false }
  },
  emits: ["update:modelValue", "save", "cancel", "group-created"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { attributeTemplates } = useTrpc();
    const toast = useToast();
    const { userRole } = useAuth();
    const form = computed({
      get: () => props.modelValue,
      set: (value) => emit("update:modelValue", value)
    });
    const errors = ref({});
    const showCreateGroupDialog = ref(false);
    const creatingGroup = ref(false);
    const newGroupForm = ref({
      name: "",
      description: ""
    });
    const showSynonymsDialog = ref(false);
    const openSynonyms = () => {
      if (!form.value.id) {
        toast.info("\u0421\u043D\u0430\u0447\u0430\u043B\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D");
        return;
      }
      showSynonymsDialog.value = true;
    };
    const selectedGroup = ref(null);
    const filteredGroups = ref([]);
    const searchTimeout = ref(null);
    const dataTypeOptions = [
      { label: "\u0421\u0442\u0440\u043E\u043A\u0430", value: "STRING" },
      { label: "\u0427\u0438\u0441\u043B\u043E", value: "NUMBER" },
      { label: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435", value: "BOOLEAN" },
      { label: "\u0414\u0430\u0442\u0430", value: "DATE" },
      { label: "JSON", value: "JSON" }
    ];
    const unitOptions = [
      // Длина
      { label: "\u043C\u043C", value: "MM" },
      { label: "\u0434\u044E\u0439\u043C\u044B", value: "INCH" },
      { label: "\u0444\u0443\u0442\u044B", value: "FT" },
      // Вес
      { label: "\u0433", value: "G" },
      { label: "\u043A\u0433", value: "KG" },
      { label: "\u0442", value: "T" },
      { label: "\u0444\u0443\u043D\u0442\u044B", value: "LB" },
      // Объем
      { label: "\u043C\u043B", value: "ML" },
      { label: "\u043B", value: "L" },
      { label: "\u0433\u0430\u043B\u043B\u043E\u043D\u044B", value: "GAL" },
      // Количество
      { label: "\u0448\u0442", value: "PCS" },
      { label: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442", value: "SET" },
      { label: "\u043F\u0430\u0440\u0430", value: "PAIR" },
      // Давление
      { label: "\u0431\u0430\u0440", value: "BAR" },
      { label: "PSI", value: "PSI" },
      // Мощность
      { label: "\u043A\u0412\u0442", value: "KW" },
      { label: "\u043B.\u0441.", value: "HP" },
      // Крутящий момент
      { label: "\u041D\u22C5\u043C", value: "NM" },
      { label: "\u043E\u0431/\u043C\u0438\u043D", value: "RPM" },
      // Температура
      { label: "\xB0C", value: "C" },
      { label: "\xB0F", value: "F" },
      // Относительные
      { label: "%", value: "PERCENT" }
    ];
    const filteredDataTypeOptions = ref(dataTypeOptions);
    const filteredUnitOptions = ref(unitOptions);
    const selectedDataType = computed({
      get: () => {
        return dataTypeOptions.find((opt) => opt.value === form.value.dataType) || null;
      },
      set: (newValue) => {
        form.value.dataType = newValue ? newValue.value : null;
      }
    });
    const selectedUnit = computed({
      get: () => {
        return unitOptions.find((opt) => opt.value === form.value.unit) || null;
      },
      set: (newValue) => {
        form.value.unit = newValue ? newValue.value : null;
      }
    });
    const filterDataTypes = (event) => {
      console.log("filterDataTypes \u0432\u044B\u0437\u0432\u0430\u043D \u0432 TemplateForm", { event });
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        filteredDataTypeOptions.value = [...dataTypeOptions];
        console.log("\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u043C \u0432\u0441\u0435 \u0442\u0438\u043F\u044B \u0434\u0430\u043D\u043D\u044B\u0445:", filteredDataTypeOptions.value.length);
      } else {
        filteredDataTypeOptions.value = dataTypeOptions.filter(
          (option) => option.label.toLowerCase().includes(query)
        );
        console.log("\u0424\u0438\u043B\u044C\u0442\u0440\u0443\u0435\u043C \u0442\u0438\u043F\u044B \u0434\u0430\u043D\u043D\u044B\u0445:", filteredDataTypeOptions.value.length);
      }
    };
    const filterUnits = (event) => {
      console.log("filterUnits \u0432\u044B\u0437\u0432\u0430\u043D \u0432 TemplateForm", { event });
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        filteredUnitOptions.value = [...unitOptions];
        console.log("\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u043C \u0432\u0441\u0435 \u0435\u0434\u0438\u043D\u0438\u0446\u044B:", filteredUnitOptions.value.length);
      } else {
        filteredUnitOptions.value = unitOptions.filter(
          (option) => option.label.toLowerCase().includes(query)
        );
        console.log("\u0424\u0438\u043B\u044C\u0442\u0440\u0443\u0435\u043C \u0435\u0434\u0438\u043D\u0438\u0446\u044B:", filteredUnitOptions.value.length);
      }
    };
    const findSelectedGroup = () => {
      if (form.value.groupId) {
        selectedGroup.value = props.groups.find((g) => g.id === form.value.groupId) || null;
      }
    };
    const isEditing = computed(() => !!form.value.id);
    const isValid = computed(() => {
      return form.value.name && form.value.title && form.value.dataType && !Object.keys(errors.value).length;
    });
    const dataTypeClass = computed(() => {
      return `w-full ${errors.value.dataType ? "p-invalid" : ""}`;
    });
    const validateForm = () => {
      errors.value = {};
      if (!form.value.name) {
        errors.value.name = "\u0421\u0438\u0441\u0442\u0435\u043C\u043D\u043E\u0435 \u0438\u043C\u044F \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
      } else if (!/^[a-z0-9_]+$/.test(form.value.name)) {
        errors.value.name = "\u0422\u043E\u043B\u044C\u043A\u043E \u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0435 \u0431\u0443\u043A\u0432\u044B, \u0446\u0438\u0444\u0440\u044B \u0438 \u043F\u043E\u0434\u0447\u0435\u0440\u043A\u0438\u0432\u0430\u043D\u0438\u044F";
      }
      if (!form.value.title) {
        errors.value.title = "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u043E";
      }
      if (!form.value.dataType) {
        errors.value.dataType = "\u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 \u043E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u0435\u043D";
      }
    };
    const save = () => {
      validateForm();
      if (isValid.value) {
        emit("save", form.value);
      } else {
        toast.error("\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u0438\u0441\u043F\u0440\u0430\u0432\u044C\u0442\u0435 \u043E\u0448\u0438\u0431\u043A\u0438 \u0432 \u0444\u043E\u0440\u043C\u0435");
      }
    };
    const searchGroups = async (event) => {
      console.log("searchGroups \u0432\u044B\u0437\u0432\u0430\u043D \u0432 TemplateForm", { event, groupsLength: props.groups.length });
      const query = event.query || "";
      if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
      }
      if (!query.trim()) {
        filteredGroups.value = [...props.groups];
        console.log("\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u043C \u0432\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B:", filteredGroups.value.length);
        return;
      }
      searchTimeout.value = setTimeout(async () => {
        try {
          filteredGroups.value = props.groups.filter(
            (group) => group.name.toLowerCase().includes(query.toLowerCase()) || group.description && group.description.toLowerCase().includes(query.toLowerCase())
          );
        } catch (error) {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430 \u0433\u0440\u0443\u043F\u043F:", error);
          toast.error("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u043F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F");
          filteredGroups.value = props.groups.filter(
            (group) => group.name.toLowerCase().includes(query.toLowerCase()) || group.description && group.description.toLowerCase().includes(query.toLowerCase())
          );
        }
      }, 300);
    };
    const onGroupSelect = (event) => {
      form.value.groupId = event.value.id;
      selectedGroup.value = event.value;
      toast.info(`\u0412\u044B\u0431\u0440\u0430\u043D\u0430 \u0433\u0440\u0443\u043F\u043F\u0430: ${event.value.name}`);
    };
    const onGroupClear = () => {
      form.value.groupId = null;
      selectedGroup.value = null;
      toast.info("\u0413\u0440\u0443\u043F\u043F\u0430 \u0441\u0431\u0440\u043E\u0448\u0435\u043D\u0430");
    };
    const createGroup = async () => {
      if (!newGroupForm.value.name) {
        toast.error("\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B");
        return;
      }
      try {
        creatingGroup.value = true;
        toast.info("\u0421\u043E\u0437\u0434\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...");
        const result = await attributeTemplates.createGroup(newGroupForm.value);
        if (result && typeof result === "object" && "id" in result) {
          form.value.groupId = result.id;
          selectedGroup.value = result;
          showCreateGroupDialog.value = false;
          newGroupForm.value = { name: "", description: "" };
          emit("group-created", result);
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0433\u0440\u0443\u043F\u043F\u044B:", error);
        if (error.message?.includes("\u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442")) {
          toast.error("\u0413\u0440\u0443\u043F\u043F\u0430 \u0441 \u0442\u0430\u043A\u0438\u043C \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435\u043C \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442");
        } else {
          toast.error(error.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443");
        }
      } finally {
        creatingGroup.value = false;
      }
    };
    watch(() => props.modelValue, (newValue) => {
      if (!newValue) {
        form.value = {
          dataType: "STRING",
          isRequired: false,
          allowedValues: []
        };
        return;
      }
      if (!newValue.dataType) {
        form.value.dataType = "STRING";
      }
      if (!newValue.isRequired) {
        form.value.isRequired = false;
      }
      if (!newValue.allowedValues) {
        form.value.allowedValues = [];
      }
      findSelectedGroup();
    }, { immediate: true });
    watch(() => props.groups, () => {
      filteredGroups.value = props.groups;
      findSelectedGroup();
    }, { immediate: true });
    onMounted(() => {
      console.log("TemplateForm \u043C\u043E\u043D\u0442\u0438\u0440\u0443\u0435\u0442\u0441\u044F");
      filteredDataTypeOptions.value = [...dataTypeOptions];
      filteredUnitOptions.value = [...unitOptions];
      filteredGroups.value = [...props.groups];
      console.log("\u0418\u043D\u0438\u0446\u0438\u0430\u043B\u0438\u0437\u0438\u0440\u043E\u0432\u0430\u043D\u044B \u0430\u0432\u0442\u043E\u043A\u043E\u043C\u043F\u043B\u0438\u0442\u044B \u0432 TemplateForm:", {
        dataTypes: filteredDataTypeOptions.value.length,
        units: filteredUnitOptions.value.length,
        groups: filteredGroups.value.length
      });
      findSelectedGroup();
    });
    onUnmounted(() => {
      if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
      }
    });
    watch(() => form.value.name, () => {
      if (errors.value.name) {
        delete errors.value.name;
      }
    });
    watch(() => form.value.title, () => {
      if (errors.value.title) {
        delete errors.value.title;
      }
    });
    watch(() => form.value.dataType, () => {
      if (errors.value.dataType) {
        delete errors.value.dataType;
      }
    });
    const __returned__ = { props, emit, attributeTemplates, toast, userRole, form, errors, showCreateGroupDialog, creatingGroup, newGroupForm, showSynonymsDialog, openSynonyms, selectedGroup, filteredGroups, searchTimeout, dataTypeOptions, unitOptions, filteredDataTypeOptions, filteredUnitOptions, selectedDataType, selectedUnit, filterDataTypes, filterUnits, findSelectedGroup, isEditing, isValid, dataTypeClass, validateForm, save, searchGroups, onGroupSelect, onGroupClear, createGroup, Icon, VInputText: InputText, VTextarea, VInputNumber: InputNumber, InputChips, VCheckbox: Checkbox, VButton: Button, VDialog: Dialog, VAutoComplete, AttributeSynonymManager };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "template-form" }, _attrs))}><div class="space-y-4"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0421\u0438\u0441\u0442\u0435\u043C\u043D\u043E\u0435 \u0438\u043C\u044F * </label>`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.form.name,
    "onUpdate:modelValue": ($event) => $setup.form.name = $event,
    placeholder: "inner_diameter",
    class: ["w-full", { "p-invalid": $setup.errors.name }]
  }, null, _parent));
  if ($setup.errors.name) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.name)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<small class="text-surface-500 dark:text-surface-400"> \u0422\u043E\u043B\u044C\u043A\u043E \u0441\u0442\u0440\u043E\u0447\u043D\u044B\u0435 \u0431\u0443\u043A\u0432\u044B, \u0446\u0438\u0444\u0440\u044B \u0438 \u043F\u043E\u0434\u0447\u0435\u0440\u043A\u0438\u0432\u0430\u043D\u0438\u044F </small></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 * </label>`);
  _push(ssrRenderComponent($setup["VInputText"], {
    modelValue: $setup.form.title,
    "onUpdate:modelValue": ($event) => $setup.form.title = $event,
    placeholder: "\u0412\u043D\u0443\u0442\u0440\u0435\u043D\u043D\u0438\u0439 \u0434\u0438\u0430\u043C\u0435\u0442\u0440",
    class: ["w-full", { "p-invalid": $setup.errors.title }]
  }, null, _parent));
  if ($setup.errors.title) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.title)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
  _push(ssrRenderComponent($setup["VTextarea"], {
    modelValue: $setup.form.description,
    "onUpdate:modelValue": ($event) => $setup.form.description = $event,
    placeholder: "\u041F\u043E\u0434\u0440\u043E\u0431\u043D\u043E\u0435 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430...",
    rows: "3",
    class: "w-full"
  }, null, _parent));
  _push(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 * </label>`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.selectedDataType,
    "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
    suggestions: $setup.filteredDataTypeOptions,
    onComplete: $setup.filterDataTypes,
    onDropdownClick: () => $setup.filterDataTypes({ query: "" }),
    "option-label": "label",
    "option-value": "value",
    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u0438\u043F",
    class: $setup.dataTypeClass,
    dropdown: ""
  }, null, _parent));
  if ($setup.errors.dataType) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.dataType)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0415\u0434\u0438\u043D\u0438\u0446\u0430 \u0438\u0437\u043C\u0435\u0440\u0435\u043D\u0438\u044F </label>`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.selectedUnit,
    "onUpdate:modelValue": ($event) => $setup.selectedUnit = $event,
    suggestions: $setup.filteredUnitOptions,
    onComplete: $setup.filterUnits,
    onDropdownClick: () => $setup.filterUnits({ query: "" }),
    "option-label": "label",
    "option-value": "value",
    placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0435\u0434\u0438\u043D\u0438\u0446\u0443",
    class: "w-full",
    dropdown: "",
    "show-clear": ""
  }, null, _parent));
  _push(`</div></div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0413\u0440\u0443\u043F\u043F\u0430 </label><div class="flex gap-2">`);
  _push(ssrRenderComponent($setup["VAutoComplete"], {
    modelValue: $setup.selectedGroup,
    "onUpdate:modelValue": ($event) => $setup.selectedGroup = $event,
    suggestions: $setup.filteredGroups,
    onComplete: $setup.searchGroups,
    onDropdownClick: () => $setup.searchGroups({ query: "" }),
    "option-label": "name",
    placeholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B...",
    class: ["flex-1", { "p-invalid": $setup.errors.groupId }],
    dropdown: "",
    "dropdown-mode": "current",
    "show-clear": "",
    onItemSelect: $setup.onGroupSelect,
    onClear: $setup.onGroupClear
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    onClick: ($event) => $setup.showCreateGroupDialog = true,
    severity: "secondary",
    outlined: "",
    size: "small",
    label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u0433\u0440\u0443\u043F\u043F\u0443"
  }, {
    icon: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-plus",
          class: "w-5 h-5"
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Icon"], {
            name: "pi pi-plus",
            class: "w-5 h-5"
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.errors.groupId) {
    _push(`<small class="p-error">${ssrInterpolate($setup.errors.groupId)}</small>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div>`);
  if ($setup.form.dataType === "NUMBER") {
    _push(`<div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041C\u0438\u043D\u0438\u043C\u0430\u043B\u044C\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 </label>`);
    _push(ssrRenderComponent($setup["VInputNumber"], {
      modelValue: $setup.form.minValue,
      "onUpdate:modelValue": ($event) => $setup.form.minValue = $event,
      placeholder: "0",
      class: "w-full",
      "use-grouping": false,
      "min-fraction-digits": 2,
      "max-fraction-digits": 2
    }, null, _parent));
    _push(`</div><div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u041C\u0430\u043A\u0441\u0438\u043C\u0430\u043B\u044C\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 </label>`);
    _push(ssrRenderComponent($setup["VInputNumber"], {
      modelValue: $setup.form.maxValue,
      "onUpdate:modelValue": ($event) => $setup.form.maxValue = $event,
      placeholder: "100",
      class: "w-full",
      "use-grouping": false,
      "min-fraction-digits": 2,
      "max-fraction-digits": 2
    }, null, _parent));
    _push(`</div></div>`);
  } else {
    _push(`<!---->`);
  }
  if ($setup.form.dataType === "NUMBER") {
    _push(`<div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u043E\u0435 \u043E\u0442\u043A\u043B\u043E\u043D\u0435\u043D\u0438\u0435 (tolerance) </label>`);
    _push(ssrRenderComponent($setup["VInputNumber"], {
      modelValue: $setup.form.tolerance,
      "onUpdate:modelValue": ($event) => $setup.form.tolerance = $event,
      placeholder: "0.1",
      class: "w-full",
      "use-grouping": false,
      "min-fraction-digits": 1,
      "max-fraction-digits": 4,
      min: 0
    }, null, _parent));
    _push(`<small class="text-surface-500 dark:text-surface-400"> \u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u043E\u0435 \u043E\u0442\u043A\u043B\u043E\u043D\u0435\u043D\u0438\u0435 \u043F\u0440\u0438 \u0441\u043E\u043F\u043E\u0441\u0442\u0430\u0432\u043B\u0435\u043D\u0438\u0438 \u0447\u0438\u0441\u043B\u043E\u0432\u044B\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0439. \u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u0435\u0441\u043B\u0438 \u044D\u0442\u0430\u043B\u043E\u043D = 30.0 \u0438 \u0434\u043E\u043F\u0443\u0441\u043A = 0.1, \u0442\u043E \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u043E\u0442 29.9 \u0434\u043E 30.1 \u0431\u0443\u0434\u0443\u0442 \u0441\u0447\u0438\u0442\u0430\u0442\u044C\u0441\u044F \u044D\u043A\u0432\u0438\u0432\u0430\u043B\u0435\u043D\u0442\u043D\u044B\u043C\u0438. </small></div>`);
  } else {
    _push(`<!---->`);
  }
  if ($setup.form.dataType === "STRING") {
    _push(`<div><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"> \u0414\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u044B\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F </label>`);
    _push(ssrRenderComponent($setup["InputChips"], {
      modelValue: $setup.form.allowedValues,
      "onUpdate:modelValue": ($event) => $setup.form.allowedValues = $event,
      placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0438 \u043D\u0430\u0436\u043C\u0438\u0442\u0435 Enter",
      class: "w-full"
    }, null, _parent));
    _push(`<small class="text-surface-500 dark:text-surface-400"> \u041E\u0441\u0442\u0430\u0432\u044C\u0442\u0435 \u043F\u0443\u0441\u0442\u044B\u043C \u0434\u043B\u044F \u043B\u044E\u0431\u044B\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0439. \u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: steel, aluminum, plastic </small><div class="mt-3">`);
    _push(ssrRenderComponent($setup["VButton"], {
      severity: "secondary",
      outlined: "",
      label: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B",
      onClick: $setup.openSynonyms
    }, {
      icon: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-tags",
            class: "w-5 h-5"
          }, null, _parent2, _scopeId));
        } else {
          return [
            createVNode($setup["Icon"], {
              name: "pi pi-tags",
              class: "w-5 h-5"
            })
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`<small class="ml-2 text-surface-500">\u0414\u043E\u0441\u0442\u0443\u043F\u043D\u043E \u043F\u043E\u0441\u043B\u0435 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F \u0448\u0430\u0431\u043B\u043E\u043D\u0430</small></div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="flex items-center gap-4">`);
  _push(ssrRenderComponent($setup["VCheckbox"], {
    modelValue: $setup.form.isRequired,
    "onUpdate:modelValue": ($event) => $setup.form.isRequired = $event,
    "input-id": "required",
    binary: ""
  }, null, _parent));
  _push(`<label for="required" class="text-sm text-surface-700 dark:text-surface-300"> \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 </label></div></div><div class="flex justify-end gap-3 mt-6 pt-4 border-t border-surface-200 dark:border-surface-700">`);
  _push(ssrRenderComponent($setup["VButton"], {
    label: "\u041E\u0442\u043C\u0435\u043D\u0430",
    severity: "secondary",
    onClick: ($event) => _ctx.$emit("cancel")
  }, null, _parent));
  _push(ssrRenderComponent($setup["VButton"], {
    label: $setup.isEditing ? "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
    onClick: $setup.save,
    loading: $props.loading,
    disabled: !$setup.isValid
  }, null, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showCreateGroupDialog,
    "onUpdate:visible": ($event) => $setup.showCreateGroupDialog = $event,
    modal: "",
    header: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432",
    style: { width: "30rem" }
  }, {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u041E\u0442\u043C\u0435\u043D\u0430",
          severity: "secondary",
          onClick: ($event) => $setup.showCreateGroupDialog = false
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["VButton"], {
          label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
          onClick: $setup.createGroup,
          loading: $setup.creatingGroup,
          disabled: !$setup.newGroupForm.name
        }, null, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["VButton"], {
            label: "\u041E\u0442\u043C\u0435\u043D\u0430",
            severity: "secondary",
            onClick: ($event) => $setup.showCreateGroupDialog = false
          }, null, 8, ["onClick"]),
          createVNode($setup["VButton"], {
            label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C",
            onClick: $setup.createGroup,
            loading: $setup.creatingGroup,
            disabled: !$setup.newGroupForm.name
          }, null, 8, ["loading", "disabled"])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B * </label>`);
        _push2(ssrRenderComponent($setup["VInputText"], {
          modelValue: $setup.newGroupForm.name,
          "onUpdate:modelValue": ($event) => $setup.newGroupForm.name = $event,
          placeholder: "\u0420\u0430\u0437\u043C\u0435\u0440\u044B",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 </label>`);
        _push2(ssrRenderComponent($setup["VTextarea"], {
          modelValue: $setup.newGroupForm.description,
          "onUpdate:modelValue": ($event) => $setup.newGroupForm.description = $event,
          placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...",
          rows: "2",
          class: "w-full"
        }, null, _parent2, _scopeId));
        _push2(`</div></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B * "),
              createVNode($setup["VInputText"], {
                modelValue: $setup.newGroupForm.name,
                "onUpdate:modelValue": ($event) => $setup.newGroupForm.name = $event,
                placeholder: "\u0420\u0430\u0437\u043C\u0435\u0440\u044B",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", null, [
              createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 "),
              createVNode($setup["VTextarea"], {
                modelValue: $setup.newGroupForm.description,
                "onUpdate:modelValue": ($event) => $setup.newGroupForm.description = $event,
                placeholder: "\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435 \u0433\u0440\u0443\u043F\u043F\u044B...",
                rows: "2",
                class: "w-full"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["VDialog"], {
    visible: $setup.showSynonymsDialog,
    "onUpdate:visible": ($event) => $setup.showSynonymsDialog = $event,
    modal: "",
    header: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F",
    style: { width: "80rem" },
    breakpoints: { "1199px": "90vw", "575px": "98vw" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        if ($setup.form.id && $setup.form.dataType === "STRING") {
          _push2(ssrRenderComponent($setup["AttributeSynonymManager"], {
            template: { id: $setup.form.id, dataType: $setup.form.dataType, title: $setup.form.title, name: $setup.form.name }
          }, null, _parent2, _scopeId));
        } else {
          _push2(`<div class="p-4 text-surface-500"${_scopeId}>\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D, \u0447\u0442\u043E\u0431\u044B \u0443\u043F\u0440\u0430\u0432\u043B\u044F\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u0430\u043C\u0438.</div>`);
        }
      } else {
        return [
          $setup.form.id && $setup.form.dataType === "STRING" ? (openBlock(), createBlock($setup["AttributeSynonymManager"], {
            key: 0,
            template: { id: $setup.form.id, dataType: $setup.form.dataType, title: $setup.form.title, name: $setup.form.name }
          }, null, 8, ["template"])) : (openBlock(), createBlock("div", {
            key: 1,
            class: "p-4 text-surface-500"
          }, "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D, \u0447\u0442\u043E\u0431\u044B \u0443\u043F\u0440\u0430\u0432\u043B\u044F\u0442\u044C \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u0430\u043C\u0438."))
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/TemplateForm.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const TemplateForm = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2]]);

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "AttributeTemplateManager",
  setup(__props, { expose: __expose }) {
    __expose();
    const { attributeTemplates, loading } = useTrpc();
    const templates = ref([]);
    const groups = ref([]);
    const totalCount = ref(0);
    const pageSize = ref(25);
    const currentPage = ref(0);
    const tableLoading = ref(false);
    let lastRequestId = 0;
    const searchQuery = ref("");
    const selectedGroup = ref(null);
    const selectedDataType = ref(null);
    const viewMode = ref("table");
    const urlSync = useUrlParams({
      search: "",
      groupId: void 0,
      dataType: void 0
    }, {
      prefix: "attr_",
      numberParams: ["groupId"],
      debounceMs: 300
    });
    watch(searchQuery, (val) => {
      urlSync.updateFilter("search", val || void 0);
    });
    watch(selectedGroup, (val) => {
      const gid = val && typeof val === "object" ? val.id : val;
      urlSync.updateFilter("groupId", gid ?? void 0);
    });
    watch(selectedDataType, (val) => {
      const dt = val && typeof val === "object" ? val.value : val;
      urlSync.updateFilter("dataType", dt ?? void 0);
    });
    watch(urlSync.filters, (f) => {
      const nextSearch = f.search || "";
      const nextGroupId = f.groupId ?? null;
      const nextDataType = f.dataType ?? null;
      if (searchQuery.value !== nextSearch) searchQuery.value = nextSearch;
      if (selectedGroup.value !== nextGroupId) selectedGroup.value = nextGroupId;
      if (selectedDataType.value !== nextDataType) selectedDataType.value = nextDataType;
      currentPage.value = 0;
      loadTemplates();
    });
    const showCreateDialog = ref(false);
    const editingTemplate = ref(null);
    const templateForm = ref({});
    const saving = ref(false);
    const showSynonymsDialog = ref(false);
    const selectedTemplateForSynonyms = ref(null);
    const dataTypeOptions = [
      { label: "\u0421\u0442\u0440\u043E\u043A\u0430", value: "STRING" },
      { label: "\u0427\u0438\u0441\u043B\u043E", value: "NUMBER" },
      { label: "\u041B\u043E\u0433\u0438\u0447\u0435\u0441\u043A\u043E\u0435", value: "BOOLEAN" },
      { label: "\u0414\u0430\u0442\u0430", value: "DATE" },
      { label: "JSON", value: "JSON" }
    ];
    const viewModeOptions = [
      { label: "\u0422\u0430\u0431\u043B\u0438\u0446\u0430", value: "table" },
      { label: "\u041A\u0430\u0440\u0442\u043E\u0447\u043A\u0438", value: "cards" }
    ];
    const groupSuggestions = ref([]);
    const dataTypeSuggestions = ref([]);
    const viewModeSuggestions = ref([]);
    const filterGroups = (event) => {
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        groupSuggestions.value = [...groups.value];
      } else {
        groupSuggestions.value = groups.value.filter(
          (group) => group.name.toLowerCase().includes(query)
        );
      }
    };
    const filterDataTypes = (event) => {
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        dataTypeSuggestions.value = [...dataTypeOptions];
      } else {
        dataTypeSuggestions.value = dataTypeOptions.filter(
          (option) => option.label.toLowerCase().includes(query)
        );
      }
    };
    const filterViewModes = (event) => {
      const query = event.query?.toLowerCase() || "";
      if (!query.trim()) {
        viewModeSuggestions.value = [...viewModeOptions];
      } else {
        viewModeSuggestions.value = viewModeOptions.filter(
          (option) => option.label.toLowerCase().includes(query)
        );
      }
    };
    const usedTemplatesCount = computed(() => {
      return templates.value.filter((template) => getTotalUsage(template._count) > 0).length;
    });
    const unusedTemplatesCount = computed(() => {
      return templates.value.filter((template) => getTotalUsage(template._count) === 0).length;
    });
    const getDataTypeLabel = (dataType) => {
      const option = dataTypeOptions.find((opt) => opt.value === dataType);
      return option?.label || dataType;
    };
    const getUnitLabel = (unit) => {
      const labels = {
        "MM": "\u043C\u043C",
        "INCH": "\u0434\u044E\u0439\u043C\u044B",
        "FT": "\u0444\u0443\u0442\u044B",
        "G": "\u0433",
        "KG": "\u043A\u0433",
        "T": "\u0442",
        "LB": "\u0444\u0443\u043D\u0442\u044B",
        "ML": "\u043C\u043B",
        "L": "\u043B",
        "GAL": "\u0433\u0430\u043B\u043B\u043E\u043D\u044B",
        "PCS": "\u0448\u0442",
        "SET": "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        "PAIR": "\u043F\u0430\u0440\u0430",
        "BAR": "\u0431\u0430\u0440",
        "PSI": "PSI",
        "KW": "\u043A\u0412\u0442",
        "HP": "\u043B.\u0441.",
        "NM": "\u041D\u22C5\u043C",
        "RPM": "\u043E\u0431/\u043C\u0438\u043D",
        "C": "\xB0C",
        "F": "\xB0F",
        "PERCENT": "%"
      };
      return labels[unit] || unit;
    };
    const getTotalUsage = (count) => {
      if (!count) return 0;
      return (count.partAttributes || 0) + (count.catalogItemAttributes || 0) + (count.equipmentAttributes || 0);
    };
    const getUsageDetails = (count) => {
      if (!count) return "";
      const parts = count.partAttributes || 0;
      const items = count.catalogItemAttributes || 0;
      const equipment = count.equipmentAttributes || 0;
      const details = [];
      if (parts > 0) details.push(`${parts} \u0437\u0430\u043F.`);
      if (items > 0) details.push(`${items} \u043A\u0430\u0442.`);
      if (equipment > 0) details.push(`${equipment} \u0442\u0435\u0445.`);
      return details.join(", ");
    };
    const loadTemplates = async () => {
      const current = ++lastRequestId;
      tableLoading.value = true;
      try {
        const groupId = selectedGroup.value ? typeof selectedGroup.value === "object" ? selectedGroup.value.id : selectedGroup.value : void 0;
        const dataType = selectedDataType.value && typeof selectedDataType.value === "object" ? selectedDataType.value.value : void 0;
        const result = await attributeTemplates.findMany({
          groupId,
          search: searchQuery.value || void 0,
          dataType,
          limit: pageSize.value,
          offset: currentPage.value * pageSize.value
        });
        if (current === lastRequestId && result && typeof result === "object") {
          templates.value = result.templates || [];
          totalCount.value = result.total || 0;
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432:", error);
        console.error("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D\u044B");
      } finally {
        if (current === lastRequestId) {
          tableLoading.value = false;
        }
      }
    };
    const loadGroups = async () => {
      try {
        const result = await attributeTemplates.findAllGroups();
        if (result && Array.isArray(result)) {
          groups.value = result;
          groupSuggestions.value = [...result];
        }
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438 \u0433\u0440\u0443\u043F\u043F:", error);
      }
    };
    let searchTimeout;
    const debouncedSearch = () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        currentPage.value = 0;
        urlSync.updateFilter("search", searchQuery.value || void 0);
        loadTemplates();
      }, 500);
    };
    const refreshData = async () => {
      await loadGroups();
      await loadTemplates();
    };
    const onPageChange = (event) => {
      currentPage.value = event.page;
      pageSize.value = event.rows;
      loadTemplates();
    };
    const editTemplate = (template) => {
      editingTemplate.value = template;
      templateForm.value = { ...template };
      showCreateDialog.value = true;
    };
    const openSynonyms = (template) => {
      selectedTemplateForSynonyms.value = template;
      showSynonymsDialog.value = true;
    };
    const createNewTemplate = () => {
      editingTemplate.value = null;
      templateForm.value = {
        dataType: "STRING",
        isRequired: false,
        allowedValues: []
      };
      showCreateDialog.value = true;
    };
    const deleteTemplate = async (template) => {
      const confirmed = window.confirm(`\u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B, \u0447\u0442\u043E \u0445\u043E\u0442\u0438\u0442\u0435 \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D "${template.title}"?`);
      if (confirmed) {
        try {
          await attributeTemplates.delete({ id: template.id });
          console.log("\u0428\u0430\u0431\u043B\u043E\u043D \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0443\u0434\u0430\u043B\u0435\u043D");
          loadTemplates();
        } catch (error) {
          console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u0448\u0430\u0431\u043B\u043E\u043D\u0430:", error);
          alert(error.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D");
        }
      }
    };
    const saveTemplate = async (formData) => {
      try {
        saving.value = true;
        if (editingTemplate.value) {
          await attributeTemplates.update({ id: editingTemplate.value.id, ...formData });
          console.log("\u0428\u0430\u0431\u043B\u043E\u043D \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D");
        } else {
          await attributeTemplates.create(formData);
          console.log("\u0428\u0430\u0431\u043B\u043E\u043D \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0441\u043E\u0437\u0434\u0430\u043D");
        }
        showCreateDialog.value = false;
        editingTemplate.value = null;
        templateForm.value = {};
        await loadGroups();
        await loadTemplates();
      } catch (error) {
        console.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u044F \u0448\u0430\u0431\u043B\u043E\u043D\u0430:", error);
        alert(error.message || "\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D");
      } finally {
        saving.value = false;
      }
    };
    onMounted(async () => {
      dataTypeSuggestions.value = [...dataTypeOptions];
      viewModeSuggestions.value = [...viewModeOptions];
      const f = urlSync.filters.value;
      searchQuery.value = f.search || "";
      selectedGroup.value = f.groupId ?? null;
      selectedDataType.value = f.dataType ?? null;
      await loadGroups();
      await loadTemplates();
    });
    const __returned__ = { attributeTemplates, loading, templates, groups, totalCount, pageSize, currentPage, tableLoading, get lastRequestId() {
      return lastRequestId;
    }, set lastRequestId(v) {
      lastRequestId = v;
    }, searchQuery, selectedGroup, selectedDataType, viewMode, urlSync, showCreateDialog, editingTemplate, templateForm, saving, showSynonymsDialog, selectedTemplateForSynonyms, dataTypeOptions, viewModeOptions, groupSuggestions, dataTypeSuggestions, viewModeSuggestions, filterGroups, filterDataTypes, filterViewModes, usedTemplatesCount, unusedTemplatesCount, getDataTypeLabel, getUnitLabel, getTotalUsage, getUsageDetails, loadTemplates, loadGroups, get searchTimeout() {
      return searchTimeout;
    }, set searchTimeout(v) {
      searchTimeout = v;
    }, debouncedSearch, refreshData, onPageChange, editTemplate, openSynonyms, createNewTemplate, deleteTemplate, saveTemplate, VCard: Card, VButton: Button, VInputText: InputText, VDataTable: DataTable, VTag: Tag, VDialog: Dialog, get Column() {
      return script;
    }, get Paginator() {
      return script$1;
    }, TemplateForm, VAutoComplete, AttributeSynonymManager, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "attribute-template-manager" }, _attrs))}>`);
  if ($setup.loading) {
    _push(`<div class="text-center py-12">`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: "pi pi-spinner pi-spin",
      class: "inline-block text-4xl text-primary mb-4"
    }, null, _parent));
    _push(`<p class="text-surface-600 dark:text-surface-400">\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432...</p></div>`);
  } else {
    _push(`<div><div class="flex items-center justify-between mb-6"><div><h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0"> \u0428\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 </h2><p class="text-surface-600 dark:text-surface-400 text-sm mt-1"> \u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u0430\u043C\u0438 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0434\u043B\u044F \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439, \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439 \u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 </p></div><div class="flex gap-3">`);
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: $setup.refreshData,
      disabled: $setup.loading,
      severity: "secondary",
      outlined: "",
      label: "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C"
    }, {
      icon: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-refresh",
            class: "w-5 h-5"
          }, null, _parent2, _scopeId));
        } else {
          return [
            createVNode($setup["Icon"], {
              name: "pi pi-refresh",
              class: "w-5 h-5"
            })
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VButton"], {
      onClick: $setup.createNewTemplate,
      label: "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D"
    }, {
      icon: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-plus",
            class: "w-5 h-5"
          }, null, _parent2, _scopeId));
        } else {
          return [
            createVNode($setup["Icon"], {
              name: "pi pi-plus",
              class: "w-5 h-5"
            })
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div></div>`);
    _push(ssrRenderComponent($setup["VCard"], { class: "mb-6" }, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-4 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041F\u043E\u0438\u0441\u043A </label>`);
          _push2(ssrRenderComponent($setup["VInputText"], {
            modelValue: $setup.searchQuery,
            "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E, \u0438\u043C\u0435\u043D\u0438 \u0438\u043B\u0438 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E...",
            class: "w-full",
            onInput: $setup.debouncedSearch
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0413\u0440\u0443\u043F\u043F\u0430 </label>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.selectedGroup,
            "onUpdate:modelValue": ($event) => $setup.selectedGroup = $event,
            suggestions: $setup.groupSuggestions,
            onComplete: $setup.filterGroups,
            "option-label": "name",
            "option-value": "id",
            placeholder: "\u0412\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B",
            class: "w-full",
            dropdown: "",
            "show-clear": "",
            onChange: $setup.loadTemplates
          }, null, _parent2, _scopeId));
          _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 </label>`);
          _push2(ssrRenderComponent($setup["VAutoComplete"], {
            modelValue: $setup.selectedDataType,
            "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
            suggestions: $setup.dataTypeSuggestions,
            onComplete: $setup.filterDataTypes,
            "option-label": "label",
            "option-value": "value",
            placeholder: "\u0412\u0441\u0435 \u0442\u0438\u043F\u044B",
            class: "w-full",
            dropdown: "",
            "show-clear": "",
            onChange: $setup.loadTemplates
          }, null, _parent2, _scopeId));
          _push2(`</div></div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4" }, [
              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-4 gap-4" }, [
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041F\u043E\u0438\u0441\u043A "),
                  createVNode($setup["VInputText"], {
                    modelValue: $setup.searchQuery,
                    "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E, \u0438\u043C\u0435\u043D\u0438 \u0438\u043B\u0438 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044E...",
                    class: "w-full",
                    onInput: $setup.debouncedSearch
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0413\u0440\u0443\u043F\u043F\u0430 "),
                  createVNode($setup["VAutoComplete"], {
                    modelValue: $setup.selectedGroup,
                    "onUpdate:modelValue": ($event) => $setup.selectedGroup = $event,
                    suggestions: $setup.groupSuggestions,
                    onComplete: $setup.filterGroups,
                    "option-label": "name",
                    "option-value": "id",
                    placeholder: "\u0412\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B",
                    class: "w-full",
                    dropdown: "",
                    "show-clear": "",
                    onChange: $setup.loadTemplates
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445 "),
                  createVNode($setup["VAutoComplete"], {
                    modelValue: $setup.selectedDataType,
                    "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
                    suggestions: $setup.dataTypeSuggestions,
                    onComplete: $setup.filterDataTypes,
                    "option-label": "label",
                    "option-value": "value",
                    placeholder: "\u0412\u0441\u0435 \u0442\u0438\u043F\u044B",
                    class: "w-full",
                    dropdown: "",
                    "show-clear": "",
                    onChange: $setup.loadTemplates
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
                ])
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">`);
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="text-2xl font-bold text-primary mb-2"${_scopeId}>${ssrInterpolate($setup.totalCount)}</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>\u0412\u0441\u0435\u0433\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "text-2xl font-bold text-primary mb-2" }, toDisplayString($setup.totalCount), 1),
              createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, "\u0412\u0441\u0435\u0433\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="text-2xl font-bold text-green-600 mb-2"${_scopeId}>${ssrInterpolate($setup.groups.length)}</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>\u0413\u0440\u0443\u043F\u043F</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "text-2xl font-bold text-green-600 mb-2" }, toDisplayString($setup.groups.length), 1),
              createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, "\u0413\u0440\u0443\u043F\u043F")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="text-2xl font-bold text-blue-600 mb-2"${_scopeId}>${ssrInterpolate($setup.usedTemplatesCount)}</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "text-2xl font-bold text-blue-600 mb-2" }, toDisplayString($setup.usedTemplatesCount), 1),
              createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VCard"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4 text-center"${_scopeId}><div class="text-2xl font-bold text-orange-600 mb-2"${_scopeId}>${ssrInterpolate($setup.unusedTemplatesCount)}</div><div class="text-sm text-surface-600 dark:text-surface-400"${_scopeId}>\u041D\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F</div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4 text-center" }, [
              createVNode("div", { class: "text-2xl font-bold text-orange-600 mb-2" }, toDisplayString($setup.unusedTemplatesCount), 1),
              createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400" }, "\u041D\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u0442\u0441\u044F")
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
    if ($setup.viewMode === "table") {
      _push(ssrRenderComponent($setup["VCard"], null, {
        content: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent($setup["VDataTable"], {
              value: $setup.templates,
              loading: $setup.tableLoading && $setup.templates.length === 0,
              paginator: "",
              rows: $setup.pageSize,
              "total-records": $setup.totalCount,
              "rows-per-page-options": [10, 25, 50],
              lazy: "",
              onPage: $setup.onPageChange,
              "table-style": "min-width: 50rem",
              class: "p-datatable-sm",
              "striped-rows": ""
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "id",
                    header: "ID",
                    sortable: "",
                    style: { "width": "80px" }
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<span class="font-mono text-sm text-surface-700 dark:text-surface-300"${_scopeId3}>#${ssrInterpolate(data.id)}</span>`);
                      } else {
                        return [
                          createVNode("span", { class: "font-mono text-sm text-surface-700 dark:text-surface-300" }, "#" + toDisplayString(data.id), 1)
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "title",
                    header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div${_scopeId3}><div class="font-medium text-surface-900 dark:text-surface-0"${_scopeId3}>${ssrInterpolate(data.title)}</div><div class="text-sm text-surface-600 dark:text-surface-400 font-mono"${_scopeId3}>${ssrInterpolate(data.name)}</div></div>`);
                      } else {
                        return [
                          createVNode("div", null, [
                            createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(data.title), 1),
                            createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400 font-mono" }, toDisplayString(data.name), 1)
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "group.name",
                    header: "\u0413\u0440\u0443\u043F\u043F\u0430",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        if (data.group) {
                          _push4(ssrRenderComponent($setup["VTag"], {
                            value: data.group.name,
                            severity: "secondary"
                          }, null, _parent4, _scopeId3));
                        } else {
                          _push4(`<span class="text-surface-400 dark:text-surface-600"${_scopeId3}>\u2014</span>`);
                        }
                      } else {
                        return [
                          data.group ? (openBlock(), createBlock($setup["VTag"], {
                            key: 0,
                            value: data.group.name,
                            severity: "secondary"
                          }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                            key: 1,
                            class: "text-surface-400 dark:text-surface-600"
                          }, "\u2014"))
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "dataType",
                    header: "\u0422\u0438\u043F",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["VTag"], {
                          value: $setup.getDataTypeLabel(data.dataType),
                          severity: "info"
                        }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["VTag"], {
                            value: $setup.getDataTypeLabel(data.dataType),
                            severity: "info"
                          }, null, 8, ["value"])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    field: "unit",
                    header: "\u0415\u0434\u0438\u043D\u0438\u0446\u0430"
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        if (data.unit) {
                          _push4(ssrRenderComponent($setup["VTag"], {
                            value: $setup.getUnitLabel(data.unit),
                            severity: "success"
                          }, null, _parent4, _scopeId3));
                        } else {
                          _push4(`<span class="text-surface-400 dark:text-surface-600"${_scopeId3}>\u2014</span>`);
                        }
                      } else {
                        return [
                          data.unit ? (openBlock(), createBlock($setup["VTag"], {
                            key: 0,
                            value: $setup.getUnitLabel(data.unit),
                            severity: "success"
                          }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                            key: 1,
                            class: "text-surface-400 dark:text-surface-600"
                          }, "\u2014"))
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    header: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div class="text-sm"${_scopeId3}>`);
                        if (data._count) {
                          _push4(`<div${_scopeId3}><div class="text-surface-700 dark:text-surface-300"${_scopeId3}>${ssrInterpolate($setup.getTotalUsage(data._count))} \u0438\u0441\u043F. </div><div class="text-xs text-surface-500 dark:text-surface-400"${_scopeId3}>${ssrInterpolate($setup.getUsageDetails(data._count))}</div></div>`);
                        } else {
                          _push4(`<span class="text-surface-400 dark:text-surface-600"${_scopeId3}>\u2014</span>`);
                        }
                        _push4(`</div>`);
                      } else {
                        return [
                          createVNode("div", { class: "text-sm" }, [
                            data._count ? (openBlock(), createBlock("div", { key: 0 }, [
                              createVNode("div", { class: "text-surface-700 dark:text-surface-300" }, toDisplayString($setup.getTotalUsage(data._count)) + " \u0438\u0441\u043F. ", 1),
                              createVNode("div", { class: "text-xs text-surface-500 dark:text-surface-400" }, toDisplayString($setup.getUsageDetails(data._count)), 1)
                            ])) : (openBlock(), createBlock("span", {
                              key: 1,
                              class: "text-surface-400 dark:text-surface-600"
                            }, "\u2014"))
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Column"], {
                    header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<div class="flex gap-2"${_scopeId3}>`);
                        if (data.dataType === "STRING") {
                          _push4(ssrRenderComponent($setup["VButton"], {
                            onClick: ($event) => $setup.openSynonyms(data),
                            severity: "secondary",
                            outlined: "",
                            size: "small",
                            label: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B"
                          }, {
                            icon: withCtx((_3, _push5, _parent5, _scopeId4) => {
                              if (_push5) {
                                _push5(ssrRenderComponent($setup["Icon"], {
                                  name: "pi pi-tags",
                                  class: "w-5 h-5"
                                }, null, _parent5, _scopeId4));
                              } else {
                                return [
                                  createVNode($setup["Icon"], {
                                    name: "pi pi-tags",
                                    class: "w-5 h-5"
                                  })
                                ];
                              }
                            }),
                            _: 2
                          }, _parent4, _scopeId3));
                        } else {
                          _push4(`<!---->`);
                        }
                        _push4(ssrRenderComponent($setup["VButton"], {
                          onClick: ($event) => $setup.editTemplate(data),
                          severity: "secondary",
                          outlined: "",
                          size: "small",
                          label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
                        }, {
                          icon: withCtx((_3, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent($setup["Icon"], {
                                name: "pi pi-pencil",
                                class: "w-5 h-5"
                              }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode($setup["Icon"], {
                                  name: "pi pi-pencil",
                                  class: "w-5 h-5"
                                })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent4, _scopeId3));
                        _push4(ssrRenderComponent($setup["VButton"], {
                          onClick: ($event) => $setup.deleteTemplate(data),
                          severity: "danger",
                          outlined: "",
                          size: "small",
                          label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                          disabled: $setup.getTotalUsage(data._count) > 0
                        }, {
                          icon: withCtx((_3, _push5, _parent5, _scopeId4) => {
                            if (_push5) {
                              _push5(ssrRenderComponent($setup["Icon"], {
                                name: "pi pi-trash",
                                class: "w-5 h-5"
                              }, null, _parent5, _scopeId4));
                            } else {
                              return [
                                createVNode($setup["Icon"], {
                                  name: "pi pi-trash",
                                  class: "w-5 h-5"
                                })
                              ];
                            }
                          }),
                          _: 2
                        }, _parent4, _scopeId3));
                        _push4(`</div>`);
                      } else {
                        return [
                          createVNode("div", { class: "flex gap-2" }, [
                            data.dataType === "STRING" ? (openBlock(), createBlock($setup["VButton"], {
                              key: 0,
                              onClick: ($event) => $setup.openSynonyms(data),
                              severity: "secondary",
                              outlined: "",
                              size: "small",
                              label: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B"
                            }, {
                              icon: withCtx(() => [
                                createVNode($setup["Icon"], {
                                  name: "pi pi-tags",
                                  class: "w-5 h-5"
                                })
                              ]),
                              _: 2
                            }, 1032, ["onClick"])) : createCommentVNode("", true),
                            createVNode($setup["VButton"], {
                              onClick: ($event) => $setup.editTemplate(data),
                              severity: "secondary",
                              outlined: "",
                              size: "small",
                              label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
                            }, {
                              icon: withCtx(() => [
                                createVNode($setup["Icon"], {
                                  name: "pi pi-pencil",
                                  class: "w-5 h-5"
                                })
                              ]),
                              _: 2
                            }, 1032, ["onClick"]),
                            createVNode($setup["VButton"], {
                              onClick: ($event) => $setup.deleteTemplate(data),
                              severity: "danger",
                              outlined: "",
                              size: "small",
                              label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                              disabled: $setup.getTotalUsage(data._count) > 0
                            }, {
                              icon: withCtx(() => [
                                createVNode($setup["Icon"], {
                                  name: "pi pi-trash",
                                  class: "w-5 h-5"
                                })
                              ]),
                              _: 2
                            }, 1032, ["onClick", "disabled"])
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Column"], {
                      field: "id",
                      header: "ID",
                      sortable: "",
                      style: { "width": "80px" }
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("span", { class: "font-mono text-sm text-surface-700 dark:text-surface-300" }, "#" + toDisplayString(data.id), 1)
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "title",
                      header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                      sortable: ""
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("div", null, [
                          createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(data.title), 1),
                          createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400 font-mono" }, toDisplayString(data.name), 1)
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "group.name",
                      header: "\u0413\u0440\u0443\u043F\u043F\u0430",
                      sortable: ""
                    }, {
                      body: withCtx(({ data }) => [
                        data.group ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: data.group.name,
                          severity: "secondary"
                        }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 dark:text-surface-600"
                        }, "\u2014"))
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "dataType",
                      header: "\u0422\u0438\u043F",
                      sortable: ""
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode($setup["VTag"], {
                          value: $setup.getDataTypeLabel(data.dataType),
                          severity: "info"
                        }, null, 8, ["value"])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      field: "unit",
                      header: "\u0415\u0434\u0438\u043D\u0438\u0446\u0430"
                    }, {
                      body: withCtx(({ data }) => [
                        data.unit ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: $setup.getUnitLabel(data.unit),
                          severity: "success"
                        }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 dark:text-surface-600"
                        }, "\u2014"))
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      header: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435",
                      style: { "width": "120px" }
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("div", { class: "text-sm" }, [
                          data._count ? (openBlock(), createBlock("div", { key: 0 }, [
                            createVNode("div", { class: "text-surface-700 dark:text-surface-300" }, toDisplayString($setup.getTotalUsage(data._count)) + " \u0438\u0441\u043F. ", 1),
                            createVNode("div", { class: "text-xs text-surface-500 dark:text-surface-400" }, toDisplayString($setup.getUsageDetails(data._count)), 1)
                          ])) : (openBlock(), createBlock("span", {
                            key: 1,
                            class: "text-surface-400 dark:text-surface-600"
                          }, "\u2014"))
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode($setup["Column"], {
                      header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                      style: { "width": "120px" }
                    }, {
                      body: withCtx(({ data }) => [
                        createVNode("div", { class: "flex gap-2" }, [
                          data.dataType === "STRING" ? (openBlock(), createBlock($setup["VButton"], {
                            key: 0,
                            onClick: ($event) => $setup.openSynonyms(data),
                            severity: "secondary",
                            outlined: "",
                            size: "small",
                            label: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B"
                          }, {
                            icon: withCtx(() => [
                              createVNode($setup["Icon"], {
                                name: "pi pi-tags",
                                class: "w-5 h-5"
                              })
                            ]),
                            _: 2
                          }, 1032, ["onClick"])) : createCommentVNode("", true),
                          createVNode($setup["VButton"], {
                            onClick: ($event) => $setup.editTemplate(data),
                            severity: "secondary",
                            outlined: "",
                            size: "small",
                            label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
                          }, {
                            icon: withCtx(() => [
                              createVNode($setup["Icon"], {
                                name: "pi pi-pencil",
                                class: "w-5 h-5"
                              })
                            ]),
                            _: 2
                          }, 1032, ["onClick"]),
                          createVNode($setup["VButton"], {
                            onClick: ($event) => $setup.deleteTemplate(data),
                            severity: "danger",
                            outlined: "",
                            size: "small",
                            label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                            disabled: $setup.getTotalUsage(data._count) > 0
                          }, {
                            icon: withCtx(() => [
                              createVNode($setup["Icon"], {
                                name: "pi pi-trash",
                                class: "w-5 h-5"
                              })
                            ]),
                            _: 2
                          }, 1032, ["onClick", "disabled"])
                        ])
                      ]),
                      _: 1
                    })
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode($setup["VDataTable"], {
                value: $setup.templates,
                loading: $setup.tableLoading && $setup.templates.length === 0,
                paginator: "",
                rows: $setup.pageSize,
                "total-records": $setup.totalCount,
                "rows-per-page-options": [10, 25, 50],
                lazy: "",
                onPage: $setup.onPageChange,
                "table-style": "min-width: 50rem",
                class: "p-datatable-sm",
                "striped-rows": ""
              }, {
                default: withCtx(() => [
                  createVNode($setup["Column"], {
                    field: "id",
                    header: "ID",
                    sortable: "",
                    style: { "width": "80px" }
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("span", { class: "font-mono text-sm text-surface-700 dark:text-surface-300" }, "#" + toDisplayString(data.id), 1)
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "title",
                    header: "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("div", null, [
                        createVNode("div", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(data.title), 1),
                        createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400 font-mono" }, toDisplayString(data.name), 1)
                      ])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "group.name",
                    header: "\u0413\u0440\u0443\u043F\u043F\u0430",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }) => [
                      data.group ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: data.group.name,
                        severity: "secondary"
                      }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 dark:text-surface-600"
                      }, "\u2014"))
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "dataType",
                    header: "\u0422\u0438\u043F",
                    sortable: ""
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode($setup["VTag"], {
                        value: $setup.getDataTypeLabel(data.dataType),
                        severity: "info"
                      }, null, 8, ["value"])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    field: "unit",
                    header: "\u0415\u0434\u0438\u043D\u0438\u0446\u0430"
                  }, {
                    body: withCtx(({ data }) => [
                      data.unit ? (openBlock(), createBlock($setup["VTag"], {
                        key: 0,
                        value: $setup.getUnitLabel(data.unit),
                        severity: "success"
                      }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                        key: 1,
                        class: "text-surface-400 dark:text-surface-600"
                      }, "\u2014"))
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    header: "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("div", { class: "text-sm" }, [
                        data._count ? (openBlock(), createBlock("div", { key: 0 }, [
                          createVNode("div", { class: "text-surface-700 dark:text-surface-300" }, toDisplayString($setup.getTotalUsage(data._count)) + " \u0438\u0441\u043F. ", 1),
                          createVNode("div", { class: "text-xs text-surface-500 dark:text-surface-400" }, toDisplayString($setup.getUsageDetails(data._count)), 1)
                        ])) : (openBlock(), createBlock("span", {
                          key: 1,
                          class: "text-surface-400 dark:text-surface-600"
                        }, "\u2014"))
                      ])
                    ]),
                    _: 1
                  }),
                  createVNode($setup["Column"], {
                    header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
                    style: { "width": "120px" }
                  }, {
                    body: withCtx(({ data }) => [
                      createVNode("div", { class: "flex gap-2" }, [
                        data.dataType === "STRING" ? (openBlock(), createBlock($setup["VButton"], {
                          key: 0,
                          onClick: ($event) => $setup.openSynonyms(data),
                          severity: "secondary",
                          outlined: "",
                          size: "small",
                          label: "\u0421\u0438\u043D\u043E\u043D\u0438\u043C\u044B"
                        }, {
                          icon: withCtx(() => [
                            createVNode($setup["Icon"], {
                              name: "pi pi-tags",
                              class: "w-5 h-5"
                            })
                          ]),
                          _: 2
                        }, 1032, ["onClick"])) : createCommentVNode("", true),
                        createVNode($setup["VButton"], {
                          onClick: ($event) => $setup.editTemplate(data),
                          severity: "secondary",
                          outlined: "",
                          size: "small",
                          label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
                        }, {
                          icon: withCtx(() => [
                            createVNode($setup["Icon"], {
                              name: "pi pi-pencil",
                              class: "w-5 h-5"
                            })
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode($setup["VButton"], {
                          onClick: ($event) => $setup.deleteTemplate(data),
                          severity: "danger",
                          outlined: "",
                          size: "small",
                          label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                          disabled: $setup.getTotalUsage(data._count) > 0
                        }, {
                          icon: withCtx(() => [
                            createVNode($setup["Icon"], {
                              name: "pi pi-trash",
                              class: "w-5 h-5"
                            })
                          ]),
                          _: 2
                        }, 1032, ["onClick", "disabled"])
                      ])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 8, ["value", "loading", "rows", "total-records"])
            ];
          }
        }),
        _: 1
      }, _parent));
    } else if ($setup.viewMode === "cards") {
      _push(`<div class="grid gap-4"><!--[-->`);
      ssrRenderList($setup.templates, (template) => {
        _push(ssrRenderComponent($setup["VCard"], {
          key: template.id,
          class: "border border-surface-200 dark:border-surface-700 hover:border-primary transition-colors"
        }, {
          content: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="p-6"${_scopeId}><div class="flex items-start justify-between mb-4"${_scopeId}><div class="flex-1"${_scopeId}><div class="flex items-center gap-3 mb-2"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-tag",
                class: "text-blue-600 w-5 h-5"
              }, null, _parent2, _scopeId));
              _push2(`<h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(template.title)}</h3>`);
              if (template.isRequired) {
                _push2(ssrRenderComponent($setup["VTag"], {
                  value: "\u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439",
                  severity: "danger",
                  size: "small"
                }, null, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="text-sm text-surface-600 dark:text-surface-400 font-mono mb-2"${_scopeId}>${ssrInterpolate(template.name)}</div>`);
              if (template.description) {
                _push2(`<p class="text-surface-600 dark:text-surface-400 mb-3"${_scopeId}>${ssrInterpolate(template.description)}</p>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="flex gap-2 ml-4"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["VButton"], {
                onClick: ($event) => $setup.editTemplate(template),
                severity: "secondary",
                outlined: "",
                size: "small",
                label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
              }, {
                icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["Icon"], {
                      name: "pi pi-pencil",
                      class: "w-5 h-5"
                    }, null, _parent3, _scopeId2));
                  } else {
                    return [
                      createVNode($setup["Icon"], {
                        name: "pi pi-pencil",
                        class: "w-5 h-5"
                      })
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(ssrRenderComponent($setup["VButton"], {
                onClick: ($event) => $setup.deleteTemplate(template),
                severity: "danger",
                outlined: "",
                size: "small",
                label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                disabled: $setup.getTotalUsage(template._count) > 0
              }, {
                icon: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["Icon"], {
                      name: "pi pi-trash",
                      class: "w-5 h-5"
                    }, null, _parent3, _scopeId2));
                  } else {
                    return [
                      createVNode($setup["Icon"], {
                        name: "pi pi-trash",
                        class: "w-5 h-5"
                      })
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></div><div class="flex items-center gap-4 mb-4"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["VTag"], {
                value: $setup.getDataTypeLabel(template.dataType),
                severity: "info"
              }, null, _parent2, _scopeId));
              if (template.unit) {
                _push2(ssrRenderComponent($setup["VTag"], {
                  value: $setup.getUnitLabel(template.unit),
                  severity: "success"
                }, null, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              if (template.group) {
                _push2(ssrRenderComponent($setup["VTag"], {
                  value: template.group.name,
                  severity: "secondary"
                }, null, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div>`);
              if (template._count) {
                _push2(`<div class="border-t border-surface-200 dark:border-surface-700 pt-4"${_scopeId}><div class="text-sm text-surface-600 dark:text-surface-400 mb-2"${_scopeId}> \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435: </div><div class="grid grid-cols-3 gap-4 text-center"${_scopeId}><div${_scopeId}><div class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(template._count.partAttributes || 0)}</div><div class="text-xs text-surface-500"${_scopeId}>\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438</div></div><div${_scopeId}><div class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(template._count.catalogItemAttributes || 0)}</div><div class="text-xs text-surface-500"${_scopeId}>\u041A\u0430\u0442\u0430\u043B\u043E\u0433</div></div><div${_scopeId}><div class="text-lg font-semibold text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(template._count.equipmentAttributes || 0)}</div><div class="text-xs text-surface-500"${_scopeId}>\u0422\u0435\u0445\u043D\u0438\u043A\u0430</div></div></div></div>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div>`);
            } else {
              return [
                createVNode("div", { class: "p-6" }, [
                  createVNode("div", { class: "flex items-start justify-between mb-4" }, [
                    createVNode("div", { class: "flex-1" }, [
                      createVNode("div", { class: "flex items-center gap-3 mb-2" }, [
                        createVNode($setup["Icon"], {
                          name: "pi pi-tag",
                          class: "text-blue-600 w-5 h-5"
                        }),
                        createVNode("h3", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString(template.title), 1),
                        template.isRequired ? (openBlock(), createBlock($setup["VTag"], {
                          key: 0,
                          value: "\u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439",
                          severity: "danger",
                          size: "small"
                        })) : createCommentVNode("", true)
                      ]),
                      createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400 font-mono mb-2" }, toDisplayString(template.name), 1),
                      template.description ? (openBlock(), createBlock("p", {
                        key: 0,
                        class: "text-surface-600 dark:text-surface-400 mb-3"
                      }, toDisplayString(template.description), 1)) : createCommentVNode("", true)
                    ]),
                    createVNode("div", { class: "flex gap-2 ml-4" }, [
                      createVNode($setup["VButton"], {
                        onClick: ($event) => $setup.editTemplate(template),
                        severity: "secondary",
                        outlined: "",
                        size: "small",
                        label: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C"
                      }, {
                        icon: withCtx(() => [
                          createVNode($setup["Icon"], {
                            name: "pi pi-pencil",
                            class: "w-5 h-5"
                          })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["VButton"], {
                        onClick: ($event) => $setup.deleteTemplate(template),
                        severity: "danger",
                        outlined: "",
                        size: "small",
                        label: "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
                        disabled: $setup.getTotalUsage(template._count) > 0
                      }, {
                        icon: withCtx(() => [
                          createVNode($setup["Icon"], {
                            name: "pi pi-trash",
                            class: "w-5 h-5"
                          })
                        ]),
                        _: 2
                      }, 1032, ["onClick", "disabled"])
                    ])
                  ]),
                  createVNode("div", { class: "flex items-center gap-4 mb-4" }, [
                    createVNode($setup["VTag"], {
                      value: $setup.getDataTypeLabel(template.dataType),
                      severity: "info"
                    }, null, 8, ["value"]),
                    template.unit ? (openBlock(), createBlock($setup["VTag"], {
                      key: 0,
                      value: $setup.getUnitLabel(template.unit),
                      severity: "success"
                    }, null, 8, ["value"])) : createCommentVNode("", true),
                    template.group ? (openBlock(), createBlock($setup["VTag"], {
                      key: 1,
                      value: template.group.name,
                      severity: "secondary"
                    }, null, 8, ["value"])) : createCommentVNode("", true)
                  ]),
                  template._count ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "border-t border-surface-200 dark:border-surface-700 pt-4"
                  }, [
                    createVNode("div", { class: "text-sm text-surface-600 dark:text-surface-400 mb-2" }, " \u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0435: "),
                    createVNode("div", { class: "grid grid-cols-3 gap-4 text-center" }, [
                      createVNode("div", null, [
                        createVNode("div", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString(template._count.partAttributes || 0), 1),
                        createVNode("div", { class: "text-xs text-surface-500" }, "\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438")
                      ]),
                      createVNode("div", null, [
                        createVNode("div", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString(template._count.catalogItemAttributes || 0), 1),
                        createVNode("div", { class: "text-xs text-surface-500" }, "\u041A\u0430\u0442\u0430\u043B\u043E\u0433")
                      ]),
                      createVNode("div", null, [
                        createVNode("div", { class: "text-lg font-semibold text-surface-900 dark:text-surface-0" }, toDisplayString(template._count.equipmentAttributes || 0), 1),
                        createVNode("div", { class: "text-xs text-surface-500" }, "\u0422\u0435\u0445\u043D\u0438\u043A\u0430")
                      ])
                    ])
                  ])) : createCommentVNode("", true)
                ])
              ];
            }
          }),
          _: 2
        }, _parent));
      });
      _push(`<!--]-->`);
      if ($setup.totalCount > $setup.pageSize) {
        _push(ssrRenderComponent($setup["VCard"], null, {
          content: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<div class="p-4"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Paginator"], {
                rows: $setup.pageSize,
                "total-records": $setup.totalCount,
                "rows-per-page-options": [10, 25, 50],
                onPage: $setup.onPageChange
              }, null, _parent2, _scopeId));
              _push2(`</div>`);
            } else {
              return [
                createVNode("div", { class: "p-4" }, [
                  createVNode($setup["Paginator"], {
                    rows: $setup.pageSize,
                    "total-records": $setup.totalCount,
                    "rows-per-page-options": [10, 25, 50],
                    onPage: $setup.onPageChange
                  }, null, 8, ["rows", "total-records"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    } else {
      _push(`<!---->`);
    }
    _push(ssrRenderComponent($setup["VDialog"], {
      visible: $setup.showCreateDialog,
      "onUpdate:visible": ($event) => $setup.showCreateDialog = $event,
      modal: "",
      header: $setup.editingTemplate ? "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D" : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D",
      style: { width: "50rem" },
      breakpoints: { "1199px": "75vw", "575px": "90vw" }
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["TemplateForm"], {
            modelValue: $setup.templateForm,
            "onUpdate:modelValue": ($event) => $setup.templateForm = $event,
            groups: $setup.groups,
            loading: $setup.saving,
            onSave: $setup.saveTemplate,
            onCancel: ($event) => $setup.showCreateDialog = false
          }, null, _parent2, _scopeId));
        } else {
          return [
            createVNode($setup["TemplateForm"], {
              modelValue: $setup.templateForm,
              "onUpdate:modelValue": ($event) => $setup.templateForm = $event,
              groups: $setup.groups,
              loading: $setup.saving,
              onSave: $setup.saveTemplate,
              onCancel: ($event) => $setup.showCreateDialog = false
            }, null, 8, ["modelValue", "onUpdate:modelValue", "groups", "loading", "onCancel"])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(ssrRenderComponent($setup["VDialog"], {
      visible: $setup.showSynonymsDialog,
      "onUpdate:visible": ($event) => $setup.showSynonymsDialog = $event,
      modal: "",
      header: "\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0441\u0438\u043D\u043E\u043D\u0438\u043C\u0430\u043C\u0438",
      style: { width: "80rem" },
      breakpoints: { "1199px": "90vw", "575px": "98vw" }
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          if ($setup.selectedTemplateForSynonyms) {
            _push2(ssrRenderComponent($setup["AttributeSynonymManager"], { template: $setup.selectedTemplateForSynonyms }, null, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
        } else {
          return [
            $setup.selectedTemplateForSynonyms ? (openBlock(), createBlock($setup["AttributeSynonymManager"], {
              key: 0,
              template: $setup.selectedTemplateForSynonyms
            }, null, 8, ["template"])) : createCommentVNode("", true)
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  }
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeTemplateManager.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const AttributeTemplateManager = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "AttributeTemplateManagerBoundary",
  setup(__props, { expose: __expose }) {
    __expose();
    const key = ref(0);
    const onRetry = () => {
      key.value++;
    };
    const __returned__ = { key, onRetry, ErrorBoundary, AttributeTemplateManager };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ErrorBoundary"], mergeProps({
    variant: "detailed",
    onRetry: $setup.onRetry,
    title: "\u041F\u0440\u043E\u0431\u043B\u0435\u043C\u0430 \u043F\u0440\u0438 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432",
    message: "\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u043E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0441\u043F\u0438\u0441\u043E\u043A \u0438\u043B\u0438 \u043F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C \u043F\u043E\u043F\u044B\u0442\u043A\u0443."
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["AttributeTemplateManager"], { key: $setup.key }, null, _parent2, _scopeId));
      } else {
        return [
          (openBlock(), createBlock($setup["AttributeTemplateManager"], { key: $setup.key }))
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/attributes/AttributeTemplateManagerBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const AttributeTemplateManagerBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Attributes = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$AdminLayout, { "title": "\u0423\u043F\u0440\u0430\u0432\u043B\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430\u043C\u0438" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "AttributeTemplateManagerBoundary", AttributeTemplateManagerBoundary, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/admin/attributes/AttributeTemplateManagerBoundary.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/attributes.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/attributes.astro";
const $$url = "/admin/attributes";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
    __proto__: null,
    default: $$Attributes,
    file: $$file,
    url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

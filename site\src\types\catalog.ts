// Базовые типы для каталога запчастей

export interface PartListItem {
  id: number;
  name: string | null;
  path: string;
  updatedAt: Date;
  partCategory: {
    id: number;
    name: string;
    slug: string;
  };
  image?: {
    id: number;
    url: string;
  } | null;
  attributes: Array<{
    id: number;
    value: string;
    numericValue: number | null;
    template: {
      id: number;
      name: string;
      title: string;
      dataType: string;
      unit: string | null;
    };
  }>;
}

export interface PartCategoryItem {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  level: number;
  path: string;
  icon: string | null;
  image?: {
    id: number;
    url: string;
  } | null;
  children?: PartCategoryItem[];
  _count?: {
    parts: number;
  };
}

export interface BrandItem {
  id: number;
  name: string;
  slug: string;
  country: string | null;
  isOem: boolean;
  _count?: {
    catalogItems: number;
    equipmentModel: number;
  };
}

export interface CatalogItemDetail {
  id: number;
  sku: string;
  description: string | null;
  source: string | null;
  brand: BrandItem;
  attributes: Array<{
    id: number;
    value: string;
    numericValue: number | null;
    template: {
      id: number;
      name: string;
      title: string;
      dataType: string;
      unit: string | null;
    };
  }>;
}

export interface PartDetail extends PartListItem {
  parent?: {
    id: number;
    name: string | null;
    partCategory: {
      id: number;
      name: string;
    };
  } | null;
  children: Array<{
    id: number;
    name: string | null;
    partCategory: {
      id: number;
      name: string;
    };
  }>;
  applicabilities: Array<{
    id: number;
    accuracy: 'EXACT_MATCH' | 'MATCH_WITH_NOTES' | 'REQUIRES_MODIFICATION' | 'PARTIAL_MATCH';
    notes: string | null;
    catalogItem: CatalogItemDetail;
  }>;
  equipmentApplicabilities: Array<{
    id: number;
    notes: string | null;
    equipmentModel: {
      id: string;
      name: string;
      brand?: BrandItem | null;
    };
  }>;
}

export interface SearchFilters {
  query?: string;
  categoryId?: number;
  brandId?: number;
  attributes?: Record<string, string>;
  isOem?: boolean;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface SearchParams extends SearchFilters, PaginationParams {
  sortBy?: 'name' | 'updatedAt' | 'category';
  sortOrder?: 'asc' | 'desc';
}

import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { defineComponent, useSSRContext, ref, reactive, computed, mergeProps, withCtx, createTextVNode, toDisplayString, createVNode, withModifiers, createBlock, createCommentVNode, openBlock } from 'vue';
import { u as useAuth, n as navigate, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { P as Password } from '../../chunks/Password_BQyeKHib.mjs';
import { M as Message } from '../../chunks/Message_acgdACvd.mjs';
import { C as Checkbox } from '../../chunks/Checkbox_Ca7GoCvq.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc } from '../../chunks/ClientRouter_avhRMbqw.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "RegisterForm",
  setup(__props, { expose: __expose }) {
    __expose();
    const { signUp, RegisterFormSchema } = useAuth();
    const isSubmitting = ref(false);
    const generalError = ref("");
    const roleOptions = [
      { label: "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C", value: "USER" },
      { label: "\u041C\u0430\u0433\u0430\u0437\u0438\u043D \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439", value: "SHOP" }
      // ADMIN роль может назначать только существующий админ
    ];
    const formData = reactive({
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "USER",
      acceptTerms: false
    });
    const fieldErrors = reactive({});
    const isFormValid = computed(() => {
      return formData.name && formData.email && formData.password && formData.confirmPassword && formData.acceptTerms && Object.keys(fieldErrors).length === 0;
    });
    const validateField = (fieldName) => {
      const result = RegisterFormSchema.safeParse(formData);
      if (!result.success) {
        const fieldError = result.error.issues.find(
          (issue) => issue.path.includes(fieldName)
        );
        if (fieldError) {
          fieldErrors[fieldName] = fieldError.message;
        }
      } else {
        delete fieldErrors[fieldName];
      }
    };
    const clearFieldError = (fieldName) => {
      delete fieldErrors[fieldName];
      generalError.value = "";
    };
    const handleSubmit = async () => {
      Object.keys(fieldErrors).forEach((key) => delete fieldErrors[key]);
      generalError.value = "";
      const validationResult = RegisterFormSchema.safeParse(formData);
      if (!validationResult.success) {
        validationResult.error.issues.forEach((issue) => {
          const fieldName = issue.path[0];
          fieldErrors[fieldName] = issue.message;
        });
        return;
      }
      isSubmitting.value = true;
      try {
        const result = await signUp(formData);
        if (result.error) {
          if (result.error.message.includes("already exists")) {
            generalError.value = "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C \u0441 \u0442\u0430\u043A\u0438\u043C email \u0443\u0436\u0435 \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u0435\u0442";
          } else if (result.error.message.includes("Invalid email")) {
            generalError.value = "\u041D\u0435\u043A\u043E\u0440\u0440\u0435\u043A\u0442\u043D\u044B\u0439 email \u0430\u0434\u0440\u0435\u0441";
          } else {
            generalError.value = result.error.message || "\u041E\u0448\u0438\u0431\u043A\u0430 \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u0438";
          }
        } else {
          console.log("\u2705 \u0423\u0441\u043F\u0435\u0448\u043D\u0430\u044F \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044F");
          setTimeout(() => {
            navigate("/admin");
          }, 100);
        }
      } catch (error) {
        console.error("Registration error:", error);
        generalError.value = "\u041F\u0440\u043E\u0438\u0437\u043E\u0448\u043B\u0430 \u043E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438 \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u0438. \u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u043F\u043E\u0437\u0436\u0435.";
      } finally {
        isSubmitting.value = false;
      }
    };
    const __returned__ = { signUp, RegisterFormSchema, isSubmitting, generalError, roleOptions, formData, fieldErrors, isFormValid, validateField, clearFieldError, handleSubmit, Button, Card, InputText, Password, Message, Checkbox, Select };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8" }, _attrs))}><div class="max-w-md w-full space-y-8"><div><div class="mx-auto h-12 w-12 flex items-center justify-center"><img class="h-12 w-12" src="/favicon.svg" alt="PartTec"></div><h2 class="mt-6 text-center text-3xl font-extrabold text-surface-900"> \u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044F \u0432 PartTec </h2><p class="mt-2 text-center text-sm text-surface-600"> \u0421\u043E\u0437\u0434\u0430\u0439\u0442\u0435 \u0430\u043A\u043A\u0430\u0443\u043D\u0442 \u0434\u043B\u044F \u0434\u043E\u0441\u0442\u0443\u043F\u0430 \u043A \u0441\u0438\u0441\u0442\u0435\u043C\u0435 </p></div>`);
  _push(ssrRenderComponent($setup["Card"], { class: "mt-8" }, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<form class="space-y-6"${_scopeId}>`);
        if ($setup.generalError) {
          _push2(ssrRenderComponent($setup["Message"], {
            severity: "error",
            closable: false
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`${ssrInterpolate($setup.generalError)}`);
              } else {
                return [
                  createTextVNode(toDisplayString($setup.generalError), 1)
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "name",
          modelValue: $setup.formData.name,
          "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
          type: "text",
          autocomplete: "name",
          class: "w-full",
          invalid: !!$setup.fieldErrors.name,
          onBlur: ($event) => $setup.validateField("name"),
          onInput: ($event) => $setup.clearFieldError("name")
        }, null, _parent2, _scopeId));
        _push2(`<label for="name"${_scopeId}>\u041F\u043E\u043B\u043D\u043E\u0435 \u0438\u043C\u044F</label></div>`);
        if ($setup.fieldErrors.name) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.fieldErrors.name)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "email",
          modelValue: $setup.formData.email,
          "onUpdate:modelValue": ($event) => $setup.formData.email = $event,
          type: "email",
          autocomplete: "email",
          class: "w-full",
          invalid: !!$setup.fieldErrors.email,
          onBlur: ($event) => $setup.validateField("email"),
          onInput: ($event) => $setup.clearFieldError("email")
        }, null, _parent2, _scopeId));
        _push2(`<label for="email"${_scopeId}>Email \u0430\u0434\u0440\u0435\u0441</label></div>`);
        if ($setup.fieldErrors.email) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.fieldErrors.email)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Password"], {
          id: "password",
          modelValue: $setup.formData.password,
          "onUpdate:modelValue": ($event) => $setup.formData.password = $event,
          autocomplete: "new-password",
          class: "w-full",
          invalid: !!$setup.fieldErrors.password,
          feedback: false,
          "toggle-mask": "",
          onBlur: ($event) => $setup.validateField("password"),
          onInput: ($event) => $setup.clearFieldError("password")
        }, null, _parent2, _scopeId));
        _push2(`<label for="password"${_scopeId}>\u041F\u0430\u0440\u043E\u043B\u044C</label></div>`);
        if ($setup.fieldErrors.password) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.fieldErrors.password)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Password"], {
          id: "confirmPassword",
          modelValue: $setup.formData.confirmPassword,
          "onUpdate:modelValue": ($event) => $setup.formData.confirmPassword = $event,
          autocomplete: "new-password",
          class: "w-full",
          invalid: !!$setup.fieldErrors.confirmPassword,
          feedback: false,
          "toggle-mask": "",
          onBlur: ($event) => $setup.validateField("confirmPassword"),
          onInput: ($event) => $setup.clearFieldError("confirmPassword")
        }, null, _parent2, _scopeId));
        _push2(`<label for="confirmPassword"${_scopeId}>\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u0435 \u043F\u0430\u0440\u043E\u043B\u044C</label></div>`);
        if ($setup.fieldErrors.confirmPassword) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.fieldErrors.confirmPassword)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Select"], {
          id: "role",
          modelValue: $setup.formData.role,
          "onUpdate:modelValue": ($event) => $setup.formData.role = $event,
          options: $setup.roleOptions,
          "option-label": "label",
          "option-value": "value",
          class: "w-full",
          invalid: !!$setup.fieldErrors.role,
          onChange: ($event) => $setup.clearFieldError("role")
        }, null, _parent2, _scopeId));
        _push2(`<label for="role"${_scopeId}>\u0422\u0438\u043F \u0430\u043A\u043A\u0430\u0443\u043D\u0442\u0430</label></div>`);
        if ($setup.fieldErrors.role) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.fieldErrors.role)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div class="flex items-start"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Checkbox"], {
          id: "acceptTerms",
          modelValue: $setup.formData.acceptTerms,
          "onUpdate:modelValue": ($event) => $setup.formData.acceptTerms = $event,
          binary: true,
          invalid: !!$setup.fieldErrors.acceptTerms,
          onChange: ($event) => $setup.clearFieldError("acceptTerms")
        }, null, _parent2, _scopeId));
        _push2(`<label for="acceptTerms" class="ml-2 block text-sm text-surface-700"${_scopeId}> \u042F \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u044E <a href="/terms" class="font-medium text-primary-600 hover:text-primary-500"${_scopeId}> \u0443\u0441\u043B\u043E\u0432\u0438\u044F \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F </a> \u0438 <a href="/privacy" class="font-medium text-primary-600 hover:text-primary-500"${_scopeId}> \u043F\u043E\u043B\u0438\u0442\u0438\u043A\u0443 \u043A\u043E\u043D\u0444\u0438\u0434\u0435\u043D\u0446\u0438\u0430\u043B\u044C\u043D\u043E\u0441\u0442\u0438 </a></label></div>`);
        if ($setup.fieldErrors.acceptTerms) {
          _push2(`<small class="text-red-500"${_scopeId}>${ssrInterpolate($setup.fieldErrors.acceptTerms)}</small>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`<div${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          type: "submit",
          disabled: $setup.isSubmitting || !$setup.isFormValid,
          loading: $setup.isSubmitting,
          label: "\u0417\u0430\u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C\u0441\u044F",
          class: "w-full",
          size: "large"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="text-center"${_scopeId}><p class="text-sm text-surface-600"${_scopeId}> \u0423\u0436\u0435 \u0435\u0441\u0442\u044C \u0430\u043A\u043A\u0430\u0443\u043D\u0442? <a href="/admin/login" class="font-medium text-primary-600 hover:text-primary-500"${_scopeId}> \u0412\u043E\u0439\u0442\u0438 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443 </a></p></div></form>`);
      } else {
        return [
          createVNode("form", {
            class: "space-y-6",
            onSubmit: withModifiers($setup.handleSubmit, ["prevent"])
          }, [
            $setup.generalError ? (openBlock(), createBlock($setup["Message"], {
              key: 0,
              severity: "error",
              closable: false
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString($setup.generalError), 1)
              ]),
              _: 1
            })) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["InputText"], {
                id: "name",
                modelValue: $setup.formData.name,
                "onUpdate:modelValue": ($event) => $setup.formData.name = $event,
                type: "text",
                autocomplete: "name",
                class: "w-full",
                invalid: !!$setup.fieldErrors.name,
                onBlur: ($event) => $setup.validateField("name"),
                onInput: ($event) => $setup.clearFieldError("name")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onBlur", "onInput"]),
              createVNode("label", { for: "name" }, "\u041F\u043E\u043B\u043D\u043E\u0435 \u0438\u043C\u044F")
            ]),
            $setup.fieldErrors.name ? (openBlock(), createBlock("small", {
              key: 1,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.name), 1)) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["InputText"], {
                id: "email",
                modelValue: $setup.formData.email,
                "onUpdate:modelValue": ($event) => $setup.formData.email = $event,
                type: "email",
                autocomplete: "email",
                class: "w-full",
                invalid: !!$setup.fieldErrors.email,
                onBlur: ($event) => $setup.validateField("email"),
                onInput: ($event) => $setup.clearFieldError("email")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onBlur", "onInput"]),
              createVNode("label", { for: "email" }, "Email \u0430\u0434\u0440\u0435\u0441")
            ]),
            $setup.fieldErrors.email ? (openBlock(), createBlock("small", {
              key: 2,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.email), 1)) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["Password"], {
                id: "password",
                modelValue: $setup.formData.password,
                "onUpdate:modelValue": ($event) => $setup.formData.password = $event,
                autocomplete: "new-password",
                class: "w-full",
                invalid: !!$setup.fieldErrors.password,
                feedback: false,
                "toggle-mask": "",
                onBlur: ($event) => $setup.validateField("password"),
                onInput: ($event) => $setup.clearFieldError("password")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onBlur", "onInput"]),
              createVNode("label", { for: "password" }, "\u041F\u0430\u0440\u043E\u043B\u044C")
            ]),
            $setup.fieldErrors.password ? (openBlock(), createBlock("small", {
              key: 3,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.password), 1)) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["Password"], {
                id: "confirmPassword",
                modelValue: $setup.formData.confirmPassword,
                "onUpdate:modelValue": ($event) => $setup.formData.confirmPassword = $event,
                autocomplete: "new-password",
                class: "w-full",
                invalid: !!$setup.fieldErrors.confirmPassword,
                feedback: false,
                "toggle-mask": "",
                onBlur: ($event) => $setup.validateField("confirmPassword"),
                onInput: ($event) => $setup.clearFieldError("confirmPassword")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onBlur", "onInput"]),
              createVNode("label", { for: "confirmPassword" }, "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u0435 \u043F\u0430\u0440\u043E\u043B\u044C")
            ]),
            $setup.fieldErrors.confirmPassword ? (openBlock(), createBlock("small", {
              key: 4,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.confirmPassword), 1)) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["Select"], {
                id: "role",
                modelValue: $setup.formData.role,
                "onUpdate:modelValue": ($event) => $setup.formData.role = $event,
                options: $setup.roleOptions,
                "option-label": "label",
                "option-value": "value",
                class: "w-full",
                invalid: !!$setup.fieldErrors.role,
                onChange: ($event) => $setup.clearFieldError("role")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onChange"]),
              createVNode("label", { for: "role" }, "\u0422\u0438\u043F \u0430\u043A\u043A\u0430\u0443\u043D\u0442\u0430")
            ]),
            $setup.fieldErrors.role ? (openBlock(), createBlock("small", {
              key: 5,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.role), 1)) : createCommentVNode("", true),
            createVNode("div", { class: "flex items-start" }, [
              createVNode($setup["Checkbox"], {
                id: "acceptTerms",
                modelValue: $setup.formData.acceptTerms,
                "onUpdate:modelValue": ($event) => $setup.formData.acceptTerms = $event,
                binary: true,
                invalid: !!$setup.fieldErrors.acceptTerms,
                onChange: ($event) => $setup.clearFieldError("acceptTerms")
              }, null, 8, ["modelValue", "onUpdate:modelValue", "invalid", "onChange"]),
              createVNode("label", {
                for: "acceptTerms",
                class: "ml-2 block text-sm text-surface-700"
              }, [
                createTextVNode(" \u042F \u043F\u0440\u0438\u043D\u0438\u043C\u0430\u044E "),
                createVNode("a", {
                  href: "/terms",
                  class: "font-medium text-primary-600 hover:text-primary-500"
                }, " \u0443\u0441\u043B\u043E\u0432\u0438\u044F \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F "),
                createTextVNode(" \u0438 "),
                createVNode("a", {
                  href: "/privacy",
                  class: "font-medium text-primary-600 hover:text-primary-500"
                }, " \u043F\u043E\u043B\u0438\u0442\u0438\u043A\u0443 \u043A\u043E\u043D\u0444\u0438\u0434\u0435\u043D\u0446\u0438\u0430\u043B\u044C\u043D\u043E\u0441\u0442\u0438 ")
              ])
            ]),
            $setup.fieldErrors.acceptTerms ? (openBlock(), createBlock("small", {
              key: 6,
              class: "text-red-500"
            }, toDisplayString($setup.fieldErrors.acceptTerms), 1)) : createCommentVNode("", true),
            createVNode("div", null, [
              createVNode($setup["Button"], {
                type: "submit",
                disabled: $setup.isSubmitting || !$setup.isFormValid,
                loading: $setup.isSubmitting,
                label: "\u0417\u0430\u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C\u0441\u044F",
                class: "w-full",
                size: "large"
              }, null, 8, ["disabled", "loading"])
            ]),
            createVNode("div", { class: "text-center" }, [
              createVNode("p", { class: "text-sm text-surface-600" }, [
                createTextVNode(" \u0423\u0436\u0435 \u0435\u0441\u0442\u044C \u0430\u043A\u043A\u0430\u0443\u043D\u0442? "),
                createVNode("a", {
                  href: "/admin/login",
                  class: "font-medium text-primary-600 hover:text-primary-500"
                }, " \u0412\u043E\u0439\u0442\u0438 \u0432 \u0441\u0438\u0441\u0442\u0435\u043C\u0443 ")
              ])
            ])
          ], 32)
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/auth/RegisterForm.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const RegisterForm = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Register = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Register;
  const user = Astro2.locals.user;
  if (user) {
    return Astro2.redirect("/admin");
  }
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, { "title": "\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044F - PartTec", "showSidebar": false }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "RegisterForm", RegisterForm, { "client:load": true, "client:component-hydration": "load", "client:component-path": "D:/Dev/PARTTEC/parttec3/frontend/src/components/auth/RegisterForm.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/register.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/register.astro";
const $$url = "/admin/register";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Register,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

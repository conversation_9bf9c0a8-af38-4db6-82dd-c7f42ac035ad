import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_AEsVEWgS.mjs';
import { defineComponent, useSSRContext, ref, computed, watch, mergeProps, withCtx, createTextVNode, createVNode, withK<PERSON><PERSON>, createBlock, createCommentVNode, openBlock, Fragment, renderList, toDisplayString } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import { f as fetchers, q as qk, u as useQueryToastErrors } from '../chunks/fetchers_CTjsTB2x.mjs';
import { u as useDebounce, C as CatalogPagination, S as Spinner } from '../chunks/Spinner_g8zgaE0e.mjs';
import { a as useSearchFilters } from '../chunks/useUrlParams_CGyErwK-.mjs';
import { C as Card } from '../chunks/Card_aE2_b9LT.mjs';
import { I as InputText } from '../chunks/InputText_DNFWprlB.mjs';
import { M as MultiSelect } from '../chunks/MultiSelect_uAHCsge7.mjs';
import { S as SecondaryButton } from '../chunks/SecondaryButton_B0hmlm1n.mjs';
import { T as Tag } from '../chunks/Tag_B6nH2bAR.mjs';
import { I as InputNumber } from '../chunks/InputNumber_B4WnM2Ea.mjs';
import { S as Select } from '../chunks/Select_DIHmHCCM.mjs';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList, ssrInterpolate, ssrRenderAttr } from 'vue/server-renderer';
/* empty css                                  */
import { _ as _export_sfc } from '../chunks/ClientRouter_avhRMbqw.mjs';
import { t as trpc } from '../chunks/trpc_DApR3DD7.mjs';
export { r as renderers } from '../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "SearchPageComponent",
  props: {
    initialQuery: { default: "" },
    initialSearchResults: {},
    initialBrands: {},
    initialCategories: {},
    initialAttributeTemplates: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const props = __props;
    const { filters, updateFilter, updateFilters } = useSearchFilters({
      query: props.initialQuery || "",
      page: 1,
      limit: 20
    });
    const attrState = ref({});
    const attributeFilters = computed(() => {
      const out = [];
      for (const [templateIdStr, st] of Object.entries(attrState.value)) {
        const templateId = Number(templateIdStr);
        if (!templateId || !st) continue;
        if (st.matchType === "range") {
          if (st.min != null || st.max != null) {
            out.push({ templateId, matchType: "range", minValue: st.min ?? void 0, maxValue: st.max ?? void 0 });
          }
        } else if (st.value && String(st.value).trim()) {
          out.push({ templateId, matchType: st.matchType || "contains", value: String(st.value) });
        }
      }
      return out;
    });
    function updateAttrRange(templateId, key, value) {
      const cur = attrState.value[templateId] || { matchType: "range", min: null, max: null };
      attrState.value[templateId] = { ...cur, matchType: "range", [key]: value };
    }
    function updateAttrMatchType(templateId, matchType) {
      const cur = attrState.value[templateId] || {};
      attrState.value[templateId] = { ...cur, matchType, ...matchType === "range" ? { min: null, max: null } : {} };
    }
    function updateAttrValue(templateId, value) {
      const cur = attrState.value[templateId] || { matchType: "contains" };
      attrState.value[templateId] = { ...cur, value };
    }
    const searchQuery = computed({
      get: () => filters.value.query || "",
      set: (v) => updateFilter("query", v || "")
    });
    const selectedCategoryIds = computed({
      get: () => filters.value.categoryIds || [],
      set: (v) => updateFilter("categoryIds", v || [])
    });
    const selectedBrandIds = computed({
      get: () => filters.value.brandIds || [],
      set: (v) => updateFilter("brandIds", v || [])
    });
    const currentPage = computed({
      get: () => filters.value.page || 1,
      set: (p) => updateFilter("page", p || 1)
    });
    const itemsPerPage = computed({
      get: () => filters.value.limit || 20,
      set: (n) => updateFilters({ limit: n || 20, page: 1 })
    });
    const showFilters = ref(true);
    const hasSearched = ref(!!props.initialQuery);
    const debouncedSearchQuery = useDebounce(searchQuery, 500);
    const hasFilters = computed(
      () => selectedCategoryIds.value.length > 0 || selectedBrandIds.value.length > 0
    );
    const searchFilters = computed(() => ({
      name: debouncedSearchQuery.value?.trim() || void 0,
      categoryIds: selectedCategoryIds.value.length ? selectedCategoryIds.value : void 0,
      brandIds: selectedBrandIds.value.length ? selectedBrandIds.value : void 0,
      attributeFilters: attributeFilters.value,
      limit: itemsPerPage.value,
      offset: (currentPage.value - 1) * itemsPerPage.value,
      orderBy: "name",
      orderDir: "asc"
    }));
    const { data: categories, error: categoriesError } = useQuery({
      queryKey: qk.categories.list({ where: { level: 0 }, orderBy: { name: "asc" } }),
      queryFn: () => fetchers.categories.list({ where: { level: 0 }, orderBy: { name: "asc" } }),
      initialData: props.initialCategories
    });
    const { data: attributeTemplates } = useQuery({
      queryKey: qk.attributeTemplates.list({ take: 100 }),
      queryFn: () => fetchers.attributeTemplates.list({ take: 100 })
    });
    useQueryToastErrors(categoriesError);
    const { data: brands, error: brandsError } = useQuery({
      queryKey: qk.brands.list(),
      queryFn: () => fetchers.brands.list(),
      initialData: props.initialBrands
    });
    useQueryToastErrors(brandsError);
    const searchQuery_enabled = computed(() => !!debouncedSearchQuery.value?.trim());
    const searchQueryObject = useQuery({
      queryKey: computed(() => qk.search.parts(searchFilters.value)),
      queryFn: () => fetchers.search.parts(searchFilters.value),
      initialData: props.initialSearchResults,
      enabled: searchQuery_enabled,
      placeholderData: (previousData) => previousData
    });
    useQueryToastErrors(searchQueryObject.error);
    const searchResults = computed(() => searchQueryObject.data);
    const isSearching = computed(() => searchQueryObject.isFetching && searchQuery_enabled.value);
    const searchError = computed(
      () => searchQueryObject.error ? searchQueryObject.error?.message || "\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u043E\u0438\u0441\u043A\u0430" : null
    );
    const performSearch = () => {
      if (searchQuery.value.trim()) {
        hasSearched.value = true;
        searchQueryObject.refetch();
      }
    };
    const retrySearch = () => {
      searchQueryObject.refetch();
    };
    const onFiltersChange = () => {
      updateFilter("page", 1);
      if (hasSearched.value && searchQuery.value.trim()) ;
    };
    const resetFilters = () => {
      updateFilters({ categoryIds: [], brandIds: [], page: 1 });
    };
    watch(debouncedSearchQuery, (newValue, oldValue) => {
      if (newValue !== oldValue) {
        updateFilter("page", 1);
        if (String(newValue).trim()) {
          hasSearched.value = true;
        }
      }
    });
    watch(() => filters.value.categoryIds, () => updateFilter("page", 1), { deep: true });
    watch(() => filters.value.brandIds, () => updateFilter("page", 1), { deep: true });
    const __returned__ = { props, filters, updateFilter, updateFilters, attrState, attributeFilters, updateAttrRange, updateAttrMatchType, updateAttrValue, searchQuery, selectedCategoryIds, selectedBrandIds, currentPage, itemsPerPage, showFilters, hasSearched, debouncedSearchQuery, hasFilters, searchFilters, categories, categoriesError, attributeTemplates, brands, brandsError, searchQuery_enabled, searchQueryObject, searchResults, isSearching, searchError, performSearch, retrySearch, onFiltersChange, resetFilters, Card, InputText, MultiSelect, SecondaryButton, Spinner, Tag, InputNumber, Select, CatalogPagination };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "search-page" }, _attrs))} data-v-1bf673bf><div class="mb-8" data-v-1bf673bf>`);
  _push(ssrRenderComponent($setup["Card"], null, {
    content: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex gap-4" data-v-1bf673bf${_scopeId}><div class="flex-1" data-v-1bf673bf${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.searchQuery,
          "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439...",
          class: "w-full",
          onKeyup: $setup.performSearch
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          onClick: $setup.performSearch,
          disabled: !$setup.searchQuery.trim()
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(` \u041D\u0430\u0439\u0442\u0438 `);
            } else {
              return [
                createTextVNode(" \u041D\u0430\u0439\u0442\u0438 ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex gap-4" }, [
            createVNode("div", { class: "flex-1" }, [
              createVNode($setup["InputText"], {
                modelValue: $setup.searchQuery,
                "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                placeholder: "\u041F\u043E\u0438\u0441\u043A \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439...",
                class: "w-full",
                onKeyup: withKeys($setup.performSearch, ["enter"])
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode($setup["SecondaryButton"], {
              onClick: $setup.performSearch,
              disabled: !$setup.searchQuery.trim()
            }, {
              default: withCtx(() => [
                createTextVNode(" \u041D\u0430\u0439\u0442\u0438 ")
              ]),
              _: 1
            }, 8, ["disabled"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  if ($setup.showFilters) {
    _push(`<div class="mb-6" data-v-1bf673bf>`);
    _push(ssrRenderComponent($setup["Card"], null, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="grid grid-cols-1 md:grid-cols-3 gap-4" data-v-1bf673bf${_scopeId}><div data-v-1bf673bf${_scopeId}><label class="block text-sm font-medium mb-2" data-v-1bf673bf${_scopeId}>\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438</label>`);
          _push2(ssrRenderComponent($setup["MultiSelect"], {
            modelValue: $setup.selectedCategoryIds,
            "onUpdate:modelValue": ($event) => $setup.selectedCategoryIds = $event,
            options: $setup.categories || [],
            "option-label": "name",
            "option-value": "id",
            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
            onChange: $setup.onFiltersChange
          }, null, _parent2, _scopeId));
          _push2(`</div><div data-v-1bf673bf${_scopeId}><label class="block text-sm font-medium mb-2" data-v-1bf673bf${_scopeId}>\u0411\u0440\u0435\u043D\u0434\u044B</label>`);
          _push2(ssrRenderComponent($setup["MultiSelect"], {
            modelValue: $setup.selectedBrandIds,
            "onUpdate:modelValue": ($event) => $setup.selectedBrandIds = $event,
            options: $setup.brands || [],
            "option-label": "name",
            "option-value": "id",
            placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0431\u0440\u0435\u043D\u0434\u044B",
            onChange: $setup.onFiltersChange
          }, null, _parent2, _scopeId));
          _push2(`</div><div class="flex items-end" data-v-1bf673bf${_scopeId}>`);
          if ($setup.hasFilters) {
            _push2(ssrRenderComponent($setup["SecondaryButton"], {
              onClick: $setup.resetFilters,
              outlined: ""
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B `);
                } else {
                  return [
                    createTextVNode(" \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div><div class="md:col-span-3" data-v-1bf673bf${_scopeId}><label class="block text-sm font-medium mb-2" data-v-1bf673bf${_scopeId}>\u0425\u0430\u0440\u0430\u043A\u0442\u0435\u0440\u0438\u0441\u0442\u0438\u043A\u0438</label><div class="grid grid-cols-1 md:grid-cols-2 gap-4" data-v-1bf673bf${_scopeId}><!--[-->`);
          ssrRenderList($setup.attributeTemplates, (tpl) => {
            _push2(`<div class="p-3 rounded border border-surface-200 dark:border-surface-700" data-v-1bf673bf${_scopeId}><div class="text-sm font-medium mb-2" data-v-1bf673bf${_scopeId}>${ssrInterpolate(tpl.title || tpl.name)}</div>`);
            if (tpl.dataType === "NUMBER") {
              _push2(`<div class="flex items-center gap-2" data-v-1bf673bf${_scopeId}>`);
              _push2(ssrRenderComponent($setup["InputNumber"], {
                "model-value": $setup.attrState[tpl.id]?.min ?? null,
                "onUpdate:modelValue": (v) => $setup.updateAttrRange(tpl.id, "min", v),
                placeholder: "\u041C\u0438\u043D",
                class: "w-full"
              }, null, _parent2, _scopeId));
              _push2(`<span class="text-surface-400" data-v-1bf673bf${_scopeId}>\u2014</span>`);
              _push2(ssrRenderComponent($setup["InputNumber"], {
                "model-value": $setup.attrState[tpl.id]?.max ?? null,
                "onUpdate:modelValue": (v) => $setup.updateAttrRange(tpl.id, "max", v),
                placeholder: "\u041C\u0430\u043A\u0441",
                class: "w-full"
              }, null, _parent2, _scopeId));
              if (tpl.unit) {
                _push2(`<span class="text-xs text-surface-500" data-v-1bf673bf${_scopeId}>${ssrInterpolate(tpl.unit)}</span>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div>`);
            } else {
              _push2(`<div class="flex items-center gap-2" data-v-1bf673bf${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Select"], {
                "model-value": $setup.attrState[tpl.id]?.matchType || "contains",
                options: [
                  { label: "\u0421\u043E\u0434\u0435\u0440\u0436\u0438\u0442", value: "contains" },
                  { label: "\u0422\u043E\u0447\u043D\u043E", value: "exact" }
                ],
                class: "w-32",
                "onUpdate:modelValue": (mt) => $setup.updateAttrMatchType(tpl.id, mt)
              }, null, _parent2, _scopeId));
              _push2(ssrRenderComponent($setup["InputText"], {
                "model-value": $setup.attrState[tpl.id]?.value ?? "",
                "onUpdate:modelValue": (v) => $setup.updateAttrValue(tpl.id, v),
                placeholder: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435",
                class: "flex-1"
              }, null, _parent2, _scopeId));
              _push2(`</div>`);
            }
            _push2(`</div>`);
          });
          _push2(`<!--]--></div></div></div>`);
        } else {
          return [
            createVNode("div", { class: "grid grid-cols-1 md:grid-cols-3 gap-4" }, [
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium mb-2" }, "\u041A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438"),
                createVNode($setup["MultiSelect"], {
                  modelValue: $setup.selectedCategoryIds,
                  "onUpdate:modelValue": ($event) => $setup.selectedCategoryIds = $event,
                  options: $setup.categories || [],
                  "option-label": "name",
                  "option-value": "id",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0438",
                  onChange: $setup.onFiltersChange
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
              ]),
              createVNode("div", null, [
                createVNode("label", { class: "block text-sm font-medium mb-2" }, "\u0411\u0440\u0435\u043D\u0434\u044B"),
                createVNode($setup["MultiSelect"], {
                  modelValue: $setup.selectedBrandIds,
                  "onUpdate:modelValue": ($event) => $setup.selectedBrandIds = $event,
                  options: $setup.brands || [],
                  "option-label": "name",
                  "option-value": "id",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0431\u0440\u0435\u043D\u0434\u044B",
                  onChange: $setup.onFiltersChange
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
              ]),
              createVNode("div", { class: "flex items-end" }, [
                $setup.hasFilters ? (openBlock(), createBlock($setup["SecondaryButton"], {
                  key: 0,
                  onClick: $setup.resetFilters,
                  outlined: ""
                }, {
                  default: withCtx(() => [
                    createTextVNode(" \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
                  ]),
                  _: 1
                })) : createCommentVNode("", true)
              ]),
              createVNode("div", { class: "md:col-span-3" }, [
                createVNode("label", { class: "block text-sm font-medium mb-2" }, "\u0425\u0430\u0440\u0430\u043A\u0442\u0435\u0440\u0438\u0441\u0442\u0438\u043A\u0438"),
                createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-4" }, [
                  (openBlock(true), createBlock(Fragment, null, renderList($setup.attributeTemplates, (tpl) => {
                    return openBlock(), createBlock("div", {
                      key: tpl.id,
                      class: "p-3 rounded border border-surface-200 dark:border-surface-700"
                    }, [
                      createVNode("div", { class: "text-sm font-medium mb-2" }, toDisplayString(tpl.title || tpl.name), 1),
                      tpl.dataType === "NUMBER" ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "flex items-center gap-2"
                      }, [
                        createVNode($setup["InputNumber"], {
                          "model-value": $setup.attrState[tpl.id]?.min ?? null,
                          "onUpdate:modelValue": (v) => $setup.updateAttrRange(tpl.id, "min", v),
                          placeholder: "\u041C\u0438\u043D",
                          class: "w-full"
                        }, null, 8, ["model-value", "onUpdate:modelValue"]),
                        createVNode("span", { class: "text-surface-400" }, "\u2014"),
                        createVNode($setup["InputNumber"], {
                          "model-value": $setup.attrState[tpl.id]?.max ?? null,
                          "onUpdate:modelValue": (v) => $setup.updateAttrRange(tpl.id, "max", v),
                          placeholder: "\u041C\u0430\u043A\u0441",
                          class: "w-full"
                        }, null, 8, ["model-value", "onUpdate:modelValue"]),
                        tpl.unit ? (openBlock(), createBlock("span", {
                          key: 0,
                          class: "text-xs text-surface-500"
                        }, toDisplayString(tpl.unit), 1)) : createCommentVNode("", true)
                      ])) : (openBlock(), createBlock("div", {
                        key: 1,
                        class: "flex items-center gap-2"
                      }, [
                        createVNode($setup["Select"], {
                          "model-value": $setup.attrState[tpl.id]?.matchType || "contains",
                          options: [
                            { label: "\u0421\u043E\u0434\u0435\u0440\u0436\u0438\u0442", value: "contains" },
                            { label: "\u0422\u043E\u0447\u043D\u043E", value: "exact" }
                          ],
                          class: "w-32",
                          "onUpdate:modelValue": (mt) => $setup.updateAttrMatchType(tpl.id, mt)
                        }, null, 8, ["model-value", "onUpdate:modelValue"]),
                        createVNode($setup["InputText"], {
                          "model-value": $setup.attrState[tpl.id]?.value ?? "",
                          "onUpdate:modelValue": (v) => $setup.updateAttrValue(tpl.id, v),
                          placeholder: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435",
                          class: "flex-1"
                        }, null, 8, ["model-value", "onUpdate:modelValue"])
                      ]))
                    ]);
                  }), 128))
                ])
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`<div class="search-results" data-v-1bf673bf>`);
  if ($setup.isSearching) {
    _push(`<div class="flex justify-center py-12" data-v-1bf673bf>`);
    _push(ssrRenderComponent($setup["Spinner"], {
      size: "lg",
      variant: "primary",
      label: "\u041F\u043E\u0438\u0441\u043A...",
      centered: true
    }, null, _parent));
    _push(`</div>`);
  } else if ($setup.searchError) {
    _push(`<div class="text-center py-12" data-v-1bf673bf><div class="text-red-500 mb-4" data-v-1bf673bf>${ssrInterpolate($setup.searchError)}</div>`);
    _push(ssrRenderComponent($setup["SecondaryButton"], { onClick: $setup.retrySearch }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`\u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C`);
        } else {
          return [
            createTextVNode("\u041F\u043E\u0432\u0442\u043E\u0440\u0438\u0442\u044C")
          ];
        }
      }),
      _: 1
    }, _parent));
    _push(`</div>`);
  } else if ($setup.hasSearched && !$setup.searchResults?.items?.length) {
    _push(`<div class="text-center py-12" data-v-1bf673bf><div class="text-surface-500 dark:text-surface-400 mb-4" data-v-1bf673bf>${ssrInterpolate($setup.hasFilters ? "\u041D\u0438\u0447\u0435\u0433\u043E \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u043E \u043F\u043E \u0437\u0430\u0434\u0430\u043D\u043D\u044B\u043C \u043A\u0440\u0438\u0442\u0435\u0440\u0438\u044F\u043C" : "\u041D\u0438\u0447\u0435\u0433\u043E \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u043E")}</div>`);
    if ($setup.hasFilters) {
      _push(ssrRenderComponent($setup["SecondaryButton"], { onClick: $setup.resetFilters }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B `);
          } else {
            return [
              createTextVNode(" \u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  } else if ($setup.searchResults?.items?.length) {
    _push(`<div data-v-1bf673bf><div class="mb-4" data-v-1bf673bf><p class="text-surface-600 dark:text-surface-400" data-v-1bf673bf> \u041D\u0430\u0439\u0434\u0435\u043D\u043E ${ssrInterpolate($setup.searchResults.total || $setup.searchResults.items.length)} \u0440\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u043E\u0432 </p></div><div class="grid gap-4" data-v-1bf673bf><!--[-->`);
    ssrRenderList($setup.searchResults.items, (item) => {
      _push(ssrRenderComponent($setup["Card"], {
        key: item.id,
        class: "hover:shadow-md transition-shadow"
      }, {
        content: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex items-start gap-4" data-v-1bf673bf${_scopeId}>`);
            if (item.image) {
              _push2(`<div class="w-16 h-16 bg-surface-100 rounded-lg overflow-hidden flex-shrink-0" data-v-1bf673bf${_scopeId}><img${ssrRenderAttr("src", item.image.url)}${ssrRenderAttr("alt", item.name)} class="w-full h-full object-cover" data-v-1bf673bf${_scopeId}></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`<div class="flex-1" data-v-1bf673bf${_scopeId}><h3 class="font-semibold text-surface-900 dark:text-surface-0 mb-1" data-v-1bf673bf${_scopeId}>${ssrInterpolate(item.name)}</h3>`);
            if (item.description) {
              _push2(`<p class="text-surface-600 dark:text-surface-400 text-sm mb-2" data-v-1bf673bf${_scopeId}>${ssrInterpolate(item.description)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`<div class="flex flex-wrap gap-2" data-v-1bf673bf${_scopeId}>`);
            if (item.partCategory) {
              _push2(ssrRenderComponent($setup["Tag"], {
                severity: "info",
                value: item.partCategory.name
              }, null, _parent2, _scopeId));
            } else {
              _push2(`<!---->`);
            }
            if (item.brand) {
              _push2(ssrRenderComponent($setup["Tag"], {
                severity: "secondary",
                value: item.brand.name
              }, null, _parent2, _scopeId));
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "flex items-start gap-4" }, [
                item.image ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "w-16 h-16 bg-surface-100 rounded-lg overflow-hidden flex-shrink-0"
                }, [
                  createVNode("img", {
                    src: item.image.url,
                    alt: item.name,
                    class: "w-full h-full object-cover"
                  }, null, 8, ["src", "alt"])
                ])) : createCommentVNode("", true),
                createVNode("div", { class: "flex-1" }, [
                  createVNode("h3", { class: "font-semibold text-surface-900 dark:text-surface-0 mb-1" }, toDisplayString(item.name), 1),
                  item.description ? (openBlock(), createBlock("p", {
                    key: 0,
                    class: "text-surface-600 dark:text-surface-400 text-sm mb-2"
                  }, toDisplayString(item.description), 1)) : createCommentVNode("", true),
                  createVNode("div", { class: "flex flex-wrap gap-2" }, [
                    item.partCategory ? (openBlock(), createBlock($setup["Tag"], {
                      key: 0,
                      severity: "info",
                      value: item.partCategory.name
                    }, null, 8, ["value"])) : createCommentVNode("", true),
                    item.brand ? (openBlock(), createBlock($setup["Tag"], {
                      key: 1,
                      severity: "secondary",
                      value: item.brand.name
                    }, null, 8, ["value"])) : createCommentVNode("", true)
                  ])
                ])
              ])
            ];
          }
        }),
        _: 2
      }, _parent));
    });
    _push(`<!--]--></div><div class="mt-6" data-v-1bf673bf>`);
    _push(ssrRenderComponent($setup["CatalogPagination"], {
      "current-page": $setup.currentPage,
      "items-per-page": $setup.itemsPerPage,
      "total-items": $setup.searchResults.total || 0,
      onPageChange: (p) => $setup.currentPage = p,
      onItemsPerPageChange: (n) => $setup.itemsPerPage = n
    }, null, _parent));
    _push(`</div></div>`);
  } else if (!$setup.hasSearched) {
    _push(`<div class="text-center py-12" data-v-1bf673bf><div class="text-surface-500 dark:text-surface-400" data-v-1bf673bf> \u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u0430\u043F\u0440\u043E\u0441 \u0434\u043B\u044F \u043F\u043E\u0438\u0441\u043A\u0430 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 </div></div>`);
  } else {
    _push(`<!---->`);
  }
  _push(`</div></div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/search/SearchPageComponent.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const SearchPageComponent = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1], ["__scopeId", "data-v-1bf673bf"]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "SearchPageBoundary",
  props: {
    initialQuery: {},
    initialSearchResults: {},
    initialBrands: {},
    initialCategories: {},
    initialAttributeTemplates: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const __returned__ = { SearchPageComponent };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["SearchPageComponent"], mergeProps({
    "initial-query": $props.initialQuery,
    "initial-search-results": $props.initialSearchResults,
    "initial-brands": $props.initialBrands,
    "initial-categories": $props.initialCategories,
    "initial-attribute-templates": $props.initialAttributeTemplates
  }, _attrs), null, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/search/SearchPageBoundary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const SearchPageBoundary = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Search = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Search;
  const query = Astro2.url.searchParams.get("q") || "";
  const filters = {
    name: query,
    limit: 50,
    offset: 0,
    orderBy: "name",
    orderDir: "asc"
  };
  let initialSearchResults, initialBrands, initialCategories, initialAttributeTemplates;
  try {
    const promises = [
      trpc.crud.brand.findMany.query(),
      trpc.crud.partCategory.findMany.query({ where: { level: 0 }, orderBy: { name: "asc" } }),
      trpc.crud.attributeTemplate.findMany.query({ take: 100 })
    ];
    if (query) {
      promises.unshift(trpc.search.searchParts.query(filters));
    }
    const results = await Promise.all(promises);
    if (query) {
      [initialSearchResults, initialBrands, initialCategories, initialAttributeTemplates] = results;
    } else {
      initialSearchResults = null;
      [initialBrands, initialCategories, initialAttributeTemplates] = results;
    }
  } catch (error) {
    console.error("Error loading search initial data:", error);
    initialSearchResults = null;
    initialBrands = [];
    initialCategories = [];
    initialAttributeTemplates = [];
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": query ? `\u041F\u043E\u0438\u0441\u043A: ${query}` : "\u041F\u043E\u0438\u0441\u043A \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439", "data-astro-cid-ipsxrsrh": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8" data-astro-cid-ipsxrsrh> ${renderComponent($$result2, "SearchPageBoundary", SearchPageBoundary, { "client:load": true, "initialQuery": query, "initialSearchResults": initialSearchResults, "initialBrands": initialBrands, "initialCategories": initialCategories, "initialAttributeTemplates": initialAttributeTemplates, "client:component-hydration": "load", "client:component-path": "@/components/search/SearchPageBoundary.vue", "client:component-export": "default", "data-astro-cid-ipsxrsrh": true })} </main> ` })} `;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/search.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/search.astro";
const $$url = "/search";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Search,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

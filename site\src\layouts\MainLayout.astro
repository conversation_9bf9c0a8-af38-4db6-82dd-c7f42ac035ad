---
import '../styles/global.css';

interface Props {
  title: string;
  description?: string;
}

const { title, description = 'PartTec3 - Каталог взаимозаменяемых запчастей' } = Astro.props;
---

<html lang="ru">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content={description}>
    <title>{title} | PartTec3</title>
    <meta name="view-transition" content="same-origin" />
  </head>
  <body class="min-h-screen bg-background font-sans antialiased">
    <header class="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div class="container flex h-14 max-w-screen-2xl items-center">
        <div class="mr-4 hidden md:flex">
          <a href="/" class="mr-6 flex items-center space-x-2">
            <span class="hidden font-bold sm:inline-block">PartTec3</span>
          </a>
          <nav class="flex items-center gap-4 text-sm lg:gap-6">
            <a href="/" class="transition-colors hover:text-foreground/80 text-foreground/60">
              Главная
            </a>
            <a href="/catalog" class="transition-colors hover:text-foreground/80 text-foreground/60">
              Каталог
            </a>
            <a href="/categories" class="transition-colors hover:text-foreground/80 text-foreground/60">
              Категории
            </a>
            <a href="/brands" class="transition-colors hover:text-foreground/80 text-foreground/60">
              Бренды
            </a>
            <a href="/search" class="transition-colors hover:text-foreground/80 text-foreground/60">
              Поиск
            </a>
          </nav>
        </div>
      </div>
    </header>

    <main class="flex-1">
      <slot />
    </main>

    <footer class="border-t py-6 md:py-0">
      <div class="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
        <div class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <p class="text-center text-sm leading-loose text-muted-foreground md:text-left">
            © 2024 PartTec3. Каталог взаимозаменяемых запчастей.
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
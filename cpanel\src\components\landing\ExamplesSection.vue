<template>
  <div class="space-y-16">
    <Motion
      v-for="(example, index) in examplesData"
      :key="index"
      :initial="{ opacity: 0, y: 60 }"
      :whileInView="{ opacity: 1, y: 0 }"
      :transition="{ duration: 0.8, delay: index * 0.2 }"
      :viewport="{ once: true }"
    >
      <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl overflow-hidden">
        <div class="bg-zinc-800 p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 bg-zinc-700 rounded-xl flex items-center justify-center">
                <Settings class="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 class="text-3xl font-bold text-white">{{ example.title }}</h3>
                <p class="text-gray-100">{{ example.description }}</p>
              </div>
            </div>
            <div class="bg-green-500 text-white border-0 px-4 py-2 text-lg rounded">
              Экономия {{ example.savings }}
            </div>
          </div>
        </div>

        <div class="grid lg:grid-cols-2 gap-0">
          <!-- Original Part -->
          <div class="p-6 bg-zinc-900 backdrop-blur-xl border-r border-zinc-700">
            <h4 class="font-semibold text-white mb-4 flex items-center gap-3">
              <div class="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                <Target class="w-4 h-4 text-white" />
              </div>
              Оригинальная деталь
            </h4>

            <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-6 mb-6">
              <div class="font-mono text-2xl font-bold text-white mb-4">{{ example.original }}</div>

              <div class="grid grid-cols-2 gap-4 text-sm">
                <div
                  v-for="([key, value], i) in Object.entries(example.originalSpecs)"
                  :key="i"
                  class="bg-zinc-900 rounded-lg p-3"
                >
                  <div class="text-gray-300 capitalize text-sm mb-1">{{ key }}:</div>
                  <div class="font-medium text-white">{{ value }}</div>
                </div>
              </div>
            </div>

            <div v-if="example.technicalNote" class="bg-zinc-800 border border-zinc-700 rounded-xl p-4 backdrop-blur-xl">
              <div class="flex items-start gap-3">
                <Info class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                <div class="text-sm">
                  <div class="font-medium text-blue-400 mb-2">Техническое примечание:</div>
                  <div class="text-gray-100">{{ example.technicalNote }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Compatible Parts -->
          <div class="p-6 bg-zinc-900 backdrop-blur-xl">
            <h4 class="font-semibold text-white mb-4 flex items-center gap-3">
              <div class="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                <CheckCircle class="w-4 h-4 text-white" />
              </div>
              Совместимые аналоги
            </h4>

            <div class="space-y-4">
              <Motion
                v-for="(part, i) in example.compatible"
                :key="i"
                :initial="{ opacity: 0, x: 20 }"
                :whileInView="{ opacity: 1, x: 0 }"
                :transition="{ duration: 0.5, delay: i * 0.1 }"
                :viewport="{ once: true }"
              >
                <div class="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4 hover:border-zinc-600 transition-all duration-300">
                  <div class="flex items-center justify-between mb-3">
                    <div class="font-mono font-bold text-white">{{ part.part }}</div>
                    <div class="flex items-center gap-2">
                      <div class="bg-green-500/20 text-green-400 border border-green-500/30 px-2 py-1 rounded text-xs">
                        {{ part.match }} совместимость
                      </div>
                      <div class="bg-blue-500 text-white border-0 px-2 py-1 rounded text-xs">{{ part.price }}</div>
                    </div>
                  </div>

                  <div class="text-sm text-gray-100 mb-3">{{ part.specs }}</div>

                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-300">Наличие:</span>
                    <span
                      :class="`font-medium ${
                        part.availability === 'В наличии'
                          ? 'text-green-400'
                          : part.availability.includes('дня')
                            ? 'text-yellow-400'
                            : 'text-gray-400'
                      }`"
                    >
                      {{ part.availability }}
                    </span>
                  </div>
                </div>
              </Motion>
            </div>
          </div>
        </div>
      </div>
    </Motion>
  </div>
</template>

<script setup lang="ts">
import { Motion } from 'motion-v'
import { Settings, Target, CheckCircle, Info } from 'lucide-vue-next'

const examplesData = [
  {
    title: "Сальники радиальные",
    original: "Corteco 12345-ABC",
    originalSpecs: {
      dimensions: "25×47×7mm",
      material: "NBR 70 Shore A",
      temperature: "-40°C до +120°C",
      speed: "до 15 м/с",
      standard: "DIN 3760",
    },
    compatible: [
      {
        part: "SKF 789-XYZ",
        match: "98%",
        price: "-35%",
        availability: "В наличии",
        specs: "25×47×7mm, NBR 70 Shore A, -40°C/+120°C",
      },
      {
        part: "Febi 456-DEF",
        match: "96%",
        price: "-28%",
        availability: "2-3 дня",
        specs: "25×47×7mm, NBR 72 Shore A, -35°C/+125°C",
      },
      {
        part: "NOK 321-GHI",
        match: "99%",
        price: "-42%",
        availability: "Под заказ",
        specs: "25×47×7mm, NBR 70 Shore A, -40°C/+120°C",
      },
    ],
    savings: "до 42%",
    description:
      "Радиальные сальники для валов с идентичными размерами и материалами. Все аналоги соответствуют стандарту DIN 3760 и имеют подтвержденную взаимозаменяемость.",
    technicalNote:
      "Различия в твердости материала (±2 Shore A) не влияют на эксплуатационные характеристики при стандартных условиях работы.",
  },
  {
    title: "Редукторы планетарные",
    original: "John Deere RE12345",
    originalSpecs: {
      ratio: "1:4.5",
      torque: "850 Нм",
      input: "1800 об/мин",
      efficiency: "96%",
      mounting: "Фланец SAE B",
    },
    compatible: [
      {
        part: "Komatsu 708-1W-00151",
        match: "95%",
        price: "-45%",
        availability: "В наличии",
        specs: "1:4.5, 850 Нм, 1800 об/мин, 95% КПД",
      },
      {
        part: "Caterpillar 123-4567",
        match: "93%",
        price: "-38%",
        availability: "1-2 дня",
        specs: "1:4.6, 820 Нм, 1800 об/мин, 94% КПД",
      },
    ],
    savings: "до 45%",
    description:
      "Планетарные редукторы для мобильной техники с совместимыми характеристиками и креплениями. Физически один агрегат, производимый под разными брендами.",
    technicalNote:
      "Незначительные отличия в передаточном числе (±0.1) и крутящем моменте (±30 Нм) находятся в пределах допустимых отклонений.",
  },
]
</script>
<template>
  <div class="relative flex h-[500px] w-full items-center justify-center overflow-hidden p-10" ref="containerRef">
    <!-- Debug frame for visibility and stacking -->
    <div class="absolute inset-0 z-10 pointer-events-none" />
    <div class="relative z-30 flex size-full max-w-3xl flex-row items-stretch justify-between gap-10">
      <div class="flex flex-col justify-center gap-2">
        <div ref="div1Ref">
          <Circle className="bg-blue-100 border-blue-300">
            <CatalogIcon />
          </Circle>
        </div>
        <div ref="div2Ref">
          <Circle>
            <!-- Google Docs -->
            <svg width="47" height="65" viewBox="0 0 47 65" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <path id="gd-path-1"
                  d="M29.375,0 L4.40625,0 C1.9828125,0 0,1.99431818 0,4.43181818 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,17.7272727 L29.375,0 Z" />
                <linearGradient id="gd-linear-5" x1="50.0053945%" y1="8.58610612%" x2="50.0053945%" y2="100.013939%">
                  <stop stop-color="#1A237E" stop-opacity="0.2" offset="0%" />
                  <stop stop-color="#1A237E" stop-opacity="0.02" offset="100%" />
                </linearGradient>
                <radialGradient id="gd-radial-16" cx="3.16804688%" cy="2.71744318%" fx="3.16804688%" fy="2.71744318%"
                  r="161.248516%"
                  gradientTransform="translate(0.031680,0.027174),scale(1.000000,0.723077),translate(-0.031680,-0.027174)">
                  <stop stop-color="#FFFFFF" stop-opacity="0.1" offset="0%" />
                  <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%" />
                </radialGradient>
              </defs>
              <g fill="none" fill-rule="evenodd">
                <mask id="gd-mask-2" fill="white">
                  <use xlink:href="#gd-path-1" />
                </mask>
                <path fill="#4285F4" fill-rule="nonzero" mask="url(#gd-mask-2)"
                  d="M29.375,0 L4.40625,0 C1.9828125,0 0,1.99431818 0,4.43181818 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,17.7272727 L36.71875,10.3409091 L29.375,0 Z" />
                <mask id="gd-mask-4" fill="white">
                  <use xlink:href="#gd-path-1" />
                </mask>
                <polygon fill="url(#gd-linear-5)" fill-rule="nonzero" mask="url(#gd-mask-4)"
                  points="30.6638281 16.4309659 47 32.8582386 47 17.7272727" />
                <mask id="gd-mask-7" fill="white">
                  <use xlink:href="#gd-path-1" />
                </mask>
                <path fill="#F1F1F1" fill-rule="nonzero" mask="url(#gd-mask-7)"
                  d="M11.75,47.2727273 L35.25,47.2727273 L35.25,44.3181818 L11.75,44.3181818 L11.75,47.2727273 Z M11.75,53.1818182 L29.375,53.1818182 L29.375,50.2272727 L11.75,50.2272727 L11.75,53.1818182 Z M11.75,32.5 L11.75,35.4545455 L35.25,35.4545455 L35.25,32.5 L11.75,32.5 Z M11.75,41.3636364 L35.25,41.3636364 L35.25,38.4090909 L11.75,38.4090909 L11.75,41.3636364 Z" />
                <mask id="gd-mask-9" fill="white">
                  <use xlink:href="#gd-path-1" />
                </mask>
                <g mask="url(#gd-mask-9)">
                  <g transform="translate(26.437500, -2.954545)">
                    <path fill="#A1C2FA" fill-rule="nonzero"
                      d="M2.9375,2.95454545 L2.9375,16.25 C2.9375,18.6985795 4.90929688,20.6818182 7.34375,20.6818182 L20.5625,20.6818182 L2.9375,2.95454545 Z" />
                  </g>
                </g>
                <mask id="gd-mask-13" fill="white">
                  <use xlink:href="#gd-path-1" />
                </mask>
                <path fill-opacity="0.2" fill="#1A237E" fill-rule="nonzero" mask="url(#gd-mask-13)"
                  d="M42.59375,64.6306818 L4.40625,64.6306818 C1.9828125,64.6306818 0,62.6363636 0,60.1988636 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,60.1988636 C47,62.6363636 45.0171875,64.6306818 42.59375,64.6306818 Z" />
                <path
                  d="M29.375,0 L4.40625,0 C1.9828125,0 0,1.99431818 0,4.43181818 L0,60.5681818 C0,63.0056818 1.9828125,65 4.40625,65 L42.59375,65 C45.0171875,65 47,63.0056818 47,60.5681818 L47,17.7272727 L29.375,0 Z"
                  fill="url(#gd-radial-16)" fill-rule="nonzero" />
              </g>
            </svg>
          </Circle>
        </div>
        <div ref="div3Ref">
          <Circle>
            <!-- WhatsApp -->
            <svg width="100" height="100" viewBox="0 0 175.216 175.552" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="wa-b" x1="85.915" x2="86.535" y1="32.567" y2="137.092"
                  gradientUnits="userSpaceOnUse">
                  <stop offset="0" stop-color="#57d163" />
                  <stop offset="1" stop-color="#23b33a" />
                </linearGradient>
                <filter id="wa-a" width="1.115" height="1.114" x="-.057" y="-.057" color-interpolation-filters="sRGB">
                  <feGaussianBlur stdDeviation="3.531" />
                </filter>
              </defs>
              <path
                d="m54.532 138.45 2.235 1.324c9.387 5.571 20.15 8.518 31.126 8.523h.023c33.707 0 61.139-27.426 61.153-61.135.006-16.335-6.349-31.696-17.895-43.251A60.75 60.75 0 0 0 87.94 25.983c-33.733 0-61.166 27.423-61.178 61.13a60.98 60.98 0 0 0 9.349 32.535l1.455 2.312-6.179 22.558zm-40.811 23.544L24.16 123.88c-6.438-11.154-9.825-23.808-9.821-36.772.017-40.556 33.021-73.55 73.578-73.55 19.681.01 38.154 7.669 52.047 21.572s21.537 32.383 21.53 52.037c-.018 40.553-33.027 73.553-73.578 73.553h-.032c-12.313-.005-24.412-3.094-35.159-8.954zm0 0"
                fill="#b3b3b3" filter="url(#wa-a)" />
              <path
                d="m12.966 161.238 10.439-38.114a73.42 73.42 0 0 1-9.821-36.772c.017-40.556 33.021-73.55 73.578-73.55 19.681.01 38.154 7.669 52.047 21.572s21.537 32.383 21.53 52.037c-.018 40.553-33.027 73.553-73.578 73.553h-.032c-12.313-.005-24.412-3.094-35.159-8.954z"
                fill="#ffffff" />
              <path
                d="M87.184 25.227c-33.733 0-61.166 27.423-61.178 61.13a60.98 60.98 0 0 0 9.349 32.535l1.455 2.312-6.179 22.559 23.146-6.069 2.235 1.324c9.387 5.571 20.15 8.518 31.126 8.524h.023c33.707 0 61.14-27.426 61.153-61.135a60.75 60.75 0 0 0-17.895-43.251 60.75 60.75 0 0 0-43.235-17.929z"
                fill="url(#wa-b)" />
              <path
                d="M68.772 55.603c-1.378-3.061-2.828-3.123-4.137-3.176l-3.524-.043c-1.226 0-3.218.46-4.902 2.3s-6.435 6.287-6.435 15.332 6.588 17.785 7.506 19.013 12.718 20.381 31.405 27.75c15.529 6.124 18.689 4.906 22.061 4.6s10.877-4.447 12.408-8.74 1.532-7.971 1.073-8.74-1.685-1.226-3.525-2.146-10.877-5.367-12.562-5.981-2.91-.919-4.137.921-4.746 5.979-5.819 7.206-2.144 1.381-3.984.462-7.76-2.861-14.784-9.124c-5.465-4.873-9.154-10.891-10.228-12.73s-.114-2.835.808-3.751c.825-.824 1.838-2.147 2.759-3.22s1.224-1.84 1.836-3.065.307-2.301-.153-3.22-4.032-10.011-5.666-13.647"
                fill="#ffffff" fill-rule="evenodd" />
            </svg>
          </Circle>
        </div>
        <div ref="div4Ref">
          <Circle>
            <!-- Messenger -->
            <svg width="100" height="100" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
              <radialGradient id="msg-grad" cx="11.087" cy="7.022" r="47.612" gradientTransform="matrix(1 0 0 -1 0 50)"
                gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#1292ff" />
                <stop offset=".079" stop-color="#2982ff" />
                <stop offset=".23" stop-color="#4e69ff" />
                <stop offset=".351" stop-color="#6559ff" />
                <stop offset=".428" stop-color="#6d53ff" />
                <stop offset=".754" stop-color="#df47aa" />
                <stop offset=".946" stop-color="#ff6257" />
              </radialGradient>
              <path fill="url(#msg-grad)"
                d="M44,23.5C44,34.27,35.05,43,24,43c-1.651,0-3.25-0.194-4.784-0.564c-0.465-0.112-0.951-0.069-1.379,0.145L13.46,44.77C12.33,45.335,11,44.513,11,43.249v-4.025c0-0.575-0.257-1.111-0.681-1.499C6.425,34.165,4,29.11,4,23.5C4,12.73,12.95,4,24,4S44,12.73,44,23.5z" />
              <path fill="#ffffff"
                d="M34.394,18.501l-5.7,4.22c-0.61,0.46-1.44,0.46-2.04,0.01L22.68,19.74c-1.68-1.25-4.06-0.82-5.19,0.94l-1.21,1.89l-4.11,6.68c-0.6,0.94,0.55,2.01,1.44,1.34l5.7-4.22c0.61-0.46,1.44-0.46,2.04-0.01l3.974,2.991c1.68,1.25,4.06,0.82,5.19-0.94l1.21-1.89l4.11-6.68C36.434,18.901,35.284,17.831,34.394,18.501z" />
            </svg>
          </Circle>
        </div>
        <div ref="div5Ref">
          <Circle>
            <!-- Notion -->
            <svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6.017 4.313l55.333 -4.087c6.797 -0.583 8.543 -0.19 12.817 2.917l17.663 12.443c2.913 2.14 3.883 2.723 3.883 5.053v68.243c0 4.277 -1.553 6.807 -6.99 7.193L24.467 99.967c-4.08 0.193 -6.023 -0.39 -8.16 -3.113L3.3 79.94c-2.333 -3.113 -3.3 -5.443 -3.3 -8.167V11.113c0 -3.497 1.553 -6.413 6.017 -6.8z"
                fill="#ffffff" />
              <path
                d="M61.35 0.227l-55.333 4.087C1.553 4.7 0 7.617 0 11.113v60.66c0 2.723 0.967 5.053 3.3 8.167l13.007 16.913c2.137 2.723 4.08 3.307 8.16 3.113l64.257 -3.89c5.433 -0.387 6.99 -2.917 6.99 -7.193V20.64c0 -2.21 -0.873 -2.847 -3.443 -4.733L74.167 3.143c-4.273 -3.107 -6.02 -3.5 -12.817 -2.917zM25.92 19.523c-5.247 0.353 -6.437 0.433 -9.417 -1.99L8.927 11.507c-0.77 -0.78 -0.383 -1.753 1.557 -1.947l53.193 -3.887c4.467 -0.39 6.793 1.167 8.54 2.527l9.123 6.61c0.39 0.197 1.36 1.36 0.193 1.36l-54.933 3.307 -0.68 0.047zM19.803 88.3V30.367c0 -2.53 0.777 -3.697 3.103 -3.893L86 22.78c2.14 -0.193 3.107 1.167 3.107 3.693v57.547c0 2.53 -0.39 4.67 -3.883 4.863l-60.377 3.5c-3.493 0.193 -5.043 -0.97 -5.043 -4.083zm59.6 -54.827c0.387 1.75 0 3.5 -1.75 3.7l-2.91 0.577v42.773c-2.527 1.36 -4.853 2.137 -6.797 2.137 -3.107 0 -3.883 -0.973 -6.21 -3.887l-19.03 -29.94v28.967l6.02 1.363s0 3.5 -4.857 3.5l-13.39 0.777c-0.39 -0.78 0 -2.723 1.357 -3.11l3.497 -0.97v-38.3L30.48 40.667c-0.39 -1.75 0.58 -4.277 3.3 -4.473l14.367 -0.967 19.8 30.327v-26.83l-5.047 -0.58c-0.39 -2.143 1.163 -3.7 3.103 -3.89l13.4 -0.78z"
                fill="#000000" fill-rule="evenodd" clip-rule="evenodd" />
            </svg>
          </Circle>
        </div>
      </div>

      <div class="flex flex-col justify-center">
        <div ref="div6Ref">
          <Circle className="size-16">
            <!-- OpenAI -->
            <svg width="100" height="100" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" />
            </svg>
          </Circle>
        </div>
      </div>

      <div class="flex flex-col justify-center">
        <div ref="div7Ref">
          <Circle>
            <!-- User -->
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#000000" stroke-width="2"
              xmlns="http://www.w3.org/2000/svg">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
          </Circle>
        </div>
      </div>
    </div>

    <!-- Beams -->
    <AnimatedBeam :containerRef="containerRef" :fromRef="div1Ref" :toRef="div6Ref" :pathColor="'#ff00ff'"
      :pathOpacity="1" :pathWidth="4" :curvature="60" />
    <AnimatedBeam :containerRef="containerRef" :fromRef="div2Ref" :toRef="div6Ref" :pathColor="'#ff00ff'"
      :pathOpacity="1" :pathWidth="4" :curvature="60" />
    <AnimatedBeam :containerRef="containerRef" :fromRef="div3Ref" :toRef="div6Ref" :pathColor="'#ff00ff'"
      :pathOpacity="1" :pathWidth="4" :curvature="60" />
    <AnimatedBeam :containerRef="containerRef" :fromRef="div4Ref" :toRef="div6Ref" :pathColor="'#ff00ff'"
      :pathOpacity="1" :pathWidth="4" :curvature="60" />
    <AnimatedBeam :containerRef="containerRef" :fromRef="div5Ref" :toRef="div6Ref" :pathColor="'#ff00ff'"
      :pathOpacity="1" :pathWidth="4" :curvature="60" />
    <AnimatedBeam :containerRef="containerRef" :fromRef="div6Ref" :toRef="div7Ref" :pathColor="'#ff00ff'"
      :pathOpacity="1" :pathWidth="4" :curvature="60" />
  </div>

  <div class="absolute bottom-4 left-4 right-4">
    <div
      class="bg-zinc-900/80 backdrop-blur-xl border border-zinc-800 rounded-xl p-4 md:p-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
      <div>
        <h4 class="text-white font-semibold mb-1 md:mb-2">Глобальная сеть</h4>
        <p class="text-gray-100 text-xs md:text-sm">Производители, интеграторы и заказчики объединены единой моделью
          данных и связями совместимости.</p>
      </div>
      <div>
        <h4 class="text-white font-semibold mb-1 md:mb-2">Стандартизированные атрибуты</h4>
        <p class="text-gray-100 text-xs md:text-sm">Единицы измерения, синонимы и классификаторы обеспечивают точный
          подбор аналогов.</p>
      </div>
      <div>
        <h4 class="text-white font-semibold mb-1 md:mb-2">Прозрачные интеграции</h4>
        <p class="text-gray-100 text-xs md:text-sm">API для ERP/CRM и складских систем. Отслеживание происхождения
          данных и аудит.</p>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import { ref } from 'vue'
import AnimatedBeam from './AnimatedBeam.vue'
import Circle from './Circle.vue'

const containerRef = ref<HTMLDivElement | null>(null)
const div1Ref = ref<HTMLDivElement | null>(null)
const div2Ref = ref<HTMLDivElement | null>(null)
const div3Ref = ref<HTMLDivElement | null>(null)
const div4Ref = ref<HTMLDivElement | null>(null)
const div5Ref = ref<HTMLDivElement | null>(null)
const div6Ref = ref<HTMLDivElement | null>(null)
const div7Ref = ref<HTMLDivElement | null>(null)
</script>



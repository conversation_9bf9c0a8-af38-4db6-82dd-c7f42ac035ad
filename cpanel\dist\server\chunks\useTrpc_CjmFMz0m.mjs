import { ref, computed } from 'vue';
import { t as trpc } from './trpc_DApR3DD7.mjs';
import { a as useErrorHandler } from './ClientRouter_avhRMbqw.mjs';

function useTrpc() {
  const loading = ref(false);
  const error = ref(null);
  const errorHandler = useErrorHandler();
  const emitToast = (payload) => {
    if (typeof window !== "undefined") {
      window.dispatchEvent(new CustomEvent("app:toast", { detail: payload }));
    }
  };
  const handleError = (err) => {
    const trpcError = errorHandler.handleTRPCError(err, false);
    error.value = trpcError.message;
    emitToast({ severity: "error", summary: "Ошибка", detail: trpcError.message });
  };
  const clearError = () => {
    error.value = null;
  };
  const execute = async (operation, opts) => {
    try {
      loading.value = true;
      clearError();
      const result = await operation();
      if (opts?.success) {
        emitToast({ severity: "success", summary: opts.success.title ?? "Успешно", detail: opts.success.message });
      }
      return result;
    } catch (err) {
      handleError(err);
      return null;
    } finally {
      loading.value = false;
    }
  };
  return {
    // Состояние
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    // Методы
    clearError,
    execute,
    // tRPC клиент
    client: trpc,
    // Админ — вызывать напрямую через trpc в компонентах (см. AdminDashboard)
    // Удобные методы для работы с основными сущностями
    parts: {
      // Получить все части
      findMany: (input) => execute(() => trpc.crud.part.findMany.query(input)),
      // Получить часть по ID
      findUnique: (input) => execute(() => trpc.crud.part.findUnique.query(input)),
      // Создать новую часть
      create: (input) => execute(() => trpc.crud.part.create.mutate(input), { success: { title: "Сохранено", message: "Запчасть создана" } }),
      // Обновить часть
      update: (input) => execute(() => trpc.crud.part.update.mutate(input), { success: { title: "Сохранено", message: "Запчасть обновлена" } }),
      // Удалить часть
      delete: (input) => execute(() => trpc.crud.part.delete.mutate(input), { success: { title: "Удалено", message: "Запчасть удалена" } })
    },
    catalogItems: {
      // Получить все каталожные позиции
      findMany: (input) => execute(() => trpc.crud.catalogItem.findMany.query(input)),
      // Получить каталожную позицию по ID
      findUnique: (input) => execute(() => trpc.crud.catalogItem.findUnique.query(input)),
      // Создать новую каталожную позицию
      create: (input) => execute(() => trpc.crud.catalogItem.create.mutate(input), { success: { title: "Сохранено", message: "Позиция создана" } }),
      // Обновить каталожную позицию
      update: (input) => execute(() => trpc.crud.catalogItem.update.mutate(input), { success: { title: "Сохранено", message: "Позиция обновлена" } }),
      // Удалить каталожную позицию
      delete: (input) => execute(() => trpc.crud.catalogItem.delete.mutate(input), { success: { title: "Удалено", message: "Позиция удалена" } })
    },
    matching: {
      findMatchingParts: (input) => execute(() => trpc.matching.findMatchingParts.query(input)),
      findMatchingCatalogItems: (input) => execute(() => trpc.matching.findMatchingCatalogItems.query(input)),
      proposeLink: (input) => execute(() => trpc.matching.proposeLink.mutate(input)),
      listProposals: (input) => execute(() => trpc.matching.listProposals.query(input)),
      approveProposal: (input) => execute(() => trpc.matching.approveProposal.mutate(input)),
      rejectProposal: (input) => execute(() => trpc.matching.rejectProposal.mutate(input)),
      generateProposals: (input) => execute(() => trpc.matching.generateProposals.mutate(input)),
      createPartFromItems: (input) => execute(() => trpc.matching.createPartFromItems.mutate(input))
    },
    brands: {
      // Получить все бренды
      findMany: (input) => execute(() => trpc.crud.brand.findMany.query(input)),
      // Создать новый бренд
      create: (input) => execute(() => trpc.crud.brand.create.mutate(input), { success: { title: "Сохранено", message: "Бренд создан" } }),
      // Обновить бренд
      update: (input) => execute(() => trpc.crud.brand.update.mutate(input), { success: { title: "Сохранено", message: "Бренд обновлён" } }),
      // Удалить бренд
      delete: (input) => execute(() => trpc.crud.brand.delete.mutate(input), { success: { title: "Удалено", message: "Бренд удалён" } })
    },
    partCategories: {
      // Получить все категории
      findMany: (input) => execute(() => trpc.crud.partCategory.findMany.query(input)),
      // Создать новую категорию
      create: (input) => execute(() => trpc.crud.partCategory.create.mutate(input), { success: { title: "Сохранено", message: "Категория создана" } }),
      // Обновить категорию
      update: (input) => execute(() => trpc.crud.partCategory.update.mutate(input), { success: { title: "Сохранено", message: "Категория обновлена" } }),
      // Удалить категорию
      delete: (input) => execute(() => trpc.crud.partCategory.delete.mutate(input), { success: { title: "Удалено", message: "Категория удалена" } })
    },
    media: {
      uploadPartImage: (input) => execute(() => trpc.upload.uploadPartImage.mutate(input), { success: { title: "Загружено", message: "Изображение запчасти обновлено" } }),
      deletePartImage: (input) => execute(() => trpc.upload.deletePartImage.mutate(input), { success: { title: "Удалено", message: "Изображение запчасти удалено" } }),
      uploadPartCategoryImage: (input) => execute(() => trpc.upload.uploadPartCategoryImage.mutate(input), { success: { title: "Загружено", message: "Изображение категории обновлено" } }),
      deletePartCategoryImage: (input) => execute(() => trpc.upload.deletePartCategoryImage.mutate(input), { success: { title: "Удалено", message: "Изображение категории удалено" } })
    },
    equipmentModels: {
      // Получить все модели техники
      findMany: (input) => execute(() => trpc.crud.equipmentModel.findMany.query(input)),
      // Получить модель техники по ID
      findUnique: (input) => execute(() => trpc.crud.equipmentModel.findUnique.query(input)),
      // Создать новую модель техники
      create: (input) => execute(() => trpc.crud.equipmentModel.create.mutate(input)),
      // Обновить модель техники
      update: (input) => execute(() => trpc.crud.equipmentModel.update.mutate(input)),
      // Удалить модель техники
      delete: (input) => execute(() => trpc.crud.equipmentModel.delete.mutate(input))
    },
    // Новые методы для работы с атрибутами на основе шаблонов
    partAttributes: {
      // Получить атрибуты запчасти
      findByPartId: (input) => execute(() => trpc.partAttributes.findByPartId.query(input)),
      // Создать атрибут запчасти
      create: (input) => execute(() => trpc.partAttributes.create.mutate(input), { success: { title: "Сохранено", message: "Атрибут добавлен" } }),
      // Обновить атрибут запчасти
      update: (input) => execute(() => trpc.partAttributes.update.mutate(input), { success: { title: "Сохранено", message: "Атрибут обновлён" } }),
      // Удалить атрибут запчасти
      delete: (input) => execute(() => trpc.partAttributes.delete.mutate(input), { success: { title: "Удалено", message: "Атрибут удалён" } }),
      // Массовое создание атрибутов
      bulkCreate: (input) => execute(() => trpc.partAttributes.bulkCreate.mutate(input))
    },
    // Методы для работы с шаблонами атрибутов
    attributeTemplates: {
      // Получить все шаблоны
      findMany: (input) => execute(() => trpc.attributeTemplates.findMany.query(input)),
      // Получить шаблон по ID
      findById: (input) => execute(() => trpc.attributeTemplates.findById.query(input)),
      // Создать шаблон
      create: (input) => execute(() => trpc.attributeTemplates.create.mutate(input), { success: { title: "Сохранено", message: "Шаблон создан" } }),
      // Обновить шаблон
      update: (input) => execute(() => trpc.attributeTemplates.update.mutate(input), { success: { title: "Сохранено", message: "Шаблон обновлён" } }),
      // Удалить шаблон
      delete: (input) => execute(() => trpc.attributeTemplates.delete.mutate(input), { success: { title: "Удалено", message: "Шаблон удалён" } }),
      // Получить все группы
      findAllGroups: () => execute(() => trpc.attributeTemplates.findAllGroups.query({})),
      // Создать группу
      createGroup: (input) => execute(() => trpc.attributeTemplates.createGroup.mutate(input), { success: { title: "Сохранено", message: "Группа создана" } }),
      // Обновить группу
      updateGroup: (input) => execute(() => trpc.attributeTemplates.updateGroup.mutate(input), { success: { title: "Сохранено", message: "Группа обновлена" } }),
      // Удалить группу
      deleteGroup: (input) => execute(() => trpc.attributeTemplates.deleteGroup.mutate(input), { success: { title: "Удалено", message: "Группа удалена" } })
    },
    // Методы для работы с группами синонимов атрибутов
    attributeSynonyms: {
      groups: {
        findMany: (input) => execute(() => trpc.attributeSynonyms.groups.findMany.query(input)),
        create: (input) => execute(() => trpc.attributeSynonyms.groups.create.mutate(input), { success: { title: "Сохранено", message: "Группа синонимов создана" } }),
        update: (input) => execute(() => trpc.attributeSynonyms.groups.update.mutate(input), { success: { title: "Сохранено", message: "Группа синонимов обновлена" } }),
        delete: (input) => execute(() => trpc.attributeSynonyms.groups.delete.mutate(input), { success: { title: "Удалено", message: "Группа синонимов удалена" } })
      },
      synonyms: {
        findMany: (input) => execute(() => trpc.attributeSynonyms.synonyms.findMany.query(input)),
        create: (input) => execute(() => trpc.attributeSynonyms.synonyms.create.mutate(input), { success: { title: "Сохранено", message: "Синоним добавлен" } }),
        delete: (input) => execute(() => trpc.attributeSynonyms.synonyms.delete.mutate(input), { success: { title: "Удалено", message: "Синоним удалён" } })
      },
      utils: {
        findGroupByValue: (input) => execute(() => trpc.attributeSynonyms.utils.findGroupByValue.query(input))
      }
    },
    // NOTE: removed duplicate short matching section; full API is provided above
    partApplicability: {
      upsert: (input) => execute(() => trpc.crud.partApplicability.upsert.mutate(input), { success: { title: "Сохранено", message: "Применимость обновлена" } }),
      findMany: (input) => execute(() => trpc.crud.partApplicability.findMany.query(input)),
      create: (input) => execute(() => trpc.crud.partApplicability.create.mutate(input)),
      update: (input) => execute(() => trpc.crud.partApplicability.update.mutate(input)),
      findFirst: (input) => execute(() => trpc.crud.partApplicability.findFirst.query(input)),
      delete: (input) => execute(() => trpc.crud.partApplicability.delete.mutate(input), { success: { title: "Удалено", message: "Связь с группой удалена" } })
    },
    equipmentApplicability: {
      upsert: (input) => execute(() => trpc.crud.equipmentApplicability.upsert.mutate(input)),
      findMany: (input) => execute(() => trpc.crud.equipmentApplicability.findMany.query(input)),
      create: (input) => execute(() => trpc.crud.equipmentApplicability.create.mutate(input), { success: { title: "Сохранено", message: "Применимость техники добавлена" } }),
      update: (input) => execute(() => trpc.crud.equipmentApplicability.update.mutate(input), { success: { title: "Сохранено", message: "Применимость техники обновлена" } }),
      findFirst: (input) => execute(() => trpc.crud.equipmentApplicability.findFirst.query(input))
    }
  };
}

export { useTrpc as u };

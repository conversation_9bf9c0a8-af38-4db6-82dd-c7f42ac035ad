import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { $ as $$AdminLayout, I as Icon } from '../../../chunks/AdminLayout_DrlBSzRq.mjs';
import { t as trpc } from '../../../chunks/trpc_DApR3DD7.mjs';
import { r as resolveMediaUrl } from '../../../chunks/utils_C3vjJnJw.mjs';
export { r as renderers } from '../../../chunks/_@astro-renderers_CicWY1rm.mjs';

const $$Astro = createAstro();
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  if (!id) {
    return Astro2.redirect("/admin/parts");
  }
  let part;
  try {
    part = await trpc.crud.part.findUnique.query({
      where: { id: Number(id) },
      include: {
        image: true,
        partCategory: { include: { image: true } },
        parent: { include: { partCategory: true } },
        children: { include: { partCategory: true } },
        attributes: {
          include: {
            template: {
              include: {
                group: true,
                synonymGroups: { include: { synonyms: true } }
              }
            }
          }
        },
        applicabilities: {
          include: {
            catalogItem: {
              include: {
                brand: true,
                attributes: {
                  include: {
                    template: { include: { group: true } }
                  }
                }
              }
            }
          }
        },
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: {
                brand: true,
                attributes: {
                  include: {
                    template: { include: { group: true } }
                  }
                }
              }
            }
          }
        },
        matchingProposals: {
          include: {
            catalogItem: { include: { brand: true } }
          }
        },
        aggregateSchemas: { include: { positions: true, annotations: true } },
        schemaPositions: { include: { schema: true } },
        _count: {
          select: {
            attributes: true,
            applicabilities: true,
            equipmentApplicabilities: true,
            children: true,
            matchingProposals: true,
            aggregateSchemas: true
          }
        }
      }
    });
    if (!part) {
      return Astro2.redirect("/admin/parts");
    }
  } catch (error) {
    console.error("Error loading part:", error);
    return Astro2.redirect("/admin/parts");
  }
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-6xl mx-auto"> <div class="mb-6"> <div class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400 mb-2"> <a href="/admin/parts" class="hover:text-primary">Запчасти</a> <span>/</span> <span>#${part.id}</span> </div> <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0"> ${part.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"} </h1> ${part.partCategory && renderTemplate`<div class="mt-2"> <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300"> ${part.partCategory.name} </span> </div>`} </div> <div class="grid grid-cols-1 lg:grid-cols-3 gap-6"> <!-- Сводка слева --> <div class="lg:col-span-1 space-y-6"> <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-info-circle", "class": "text-sky-600 w-4 h-4" })}
Общая информация
</h2> <dl class="space-y-2 text-sm"> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">ID</dt> <dd class="font-medium">#${part.id}</dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Категория</dt> <dd class="font-medium">${part.partCategory?.name || "\u2014"}</dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Уровень</dt> <dd class="font-medium">${part.level}</dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Путь</dt> <dd class="font-medium font-mono">${part.path}</dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Родитель</dt> <dd> ${part.parent ? renderTemplate`<a class="text-primary hover:underline"${addAttribute(`/admin/parts/${part.parent.id}`, "href")}>${part.parent.name || `#${part.parent.id}`}</a>` : "\u2014"} </dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Дочерние группы</dt> <dd class="font-medium">${part._count?.children ?? part.children?.length ?? 0}</dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Создано</dt> <dd>${new Date(part.createdAt).toLocaleString("ru-RU")}</dd> </div> <div class="flex justify-between"> <dt class="text-surface-600 dark:text-surface-400">Обновлено</dt> <dd>${new Date(part.updatedAt).toLocaleString("ru-RU")}</dd> </div> </dl> </div> ${(part.image?.url || part.partCategory?.image?.url) && renderTemplate`<div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-image", "class": "text-amber-600 w-4 h-4" })}
Изображение
</h2> <div class="aspect-video w-full overflow-hidden rounded border"> <img${addAttribute(resolveMediaUrl(part.image?.url || part.partCategory?.image?.url), "src")} alt="Изображение" class="w-full h-full object-cover"> </div> </div>`} ${part.children && part.children.length > 0 && renderTemplate`<div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-sitemap", "class": "text-emerald-600 w-4 h-4" })}
Дочерние группы
</h2> <ul class="space-y-2 text-sm"> ${part.children.map((child) => renderTemplate`<li class="flex items-center justify-between"> <a class="text-primary hover:underline"${addAttribute(`/admin/parts/${child.id}`, "href")}>${child.name || `#${child.id}`}</a> ${child.partCategory && renderTemplate`<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded bg-surface-100 dark:bg-surface-800 text-xs">${child.partCategory.name}</span>`} </li>`)} </ul> </div>`} </div> <!-- Контент справа (2 колонки) --> <div class="lg:col-span-2 space-y-6"> <!-- Атрибуты --> <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-list", "class": "text-blue-600 w-4 h-4" })}
Атрибуты (${part._count?.attributes ?? part.attributes?.length ?? 0})
</h2> ${part.attributes && part.attributes.length > 0 ? renderTemplate`<div class="space-y-3"> ${part.attributes.map((attr) => renderTemplate`<div class="p-3 bg-surface-50 dark:bg-surface-800 rounded border"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium text-surface-900 dark:text-surface-0"> ${attr.template?.title || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"} ${attr.template?.isRequired && renderTemplate`<span class="text-red-500 ml-1">*</span>`} </div> ${attr.template?.description && renderTemplate`<div class="text-sm text-surface-500 dark:text-surface-400 mt-1">${attr.template.description}</div>`} <div class="mt-2 flex flex-wrap gap-2 text-xs"> ${attr.template?.group && renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded">${attr.template.group.name}</span>`} ${attr.template?.dataType && renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-surface-100 dark:bg-surface-800 rounded">Тип: ${attr.template.dataType}</span>`} ${"tolerance" in (attr.template || {}) && attr.template?.tolerance != null && renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-surface-100 dark:bg-surface-800 rounded">Допуск: ${attr.template.tolerance}</span>`} ${Array.isArray(attr.template?.allowedValues) && attr.template.allowedValues.length > 0 && renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-surface-100 dark:bg-surface-800 rounded">Допустимые: ${attr.template.allowedValues.join(", ")}</span>`} </div> ${attr.template?.synonymGroups && attr.template.synonymGroups.length > 0 && renderTemplate`<div class="mt-3 text-xs text-surface-600 dark:text-surface-400"> <div class="font-semibold mb-1">Группы синонимов</div> <ul class="list-disc pl-5 space-y-1"> ${attr.template.synonymGroups.map((g) => renderTemplate`<li> <span class="font-medium">${g.name}</span> ${g.compatibilityLevel && renderTemplate`<span class="ml-1 opacity-70">[${g.compatibilityLevel}]</span>`} ${g.notes && renderTemplate`<span class="ml-2 italic">— ${g.notes}</span>`} ${g.synonyms && g.synonyms.length > 0 && renderTemplate`<div class="mt-1"> <span class="opacity-70">Синонимы:</span> ${g.synonyms.map((s) => s.value).join(", ")} </div>`} </li>`)} </ul> </div>`} </div> <div class="ml-3 text-right"> <div class="font-medium text-surface-700 dark:text-surface-300">${attr.value || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E"}</div> ${attr.template?.unit && renderTemplate`<div class="text-sm text-surface-500 dark:text-surface-400">${attr.template.unit}</div>`} ${"numericValue" in attr && attr.numericValue != null && renderTemplate`<div class="text-xs text-surface-500 dark:text-surface-400 mt-1">Число: ${attr.numericValue}</div>`} </div> </div> </div>`)} </div>` : renderTemplate`<div class="text-center py-8 text-surface-500 dark:text-surface-400"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-info-circle", "class": "text-2xl mb-2 inline-block" })}
Атрибуты не заданы
</div>`} </div> <!-- Каталожные позиции --> <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-box", "class": "text-green-600 w-4 h-4" })}
Каталожные позиции (${part._count?.applicabilities ?? part.applicabilities?.length ?? 0})
</h2> ${part.applicabilities && part.applicabilities.length > 0 ? renderTemplate`<div class="space-y-3"> ${part.applicabilities.map((app) => renderTemplate`<div class="p-4 bg-surface-50 dark:bg-surface-800 rounded border"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium text-surface-900 dark:text-surface-0">${app.catalogItem?.sku || "N/A"}</div> <div class="text-sm text-surface-600 dark:text-surface-400 mt-1">${app.catalogItem?.brand?.name || "\u041D\u0435\u0438\u0437\u0432\u0435\u0441\u0442\u043D\u044B\u0439 \u0431\u0440\u0435\u043D\u0434"}</div> ${app.catalogItem?.description && renderTemplate`<div class="text-sm text-surface-500 dark:text-surface-400 mt-1">${app.catalogItem.description}</div>`} <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2"> <div class="text-xs text-surface-500">Источник: ${app.catalogItem?.source || "\u2014"}</div> <div class="text-xs text-surface-500">Публичный: ${app.catalogItem?.isPublic ? "\u0414\u0430" : "\u041D\u0435\u0442"}</div> </div> </div> <div class="ml-3"> <span${addAttribute(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${app.accuracy === "EXACT_MATCH" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300" : app.accuracy === "MATCH_WITH_NOTES" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300" : app.accuracy === "REQUIRES_MODIFICATION" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300" : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"}`, "class")}> ${app.accuracy === "EXACT_MATCH" ? "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435" : app.accuracy === "MATCH_WITH_NOTES" ? "\u0421 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438" : app.accuracy === "REQUIRES_MODIFICATION" ? "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438" : "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435"} </span> </div> </div> ${app.notes && renderTemplate`<div class="mt-2 p-2 bg-surface-100 dark:bg-surface-700 rounded text-sm text-surface-600 dark:text-surface-400"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-info-circle", "class": "mr-1 w-4 h-4 inline-block" })} ${app.notes} </div>`} ${app.catalogItem?.attributes && app.catalogItem.attributes.length > 0 && renderTemplate`<div class="mt-3"> <div class="text-surface-600 dark:text-surface-400 text-sm mb-2">Атрибуты каталожной позиции</div> <div class="grid grid-cols-1 md:grid-cols-2 gap-2"> ${app.catalogItem.attributes.map((ca) => renderTemplate`<div class="p-2 bg-surface-0 dark:bg-surface-900 rounded border text-sm"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium text-surface-900 dark:text-surface-0">${ca.template?.title || ca.template?.name || "\u2014"}</div> ${ca.template?.group && renderTemplate`<div class="text-xs text-surface-500 mt-1">${ca.template.group.name}</div>`} </div> <div class="ml-2 text-right"> <div class="text-surface-700 dark:text-surface-300">${ca.value}</div> ${ca.template?.unit && renderTemplate`<div class="text-xs text-surface-500">${ca.template.unit}</div>`} </div> </div> </div>`)} </div> </div>`} </div>`)} </div>` : renderTemplate`<div class="text-center py-8 text-surface-500 dark:text-surface-400"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-info-circle", "class": "text-2xl mb-2 inline-block" })}
Каталожные позиции не добавлены
</div>`} </div> <!-- Применимость к технике --> <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-cog", "class": "text-purple-600 w-4 h-4" })}
Применимость к технике (${part._count?.equipmentApplicabilities ?? part.equipmentApplicabilities?.length ?? 0})
</h2> ${part.equipmentApplicabilities && part.equipmentApplicabilities.length > 0 ? renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 gap-3"> ${part.equipmentApplicabilities.map((equipApp) => renderTemplate`<div class="p-3 bg-surface-50 dark:bg-surface-800 rounded border"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium text-surface-900 dark:text-surface-0">${equipApp.equipmentModel?.name || "N/A"}</div> ${equipApp.equipmentModel?.brand && renderTemplate`<div class="text-sm text-surface-600 dark:text-surface-400 mt-1">${equipApp.equipmentModel.brand.name}</div>`} </div> <div class="ml-3"> <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300">Техника</span> </div> </div> ${equipApp.notes && renderTemplate`<div class="mt-2 p-2 bg-surface-100 dark:bg-surface-700 rounded text-sm text-surface-600 dark:text-surface-400"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-info-circle", "class": "mr-1 w-4 h-4 inline-block" })} ${equipApp.notes} </div>`} ${equipApp.equipmentModel?.attributes && equipApp.equipmentModel.attributes.length > 0 && renderTemplate`<div class="mt-3"> <div class="text-surface-600 dark:text-surface-400 text-sm mb-2">Атрибуты модели</div> <div class="grid grid-cols-1 sm:grid-cols-2 gap-2"> ${equipApp.equipmentModel.attributes.map((ea) => renderTemplate`<div class="p-2 bg-surface-0 dark:bg-surface-900 rounded border text-sm"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium text-surface-900 dark:text-surface-0">${ea.template?.title || ea.template?.name || "\u2014"}</div> ${ea.template?.group && renderTemplate`<div class="text-xs text-surface-500 mt-1">${ea.template.group.name}</div>`} </div> <div class="ml-2 text-right"> <div class="text-surface-700 dark:text-surface-300">${ea.value}</div> ${ea.template?.unit && renderTemplate`<div class="text-xs text-surface-500">${ea.template.unit}</div>`} </div> </div> </div>`)} </div> </div>`} </div>`)} </div>` : renderTemplate`<div class="text-center py-8 text-surface-500 dark:text-surface-400"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-info-circle", "class": "text-2xl mb-2 inline-block" })}
Применимость к технике не указана
</div>`} </div> <!-- Схемы и позиции --> <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-diagram-project", "class": "text-orange-600 w-4 h-4" })}
Схемы агрегатов (${part._count?.aggregateSchemas ?? part.aggregateSchemas?.length ?? 0})
</h2> ${part.aggregateSchemas && part.aggregateSchemas.length > 0 ? renderTemplate`<div class="grid grid-cols-1 md:grid-cols-2 gap-3"> ${part.aggregateSchemas.map((schema) => renderTemplate`<div class="p-3 bg-surface-50 dark:bg-surface-800 rounded border text-sm"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium">${schema.name}</div> ${schema.description && renderTemplate`<div class="mt-1 text-surface-600 dark:text-surface-400">${schema.description}</div>`} <div class="mt-2 grid grid-cols-2 gap-2 text-xs text-surface-600"> <div>Позиции: ${schema.positions?.length ?? 0}</div> <div>Аннотации: ${schema.annotations?.length ?? 0}</div> <div>Активна: ${schema.isActive ? "\u0414\u0430" : "\u041D\u0435\u0442"}</div> <div>Порядок: ${schema.sortOrder}</div> </div> </div> </div> </div>`)} </div>` : renderTemplate`<div class="text-sm text-surface-500">Нет связанных схем</div>`} ${part.schemaPositions && part.schemaPositions.length > 0 && renderTemplate`<div class="mt-6"> <div class="text-surface-600 dark:text-surface-400 text-sm mb-2">Позиции на схемах</div> <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm"> ${part.schemaPositions.map((pos) => renderTemplate`<div class="p-2 bg-surface-50 dark:bg-surface-800 rounded border"> <div class="flex items-center justify-between"> <div class="font-medium">${pos.schema?.name || "\u0421\u0445\u0435\u043C\u0430"}</div> <div class="text-xs">Поз. #${pos.positionNumber}</div> </div> <div class="mt-1 text-xs text-surface-600">Коорд.: ${pos.x}, ${pos.y}${pos.width ? `, ${pos.width}\xD7${pos.height || "\u2014"}` : ""}</div> ${pos.label && renderTemplate`<div class="mt-1 text-xs text-surface-600">Метка: ${pos.label}</div>`} ${pos.notes && renderTemplate`<div class="mt-1 text-xs text-surface-600">Примеч.: ${pos.notes}</div>`} </div>`)} </div> </div>`} </div> <!-- Предложения сопоставления --> <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6"> <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-4 flex items-center gap-2"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-link", "class": "text-teal-600 w-4 h-4" })}
Предложения сопоставления (${part._count?.matchingProposals ?? part.matchingProposals?.length ?? 0})
</h2> ${part.matchingProposals && part.matchingProposals.length > 0 ? renderTemplate`<div class="space-y-3"> ${part.matchingProposals.map((mp) => renderTemplate`<div class="p-3 bg-surface-50 dark:bg-surface-800 rounded border text-sm"> <div class="flex items-start justify-between"> <div class="flex-1"> <div class="font-medium">${mp.catalogItem?.sku} — ${mp.catalogItem?.brand?.name}</div> <div class="mt-1 text-surface-600 dark:text-surface-400">Статус: ${mp.status}</div> ${mp.notesSuggestion && renderTemplate`<div class="mt-1 text-surface-600 dark:text-surface-400">Примеч.: ${mp.notesSuggestion}</div>`} </div> <div class="ml-3"> <span${addAttribute(`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${mp.accuracySuggestion === "EXACT_MATCH" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300" : mp.accuracySuggestion === "MATCH_WITH_NOTES" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300" : mp.accuracySuggestion === "REQUIRES_MODIFICATION" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300" : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"}`, "class")}> ${mp.accuracySuggestion === "EXACT_MATCH" ? "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435" : mp.accuracySuggestion === "MATCH_WITH_NOTES" ? "\u0421 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438" : mp.accuracySuggestion === "REQUIRES_MODIFICATION" ? "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438" : "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435"} </span> </div> </div> ${mp.details && renderTemplate`<details class="mt-2"> <summary class="cursor-pointer text-xs text-surface-600 dark:text-surface-400">Детали сопоставления</summary> <pre class="mt-2 p-2 bg-surface-0 dark:bg-surface-900 rounded overflow-auto text-xs">${JSON.stringify(mp.details, null, 2)}</pre> </details>`} <div class="mt-2 text-xs text-surface-500">Создано: ${new Date(mp.createdAt).toLocaleString("ru-RU")}</div> </div>`)} </div>` : renderTemplate`<div class="text-sm text-surface-500">Нет предложений</div>`} </div> <!-- Действия --> <div class="flex gap-3"> <a${addAttribute(`/admin/parts/${part.id}/edit`, "href")} class="inline-flex items-center px-4 py-2 bg-primary text-primary-contrast rounded-lg hover:bg-primary-600 transition-colors"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-pencil", "class": "mr-2 w-4 h-4 inline-block" })}
Редактировать запчасть
</a> <a href="/admin/parts" class="inline-flex items-center px-4 py-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-lg hover:bg-surface-300 dark:hover:bg-surface-600 transition-colors"> ${renderComponent($$result2, "Icon", Icon, { "name": "pi pi-arrow-left", "class": "mr-2 w-4 h-4 inline-block" })}
Назад к списку
</a> </div> </div> </div> </div> ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/[id].astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/parts/[id].astro";
const $$url = "/admin/parts/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

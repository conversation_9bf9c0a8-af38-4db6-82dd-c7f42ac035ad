import{w as B}from"./runtime-dom.esm-bundler.DXo4nCak.js";import{u as C}from"./useAuth.D4HmQrUw.js";import D from"./Button.DrThv2lH.js";import V from"./Card.C4y0_bWr.js";import{I as k}from"./InputText.DOJMNEP-.js";import{P as S}from"./Password.CGsZf6xB.js";import{M as h}from"./Message.BY1UiDHQ.js";import{C as A}from"./Checkbox.C7bFmmzc.js";import{n as _}from"./router.DKcY2uv6.js";/* empty css                       */import{_ as I}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as M,c as f,a as r,e as i,w as x,h as P,o as n,g as T,b as c,f as b}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{r as g,a as F,t as p}from"./reactivity.esm-bundler.BQ12LWmY.js";import"./auth-client.CMFsScx1.js";import"./types.C07aSKae.js";import"./types.FgRm47Sn.js";import"./index.6ykohhwZ.js";import"./index.BaVCXmir.js";import"./index.BH7IgUdp.js";import"./utils.BUKUcbtE.js";import"./bundle-mjs.D6B6e0vX.js";import"./index.CDQpPXyE.js";import"./index.COq_zjeV.js";import"./index.CLs7nh7g.js";import"./index.S_9XL1GF.js";import"./index.n7VWMPJ9.js";import"./index.BZ4rDiaJ.js";import"./index.By2TJOuX.js";import"./index.D4QD70nN.js";const j=M({__name:"LoginForm",setup(w,{expose:e}){e();const{signIn:d,LoginFormSchema:u}=C(),m=g(!1),l=g(""),t=F({email:"",password:"",rememberMe:!1}),s=F({}),y=P(()=>t.email&&t.password&&Object.keys(s).length===0),E={signIn:d,LoginFormSchema:u,isSubmitting:m,generalError:l,formData:t,fieldErrors:s,isFormValid:y,validateField:a=>{try{const v=u.shape[a].safeParse(t[a]);v.success?delete s[a]:s[a]=v.error.issues[0]?.message||"Ошибка валидации"}catch(o){console.error("Validation error for field:",a,o),delete s[a]}},clearFieldError:a=>{s[a]&&delete s[a],l.value&&(l.value="")},handleSubmit:async()=>{l.value="",Object.keys(s).forEach(o=>delete s[o]);const a=u.safeParse(t);if(!a.success){a.error.issues.forEach(o=>{o.path[0]&&(s[o.path[0]]=o.message)});return}m.value=!0;try{const o=await d(t);o.error?o.error.message.includes("Invalid email or password")?l.value="Неверный email или пароль":o.error.message.includes("Too many requests")?l.value="Слишком много попыток входа. Попробуйте позже.":l.value=o.error.message||"Ошибка входа в систему":(console.log("✅ Успешный вход в систему"),setTimeout(()=>{_("/admin")},100))}catch(o){console.error("Login error:",o),l.value="Произошла неожиданная ошибка. Попробуйте еще раз."}finally{m.value=!1}},Button:D,Card:V,InputText:k,Password:S,Message:h,Checkbox:A};return Object.defineProperty(E,"__isScriptSetup",{enumerable:!1,value:!0}),E}}),L={class:"min-h-screen flex items-center justify-center bg-surface-50 py-12 px-4 sm:px-6 lg:px-8"},N={class:"max-w-md w-full space-y-8"},O={key:1,class:"text-red-500"},U={key:2,class:"text-red-500"},q={class:"flex items-center justify-between"},z={class:"flex items-center"};function R(w,e,d,u,m,l){return n(),f("div",L,[r("div",N,[e[12]||(e[12]=r("div",null,[r("div",{class:"mx-auto h-12 w-12 flex items-center justify-center"},[r("img",{class:"h-12 w-12",src:"/favicon.svg",alt:"PartTec"})]),r("h2",{class:"mt-6 text-center text-3xl font-extrabold text-surface-900"}," Вход в админ панель "),r("p",{class:"mt-2 text-center text-sm text-surface-600"}," Система управления каталогом PartTec ")],-1)),i(u.Card,{class:"mt-8"},{content:x(()=>[r("form",{class:"space-y-6",onSubmit:B(u.handleSubmit,["prevent"])},[u.generalError?(n(),T(u.Message,{key:0,severity:"error",closable:!1},{default:x(()=>[b(p(u.generalError),1)]),_:1})):c("",!0),r("div",null,[i(u.InputText,{id:"email",modelValue:u.formData.email,"onUpdate:modelValue":e[0]||(e[0]=t=>u.formData.email=t),type:"email",autocomplete:"email",class:"w-full",invalid:!!u.fieldErrors.email,onBlur:e[1]||(e[1]=t=>u.validateField("email")),onInput:e[2]||(e[2]=t=>u.clearFieldError("email"))},null,8,["modelValue","invalid"]),e[7]||(e[7]=r("label",{for:"email"},"Email адрес",-1))]),u.fieldErrors.email?(n(),f("small",O,p(u.fieldErrors.email),1)):c("",!0),r("div",null,[i(u.Password,{id:"password",modelValue:u.formData.password,"onUpdate:modelValue":e[3]||(e[3]=t=>u.formData.password=t),autocomplete:"current-password",class:"w-full",invalid:!!u.fieldErrors.password,feedback:!1,toggleMask:"",onBlur:e[4]||(e[4]=t=>u.validateField("password")),onInput:e[5]||(e[5]=t=>u.clearFieldError("password"))},null,8,["modelValue","invalid"]),e[8]||(e[8]=r("label",{for:"password"},"Пароль",-1))]),u.fieldErrors.password?(n(),f("small",U,p(u.fieldErrors.password),1)):c("",!0),r("div",q,[r("div",z,[i(u.Checkbox,{id:"remember-me",modelValue:u.formData.rememberMe,"onUpdate:modelValue":e[6]||(e[6]=t=>u.formData.rememberMe=t),binary:!0},null,8,["modelValue"]),e[9]||(e[9]=r("label",{for:"remember-me",class:"ml-2 block text-sm text-surface-700"}," Запомнить меня ",-1))]),e[10]||(e[10]=r("div",{class:"text-sm"},[r("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Забыли пароль? ")],-1))]),r("div",null,[i(u.Button,{type:"submit",disabled:u.isSubmitting||!u.isFormValid,loading:u.isSubmitting,label:"Войти",class:"w-full",size:"large"},null,8,["disabled","loading"])]),e[11]||(e[11]=r("div",{class:"text-center"},[r("p",{class:"text-sm text-surface-600"},[b(" Нет аккаунта? "),r("a",{href:"/admin/register",class:"font-medium text-primary-600 hover:text-primary-500"}," Зарегистрироваться ")])],-1))],32)]),_:1})])])}const y4=I(j,[["render",R],["__scopeId","data-v-e3f15633"]]);export{y4 as default};

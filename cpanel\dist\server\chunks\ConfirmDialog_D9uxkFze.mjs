import { u as useConfirm$1, C as ConfirmationEventBus } from './_@astro-renderers_CicWY1rm.mjs';
import { resolveComponent, createBlock, openBlock, normalizeClass, createSlots, withCtx, createElementBlock, createCommentVNode, Fragment, renderSlot, createElementVNode, resolveDynamicComponent, mergeProps, toDisplayString, createVNode, defineComponent, useSSRContext, ref, createTextVNode } from 'vue';
import { s as script$2, S as SecondaryButton, p as ptViewMerge } from './SecondaryButton_B0hmlm1n.mjs';
import { s as script$3 } from './Dialog_DqmfICId.mjs';
import BaseComponent from '@primevue/core/basecomponent';
import { style } from '@primeuix/styles/confirmdialog';
import BaseStyle from '@primevue/core/base/style';
import './Button_0V33JvkC.mjs';
import { Undo2Icon, Trash2Icon, XCircleIcon, AlertCircle } from 'lucide-vue-next';
import { D as DangerButton } from './DangerButton_BHpYZbTv.mjs';
import { ssrRenderComponent, ssrInterpolate } from 'vue/server-renderer';
import { _ as _export_sfc } from './ClientRouter_avhRMbqw.mjs';

const useConfirm = () => {
  const confirm = useConfirm$1();
  const show = (options) => {
    confirm.require({
      message: options.message || "Вы уверены?",
      header: options.header || "Подтверждение",
      icon: options.icon || "pi pi-exclamation-triangle",
      acceptLabel: options.acceptLabel || "Да",
      rejectLabel: options.rejectLabel || "Отмена",
      acceptClass: options.acceptClass,
      rejectClass: options.rejectClass,
      accept: options.accept,
      reject: options.reject
    });
  };
  const confirmDelete = (entityName = "запись", onConfirm, onReject) => {
    show({
      message: `Вы действительно хотите удалить эту ${entityName}? Это действие нельзя отменить.`,
      header: "Подтверждение удаления",
      icon: "pi pi-trash",
      acceptLabel: "Удалить",
      rejectLabel: "Отмена",
      acceptClass: "bg-red-500 hover:bg-red-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  const confirmSave = (message = "Сохранить изменения?", onConfirm, onReject) => {
    show({
      message,
      header: "Сохранение",
      icon: "pi pi-save",
      acceptLabel: "Сохранить",
      rejectLabel: "Отмена",
      acceptClass: "bg-primary-500 hover:bg-primary-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  const confirmCancel = (message = "У вас есть несохраненные изменения. Вы действительно хотите выйти без сохранения?", onConfirm, onReject) => {
    show({
      message,
      header: "Несохраненные изменения",
      icon: "pi pi-exclamation-triangle",
      acceptLabel: "Выйти без сохранения",
      rejectLabel: "Остаться",
      acceptClass: "bg-orange-500 hover:bg-orange-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  const confirmPublish = (entityName = "запись", onConfirm, onReject) => {
    show({
      message: `Опубликовать эту ${entityName}? После публикации она станет доступна всем пользователям.`,
      header: "Подтверждение публикации",
      icon: "pi pi-globe",
      acceptLabel: "Опубликовать",
      rejectLabel: "Отмена",
      acceptClass: "bg-green-500 hover:bg-green-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  const confirmArchive = (entityName = "запись", onConfirm, onReject) => {
    show({
      message: `Архивировать эту ${entityName}? Архивированные записи не отображаются в основном списке.`,
      header: "Подтверждение архивирования",
      icon: "pi pi-archive",
      acceptLabel: "Архивировать",
      rejectLabel: "Отмена",
      acceptClass: "bg-gray-500 hover:bg-gray-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  const confirmRestore = (entityName = "запись", onConfirm, onReject) => {
    show({
      message: `Восстановить эту ${entityName}? Она снова станет доступна в основном списке.`,
      header: "Подтверждение восстановления",
      icon: "pi pi-refresh",
      acceptLabel: "Восстановить",
      rejectLabel: "Отмена",
      acceptClass: "bg-blue-500 hover:bg-blue-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  const confirmBulkAction = (action, count, onConfirm, onReject) => {
    show({
      message: `Выполнить действие "${action}" для ${count} записей?`,
      header: "Массовое действие",
      icon: "pi pi-list",
      acceptLabel: "Выполнить",
      rejectLabel: "Отмена",
      acceptClass: "bg-primary-500 hover:bg-primary-600",
      accept: onConfirm,
      reject: onReject
    });
  };
  return {
    // Базовый метод
    show,
    // Специализированные методы
    confirmDelete,
    confirmSave,
    confirmCancel,
    confirmPublish,
    confirmArchive,
    confirmRestore,
    confirmBulkAction
  };
};

var classes = {
  root: 'p-confirmdialog',
  icon: 'p-confirmdialog-icon',
  message: 'p-confirmdialog-message',
  pcRejectButton: 'p-confirmdialog-reject-button',
  pcAcceptButton: 'p-confirmdialog-accept-button'
};
var ConfirmDialogStyle = BaseStyle.extend({
  name: 'confirmdialog',
  style: style,
  classes: classes
});

var script$1 = {
  name: 'BaseConfirmDialog',
  "extends": BaseComponent,
  props: {
    group: String,
    breakpoints: {
      type: Object,
      "default": null
    },
    draggable: {
      type: Boolean,
      "default": true
    }
  },
  style: ConfirmDialogStyle,
  provide: function provide() {
    return {
      $pcConfirmDialog: this,
      $parentInstance: this
    };
  }
};

var script = {
  name: 'ConfirmDialog',
  "extends": script$1,
  confirmListener: null,
  closeListener: null,
  data: function data() {
    return {
      visible: false,
      confirmation: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    this.confirmListener = function (options) {
      if (!options) {
        return;
      }
      if (options.group === _this.group) {
        _this.confirmation = options;
        if (_this.confirmation.onShow) {
          _this.confirmation.onShow();
        }
        _this.visible = true;
      }
    };
    this.closeListener = function () {
      _this.visible = false;
      _this.confirmation = null;
    };
    ConfirmationEventBus.on('confirm', this.confirmListener);
    ConfirmationEventBus.on('close', this.closeListener);
  },
  beforeUnmount: function beforeUnmount() {
    ConfirmationEventBus.off('confirm', this.confirmListener);
    ConfirmationEventBus.off('close', this.closeListener);
  },
  methods: {
    accept: function accept() {
      if (this.confirmation.accept) {
        this.confirmation.accept();
      }
      this.visible = false;
    },
    reject: function reject() {
      if (this.confirmation.reject) {
        this.confirmation.reject();
      }
      this.visible = false;
    },
    onHide: function onHide() {
      if (this.confirmation.onHide) {
        this.confirmation.onHide();
      }
      this.visible = false;
    }
  },
  computed: {
    appendTo: function appendTo() {
      return this.confirmation ? this.confirmation.appendTo : 'body';
    },
    target: function target() {
      return this.confirmation ? this.confirmation.target : null;
    },
    modal: function modal() {
      return this.confirmation ? this.confirmation.modal == null ? true : this.confirmation.modal : true;
    },
    header: function header() {
      return this.confirmation ? this.confirmation.header : null;
    },
    message: function message() {
      return this.confirmation ? this.confirmation.message : null;
    },
    blockScroll: function blockScroll() {
      return this.confirmation ? this.confirmation.blockScroll : true;
    },
    position: function position() {
      return this.confirmation ? this.confirmation.position : null;
    },
    acceptLabel: function acceptLabel() {
      if (this.confirmation) {
        var _confirmation$acceptP;
        var confirmation = this.confirmation;
        return confirmation.acceptLabel || ((_confirmation$acceptP = confirmation.acceptProps) === null || _confirmation$acceptP === void 0 ? void 0 : _confirmation$acceptP.label) || this.$primevue.config.locale.accept;
      }
      return this.$primevue.config.locale.accept;
    },
    rejectLabel: function rejectLabel() {
      if (this.confirmation) {
        var _confirmation$rejectP;
        var confirmation = this.confirmation;
        return confirmation.rejectLabel || ((_confirmation$rejectP = confirmation.rejectProps) === null || _confirmation$rejectP === void 0 ? void 0 : _confirmation$rejectP.label) || this.$primevue.config.locale.reject;
      }
      return this.$primevue.config.locale.reject;
    },
    acceptIcon: function acceptIcon() {
      var _this$confirmation;
      return this.confirmation ? this.confirmation.acceptIcon : (_this$confirmation = this.confirmation) !== null && _this$confirmation !== void 0 && _this$confirmation.acceptProps ? this.confirmation.acceptProps.icon : null;
    },
    rejectIcon: function rejectIcon() {
      var _this$confirmation2;
      return this.confirmation ? this.confirmation.rejectIcon : (_this$confirmation2 = this.confirmation) !== null && _this$confirmation2 !== void 0 && _this$confirmation2.rejectProps ? this.confirmation.rejectProps.icon : null;
    },
    autoFocusAccept: function autoFocusAccept() {
      return this.confirmation.defaultFocus === undefined || this.confirmation.defaultFocus === 'accept' ? true : false;
    },
    autoFocusReject: function autoFocusReject() {
      return this.confirmation.defaultFocus === 'reject' ? true : false;
    },
    closeOnEscape: function closeOnEscape() {
      return this.confirmation ? this.confirmation.closeOnEscape : true;
    }
  },
  components: {
    Dialog: script$3,
    Button: script$2
  }
};

function render(_ctx, _cache, $props, $setup, $data, $options) {
  var _component_Button = resolveComponent("Button");
  var _component_Dialog = resolveComponent("Dialog");
  return openBlock(), createBlock(_component_Dialog, {
    visible: $data.visible,
    "onUpdate:visible": [_cache[2] || (_cache[2] = function ($event) {
      return $data.visible = $event;
    }), $options.onHide],
    role: "alertdialog",
    "class": normalizeClass(_ctx.cx('root')),
    modal: $options.modal,
    header: $options.header,
    blockScroll: $options.blockScroll,
    appendTo: $options.appendTo,
    position: $options.position,
    breakpoints: _ctx.breakpoints,
    closeOnEscape: $options.closeOnEscape,
    draggable: _ctx.draggable,
    pt: _ctx.pt,
    unstyled: _ctx.unstyled
  }, createSlots({
    "default": withCtx(function () {
      return [!_ctx.$slots.container ? (openBlock(), createElementBlock(Fragment, {
        key: 0
      }, [!_ctx.$slots.message ? (openBlock(), createElementBlock(Fragment, {
        key: 0
      }, [renderSlot(_ctx.$slots, "icon", {}, function () {
        return [_ctx.$slots.icon ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.icon), {
          key: 0,
          "class": normalizeClass(_ctx.cx('icon'))
        }, null, 8, ["class"])) : $data.confirmation.icon ? (openBlock(), createElementBlock("span", mergeProps({
          key: 1,
          "class": [$data.confirmation.icon, _ctx.cx('icon')]
        }, _ctx.ptm('icon')), null, 16)) : createCommentVNode("", true)];
      }), createElementVNode("span", mergeProps({
        "class": _ctx.cx('message')
      }, _ctx.ptm('message')), toDisplayString($options.message), 17)], 64)) : (openBlock(), createBlock(resolveDynamicComponent(_ctx.$slots.message), {
        key: 1,
        message: $data.confirmation
      }, null, 8, ["message"]))], 64)) : createCommentVNode("", true)];
    }),
    _: 2
  }, [_ctx.$slots.container ? {
    name: "container",
    fn: withCtx(function (slotProps) {
      return [renderSlot(_ctx.$slots, "container", {
        message: $data.confirmation,
        closeCallback: slotProps.onclose,
        acceptCallback: $options.accept,
        rejectCallback: $options.reject
      })];
    }),
    key: "0"
  } : undefined, !_ctx.$slots.container ? {
    name: "footer",
    fn: withCtx(function () {
      var _$data$confirmation$r;
      return [createVNode(_component_Button, mergeProps({
        "class": [_ctx.cx('pcRejectButton'), $data.confirmation.rejectClass],
        autofocus: $options.autoFocusReject,
        unstyled: _ctx.unstyled,
        text: ((_$data$confirmation$r = $data.confirmation.rejectProps) === null || _$data$confirmation$r === void 0 ? void 0 : _$data$confirmation$r.text) || false,
        onClick: _cache[0] || (_cache[0] = function ($event) {
          return $options.reject();
        })
      }, $data.confirmation.rejectProps, {
        label: $options.rejectLabel,
        pt: _ctx.ptm('pcRejectButton')
      }), createSlots({
        _: 2
      }, [$options.rejectIcon || _ctx.$slots.rejecticon ? {
        name: "icon",
        fn: withCtx(function (iconProps) {
          return [renderSlot(_ctx.$slots, "rejecticon", {}, function () {
            return [createElementVNode("span", mergeProps({
              "class": [$options.rejectIcon, iconProps["class"]]
            }, _ctx.ptm('pcRejectButton')['icon'], {
              "data-pc-section": "rejectbuttonicon"
            }), null, 16)];
          })];
        }),
        key: "0"
      } : undefined]), 1040, ["class", "autofocus", "unstyled", "text", "label", "pt"]), createVNode(_component_Button, mergeProps({
        label: $options.acceptLabel,
        "class": [_ctx.cx('pcAcceptButton'), $data.confirmation.acceptClass],
        autofocus: $options.autoFocusAccept,
        unstyled: _ctx.unstyled,
        onClick: _cache[1] || (_cache[1] = function ($event) {
          return $options.accept();
        })
      }, $data.confirmation.acceptProps, {
        pt: _ctx.ptm('pcAcceptButton')
      }), createSlots({
        _: 2
      }, [$options.acceptIcon || _ctx.$slots.accepticon ? {
        name: "icon",
        fn: withCtx(function (iconProps) {
          return [renderSlot(_ctx.$slots, "accepticon", {}, function () {
            return [createElementVNode("span", mergeProps({
              "class": [$options.acceptIcon, iconProps["class"]]
            }, _ctx.ptm('pcAcceptButton')['icon'], {
              "data-pc-section": "acceptbuttonicon"
            }), null, 16)];
          })];
        }),
        key: "0"
      } : undefined]), 1040, ["label", "class", "autofocus", "unstyled", "pt"])];
    }),
    key: "1"
  } : undefined]), 1032, ["visible", "class", "modal", "header", "blockScroll", "appendTo", "position", "breakpoints", "closeOnEscape", "draggable", "onUpdate:visible", "pt", "unstyled"]);
}

script.render = render;

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "ConfirmDialog",
  setup(__props, { expose: __expose }) {
    __expose();
    const theme = ref({
      root: `max-h-[90%] max-w-screen rounded-xl
        border border-surface-200 dark:border-surface-700
        bg-surface-0 dark:bg-surface-900
        text-surface-700 dark:text-surface-0 shadow-lg`,
      mask: `bg-black/50 fixed top-0 start-0 w-full h-full`,
      transition: {
        enterFromClass: "opacity-0 scale-75",
        enterActiveClass: "transition-all duration-150 ease-[cubic-bezier(0,0,0.2,1)]",
        leaveActiveClass: "transition-all duration-150 ease-[cubic-bezier(0.4,0,0.2,1)]",
        leaveToClass: "opacity-0 scale-75"
      }
    });
    const __returned__ = { theme, get ConfirmDialog() {
      return script;
    }, SecondaryButton, get ptViewMerge() {
      return ptViewMerge;
    }, get AlertCircle() {
      return AlertCircle;
    }, get XCircleIcon() {
      return XCircleIcon;
    }, DangerButton, get Trash2Icon() {
      return Trash2Icon;
    }, get Undo2Icon() {
      return Undo2Icon;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["ConfirmDialog"], mergeProps({
    unstyled: "",
    pt: $setup.theme,
    ptOptions: {
      mergeProps: $setup.ptViewMerge
    }
  }, _attrs), {
    container: withCtx(({ message, acceptCallback, rejectCallback }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex items-center justify-between shrink-0 p-5"${_scopeId}><span class="font-semibold text-xl"${_scopeId}>${ssrInterpolate(message.header)}</span>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          variant: "text",
          rounded: "",
          onClick: rejectCallback,
          autofocus: ""
        }, {
          default: withCtx((_, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["XCircleIcon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
            } else {
              return [
                createVNode($setup["XCircleIcon"], { class: "w-4 h-4" })
              ];
            }
          }),
          _: 2
        }, _parent2, _scopeId));
        _push2(`</div><div class="overflow-y-auto pt-0 px-5 pb-5 flex items-center gap-4"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["AlertCircle"], { class: "w-4 h-4" }, null, _parent2, _scopeId));
        _push2(` ${ssrInterpolate(message.message)}</div><div class="pt-0 px-5 pb-5 flex justify-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          onClick: rejectCallback,
          size: "small"
        }, {
          default: withCtx((_, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["Undo2Icon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              _push3(` ${ssrInterpolate(message.rejectProps?.label)}`);
            } else {
              return [
                createVNode($setup["Undo2Icon"], { class: "w-4 h-4" }),
                createTextVNode(" " + toDisplayString(message.rejectProps?.label), 1)
              ];
            }
          }),
          _: 2
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["DangerButton"], {
          onClick: acceptCallback,
          size: "small"
        }, {
          default: withCtx((_, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["Trash2Icon"], { class: "w-4 h-4" }, null, _parent3, _scopeId2));
              _push3(` ${ssrInterpolate(message.acceptProps?.label)}`);
            } else {
              return [
                createVNode($setup["Trash2Icon"], { class: "w-4 h-4" }),
                createTextVNode(" " + toDisplayString(message.acceptProps?.label), 1)
              ];
            }
          }),
          _: 2
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex items-center justify-between shrink-0 p-5" }, [
            createVNode("span", { class: "font-semibold text-xl" }, toDisplayString(message.header), 1),
            createVNode($setup["SecondaryButton"], {
              variant: "text",
              rounded: "",
              onClick: rejectCallback,
              autofocus: ""
            }, {
              default: withCtx(() => [
                createVNode($setup["XCircleIcon"], { class: "w-4 h-4" })
              ]),
              _: 2
            }, 1032, ["onClick"])
          ]),
          createVNode("div", { class: "overflow-y-auto pt-0 px-5 pb-5 flex items-center gap-4" }, [
            createVNode($setup["AlertCircle"], { class: "w-4 h-4" }),
            createTextVNode(" " + toDisplayString(message.message), 1)
          ]),
          createVNode("div", { class: "pt-0 px-5 pb-5 flex justify-end gap-2" }, [
            createVNode($setup["SecondaryButton"], {
              onClick: rejectCallback,
              size: "small"
            }, {
              default: withCtx(() => [
                createVNode($setup["Undo2Icon"], { class: "w-4 h-4" }),
                createTextVNode(" " + toDisplayString(message.rejectProps?.label), 1)
              ]),
              _: 2
            }, 1032, ["onClick"]),
            createVNode($setup["DangerButton"], {
              onClick: acceptCallback,
              size: "small"
            }, {
              default: withCtx(() => [
                createVNode($setup["Trash2Icon"], { class: "w-4 h-4" }),
                createTextVNode(" " + toDisplayString(message.acceptProps?.label), 1)
              ]),
              _: 2
            }, 1032, ["onClick"])
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/volt/ConfirmDialog.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const ConfirmDialog = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { ConfirmDialog as C, useConfirm as u };

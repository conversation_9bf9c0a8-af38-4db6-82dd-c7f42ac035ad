<template>
  <div>
    <label v-if="label" class="mb-2 block text-sm font-medium">{{ label }}</label>
    <MultiSelect
      v-model="modelValueProxy"
      :options="options || []"
      :option-label="optionLabel"
      :option-value="optionValue"
      :placeholder="placeholder"
      @change="$emit('update:modelValue', modelValueProxy)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MultiSelect from '@/volt/MultiSelect.vue'

type Option = Record<string, any>

const props = withDefaults(defineProps<{
  modelValue: any[]
  options: Option[] | null | undefined
  optionLabel?: string
  optionValue?: string
  label?: string
  placeholder?: string
}>(), {
  optionLabel: 'name',
  optionValue: 'id',
  placeholder: 'Выберите...'
})

const emit = defineEmits<{
  'update:modelValue': [value: any[]]
}>()

const modelValueProxy = computed({
  get: () => props.modelValue || [],
  set: (v: any[]) => emit('update:modelValue', v || [])
})
</script>


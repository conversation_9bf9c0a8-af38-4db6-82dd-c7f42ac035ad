import{c as s}from"./createLucideIcon.NtN1-Ts2.js";import{_ as n}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{d as u,c,o,e as i,a as _}from"./runtime-core.esm-bundler.CRb7Pg8a.js";import{t as h,n as m}from"./reactivity.esm-bundler.BQ12LWmY.js";/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=s("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l=s("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]]);/**
 * @license lucide-vue-next v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j=s("refresh-ccw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]]),f=u({__name:"MatchingEmptyState",setup(a,{expose:t}){t();const e={};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),y={class:"text-center py-10 text-surface-500"};function k(a,t,e,r,p,d){return o(),c("div",y," Кандидаты не найдены ")}const B=n(f,[["render",k]]),g=u({__name:"MatchingLoadingState",props:{message:{default:"Выполняется подбор..."},paddingClass:{default:"py-10"}},setup(a,{expose:t}){t();const e={get LoaderIcon(){return l}};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),x={class:"mt-2 text-surface-500"};function M(a,t,e,r,p,d){return o(),c("div",{class:m(["text-center flex justify-center items-center gap-3",e.paddingClass])},[i(r.LoaderIcon,{class:"animate-spin"}),_("div",x,h(e.message),1)],2)}const D=n(g,[["render",M]]);export{$ as L,D as M,j as R,B as a};

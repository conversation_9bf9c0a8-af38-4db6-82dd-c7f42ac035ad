---
import Layout from '@/layouts/Layout.astro';
import StatsPageBoundary from '@/components/stats/StatsPageBoundary.vue';
import { trpc } from '@/lib/trpc';

// Загружаем начальную статистику
let initialStats;

try {
  // Получаем базовую статистику
  const [partsCount, brandsCount, categoriesCount] = await Promise.all([
    trpc.crud.part.count.query(),
    trpc.crud.brand.count.query(),
    trpc.crud.partCategory.count.query(),
  ]);

  initialStats = {
    parts: partsCount,
    brands: brandsCount,
    categories: categoriesCount,
  };
} catch (error) {
  console.error('Error loading stats initial data:', error);
  initialStats = {
    parts: 0,
    brands: 0,
    categories: 0,
  };
}
---

<Layout title="Статистика системы">
  <main class="container mx-auto px-4 py-8">
    <StatsPageBoundary
      client:load
      initialStats={initialStats}
    />
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>

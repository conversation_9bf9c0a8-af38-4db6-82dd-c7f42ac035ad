<template>
  <div class="relative w-full h-[520px] md:h-[720px] rounded-2xl border border-zinc-800 bg-gradient-to-b from-zinc-900/60 via-black to-zinc-950 overflow-hidden" ref="containerRef">
    <!-- Фоновая сетка и свечение -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_rgba(59,130,246,0.12),_transparent_60%)]" />
      <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_rgba(59,130,246,0.06),_transparent_30%)]" />
    </div>

    <!-- Убрали статичные рёбра графа для визуальной чистоты; оставляем только анимированные лучи -->

    <!-- Центральный хаб -->
    <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-20 text-center" ref="centerRef">
      <div class="relative">
        <div class="absolute -inset-8 rounded-full bg-blue-500/10 blur-2xl" />
        <Circle className="size-[80px] md:size-[96px] bg-blue-500 border-blue-300" label="Каталог PartTec">
          <CatalogIcon />
        </Circle>
      </div>
    </div>

    <!-- Узлы -->
    <div v-for="node in positionedNodes" :key="node.id" class="absolute z-10" :style="node.style" :ref="(el:any) => (getNodeRef(node.id).value = el)">
      <div class="relative">
        <div class="absolute inset-0 -m-3 rounded-full bg-blue-500/10 blur-xl" />
        <Circle :className="node.className">
          <component :is="node.icon" />
        </Circle>
      </div>
      <div class="absolute -translate-x-1/2 -translate-y-1/2 z-30" :style="node.labelStyle">
        <div class="text-white text-[10px] md:text-xs font-medium px-1.5 py-0.5 md:px-2 md:py-1 bg-zinc-800/80 rounded-lg border border-zinc-700 whitespace-nowrap shadow-sm">
          {{ node.label }}
        </div>
      </div>
    </div>

    <!-- Анимированные лучи между узлами и центром -->
    <AnimatedBeam
      v-for="node in beamNodes"
      :key="`beam-${node.id}`"
      :containerRef="containerRef"
      :fromRef="node.ref"
      :toRef="centerRef"
      :curvature="120"
      :pathColor="'rgba(96,165,250,0.15)'"
      :pathOpacity="0.2"
      :pathWidth="2"
      :gradientStartColor="'#60a5fa'"
      :gradientStopColor="'#a78bfa'"
      :delay="node.delay"
      :duration="6"
    />

    <!-- Информационный блок -->
    <div class="absolute bottom-4 left-4 right-4">
      <div class="bg-zinc-900/80 backdrop-blur-xl border border-zinc-800 rounded-xl p-4 md:p-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        <div>
          <h4 class="text-white font-semibold mb-1 md:mb-2">Глобальная сеть</h4>
          <p class="text-gray-100 text-xs md:text-sm">Производители, интеграторы и заказчики объединены единой моделью данных и связями совместимости.</p>
        </div>
        <div>
          <h4 class="text-white font-semibold mb-1 md:mb-2">Стандартизированные атрибуты</h4>
          <p class="text-gray-100 text-xs md:text-sm">Единицы измерения, синонимы и классификаторы обеспечивают точный подбор аналогов.</p>
        </div>
        <div>
          <h4 class="text-white font-semibold mb-1 md:mb-2">Прозрачные интеграции</h4>
          <p class="text-gray-100 text-xs md:text-sm">API для ERP/CRM и складских систем. Отслеживание происхождения данных и аудит.</p>
        </div>
      </div>
    </div>
  </div>
  
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, type Ref, type Component as VueComponent } from 'vue'
import Circle from './Circle.vue'
import AnimatedBeam from './AnimatedBeam.vue'
import CaterpillarIcon from './icons/CaterpillarIcon.vue'
import JohnDeereIcon from './icons/JohnDeereIcon.vue'
import KomatsuIcon from './icons/KomatsuIcon.vue'
import VolvoIcon from './icons/VolvoIcon.vue'
import LiebherrIcon from './icons/LiebherrIcon.vue'
import CatalogIcon from './icons/CatalogIcon.vue'
import UserIcon from './icons/UserIcon.vue'

interface GraphNode {
  id: string
  label: string
  icon: VueComponent
  angleDeg: number
  radiusRatio: number
  className?: string
}

const containerRef = ref<HTMLDivElement>()
const size = ref({ width: 0, height: 0 })

const center = computed(() => ({ x: size.value.width / 2, y: size.value.height / 2 }))
const centerRef = ref<HTMLDivElement>()

const baseNodes: GraphNode[] = [
  { id: 'cat', label: 'Caterpillar', icon: CaterpillarIcon, angleDeg: -15, radiusRatio: 0.58 },
  { id: 'jd', label: 'John Deere', icon: JohnDeereIcon, angleDeg: -55, radiusRatio: 0.62 },
  { id: 'kom', label: 'Komatsu', icon: KomatsuIcon, angleDeg: -135, radiusRatio: 0.60 },
  { id: 'vol', label: 'Volvo', icon: VolvoIcon, angleDeg: 165, radiusRatio: 0.56 },
  { id: 'lieb', label: 'Liebherr', icon: LiebherrIcon, angleDeg: 115, radiusRatio: 0.64 },
  { id: 'sup', label: 'Поставщик', icon: UserIcon, angleDeg: 35, radiusRatio: 0.52, className: 'bg-emerald-100 border-emerald-300' },
  { id: 'cust', label: 'Заказчик', icon: UserIcon, angleDeg: 80, radiusRatio: 0.50, className: 'bg-teal-100 border-teal-300' },
]

const positionedNodes = computed(() => {
  const w = size.value.width
  const h = size.value.height
  const minSide = Math.min(w, h)
  const baseRadius = (minSide / 2) - 110 // визуально раздвигаем узлы от центра

  return baseNodes.map((n) => {
    const theta = (n.angleDeg * Math.PI) / 180
    const r = baseRadius * n.radiusRatio
    const x = w / 2 + r * Math.cos(theta)
    const y = h / 2 + r * Math.sin(theta)
    return {
      ...n,
      style: {
        left: `${x}px`,
        top: `${y}px`,
        transform: 'translate(-50%, -50%)'
      },
      labelStyle: {
        left: `${x}px`,
        top: `${y + 46}px`
      }
    }
  })
})

const edges = computed(() => positionedNodes.value.map((n) => ({ id: `e-${n.id}`, to: { x: parseFloat((n.style.left as string)), y: parseFloat((n.style.top as string)) } })))

// Бимы: refs для каждого узла (нереактивный словарь во избежание циклов рендера)
const nodeRefs: Record<string, Ref<HTMLElement | null> | undefined> = {}
const getNodeRef = (id: string): Ref<HTMLElement | null> => {
  if (!nodeRefs[id]) nodeRefs[id] = ref<HTMLElement | null>(null)
  return nodeRefs[id] as Ref<HTMLElement | null>
}
const beamNodes = computed(() => positionedNodes.value.map((n, idx) => ({ id: n.id, ref: getNodeRef(n.id), delay: idx * 0.3 })))

const recalc = () => {
  const el = containerRef.value
  if (!el) return
  size.value = { width: el.clientWidth, height: el.clientHeight }
}

onMounted(() => {
  recalc()
  window.addEventListener('resize', recalc)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', recalc)
})
</script>

<style scoped>
@keyframes dash {
  0% { stroke-dashoffset: 24; opacity: 0.6; }
  50% { stroke-dashoffset: 0; opacity: 1; }
  100% { stroke-dashoffset: 24; opacity: 0.6; }
}
</style>
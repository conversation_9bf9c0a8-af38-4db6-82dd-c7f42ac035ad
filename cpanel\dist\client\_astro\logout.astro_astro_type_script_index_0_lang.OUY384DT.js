import{n as r}from"./router.DKcY2uv6.js";import{b as e}from"./auth-client.CMFsScx1.js";import"./reactivity.esm-bundler.BQ12LWmY.js";import"./types.C07aSKae.js";async function n(){try{console.log("🔄 Выполняется выход из системы...");const o=await e.signOut();o.error?console.error("❌ Ошибка при выходе:",o.error):console.log("✅ Успешный выход из системы")}catch(o){console.error("❌ Ошибка при выходе:",o)}finally{setTimeout(()=>{r("/admin/login")},1e3)}}typeof window<"u"&&n();

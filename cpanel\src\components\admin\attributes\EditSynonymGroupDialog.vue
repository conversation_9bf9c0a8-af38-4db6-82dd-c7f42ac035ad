<template>
  <VDialog v-model:visible="visible" modal :header="dialogTitle" :style="{ width: '34rem' }">
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Название *</label>
        <VInputText v-model="form.name" placeholder="Стандартные типы уплотнений" class="w-full" :class="{ 'p-invalid': !!errors.name }" />
        <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
      </div>
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Описание</label>
        <VTextarea v-model="form.description" rows="2" class="w-full" />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Уровень совместимости</label>
          <VSelect v-model="form.compatibilityLevel" :options="compatibilityOptions" option-label="label" option-value="value" class="w-full" />
        </div>
        <div>
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">Заметки</label>
          <VInputText v-model="form.notes" placeholder="Например: для старых спецификаций" class="w-full" />
        </div>
      </div>
    </div>
    <template #footer>
      <VButton label="Отмена" severity="secondary" @click="close" />
      <VButton :label="isEdit ? 'Сохранить' : 'Создать'" :loading="saving" @click="save" />
    </template>
  </VDialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import VDialog from '@/volt/Dialog.vue'
import VInputText from '@/volt/InputText.vue'
import VTextarea from '@/volt/Textarea.vue'
import VButton from '@/volt/Button.vue'
import VSelect from '@/volt/Select.vue'
import { useTrpc } from '@/composables/useTrpc'
import { useToast } from '@/composables/useToast'

const props = defineProps<{ visible: boolean; templateId: number; group?: any | null }>()
const emit = defineEmits<{ 'update:visible': [boolean]; saved: [] }>()

const { attributeSynonyms } = useTrpc()
const toast = useToast()

const visible = computed({
  get: () => props.visible,
  set: (v: boolean) => emit('update:visible', v)
})

const isEdit = computed(() => !!props.group?.id)
const dialogTitle = computed(() => isEdit.value ? 'Редактировать группу' : 'Создать группу')

const compatibilityOptions = [
  { label: 'EXACT', value: 'EXACT' },
  { label: 'NEAR', value: 'NEAR' },
  { label: 'LEGACY', value: 'LEGACY' }
]

const form = ref({
  name: '',
  description: '' as string | null,
  compatibilityLevel: 'EXACT' as 'EXACT' | 'NEAR' | 'LEGACY',
  notes: '' as string | null,
})

const errors = ref<Record<string, string>>({})
const saving = ref(false)

watch(() => props.group, (g) => {
  if (g) {
    form.value = {
      name: g.name || '',
      description: g.description || null,
      compatibilityLevel: g.compatibilityLevel || 'EXACT',
      notes: g.notes || null
    }
  } else {
    form.value = { name: '', description: null, compatibilityLevel: 'EXACT', notes: null }
  }
}, { immediate: true })

const validate = () => {
  errors.value = {}
  if (!form.value.name.trim()) errors.value.name = 'Введите название'
  return Object.keys(errors.value).length === 0
}

const close = () => { visible.value = false }

const save = async () => {
  if (!validate()) return
  saving.value = true
  try {
    if (isEdit.value) {
      await attributeSynonyms.groups.update({ id: props.group!.id, ...form.value })
      toast.success('Группа обновлена')
    } else {
      await attributeSynonyms.groups.create({ templateId: props.templateId, ...form.value })
      toast.success('Группа создана')
    }
    emit('saved')
    close()
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось сохранить группу')
  } finally {
    saving.value = false
  }
}
</script>



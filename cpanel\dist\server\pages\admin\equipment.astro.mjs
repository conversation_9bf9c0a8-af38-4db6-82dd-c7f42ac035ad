import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_DbndhTWv.mjs';
import 'kleur/colors';
import { defineComponent, useSSRContext, mergeModels, ref, useModel, watch, onMounted, mergeProps, withCtx, createVNode, computed, createTextVNode, createBlock, createCommentVNode, toDisplayString, openBlock, resolveComponent, Fragment, renderList, createSlots } from 'vue';
import { t as trpc } from '../../chunks/trpc_DApR3DD7.mjs';
import { B as Button } from '../../chunks/Button_0V33JvkC.mjs';
import { D as DataTable, s as script } from '../../chunks/index_CxapvcaX.mjs';
import { XIcon, CheckIcon, TrashIcon, PencilIcon, InfoIcon, SearchIcon, TagsIcon, GridIcon, TableIcon, ChevronUpIcon, ChevronDownIcon, PlusIcon, ExternalLinkIcon } from 'lucide-vue-next';
import { D as Dialog } from '../../chunks/Dialog_DqmfICId.mjs';
import { I as InputText } from '../../chunks/InputText_DNFWprlB.mjs';
import { D as Dropdown } from '../../chunks/Dropdown_BiXUc1_B.mjs';
import { S as SecondaryButton } from '../../chunks/SecondaryButton_B0hmlm1n.mjs';
import { ssrRenderComponent, ssrRenderAttrs, ssrRenderList, ssrInterpolate, ssrRenderClass, ssrRenderAttr } from 'vue/server-renderer';
import { _ as _export_sfc, u as useToast } from '../../chunks/ClientRouter_avhRMbqw.mjs';
import { T as Tag } from '../../chunks/Tag_B6nH2bAR.mjs';
import { C as Card } from '../../chunks/Card_aE2_b9LT.mjs';
import { V as VAutoComplete } from '../../chunks/AutoComplete_BeMdq3W3.mjs';
import { A as AttributeValueInput } from '../../chunks/AttributeValueInput_DQ37vvbe.mjs';
import { z } from 'zod';
import { I as Icon, n as navigate, $ as $$AdminLayout } from '../../chunks/AdminLayout_DrlBSzRq.mjs';
/* empty css                                    */
import '../../chunks/InputNumber_B4WnM2Ea.mjs';
import { S as Select } from '../../chunks/Select_DIHmHCCM.mjs';
import '../../chunks/Checkbox_Ca7GoCvq.mjs';
import '../../chunks/Textarea_nBNQZgaf.mjs';
import { M as Message } from '../../chunks/Message_acgdACvd.mjs';
import { u as useTrpc } from '../../chunks/useTrpc_CjmFMz0m.mjs';
export { r as renderers } from '../../chunks/_@astro-renderers_CicWY1rm.mjs';

const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "EditEquipmentDialog",
  props: /* @__PURE__ */ mergeModels({
    equipment: {}
  }, {
    "isVisible": { type: Boolean, ...{ required: true } },
    "isVisibleModifiers": {}
  }),
  emits: /* @__PURE__ */ mergeModels(["save", "cancel"], ["update:isVisible"]),
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const brandOptions = ref([]);
    const isVisible = useModel(__props, "isVisible");
    const props = __props;
    const emit = __emit;
    const localEquipment = ref({});
    const dialogTitle = ref("\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043C\u043E\u0434\u0435\u043B\u044C \u0442\u0435\u0445\u043D\u0438\u043A\u0438");
    watch(isVisible, (newValue) => {
      if (newValue) {
        localEquipment.value = { ...props.equipment || {} };
        dialogTitle.value = props.equipment?.id ? `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C: ${props.equipment.name}` : "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043C\u043E\u0434\u0435\u043B\u044C \u0442\u0435\u0445\u043D\u0438\u043A\u0438";
      }
    });
    function handleCancel() {
      isVisible.value = false;
      emit("cancel");
    }
    function handleSave() {
      emit("save", { ...localEquipment.value });
      isVisible.value = false;
    }
    async function loadBrands() {
      const brands = await trpc.crud.brand.findMany.query({
        select: {
          id: true,
          name: true
        },
        orderBy: {
          name: "asc"
        }
      });
      brandOptions.value = brands;
    }
    onMounted(() => {
      loadBrands();
    });
    const __returned__ = { brandOptions, isVisible, props, emit, localEquipment, dialogTitle, handleCancel, handleSave, loadBrands, Button, Dialog, InputText, Dropdown, SecondaryButton };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$4(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(ssrRenderComponent($setup["Dialog"], mergeProps({
    visible: $setup.isVisible,
    "onUpdate:visible": ($event) => $setup.isVisible = $event,
    modal: "",
    header: $setup.dialogTitle,
    class: "sm:w-100 w-9/10"
  }, _attrs), {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex flex-col gap-4 py-4"${_scopeId}><div class="flex flex-col"${_scopeId}><label for="name"${_scopeId}>\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435 \u043C\u043E\u0434\u0435\u043B\u0438</label>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          id: "name",
          modelValue: $setup.localEquipment.name,
          "onUpdate:modelValue": ($event) => $setup.localEquipment.name = $event,
          placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u042D\u043A\u0441\u043A\u0430\u0432\u0430\u0442\u043E\u0440 CAT 320D"
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex flex-col"${_scopeId}><label for="brand"${_scopeId}>\u0411\u0440\u0435\u043D\u0434</label>`);
        _push2(ssrRenderComponent($setup["Dropdown"], {
          id: "brand",
          modelValue: $setup.localEquipment.brandId,
          "onUpdate:modelValue": ($event) => $setup.localEquipment.brandId = $event,
          options: $setup.brandOptions,
          optionLabel: "name",
          optionValue: "id",
          placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0431\u0440\u0435\u043D\u0434",
          showClear: ""
        }, null, _parent2, _scopeId));
        _push2(`</div></div><div class="flex justify-end gap-2"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["SecondaryButton"], {
          type: "button",
          label: "Cancel",
          onClick: $setup.handleCancel
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          type: "button",
          label: "Save",
          onClick: $setup.handleSave
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex flex-col gap-4 py-4" }, [
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "name" }, "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435 \u043C\u043E\u0434\u0435\u043B\u0438"),
              createVNode($setup["InputText"], {
                id: "name",
                modelValue: $setup.localEquipment.name,
                "onUpdate:modelValue": ($event) => $setup.localEquipment.name = $event,
                placeholder: "\u041D\u0430\u043F\u0440\u0438\u043C\u0435\u0440: \u042D\u043A\u0441\u043A\u0430\u0432\u0430\u0442\u043E\u0440 CAT 320D"
              }, null, 8, ["modelValue", "onUpdate:modelValue"])
            ]),
            createVNode("div", { class: "flex flex-col" }, [
              createVNode("label", { for: "brand" }, "\u0411\u0440\u0435\u043D\u0434"),
              createVNode($setup["Dropdown"], {
                id: "brand",
                modelValue: $setup.localEquipment.brandId,
                "onUpdate:modelValue": ($event) => $setup.localEquipment.brandId = $event,
                options: $setup.brandOptions,
                optionLabel: "name",
                optionValue: "id",
                placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0431\u0440\u0435\u043D\u0434",
                showClear: ""
              }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
            ])
          ]),
          createVNode("div", { class: "flex justify-end gap-2" }, [
            createVNode($setup["SecondaryButton"], {
              type: "button",
              label: "Cancel",
              onClick: $setup.handleCancel
            }),
            createVNode($setup["Button"], {
              type: "button",
              label: "Save",
              onClick: $setup.handleSave
            })
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/equipment/EditEquipmentDialog.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const EditEquipmentDialog = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["ssrRender", _sfc_ssrRender$4]]);

const baseSchema$2 = z.object(
  {
    id: z.number(),
    value: z.string(),
    numericValue: z.number().nullish()
  }
).strict();
const relationSchema$2 = z.object(
  {
    equipmentModel: z.record(z.unknown()),
    template: z.record(z.unknown())
  }
);
const fkSchema$1 = z.object(
  {
    equipmentModelId: z.string(),
    templateId: z.number()
  }
);
const EquipmentModelAttributeScalarSchema = baseSchema$2;
EquipmentModelAttributeScalarSchema.merge(fkSchema$1).merge(relationSchema$2.partial());
baseSchema$2.partial().passthrough();
z.object({
  id: z.union([z.number(), z.record(z.unknown())]),
  value: z.string(),
  numericValue: z.union([z.number().nullish(), z.record(z.unknown())])
}).partial().passthrough();
const EquipmentModelAttributeCreateScalarSchema = baseSchema$2.partial({
  id: true
});
EquipmentModelAttributeCreateScalarSchema.merge(fkSchema$1);
const EquipmentModelAttributeUpdateScalarSchema = baseSchema$2.partial();
EquipmentModelAttributeUpdateScalarSchema.merge(fkSchema$1.partial());

const AttributeDataTypeSchema = z.enum(["STRING", "NUMBER", "BOOLEAN", "DATE", "JSON"]);

const AttributeUnitSchema = z.enum(["MM", "INCH", "FT", "G", "KG", "T", "LB", "ML", "L", "GAL", "SEC", "MIN", "H", "PCS", "SET", "PAIR", "BAR", "PSI", "KW", "HP", "NM", "RPM", "C", "F", "PERCENT"]);

const baseSchema$1 = z.object(
  {
    id: z.number(),
    name: z.string(),
    title: z.string(),
    description: z.string().nullish(),
    dataType: AttributeDataTypeSchema,
    unit: AttributeUnitSchema.nullish(),
    isRequired: z.boolean().default(false),
    minValue: z.number().nullish(),
    maxValue: z.number().nullish(),
    allowedValues: z.array(z.string()),
    tolerance: z.number().default(0).nullish(),
    createdAt: z.coerce.date().default(() => /* @__PURE__ */ new Date()),
    updatedAt: z.coerce.date()
  }
).strict();
const relationSchema$1 = z.object(
  {
    group: z.record(z.unknown()).optional(),
    partAttributes: z.array(z.unknown()).optional(),
    catalogItemAttributes: z.array(z.unknown()).optional(),
    equipmentAttributes: z.array(z.unknown()).optional(),
    synonymGroups: z.array(z.unknown()).optional()
  }
);
const fkSchema = z.object(
  {
    groupId: z.number().nullish()
  }
);
const AttributeTemplateScalarSchema = baseSchema$1;
AttributeTemplateScalarSchema.merge(fkSchema).merge(relationSchema$1.partial());
baseSchema$1.partial().passthrough();
z.object({
  id: z.union([z.number(), z.record(z.unknown())]),
  name: z.string(),
  title: z.string(),
  description: z.string().nullish(),
  dataType: AttributeDataTypeSchema,
  unit: AttributeUnitSchema.nullish(),
  isRequired: z.boolean().default(false),
  minValue: z.union([z.number().nullish(), z.record(z.unknown())]),
  maxValue: z.union([z.number().nullish(), z.record(z.unknown())]),
  allowedValues: z.array(z.string()),
  tolerance: z.union([z.number().default(0).nullish(), z.record(z.unknown())]),
  createdAt: z.coerce.date().default(() => /* @__PURE__ */ new Date()),
  updatedAt: z.coerce.date()
}).partial().passthrough();
const AttributeTemplateCreateScalarSchema = baseSchema$1.partial({
  id: true,
  dataType: true,
  isRequired: true,
  allowedValues: true,
  tolerance: true,
  createdAt: true,
  updatedAt: true
});
AttributeTemplateCreateScalarSchema.merge(fkSchema);
const AttributeTemplateUpdateScalarSchema = baseSchema$1.partial();
AttributeTemplateUpdateScalarSchema.merge(fkSchema.partial());

const baseSchema = z.object(
  {
    id: z.number(),
    name: z.string(),
    description: z.string().nullish()
  }
).strict();
const relationSchema = z.object(
  {
    templates: z.array(z.unknown()).optional()
  }
);
const AttributeGroupScalarSchema = baseSchema;
AttributeGroupScalarSchema.merge(relationSchema.partial());
baseSchema.partial().passthrough();
z.object({
  id: z.union([z.number(), z.record(z.unknown())]),
  name: z.string(),
  description: z.string().nullish()
}).partial().passthrough();
baseSchema.partial({
  id: true
});
baseSchema.partial({
  id: true
});
baseSchema.partial();

const createAttributeValueSchema = (template) => {
  switch (template.dataType) {
    case "STRING":
      let stringSchema = z.string().min(1, "Значение не может быть пустым");
      if (template.allowedValues && template.allowedValues.length > 0) {
        return z.enum(template.allowedValues);
      }
      return stringSchema;
    case "NUMBER":
      let numberSchema = z.number({
        required_error: "Значение обязательно",
        invalid_type_error: "Значение должно быть числом"
      });
      if (template.minValue !== null && template.minValue !== void 0) {
        numberSchema = numberSchema.min(template.minValue, `Минимальное значение: ${template.minValue}`);
      }
      if (template.maxValue !== null && template.maxValue !== void 0) {
        numberSchema = numberSchema.max(template.maxValue, `Максимальное значение: ${template.maxValue}`);
      }
      return numberSchema;
    case "BOOLEAN":
      return z.boolean();
    case "DATE":
      return z.date({
        required_error: "Дата обязательна",
        invalid_type_error: "Неверный формат даты"
      });
    case "JSON":
      return z.record(z.unknown()).or(z.array(z.unknown()));
    default:
      return z.string();
  }
};
z.object({
  templateId: z.number().min(1, "Выберите шаблон атрибута"),
  value: z.union([z.string(), z.number(), z.boolean(), z.date()]),
  equipmentModelId: z.string().min(1, "ID модели техники обязателен")
});
z.object({
  id: z.number().min(1, "ID атрибута обязателен"),
  value: z.union([z.string(), z.number(), z.boolean(), z.date()])
});

function formatAttributeValue(attribute) {
  const { value, template } = attribute;
  const { dataType, unit } = template;
  let displayValue;
  let rawValue;
  try {
    switch (dataType) {
      case "STRING":
        rawValue = value;
        displayValue = value;
        break;
      case "NUMBER":
        rawValue = parseFloat(value);
        displayValue = isNaN(rawValue) ? value : rawValue.toLocaleString("ru-RU");
        if (unit) {
          displayValue += ` ${getUnitDisplayName(unit)}`;
        }
        break;
      case "BOOLEAN":
        rawValue = value.toLowerCase() === "true";
        displayValue = rawValue ? "Да" : "Нет";
        break;
      case "DATE":
        rawValue = new Date(value);
        displayValue = isNaN(rawValue.getTime()) ? value : rawValue.toLocaleDateString("ru-RU");
        break;
      case "JSON":
        try {
          rawValue = JSON.parse(value);
          displayValue = JSON.stringify(rawValue, null, 2);
        } catch {
          rawValue = value;
          displayValue = value;
        }
        break;
      default:
        rawValue = value;
        displayValue = value;
    }
  } catch (error) {
    rawValue = value;
    displayValue = value;
  }
  return {
    displayValue,
    rawValue,
    unit,
    dataType
  };
}
function groupAttributes(attributes) {
  const grouped = {};
  attributes.forEach((attribute) => {
    const groupName = attribute.template.group?.name || "Общие";
    if (!grouped[groupName]) {
      grouped[groupName] = [];
    }
    grouped[groupName].push(attribute);
  });
  Object.keys(grouped).forEach((groupName) => {
    grouped[groupName].sort((a, b) => a.template.title.localeCompare(b.template.title, "ru"));
  });
  return grouped;
}
function getAvailableTemplates(allTemplates, existingAttributes, filter) {
  const usedTemplateIds = new Set(existingAttributes.map((attr) => attr.templateId));
  let availableTemplates = allTemplates.filter(
    (template) => !usedTemplateIds.has(template.id)
  );
  if (filter) {
    if (filter.excludeTemplateIds) {
      const excludeIds = new Set(filter.excludeTemplateIds);
      availableTemplates = availableTemplates.filter(
        (template) => !excludeIds.has(template.id)
      );
    }
    if (filter.groupId) {
      availableTemplates = availableTemplates.filter(
        (template) => template.groupId === filter.groupId
      );
    }
    if (filter.dataType) {
      availableTemplates = availableTemplates.filter(
        (template) => template.dataType === filter.dataType
      );
    }
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      availableTemplates = availableTemplates.filter(
        (template) => template.title.toLowerCase().includes(query) || template.name.toLowerCase().includes(query) || template.description && template.description.toLowerCase().includes(query)
      );
    }
  }
  return availableTemplates.sort((a, b) => a.title.localeCompare(b.title, "ru"));
}
function getUnitDisplayName(unit) {
  if (!unit) return "";
  const unitNames = {
    MM: "мм",
    INCH: "дюйм",
    FT: "фт",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунт",
    ML: "мл",
    L: "л",
    GAL: "гал",
    SEC: "сек",
    MIN: "мин",
    H: "ч",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "psi",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%"
  };
  return unitNames[unit] || unit;
}
function getDataTypeDisplayName(dataType) {
  const typeNames = {
    STRING: "Текст",
    NUMBER: "Число",
    BOOLEAN: "Да/Нет",
    DATE: "Дата",
    JSON: "JSON"
  };
  return typeNames[dataType] || dataType;
}
function convertValueToType(value, dataType) {
  switch (dataType) {
    case "NUMBER":
      const num = parseFloat(value);
      return isNaN(num) ? 0 : num;
    case "BOOLEAN":
      return value.toLowerCase() === "true" || value === "1" || value === "да";
    case "DATE":
      const date = new Date(value);
      return isNaN(date.getTime()) ? /* @__PURE__ */ new Date() : date;
    case "JSON":
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    default:
      return value;
  }
}

const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "EquipmentAttributesList",
  props: {
    attributes: {},
    readonly: { type: Boolean, default: false },
    showGroupColumn: { type: Boolean, default: true },
    showDataTypeColumn: { type: Boolean, default: false },
    compact: { type: Boolean, default: false },
    groupByTemplate: { type: Boolean, default: false }
  },
  emits: ["edit-attribute", "delete-attribute", "update-value"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const editingAttributeId = ref(null);
    const editingValue = ref(null);
    const tableData = computed(() => {
      return props.attributes.map((attribute) => {
        const formatted = formatAttributeValue(attribute);
        return {
          id: attribute.id,
          name: attribute.template.title,
          value: formatted.displayValue,
          rawValue: formatted.rawValue,
          unit: formatted.unit,
          dataType: attribute.template.dataType,
          dataTypeDisplay: getDataTypeDisplayName(attribute.template.dataType),
          group: attribute.template.group?.name || "\u041E\u0431\u0449\u0438\u0435",
          description: attribute.template.description,
          isRequired: attribute.template.isRequired,
          template: attribute.template,
          attribute
        };
      });
    });
    const groupedAttributes = computed(() => {
      if (!props.groupByTemplate) return {};
      return groupAttributes(props.attributes);
    });
    function handleEditAttribute(attributeId) {
      emit("edit-attribute", attributeId);
    }
    function handleDeleteAttribute(attributeId) {
      emit("delete-attribute", attributeId);
    }
    function getValueSeverity(dataType) {
      switch (dataType) {
        case "NUMBER":
          return "info";
        case "BOOLEAN":
          return "success";
        case "DATE":
          return "warning";
        case "JSON":
          return "secondary";
        default:
          return void 0;
      }
    }
    function formatValueForDisplay(item) {
      if (item.dataType === "BOOLEAN") {
        return item.rawValue ? "\u2713" : "\u2717";
      }
      if (item.dataType === "NUMBER" && item.unit) {
        const unitDisplay = getUnitDisplayName(item.unit);
        return `${item.rawValue.toLocaleString("ru-RU")} ${unitDisplay}`;
      }
      return item.value;
    }
    function startEditing(attribute) {
      editingAttributeId.value = attribute.id;
      editingValue.value = attribute.rawValue;
    }
    function cancelEditing() {
      editingAttributeId.value = null;
      editingValue.value = null;
    }
    function saveEditing(attributeId) {
      if (editingValue.value !== null && editingValue.value !== void 0) {
        emit("update-value", attributeId, editingValue.value);
      }
      cancelEditing();
    }
    function getDataTypeIcon(dataType) {
      const icons = {
        STRING: "pi pi-font",
        NUMBER: "pi pi-hashtag",
        BOOLEAN: "pi pi-check-square",
        DATE: "pi pi-calendar",
        JSON: "pi pi-code"
      };
      return icons[dataType] || "pi pi-question";
    }
    const __returned__ = { props, emit, editingAttributeId, editingValue, tableData, groupedAttributes, handleEditAttribute, handleDeleteAttribute, getValueSeverity, formatValueForDisplay, startEditing, cancelEditing, saveEditing, getDataTypeIcon, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, get CheckIcon() {
      return CheckIcon;
    }, get XIcon() {
      return XIcon;
    }, Button, Tag, DataTable, get Column() {
      return script;
    }, AttributeValueInput, get formatAttributeValue() {
      return formatAttributeValue;
    }, get getUnitDisplayName() {
      return getUnitDisplayName;
    }, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$3(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "equipment-attributes-list" }, _attrs))} data-v-b449289d>`);
  if ($props.groupByTemplate && Object.keys($setup.groupedAttributes).length > 0) {
    _push(`<div class="space-y-6" data-v-b449289d><!--[-->`);
    ssrRenderList($setup.groupedAttributes, (groupAttrs, groupName) => {
      _push(`<div class="attribute-group" data-v-b449289d><div class="flex items-center gap-2 mb-3 pb-2 border-b border-surface-200 dark:border-surface-700" data-v-b449289d><i class="pi pi-folder text-blue-600" data-v-b449289d></i><h4 class="font-medium text-surface-900 dark:text-surface-0" data-v-b449289d>${ssrInterpolate(groupName === "undefined" ? "\u0411\u0435\u0437 \u0433\u0440\u0443\u043F\u043F\u044B" : groupName)}</h4>`);
      _push(ssrRenderComponent($setup["Tag"], {
        value: `${groupAttrs.length} \u0430\u0442\u0440.`,
        severity: "secondary",
        size: "small"
      }, null, _parent));
      _push(`</div><div class="space-y-3" data-v-b449289d><!--[-->`);
      ssrRenderList(groupAttrs, (attribute) => {
        _push(`<div class="attribute-item" data-v-b449289d><div class="${ssrRenderClass([[
          attribute.value && String(attribute.value).trim() ? "border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10" : "border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10"
        ], "flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"])}" data-v-b449289d><div class="flex-shrink-0 relative" data-v-b449289d>`);
        _push(ssrRenderComponent($setup["Icon"], {
          name: $setup.getDataTypeIcon(attribute.template.dataType),
          class: "text-lg text-primary w-5 h-5"
        }, null, _parent));
        _push(`<div class="${ssrRenderClass([attribute.value && String(attribute.value).trim() ? "bg-green-500" : "bg-orange-500", "absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"])}"${ssrRenderAttr("title", attribute.value && String(attribute.value).trim() ? "\u0417\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E" : "\u041D\u0435 \u0437\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E")} data-v-b449289d></div></div><div class="flex-shrink-0 w-48" data-v-b449289d><div class="font-medium text-surface-900 dark:text-surface-0 text-sm" data-v-b449289d>${ssrInterpolate(attribute.template.title)} `);
        if (attribute.template.isRequired) {
          _push(ssrRenderComponent($setup["Tag"], {
            severity: "danger",
            class: "text-xs ml-1"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`*`);
              } else {
                return [
                  createTextVNode("*")
                ];
              }
            }),
            _: 2
          }, _parent));
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
        if (attribute.template.description) {
          _push(`<div class="text-xs text-surface-500 dark:text-surface-400 mt-1" data-v-b449289d>${ssrInterpolate(attribute.template.description)}</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="flex-1" data-v-b449289d>`);
        if ($setup.editingAttributeId === attribute.id) {
          _push(`<div class="flex items-center gap-2" data-v-b449289d>`);
          _push(ssrRenderComponent($setup["AttributeValueInput"], {
            modelValue: $setup.editingValue,
            "onUpdate:modelValue": ($event) => $setup.editingValue = $event,
            template: attribute.template,
            class: "flex-1",
            size: "small"
          }, null, _parent));
          _push(`<div class="flex gap-1" data-v-b449289d>`);
          _push(ssrRenderComponent($setup["Button"], {
            onClick: ($event) => $setup.saveEditing(attribute.id),
            size: "small",
            class: "p-1"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent($setup["CheckIcon"], { class: "w-3 h-3" }, null, _parent2, _scopeId));
              } else {
                return [
                  createVNode($setup["CheckIcon"], { class: "w-3 h-3" })
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(ssrRenderComponent($setup["Button"], {
            onClick: $setup.cancelEditing,
            severity: "secondary",
            size: "small",
            class: "p-1"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent($setup["XIcon"], { class: "w-3 h-3" }, null, _parent2, _scopeId));
              } else {
                return [
                  createVNode($setup["XIcon"], { class: "w-3 h-3" })
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div></div>`);
        } else {
          _push(`<div class="flex items-center gap-2 group" data-v-b449289d><span class="text-surface-900 dark:text-surface-0" data-v-b449289d>${ssrInterpolate($setup.formatAttributeValue(attribute).displayValue || "\u041D\u0435 \u0443\u043A\u0430\u0437\u0430\u043D\u043E")}</span>`);
          if (!$props.readonly) {
            _push(ssrRenderComponent($setup["Button"], {
              onClick: ($event) => $setup.startEditing(attribute),
              text: "",
              size: "small",
              class: "p-1 opacity-0 group-hover:opacity-100 transition-opacity"
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(ssrRenderComponent($setup["PencilIcon"], { class: "w-3 h-3" }, null, _parent2, _scopeId));
                } else {
                  return [
                    createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                  ];
                }
              }),
              _: 2
            }, _parent));
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
        }
        _push(`</div><div class="flex-shrink-0 w-16 text-center" data-v-b449289d>`);
        if (attribute.template.unit) {
          _push(`<span class="text-sm text-surface-500 font-medium" data-v-b449289d>${ssrInterpolate($setup.getUnitDisplayName(attribute.template.unit))}</span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
        if (!$props.readonly) {
          _push(`<div class="flex-shrink-0 flex gap-2" data-v-b449289d>`);
          _push(ssrRenderComponent($setup["Button"], {
            onClick: ($event) => $setup.handleEditAttribute(attribute.id),
            severity: "secondary",
            size: "small",
            outlined: "",
            class: "p-1"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent($setup["PencilIcon"], { class: "w-3 h-3" }, null, _parent2, _scopeId));
              } else {
                return [
                  createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(ssrRenderComponent($setup["Button"], {
            onClick: ($event) => $setup.handleDeleteAttribute(attribute.id),
            severity: "danger",
            size: "small",
            outlined: "",
            class: "p-1",
            disabled: attribute.template.isRequired
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(ssrRenderComponent($setup["TrashIcon"], { class: "w-3 h-3" }, null, _parent2, _scopeId));
              } else {
                return [
                  createVNode($setup["TrashIcon"], { class: "w-3 h-3" })
                ];
              }
            }),
            _: 2
          }, _parent));
          _push(`</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
      });
      _push(`<!--]--></div></div>`);
    });
    _push(`<!--]--></div>`);
  } else {
    _push(ssrRenderComponent($setup["DataTable"], {
      value: $setup.tableData,
      paginator: !$props.compact && $setup.tableData.length > 10,
      rows: $props.compact ? 5 : 10,
      rowsPerPageOptions: [5, 10, 25, 50],
      sortMode: "multiple",
      removableSort: "",
      loading: false,
      dataKey: "id",
      size: $props.compact ? "small" : "normal",
      class: ["p-datatable-sm", { "compact-table": $props.compact }]
    }, {
      empty: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="text-center py-6 text-surface-500 dark:text-surface-400" data-v-b449289d${_scopeId}><i class="pi pi-info-circle text-2xl mb-2 block" data-v-b449289d${_scopeId}></i><p class="text-sm" data-v-b449289d${_scopeId}>\u041D\u0435\u0442 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0434\u043B\u044F \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F</p></div>`);
        } else {
          return [
            createVNode("div", { class: "text-center py-6 text-surface-500 dark:text-surface-400" }, [
              createVNode("i", { class: "pi pi-info-circle text-2xl mb-2 block" }),
              createVNode("p", { class: "text-sm" }, "\u041D\u0435\u0442 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0434\u043B\u044F \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F")
            ])
          ];
        }
      }),
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["Column"], {
            field: "name",
            header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442",
            sortable: "",
            style: { minWidth: "200px" }
          }, {
            body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`<div class="flex items-start gap-2" data-v-b449289d${_scopeId2}><div class="flex-1 min-w-0" data-v-b449289d${_scopeId2}><div class="flex items-center gap-2 mb-1" data-v-b449289d${_scopeId2}><span class="font-medium text-surface-900 dark:text-surface-0 truncate" data-v-b449289d${_scopeId2}>${ssrInterpolate(data.name)}</span>`);
                if (data.isRequired) {
                  _push3(ssrRenderComponent($setup["Tag"], {
                    severity: "danger",
                    class: "text-xs flex-shrink-0"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(` \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 `);
                      } else {
                        return [
                          createTextVNode(" \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 ")
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                } else {
                  _push3(`<!---->`);
                }
                _push3(`</div>`);
                if (data.description && !$props.compact) {
                  _push3(`<div class="text-xs text-surface-500 dark:text-surface-400 line-clamp-2"${ssrRenderAttr("title", data.description)} data-v-b449289d${_scopeId2}>${ssrInterpolate(data.description)}</div>`);
                } else {
                  _push3(`<!---->`);
                }
                _push3(`</div></div>`);
              } else {
                return [
                  createVNode("div", { class: "flex items-start gap-2" }, [
                    createVNode("div", { class: "flex-1 min-w-0" }, [
                      createVNode("div", { class: "flex items-center gap-2 mb-1" }, [
                        createVNode("span", { class: "font-medium text-surface-900 dark:text-surface-0 truncate" }, toDisplayString(data.name), 1),
                        data.isRequired ? (openBlock(), createBlock($setup["Tag"], {
                          key: 0,
                          severity: "danger",
                          class: "text-xs flex-shrink-0"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 ")
                          ]),
                          _: 1
                        })) : createCommentVNode("", true)
                      ]),
                      data.description && !$props.compact ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "text-xs text-surface-500 dark:text-surface-400 line-clamp-2",
                        title: data.description
                      }, toDisplayString(data.description), 9, ["title"])) : createCommentVNode("", true)
                    ])
                  ])
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["Column"], {
            field: "value",
            header: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435",
            sortable: "",
            style: { minWidth: "150px" }
          }, {
            body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                if ($setup.editingAttributeId === data.id) {
                  _push3(`<div class="flex items-center gap-2" data-v-b449289d${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["AttributeValueInput"], {
                    modelValue: $setup.editingValue,
                    "onUpdate:modelValue": ($event) => $setup.editingValue = $event,
                    template: data.template,
                    class: "w-full",
                    size: "small"
                  }, null, _parent3, _scopeId2));
                  _push3(`<div class="flex gap-1" data-v-b449289d${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["Button"], {
                    onClick: ($event) => $setup.saveEditing(data.id),
                    size: "small",
                    class: "p-1"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["CheckIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["CheckIcon"], { class: "w-3 h-3" })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Button"], {
                    onClick: $setup.cancelEditing,
                    severity: "secondary",
                    size: "small",
                    class: "p-1"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["XIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["XIcon"], { class: "w-3 h-3" })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                } else {
                  _push3(`<div class="flex items-center gap-2 group" data-v-b449289d${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["Tag"], {
                    severity: $setup.getValueSeverity(data.dataType),
                    class: ["font-mono text-sm", {
                      "text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20": data.dataType === "BOOLEAN" && data.rawValue,
                      "text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20": data.dataType === "BOOLEAN" && !data.rawValue
                    }]
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate($setup.formatValueForDisplay(data))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString($setup.formatValueForDisplay(data)), 1)
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  if (!$props.readonly) {
                    _push3(ssrRenderComponent($setup["Button"], {
                      onClick: ($event) => $setup.startEditing(data),
                      text: "",
                      size: "small",
                      class: "p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    }, {
                      default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                        if (_push4) {
                          _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-3 h-3" }, null, _parent4, _scopeId3));
                        } else {
                          return [
                            createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                          ];
                        }
                      }),
                      _: 2
                    }, _parent3, _scopeId2));
                  } else {
                    _push3(`<!---->`);
                  }
                  if ($props.compact && !$props.showDataTypeColumn) {
                    _push3(`<span class="text-xs text-surface-400 dark:text-surface-500"${ssrRenderAttr("title", `\u0422\u0438\u043F: ${data.dataTypeDisplay}`)} data-v-b449289d${_scopeId2}>${ssrInterpolate(data.dataType)}</span>`);
                  } else {
                    _push3(`<!---->`);
                  }
                  _push3(`</div>`);
                }
              } else {
                return [
                  $setup.editingAttributeId === data.id ? (openBlock(), createBlock("div", {
                    key: 0,
                    class: "flex items-center gap-2"
                  }, [
                    createVNode($setup["AttributeValueInput"], {
                      modelValue: $setup.editingValue,
                      "onUpdate:modelValue": ($event) => $setup.editingValue = $event,
                      template: data.template,
                      class: "w-full",
                      size: "small"
                    }, null, 8, ["modelValue", "onUpdate:modelValue", "template"]),
                    createVNode("div", { class: "flex gap-1" }, [
                      createVNode($setup["Button"], {
                        onClick: ($event) => $setup.saveEditing(data.id),
                        size: "small",
                        class: "p-1"
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["CheckIcon"], { class: "w-3 h-3" })
                        ]),
                        _: 2
                      }, 1032, ["onClick"]),
                      createVNode($setup["Button"], {
                        onClick: $setup.cancelEditing,
                        severity: "secondary",
                        size: "small",
                        class: "p-1"
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["XIcon"], { class: "w-3 h-3" })
                        ]),
                        _: 1
                      })
                    ])
                  ])) : (openBlock(), createBlock("div", {
                    key: 1,
                    class: "flex items-center gap-2 group"
                  }, [
                    createVNode($setup["Tag"], {
                      severity: $setup.getValueSeverity(data.dataType),
                      class: ["font-mono text-sm", {
                        "text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20": data.dataType === "BOOLEAN" && data.rawValue,
                        "text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20": data.dataType === "BOOLEAN" && !data.rawValue
                      }]
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString($setup.formatValueForDisplay(data)), 1)
                      ]),
                      _: 2
                    }, 1032, ["severity", "class"]),
                    !$props.readonly ? (openBlock(), createBlock($setup["Button"], {
                      key: 0,
                      onClick: ($event) => $setup.startEditing(data),
                      text: "",
                      size: "small",
                      class: "p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"])) : createCommentVNode("", true),
                    $props.compact && !$props.showDataTypeColumn ? (openBlock(), createBlock("span", {
                      key: 1,
                      class: "text-xs text-surface-400 dark:text-surface-500",
                      title: `\u0422\u0438\u043F: ${data.dataTypeDisplay}`
                    }, toDisplayString(data.dataType), 9, ["title"])) : createCommentVNode("", true)
                  ]))
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          if ($props.showGroupColumn) {
            _push2(ssrRenderComponent($setup["Column"], {
              field: "group",
              header: "\u0413\u0440\u0443\u043F\u043F\u0430",
              sortable: "",
              style: { minWidth: "120px" }
            }, {
              body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Tag"], {
                    severity: "secondary",
                    class: "text-xs"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<i class="pi pi-folder text-xs mr-1" data-v-b449289d${_scopeId3}></i> ${ssrInterpolate(data.group)}`);
                      } else {
                        return [
                          createVNode("i", { class: "pi pi-folder text-xs mr-1" }),
                          createTextVNode(" " + toDisplayString(data.group), 1)
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Tag"], {
                      severity: "secondary",
                      class: "text-xs"
                    }, {
                      default: withCtx(() => [
                        createVNode("i", { class: "pi pi-folder text-xs mr-1" }),
                        createTextVNode(" " + toDisplayString(data.group), 1)
                      ]),
                      _: 2
                    }, 1024)
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
          if ($props.showDataTypeColumn) {
            _push2(ssrRenderComponent($setup["Column"], {
              field: "dataTypeDisplay",
              header: "\u0422\u0438\u043F",
              sortable: "",
              style: { minWidth: "100px" }
            }, {
              body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent($setup["Tag"], {
                    severity: $setup.getValueSeverity(data.dataType),
                    class: "text-xs"
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`${ssrInterpolate(data.dataTypeDisplay)}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString(data.dataTypeDisplay), 1)
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode($setup["Tag"], {
                      severity: $setup.getValueSeverity(data.dataType),
                      class: "text-xs"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(data.dataTypeDisplay), 1)
                      ]),
                      _: 2
                    }, 1032, ["severity"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
          if (!$props.readonly) {
            _push2(ssrRenderComponent($setup["Column"], {
              field: "actions",
              header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
              sortable: false,
              style: { minWidth: "120px", width: "120px" }
            }, {
              body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="flex items-center gap-1" data-v-b449289d${_scopeId2}>`);
                  _push3(ssrRenderComponent($setup["Button"], {
                    onClick: ($event) => $setup.handleEditAttribute(data.id),
                    text: "",
                    size: "small",
                    class: "p-2",
                    title: `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ${data.name}`
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-4 h-4" }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(ssrRenderComponent($setup["Button"], {
                    onClick: ($event) => $setup.handleDeleteAttribute(data.id),
                    text: "",
                    severity: "danger",
                    size: "small",
                    class: "p-2",
                    title: `\u0423\u0434\u0430\u043B\u0438\u0442\u044C ${data.name}`,
                    disabled: data.isRequired
                  }, {
                    default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-4 h-4" }, null, _parent4, _scopeId3));
                      } else {
                        return [
                          createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                        ];
                      }
                    }),
                    _: 2
                  }, _parent3, _scopeId2));
                  _push3(`</div>`);
                } else {
                  return [
                    createVNode("div", { class: "flex items-center gap-1" }, [
                      createVNode($setup["Button"], {
                        onClick: ($event) => $setup.handleEditAttribute(data.id),
                        text: "",
                        size: "small",
                        class: "p-2",
                        title: `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ${data.name}`
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick", "title"]),
                      createVNode($setup["Button"], {
                        onClick: ($event) => $setup.handleDeleteAttribute(data.id),
                        text: "",
                        severity: "danger",
                        size: "small",
                        class: "p-2",
                        title: `\u0423\u0434\u0430\u043B\u0438\u0442\u044C ${data.name}`,
                        disabled: data.isRequired
                      }, {
                        default: withCtx(() => [
                          createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                        ]),
                        _: 2
                      }, 1032, ["onClick", "title", "disabled"])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            _push2(`<!---->`);
          }
        } else {
          return [
            createVNode($setup["Column"], {
              field: "name",
              header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442",
              sortable: "",
              style: { minWidth: "200px" }
            }, {
              body: withCtx(({ data }) => [
                createVNode("div", { class: "flex items-start gap-2" }, [
                  createVNode("div", { class: "flex-1 min-w-0" }, [
                    createVNode("div", { class: "flex items-center gap-2 mb-1" }, [
                      createVNode("span", { class: "font-medium text-surface-900 dark:text-surface-0 truncate" }, toDisplayString(data.name), 1),
                      data.isRequired ? (openBlock(), createBlock($setup["Tag"], {
                        key: 0,
                        severity: "danger",
                        class: "text-xs flex-shrink-0"
                      }, {
                        default: withCtx(() => [
                          createTextVNode(" \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 ")
                        ]),
                        _: 1
                      })) : createCommentVNode("", true)
                    ]),
                    data.description && !$props.compact ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "text-xs text-surface-500 dark:text-surface-400 line-clamp-2",
                      title: data.description
                    }, toDisplayString(data.description), 9, ["title"])) : createCommentVNode("", true)
                  ])
                ])
              ]),
              _: 1
            }),
            createVNode($setup["Column"], {
              field: "value",
              header: "\u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435",
              sortable: "",
              style: { minWidth: "150px" }
            }, {
              body: withCtx(({ data }) => [
                $setup.editingAttributeId === data.id ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "flex items-center gap-2"
                }, [
                  createVNode($setup["AttributeValueInput"], {
                    modelValue: $setup.editingValue,
                    "onUpdate:modelValue": ($event) => $setup.editingValue = $event,
                    template: data.template,
                    class: "w-full",
                    size: "small"
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "template"]),
                  createVNode("div", { class: "flex gap-1" }, [
                    createVNode($setup["Button"], {
                      onClick: ($event) => $setup.saveEditing(data.id),
                      size: "small",
                      class: "p-1"
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["CheckIcon"], { class: "w-3 h-3" })
                      ]),
                      _: 2
                    }, 1032, ["onClick"]),
                    createVNode($setup["Button"], {
                      onClick: $setup.cancelEditing,
                      severity: "secondary",
                      size: "small",
                      class: "p-1"
                    }, {
                      default: withCtx(() => [
                        createVNode($setup["XIcon"], { class: "w-3 h-3" })
                      ]),
                      _: 1
                    })
                  ])
                ])) : (openBlock(), createBlock("div", {
                  key: 1,
                  class: "flex items-center gap-2 group"
                }, [
                  createVNode($setup["Tag"], {
                    severity: $setup.getValueSeverity(data.dataType),
                    class: ["font-mono text-sm", {
                      "text-green-700 bg-green-50 dark:text-green-300 dark:bg-green-900/20": data.dataType === "BOOLEAN" && data.rawValue,
                      "text-red-700 bg-red-50 dark:text-red-300 dark:bg-red-900/20": data.dataType === "BOOLEAN" && !data.rawValue
                    }]
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString($setup.formatValueForDisplay(data)), 1)
                    ]),
                    _: 2
                  }, 1032, ["severity", "class"]),
                  !$props.readonly ? (openBlock(), createBlock($setup["Button"], {
                    key: 0,
                    onClick: ($event) => $setup.startEditing(data),
                    text: "",
                    size: "small",
                    class: "p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilIcon"], { class: "w-3 h-3" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])) : createCommentVNode("", true),
                  $props.compact && !$props.showDataTypeColumn ? (openBlock(), createBlock("span", {
                    key: 1,
                    class: "text-xs text-surface-400 dark:text-surface-500",
                    title: `\u0422\u0438\u043F: ${data.dataTypeDisplay}`
                  }, toDisplayString(data.dataType), 9, ["title"])) : createCommentVNode("", true)
                ]))
              ]),
              _: 1
            }),
            $props.showGroupColumn ? (openBlock(), createBlock($setup["Column"], {
              key: 0,
              field: "group",
              header: "\u0413\u0440\u0443\u043F\u043F\u0430",
              sortable: "",
              style: { minWidth: "120px" }
            }, {
              body: withCtx(({ data }) => [
                createVNode($setup["Tag"], {
                  severity: "secondary",
                  class: "text-xs"
                }, {
                  default: withCtx(() => [
                    createVNode("i", { class: "pi pi-folder text-xs mr-1" }),
                    createTextVNode(" " + toDisplayString(data.group), 1)
                  ]),
                  _: 2
                }, 1024)
              ]),
              _: 1
            })) : createCommentVNode("", true),
            $props.showDataTypeColumn ? (openBlock(), createBlock($setup["Column"], {
              key: 1,
              field: "dataTypeDisplay",
              header: "\u0422\u0438\u043F",
              sortable: "",
              style: { minWidth: "100px" }
            }, {
              body: withCtx(({ data }) => [
                createVNode($setup["Tag"], {
                  severity: $setup.getValueSeverity(data.dataType),
                  class: "text-xs"
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(data.dataTypeDisplay), 1)
                  ]),
                  _: 2
                }, 1032, ["severity"])
              ]),
              _: 1
            })) : createCommentVNode("", true),
            !$props.readonly ? (openBlock(), createBlock($setup["Column"], {
              key: 2,
              field: "actions",
              header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F",
              sortable: false,
              style: { minWidth: "120px", width: "120px" }
            }, {
              body: withCtx(({ data }) => [
                createVNode("div", { class: "flex items-center gap-1" }, [
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.handleEditAttribute(data.id),
                    text: "",
                    size: "small",
                    class: "p-2",
                    title: `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ${data.name}`
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilIcon"], { class: "w-4 h-4" })
                    ]),
                    _: 2
                  }, 1032, ["onClick", "title"]),
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.handleDeleteAttribute(data.id),
                    text: "",
                    severity: "danger",
                    size: "small",
                    class: "p-2",
                    title: `\u0423\u0434\u0430\u043B\u0438\u0442\u044C ${data.name}`,
                    disabled: data.isRequired
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["TrashIcon"], { class: "w-4 h-4" })
                    ]),
                    _: 2
                  }, 1032, ["onClick", "title", "disabled"])
                ])
              ]),
              _: 1
            })) : createCommentVNode("", true)
          ];
        }
      }),
      _: 1
    }, _parent));
  }
  _push(`</div>`);
}
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/equipment/EquipmentAttributesList.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const EquipmentAttributesList = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["ssrRender", _sfc_ssrRender$3], ["__scopeId", "data-v-b449289d"]]);

const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "AddEquipmentAttributeDialog",
  props: {
    visible: { type: Boolean },
    equipmentId: {},
    existingAttributes: { default: () => [] }
  },
  emits: ["update:visible", "save", "cancel"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { $trpc } = useTrpc();
    const toast = useToast();
    const formData = ref({
      templateId: 0,
      value: "",
      template: void 0
    });
    const loading = ref(false);
    const saving = ref(false);
    const searchQuery = ref("");
    const selectedGroupId = ref(null);
    const selectedDataType = ref(null);
    const validationErrors = ref([]);
    const allTemplates = ref([]);
    const attributeGroups = ref([]);
    const isVisible = computed({
      get: () => props.visible,
      set: (value) => emit("update:visible", value)
    });
    const availableTemplates = computed(() => {
      const filter = {
        searchQuery: searchQuery.value || void 0,
        groupId: selectedGroupId.value || void 0,
        dataType: selectedDataType.value || void 0,
        excludeTemplateIds: props.existingAttributes.map((attr) => attr.templateId)
      };
      return getAvailableTemplates(allTemplates.value, props.existingAttributes, filter);
    });
    const selectedTemplate = computed(() => {
      return allTemplates.value.find((t) => t.id === formData.value.templateId) || null;
    });
    const isFormValid = computed(() => {
      return formData.value.templateId > 0 && formData.value.value !== null && formData.value.value !== "" && validationErrors.value.length === 0;
    });
    const dataTypeOptions = computed(() => {
      const types = ["STRING", "NUMBER", "BOOLEAN", "DATE", "JSON"];
      return types.map((type) => ({
        label: getDataTypeDisplayName(type),
        value: type
      }));
    });
    const groupOptions = computed(() => {
      return [
        { label: "\u0412\u0441\u0435 \u0433\u0440\u0443\u043F\u043F\u044B", value: null },
        ...attributeGroups.value.map((group) => ({
          label: group.name,
          value: group.id
        }))
      ];
    });
    async function loadTemplates() {
      loading.value = true;
      try {
        const templates = await $trpc.crud.attributeTemplate.findMany.query({
          include: {
            group: true
          },
          orderBy: [
            { group: { name: "asc" } },
            { title: "asc" }
          ]
        });
        allTemplates.value = templates;
      } catch (error) {
        toast.error("\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432");
        console.error("Failed to load templates:", error);
      } finally {
        loading.value = false;
      }
    }
    async function loadAttributeGroups() {
      try {
        const groups = await $trpc.crud.attributeGroup.findMany.query({
          orderBy: { name: "asc" }
        });
        attributeGroups.value = groups;
      } catch (error) {
        console.error("Failed to load attribute groups:", error);
      }
    }
    function selectTemplate(template) {
      formData.value.templateId = template.id;
      formData.value.template = template;
      resetValue();
      searchQuery.value = "";
    }
    function resetValue() {
      const template = selectedTemplate.value;
      if (!template) {
        formData.value.value = "";
        return;
      }
      switch (template.dataType) {
        case "STRING":
          formData.value.value = "";
          break;
        case "NUMBER":
          formData.value.value = template.minValue || 0;
          break;
        case "BOOLEAN":
          formData.value.value = false;
          break;
        case "DATE":
          formData.value.value = /* @__PURE__ */ new Date();
          break;
        case "JSON":
          formData.value.value = "";
          break;
        default:
          formData.value.value = "";
      }
      validateValue();
    }
    function validateValue() {
      validationErrors.value = [];
      const template = selectedTemplate.value;
      if (!template || formData.value.value === null || formData.value.value === "") {
        return;
      }
      try {
        const schema = createAttributeValueSchema(template);
        const convertedValue = convertValueToType(String(formData.value.value), template.dataType);
        schema.parse(convertedValue);
      } catch (error) {
        if (error.errors) {
          validationErrors.value = error.errors.map((err) => err.message);
        } else {
          validationErrors.value = ["\u041D\u0435\u0432\u0435\u0440\u043D\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430"];
        }
      }
    }
    async function handleSave() {
      if (!isFormValid.value) return;
      saving.value = true;
      try {
        const template = selectedTemplate.value;
        const convertedValue = convertValueToType(String(formData.value.value), template.dataType);
        const saveData = {
          templateId: formData.value.templateId,
          value: convertedValue,
          template
        };
        emit("save", saveData);
        handleCancel();
      } catch (error) {
        toast.error("\u041E\u0448\u0438\u0431\u043A\u0430 \u043F\u0440\u0438 \u0441\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u0438 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430");
        console.error("Save error:", error);
      } finally {
        saving.value = false;
      }
    }
    function handleCancel() {
      formData.value = {
        templateId: 0,
        value: "",
        template: void 0
      };
      searchQuery.value = "";
      selectedGroupId.value = null;
      selectedDataType.value = null;
      validationErrors.value = [];
      emit("cancel");
    }
    function clearFilters() {
      searchQuery.value = "";
      selectedGroupId.value = null;
      selectedDataType.value = null;
    }
    function getTemplateDisplayInfo(template) {
      const parts = [];
      if (template.group) {
        parts.push(template.group.name);
      }
      parts.push(getDataTypeDisplayName(template.dataType));
      if (template.unit) {
        parts.push(getUnitDisplayName(template.unit));
      }
      if (template.isRequired) {
        parts.push("\u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439");
      }
      return parts.join(" \u2022 ");
    }
    watch(() => props.visible, (newVisible) => {
      if (newVisible) {
        loadTemplates();
        loadAttributeGroups();
      }
    });
    watch(() => formData.value.value, () => {
      validateValue();
    }, { deep: true });
    watch(() => formData.value.templateId, () => {
      validateValue();
    });
    onMounted(() => {
      if (props.visible) {
        loadTemplates();
        loadAttributeGroups();
      }
    });
    const __returned__ = { props, emit, $trpc, toast, formData, loading, saving, searchQuery, selectedGroupId, selectedDataType, validationErrors, allTemplates, attributeGroups, isVisible, availableTemplates, selectedTemplate, isFormValid, dataTypeOptions, groupOptions, loadTemplates, loadAttributeGroups, selectTemplate, resetValue, validateValue, handleSave, handleCancel, clearFilters, getTemplateDisplayInfo, get SearchIcon() {
      return SearchIcon;
    }, get XIcon() {
      return XIcon;
    }, get InfoIcon() {
      return InfoIcon;
    }, Dialog, Button, InputText, Select, Tag, Message, AttributeValueInput };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$2(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  const _component_Icon = resolveComponent("Icon");
  _push(ssrRenderComponent($setup["Dialog"], mergeProps({
    visible: $setup.isVisible,
    "onUpdate:visible": ($event) => $setup.isVisible = $event,
    modal: "",
    closable: true,
    draggable: false,
    class: "w-full max-w-4xl",
    header: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442"
  }, _attrs), {
    footer: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-end gap-2" data-v-cb27c7ef${_scopeId}>`);
        _push2(ssrRenderComponent($setup["Button"], {
          onClick: $setup.handleCancel,
          outlined: "",
          disabled: $setup.saving
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(` \u041E\u0442\u043C\u0435\u043D\u0430 `);
            } else {
              return [
                createTextVNode(" \u041E\u0442\u043C\u0435\u043D\u0430 ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Button"], {
          onClick: $setup.handleSave,
          disabled: !$setup.isFormValid || $setup.saving,
          loading: $setup.saving
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate($setup.saving ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u0435..." : "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442")}`);
            } else {
              return [
                createTextVNode(toDisplayString($setup.saving ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u0435..." : "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-end gap-2" }, [
            createVNode($setup["Button"], {
              onClick: $setup.handleCancel,
              outlined: "",
              disabled: $setup.saving
            }, {
              default: withCtx(() => [
                createTextVNode(" \u041E\u0442\u043C\u0435\u043D\u0430 ")
              ]),
              _: 1
            }, 8, ["disabled"]),
            createVNode($setup["Button"], {
              onClick: $setup.handleSave,
              disabled: !$setup.isFormValid || $setup.saving,
              loading: $setup.saving
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString($setup.saving ? "\u0421\u043E\u0445\u0440\u0430\u043D\u0435\u043D\u0438\u0435..." : "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442"), 1)
              ]),
              _: 1
            }, 8, ["disabled", "loading"])
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-6" data-v-cb27c7ef${_scopeId}><div data-v-cb27c7ef${_scopeId}><h6 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3" data-v-cb27c7ef${_scopeId}> \u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430 </h6><div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-surface-50 dark:bg-surface-800 rounded-lg" data-v-cb27c7ef${_scopeId}><div class="relative" data-v-cb27c7ef${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.searchQuery,
          "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E...",
          class: "w-full pl-10"
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["SearchIcon"], { class: "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-surface-400" }, null, _parent2, _scopeId));
        _push2(`</div>`);
        _push2(ssrRenderComponent($setup["Select"], {
          modelValue: $setup.selectedGroupId,
          "onUpdate:modelValue": ($event) => $setup.selectedGroupId = $event,
          options: $setup.groupOptions,
          "option-label": "label",
          "option-value": "value",
          placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443",
          class: "w-full",
          "show-clear": true
        }, null, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Select"], {
          modelValue: $setup.selectedDataType,
          "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
          options: $setup.dataTypeOptions,
          "option-label": "label",
          "option-value": "value",
          placeholder: "\u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445",
          class: "w-full",
          "show-clear": true
        }, null, _parent2, _scopeId));
        _push2(`</div><div class="flex justify-between items-center mb-4" data-v-cb27c7ef${_scopeId}><span class="text-sm text-surface-600 dark:text-surface-400" data-v-cb27c7ef${_scopeId}> \u041D\u0430\u0439\u0434\u0435\u043D\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432: ${ssrInterpolate($setup.availableTemplates.length)}</span>`);
        _push2(ssrRenderComponent($setup["Button"], {
          onClick: $setup.clearFilters,
          text: "",
          size: "small",
          class: "text-sm"
        }, {
          default: withCtx((_2, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(ssrRenderComponent($setup["XIcon"], { class: "w-4 h-4 mr-1" }, null, _parent3, _scopeId2));
              _push3(` \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B `);
            } else {
              return [
                createVNode($setup["XIcon"], { class: "w-4 h-4 mr-1" }),
                createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(`</div><div class="max-h-64 overflow-y-auto border border-surface-200 dark:border-surface-700 rounded-lg" data-v-cb27c7ef${_scopeId}>`);
        if ($setup.loading) {
          _push2(`<div class="p-4 text-center text-surface-500" data-v-cb27c7ef${_scopeId}>`);
          _push2(ssrRenderComponent(_component_Icon, {
            name: "pi pi-spinner pi-spin",
            class: "mr-2 inline-block"
          }, null, _parent2, _scopeId));
          _push2(` \u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432... </div>`);
        } else if ($setup.availableTemplates.length === 0) {
          _push2(`<div class="p-4 text-center text-surface-500" data-v-cb27c7ef${_scopeId}>`);
          _push2(ssrRenderComponent($setup["InfoIcon"], { class: "w-5 h-5 mx-auto mb-2" }, null, _parent2, _scopeId));
          _push2(`<p class="text-sm" data-v-cb27c7ef${_scopeId}>\u041D\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u044B\u0445 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432</p><p class="text-xs mt-1" data-v-cb27c7ef${_scopeId}>\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B \u0438\u043B\u0438 \u0432\u0441\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0443\u0436\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u044E\u0442\u0441\u044F</p></div>`);
        } else {
          _push2(`<div class="divide-y divide-surface-200 dark:divide-surface-700" data-v-cb27c7ef${_scopeId}><!--[-->`);
          ssrRenderList($setup.availableTemplates, (template) => {
            _push2(`<div class="${ssrRenderClass([{
              "bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary": $setup.formData.templateId === template.id
            }, "p-4 hover:bg-surface-50 dark:hover:bg-surface-800 cursor-pointer transition-colors"])}" data-v-cb27c7ef${_scopeId}><div class="flex items-start justify-between" data-v-cb27c7ef${_scopeId}><div class="flex-1" data-v-cb27c7ef${_scopeId}><div class="flex items-center gap-2 mb-1" data-v-cb27c7ef${_scopeId}><h6 class="font-medium text-surface-900 dark:text-surface-0" data-v-cb27c7ef${_scopeId}>${ssrInterpolate(template.title)}</h6>`);
            if (template.isRequired) {
              _push2(ssrRenderComponent($setup["Tag"], {
                severity: "danger",
                class: "text-xs"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 `);
                  } else {
                    return [
                      createTextVNode(" \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><p class="text-sm text-surface-600 dark:text-surface-400 mb-2" data-v-cb27c7ef${_scopeId}>${ssrInterpolate($setup.getTemplateDisplayInfo(template))}</p>`);
            if (template.description) {
              _push2(`<p class="text-xs text-surface-500 dark:text-surface-400 line-clamp-2" data-v-cb27c7ef${_scopeId}>${ssrInterpolate(template.description)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div>`);
            if ($setup.formData.templateId === template.id) {
              _push2(`<div class="ml-4" data-v-cb27c7ef${_scopeId}><i class="pi pi-check text-primary text-lg" data-v-cb27c7ef${_scopeId}></i></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div>`);
          });
          _push2(`<!--]--></div>`);
        }
        _push2(`</div></div>`);
        if ($setup.selectedTemplate) {
          _push2(`<div data-v-cb27c7ef${_scopeId}><h6 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3" data-v-cb27c7ef${_scopeId}> \u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0434\u043B\u044F &quot;${ssrInterpolate($setup.selectedTemplate.title)}&quot; </h6><div class="p-3 bg-surface-50 dark:bg-surface-800 rounded-lg mb-4" data-v-cb27c7ef${_scopeId}><div class="flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400" data-v-cb27c7ef${_scopeId}>`);
          _push2(ssrRenderComponent($setup["InfoIcon"], { class: "w-4 h-4" }, null, _parent2, _scopeId));
          _push2(`<span data-v-cb27c7ef${_scopeId}>${ssrInterpolate($setup.getTemplateDisplayInfo($setup.selectedTemplate))}</span></div>`);
          if ($setup.selectedTemplate.description) {
            _push2(`<p class="text-xs text-surface-500 dark:text-surface-400 mt-1" data-v-cb27c7ef${_scopeId}>${ssrInterpolate($setup.selectedTemplate.description)}</p>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div><div class="space-y-4" data-v-cb27c7ef${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" data-v-cb27c7ef${_scopeId}> \u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 </label>`);
          _push2(ssrRenderComponent($setup["AttributeValueInput"], {
            modelValue: $setup.formData.value,
            "onUpdate:modelValue": ($event) => $setup.formData.value = $event,
            template: $setup.selectedTemplate,
            placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430",
            class: "w-full"
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
          if ($setup.validationErrors.length > 0) {
            _push2(`<div class="mt-4" data-v-cb27c7ef${_scopeId}><!--[-->`);
            ssrRenderList($setup.validationErrors, (error) => {
              _push2(ssrRenderComponent($setup["Message"], {
                key: error,
                severity: "error",
                closable: false,
                class: "mb-2"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(`${ssrInterpolate(error)}`);
                  } else {
                    return [
                      createTextVNode(toDisplayString(error), 1)
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
            });
            _push2(`<!--]--></div>`);
          } else {
            _push2(`<!---->`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-6" }, [
            createVNode("div", null, [
              createVNode("h6", { class: "text-sm font-medium text-surface-900 dark:text-surface-0 mb-3" }, " \u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0448\u0430\u0431\u043B\u043E\u043D \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430 "),
              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-surface-50 dark:bg-surface-800 rounded-lg" }, [
                createVNode("div", { class: "relative" }, [
                  createVNode($setup["InputText"], {
                    modelValue: $setup.searchQuery,
                    "onUpdate:modelValue": ($event) => $setup.searchQuery = $event,
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044E...",
                    class: "w-full pl-10"
                  }, null, 8, ["modelValue", "onUpdate:modelValue"]),
                  createVNode($setup["SearchIcon"], { class: "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-surface-400" })
                ]),
                createVNode($setup["Select"], {
                  modelValue: $setup.selectedGroupId,
                  "onUpdate:modelValue": ($event) => $setup.selectedGroupId = $event,
                  options: $setup.groupOptions,
                  "option-label": "label",
                  "option-value": "value",
                  placeholder: "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0433\u0440\u0443\u043F\u043F\u0443",
                  class: "w-full",
                  "show-clear": true
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options"]),
                createVNode($setup["Select"], {
                  modelValue: $setup.selectedDataType,
                  "onUpdate:modelValue": ($event) => $setup.selectedDataType = $event,
                  options: $setup.dataTypeOptions,
                  "option-label": "label",
                  "option-value": "value",
                  placeholder: "\u0422\u0438\u043F \u0434\u0430\u043D\u043D\u044B\u0445",
                  class: "w-full",
                  "show-clear": true
                }, null, 8, ["modelValue", "onUpdate:modelValue", "options"])
              ]),
              createVNode("div", { class: "flex justify-between items-center mb-4" }, [
                createVNode("span", { class: "text-sm text-surface-600 dark:text-surface-400" }, " \u041D\u0430\u0439\u0434\u0435\u043D\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432: " + toDisplayString($setup.availableTemplates.length), 1),
                createVNode($setup["Button"], {
                  onClick: $setup.clearFilters,
                  text: "",
                  size: "small",
                  class: "text-sm"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["XIcon"], { class: "w-4 h-4 mr-1" }),
                    createTextVNode(" \u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B ")
                  ]),
                  _: 1
                })
              ]),
              createVNode("div", { class: "max-h-64 overflow-y-auto border border-surface-200 dark:border-surface-700 rounded-lg" }, [
                $setup.loading ? (openBlock(), createBlock("div", {
                  key: 0,
                  class: "p-4 text-center text-surface-500"
                }, [
                  createVNode(_component_Icon, {
                    name: "pi pi-spinner pi-spin",
                    class: "mr-2 inline-block"
                  }),
                  createTextVNode(" \u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432... ")
                ])) : $setup.availableTemplates.length === 0 ? (openBlock(), createBlock("div", {
                  key: 1,
                  class: "p-4 text-center text-surface-500"
                }, [
                  createVNode($setup["InfoIcon"], { class: "w-5 h-5 mx-auto mb-2" }),
                  createVNode("p", { class: "text-sm" }, "\u041D\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u044B\u0445 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432"),
                  createVNode("p", { class: "text-xs mt-1" }, "\u041F\u043E\u043F\u0440\u043E\u0431\u0443\u0439\u0442\u0435 \u0438\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0444\u0438\u043B\u044C\u0442\u0440\u044B \u0438\u043B\u0438 \u0432\u0441\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0443\u0436\u0435 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u044E\u0442\u0441\u044F")
                ])) : (openBlock(), createBlock("div", {
                  key: 2,
                  class: "divide-y divide-surface-200 dark:divide-surface-700"
                }, [
                  (openBlock(true), createBlock(Fragment, null, renderList($setup.availableTemplates, (template) => {
                    return openBlock(), createBlock("div", {
                      key: template.id,
                      class: ["p-4 hover:bg-surface-50 dark:hover:bg-surface-800 cursor-pointer transition-colors", {
                        "bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary": $setup.formData.templateId === template.id
                      }],
                      onClick: ($event) => $setup.selectTemplate(template)
                    }, [
                      createVNode("div", { class: "flex items-start justify-between" }, [
                        createVNode("div", { class: "flex-1" }, [
                          createVNode("div", { class: "flex items-center gap-2 mb-1" }, [
                            createVNode("h6", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(template.title), 1),
                            template.isRequired ? (openBlock(), createBlock($setup["Tag"], {
                              key: 0,
                              severity: "danger",
                              class: "text-xs"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(" \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 ")
                              ]),
                              _: 1
                            })) : createCommentVNode("", true)
                          ]),
                          createVNode("p", { class: "text-sm text-surface-600 dark:text-surface-400 mb-2" }, toDisplayString($setup.getTemplateDisplayInfo(template)), 1),
                          template.description ? (openBlock(), createBlock("p", {
                            key: 0,
                            class: "text-xs text-surface-500 dark:text-surface-400 line-clamp-2"
                          }, toDisplayString(template.description), 1)) : createCommentVNode("", true)
                        ]),
                        $setup.formData.templateId === template.id ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "ml-4"
                        }, [
                          createVNode("i", { class: "pi pi-check text-primary text-lg" })
                        ])) : createCommentVNode("", true)
                      ])
                    ], 10, ["onClick"]);
                  }), 128))
                ]))
              ])
            ]),
            $setup.selectedTemplate ? (openBlock(), createBlock("div", { key: 0 }, [
              createVNode("h6", { class: "text-sm font-medium text-surface-900 dark:text-surface-0 mb-3" }, ' \u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0434\u043B\u044F "' + toDisplayString($setup.selectedTemplate.title) + '" ', 1),
              createVNode("div", { class: "p-3 bg-surface-50 dark:bg-surface-800 rounded-lg mb-4" }, [
                createVNode("div", { class: "flex items-center gap-2 text-sm text-surface-600 dark:text-surface-400" }, [
                  createVNode($setup["InfoIcon"], { class: "w-4 h-4" }),
                  createVNode("span", null, toDisplayString($setup.getTemplateDisplayInfo($setup.selectedTemplate)), 1)
                ]),
                $setup.selectedTemplate.description ? (openBlock(), createBlock("p", {
                  key: 0,
                  class: "text-xs text-surface-500 dark:text-surface-400 mt-1"
                }, toDisplayString($setup.selectedTemplate.description), 1)) : createCommentVNode("", true)
              ]),
              createVNode("div", { class: "space-y-4" }, [
                createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0417\u043D\u0430\u0447\u0435\u043D\u0438\u0435 "),
                createVNode($setup["AttributeValueInput"], {
                  modelValue: $setup.formData.value,
                  "onUpdate:modelValue": ($event) => $setup.formData.value = $event,
                  template: $setup.selectedTemplate,
                  placeholder: "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430",
                  class: "w-full"
                }, null, 8, ["modelValue", "onUpdate:modelValue", "template"])
              ]),
              $setup.validationErrors.length > 0 ? (openBlock(), createBlock("div", {
                key: 0,
                class: "mt-4"
              }, [
                (openBlock(true), createBlock(Fragment, null, renderList($setup.validationErrors, (error) => {
                  return openBlock(), createBlock($setup["Message"], {
                    key: error,
                    severity: "error",
                    closable: false,
                    class: "mb-2"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(error), 1)
                    ]),
                    _: 2
                  }, 1024);
                }), 128))
              ])) : createCommentVNode("", true)
            ])) : createCommentVNode("", true)
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/equipment/AddEquipmentAttributeDialog.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const AddEquipmentAttributeDialog = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$2], ["__scopeId", "data-v-cb27c7ef"]]);

const COMPACT_LIMIT = 6;
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "EquipmentAttributesSection",
  props: {
    equipmentId: {},
    attributes: {},
    readonly: { type: Boolean, default: false }
  },
  emits: ["add-attribute", "edit-attribute", "delete-attribute"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose();
    const props = __props;
    const emit = __emit;
    const { client } = useTrpc();
    const showAllAttributes = ref(false);
    const viewMode = ref("grid");
    const showAddDialog = ref(false);
    const showGroupDialog = ref(false);
    const selectedTemplateGroup = ref(null);
    const selectedTemplate = ref(null);
    const groupSuggestions = ref([]);
    const templateSuggestions = ref([]);
    const templateGroups = ref([]);
    const loadingTemplates = ref(false);
    const groupedAttributes = computed(() => {
      if (!props.attributes) return {};
      return groupAttributes(props.attributes);
    });
    const hasAttributes = computed(() => {
      return props.attributes && props.attributes.length > 0;
    });
    const totalAttributesCount = computed(() => {
      return props.attributes?.length || 0;
    });
    const shouldShowExpandButton = computed(() => {
      return totalAttributesCount.value > COMPACT_LIMIT;
    });
    const visibleAttributes = computed(() => {
      if (!props.attributes) return [];
      if (showAllAttributes.value || totalAttributesCount.value <= COMPACT_LIMIT) {
        return props.attributes;
      }
      return props.attributes.slice(0, COMPACT_LIMIT);
    });
    const visibleGroupedAttributes = computed(() => {
      if (!visibleAttributes.value.length) return {};
      return groupAttributes(visibleAttributes.value);
    });
    const hiddenAttributesCount = computed(() => {
      return Math.max(0, totalAttributesCount.value - COMPACT_LIMIT);
    });
    const filledAttributesCount = computed(() => {
      return props.attributes?.filter((attr) => attr.value && String(attr.value).trim()).length || 0;
    });
    function handleAddAttribute() {
      showAddDialog.value = true;
    }
    function handleSaveAttribute(data) {
      emit("add-attribute", data);
      showAddDialog.value = false;
    }
    function handleCancelAddAttribute() {
      showAddDialog.value = false;
    }
    function handleEditAttribute(attributeId) {
      emit("edit-attribute", attributeId);
    }
    function handleDeleteAttribute(attributeId) {
      emit("delete-attribute", attributeId);
    }
    function toggleShowAll() {
      showAllAttributes.value = !showAllAttributes.value;
    }
    function toggleViewMode() {
      viewMode.value = viewMode.value === "grid" ? "table" : "grid";
    }
    function formatAttribute(attribute) {
      return formatAttributeValue(attribute);
    }
    const canManageAttributes = computed(() => {
      return !props.readonly;
    });
    async function filterGroups(event) {
      const query = event.query.toLowerCase();
      try {
        const groups = await client.crud.attributeGroup.findMany.query({
          where: {
            name: {
              contains: query,
              mode: "insensitive"
            }
          },
          take: 10
        });
        groupSuggestions.value = groups || [];
      } catch (error) {
        console.error("Error filtering groups:", error);
        groupSuggestions.value = [];
      }
    }
    async function filterTemplates(event) {
      const query = event.query.toLowerCase();
      try {
        const templates = await client.crud.attributeTemplate.findMany.query({
          where: {
            OR: [
              { title: { contains: query, mode: "insensitive" } },
              { name: { contains: query, mode: "insensitive" } }
            ]
          },
          include: {
            group: true
          },
          take: 10
        });
        templateSuggestions.value = templates || [];
      } catch (error) {
        console.error("Error filtering templates:", error);
        templateSuggestions.value = [];
      }
    }
    async function loadSelectedGroupTemplates() {
      if (!selectedTemplateGroup.value) return;
      loadingTemplates.value = true;
      try {
        const groupId = selectedTemplateGroup.value.id || selectedTemplateGroup.value;
        const templates = await client.crud.attributeTemplate.findMany.query({
          where: { groupId },
          include: { group: true }
        });
        if (templates) {
          for (const template of templates) {
            const attributeData = {
              templateId: template.id,
              value: getDefaultValueForType(template.dataType),
              template
            };
            emit("add-attribute", attributeData);
          }
        }
        selectedTemplateGroup.value = null;
      } catch (error) {
        console.error("Error loading group templates:", error);
      } finally {
        loadingTemplates.value = false;
      }
    }
    function addSingleTemplate() {
      if (!selectedTemplate.value) return;
      const template = selectedTemplate.value;
      const attributeData = {
        templateId: template.id,
        value: getDefaultValueForType(template.dataType),
        template
      };
      emit("add-attribute", attributeData);
      selectedTemplate.value = null;
    }
    async function loadTemplatesByGroupId(groupId) {
      loadingTemplates.value = true;
      try {
        const templates = await client.crud.attributeTemplate.findMany.query({
          where: { groupId },
          include: { group: true }
        });
        if (templates) {
          for (const template of templates) {
            const attributeData = {
              templateId: template.id,
              value: getDefaultValueForType(template.dataType),
              template
            };
            emit("add-attribute", attributeData);
          }
        }
        showGroupDialog.value = false;
      } catch (error) {
        console.error("Error loading templates by group:", error);
      } finally {
        loadingTemplates.value = false;
      }
    }
    function getDefaultValueForType(dataType) {
      switch (dataType) {
        case "STRING":
          return "";
        case "NUMBER":
          return 0;
        case "BOOLEAN":
          return false;
        case "DATE":
          return /* @__PURE__ */ new Date();
        case "JSON":
          return "";
        default:
          return "";
      }
    }
    function getDataTypeIcon(dataType) {
      const icons = {
        STRING: "pi pi-font",
        NUMBER: "pi pi-hashtag",
        BOOLEAN: "pi pi-check-square",
        DATE: "pi pi-calendar",
        JSON: "pi pi-code"
      };
      return icons[dataType] || "pi pi-question";
    }
    onMounted(async () => {
      try {
        const groups = await client.crud.attributeGroup.findMany.query({
          include: {
            _count: {
              select: { templates: true }
            }
          },
          orderBy: { name: "asc" }
        });
        templateGroups.value = groups || [];
      } catch (error) {
        console.error("Error loading template groups:", error);
      }
    });
    const __returned__ = { props, emit, client, showAllAttributes, COMPACT_LIMIT, viewMode, showAddDialog, showGroupDialog, selectedTemplateGroup, selectedTemplate, groupSuggestions, templateSuggestions, templateGroups, loadingTemplates, groupedAttributes, hasAttributes, totalAttributesCount, shouldShowExpandButton, visibleAttributes, visibleGroupedAttributes, hiddenAttributesCount, filledAttributesCount, handleAddAttribute, handleSaveAttribute, handleCancelAddAttribute, handleEditAttribute, handleDeleteAttribute, toggleShowAll, toggleViewMode, formatAttribute, canManageAttributes, filterGroups, filterTemplates, loadSelectedGroupTemplates, addSingleTemplate, loadTemplatesByGroupId, getDefaultValueForType, getDataTypeIcon, get PlusIcon() {
      return PlusIcon;
    }, get ChevronDownIcon() {
      return ChevronDownIcon;
    }, get ChevronUpIcon() {
      return ChevronUpIcon;
    }, get TableIcon() {
      return TableIcon;
    }, get GridIcon() {
      return GridIcon;
    }, get TagsIcon() {
      return TagsIcon;
    }, Button, Tag, Card, AutoComplete: VAutoComplete, Dialog, EquipmentAttributesList, AddEquipmentAttributeDialog, get groupAttributes() {
      return groupAttributes;
    }, get getDataTypeDisplayName() {
      return getDataTypeDisplayName;
    }, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "equipment-attributes-section" }, _attrs))} data-v-2cbb67a9><div class="flex items-center justify-between mb-4" data-v-2cbb67a9><div class="flex items-center gap-3" data-v-2cbb67a9><h5 class="font-semibold flex items-center gap-2 text-surface-900 dark:text-surface-0" data-v-2cbb67a9>`);
  _push(ssrRenderComponent($setup["Icon"], {
    name: "pi pi-list",
    class: "text-green-600 w-4 h-4"
  }, null, _parent));
  _push(` \u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u043C\u043E\u0434\u0435\u043B\u0438 </h5>`);
  if ($setup.totalAttributesCount > 0) {
    _push(ssrRenderComponent($setup["Tag"], {
      value: `${$setup.filledAttributesCount}/${$setup.totalAttributesCount} \u0437\u0430\u043F\u043E\u043B\u043D\u0435\u043D\u043E`,
      severity: $setup.filledAttributesCount === $setup.totalAttributesCount ? "success" : "warn",
      size: "small"
    }, null, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(`</div><div class="flex items-center gap-2" data-v-2cbb67a9>`);
  if ($setup.hasAttributes) {
    _push(ssrRenderComponent($setup["Button"], {
      onClick: $setup.toggleViewMode,
      text: "",
      size: "small",
      title: $setup.viewMode === "grid" ? "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043D\u0430 \u0442\u0430\u0431\u043B\u0438\u0447\u043D\u044B\u0439 \u0432\u0438\u0434" : "\u041F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043D\u0430 \u0441\u0435\u0442\u043E\u0447\u043D\u044B\u0439 \u0432\u0438\u0434"
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          if ($setup.viewMode === "grid") {
            _push2(ssrRenderComponent($setup["TableIcon"], { class: "w-4 h-4" }, null, _parent2, _scopeId));
          } else {
            _push2(ssrRenderComponent($setup["GridIcon"], { class: "w-4 h-4" }, null, _parent2, _scopeId));
          }
        } else {
          return [
            $setup.viewMode === "grid" ? (openBlock(), createBlock($setup["TableIcon"], {
              key: 0,
              class: "w-4 h-4"
            })) : (openBlock(), createBlock($setup["GridIcon"], {
              key: 1,
              class: "w-4 h-4"
            }))
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  if ($setup.canManageAttributes) {
    _push(ssrRenderComponent($setup["Button"], {
      onClick: ($event) => $setup.showGroupDialog = true,
      outlined: "",
      severity: "secondary",
      size: "small",
      class: "text-sm"
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["TagsIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
          _push2(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 `);
        } else {
          return [
            createVNode($setup["TagsIcon"], { class: "w-4 h-4 mr-1" }),
            createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443 ")
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  if ($setup.canManageAttributes) {
    _push(ssrRenderComponent($setup["Button"], {
      onClick: $setup.handleAddAttribute,
      size: "small",
      class: "text-sm"
    }, {
      default: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
          _push2(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442 `);
        } else {
          return [
            createVNode($setup["PlusIcon"], { class: "w-4 h-4 mr-1" }),
            createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442 ")
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  _push(`</div></div>`);
  if ($setup.canManageAttributes) {
    _push(ssrRenderComponent($setup["Card"], { class: "mb-4" }, {
      content: withCtx((_, _push2, _parent2, _scopeId) => {
        if (_push2) {
          _push2(`<div class="p-4" data-v-2cbb67a9${_scopeId}><div class="grid grid-cols-1 md:grid-cols-3 gap-4" data-v-2cbb67a9${_scopeId}><div data-v-2cbb67a9${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" data-v-2cbb67a9${_scopeId}> \u0413\u0440\u0443\u043F\u043F\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 </label>`);
          _push2(ssrRenderComponent($setup["AutoComplete"], {
            modelValue: $setup.selectedTemplateGroup,
            "onUpdate:modelValue": ($event) => $setup.selectedTemplateGroup = $event,
            suggestions: $setup.groupSuggestions,
            onComplete: $setup.filterGroups,
            "option-label": "name",
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B...",
            class: "w-full",
            dropdown: ""
          }, null, _parent2, _scopeId));
          _push2(`</div><div data-v-2cbb67a9${_scopeId}><label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" data-v-2cbb67a9${_scopeId}> \u0418\u043B\u0438 \u043E\u0442\u0434\u0435\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D </label>`);
          _push2(ssrRenderComponent($setup["AutoComplete"], {
            modelValue: $setup.selectedTemplate,
            "onUpdate:modelValue": ($event) => $setup.selectedTemplate = $event,
            suggestions: $setup.templateSuggestions,
            onComplete: $setup.filterTemplates,
            "option-label": "title",
            placeholder: "\u041F\u043E\u0438\u0441\u043A \u0448\u0430\u0431\u043B\u043E\u043D\u0430...",
            class: "w-full",
            dropdown: ""
          }, {
            option: withCtx(({ option }, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(`<div class="flex items-center gap-2" data-v-2cbb67a9${_scopeId2}>`);
                _push3(ssrRenderComponent($setup["Icon"], {
                  name: $setup.getDataTypeIcon(option.dataType),
                  class: "text-primary w-4 h-4"
                }, null, _parent3, _scopeId2));
                _push3(`<div class="flex-1" data-v-2cbb67a9${_scopeId2}><div class="font-medium" data-v-2cbb67a9${_scopeId2}>${ssrInterpolate(option.title)}</div><div class="text-sm text-surface-600" data-v-2cbb67a9${_scopeId2}>${ssrInterpolate(option.group?.name)} \u2022 ${ssrInterpolate($setup.getDataTypeDisplayName(option.dataType))}</div></div></div>`);
              } else {
                return [
                  createVNode("div", { class: "flex items-center gap-2" }, [
                    createVNode($setup["Icon"], {
                      name: $setup.getDataTypeIcon(option.dataType),
                      class: "text-primary w-4 h-4"
                    }, null, 8, ["name"]),
                    createVNode("div", { class: "flex-1" }, [
                      createVNode("div", { class: "font-medium" }, toDisplayString(option.title), 1),
                      createVNode("div", { class: "text-sm text-surface-600" }, toDisplayString(option.group?.name) + " \u2022 " + toDisplayString($setup.getDataTypeDisplayName(option.dataType)), 1)
                    ])
                  ])
                ];
              }
            }),
            _: 1
          }, _parent2, _scopeId));
          _push2(`</div><div class="flex items-end gap-2" data-v-2cbb67a9${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Button"], {
            onClick: $setup.loadSelectedGroupTemplates,
            size: "small",
            outlined: "",
            disabled: !$setup.selectedTemplateGroup || $setup.loadingTemplates,
            loading: $setup.loadingTemplates,
            label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443",
            class: "flex-1"
          }, null, _parent2, _scopeId));
          _push2(ssrRenderComponent($setup["Button"], {
            onClick: $setup.addSingleTemplate,
            size: "small",
            outlined: "",
            disabled: !$setup.selectedTemplate,
            label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
            class: "flex-1"
          }, null, _parent2, _scopeId));
          _push2(`</div></div></div>`);
        } else {
          return [
            createVNode("div", { class: "p-4" }, [
              createVNode("div", { class: "grid grid-cols-1 md:grid-cols-3 gap-4" }, [
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0413\u0440\u0443\u043F\u043F\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 "),
                  createVNode($setup["AutoComplete"], {
                    modelValue: $setup.selectedTemplateGroup,
                    "onUpdate:modelValue": ($event) => $setup.selectedTemplateGroup = $event,
                    suggestions: $setup.groupSuggestions,
                    onComplete: $setup.filterGroups,
                    "option-label": "name",
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u0433\u0440\u0443\u043F\u043F\u044B...",
                    class: "w-full",
                    dropdown: ""
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
                ]),
                createVNode("div", null, [
                  createVNode("label", { class: "block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u0418\u043B\u0438 \u043E\u0442\u0434\u0435\u043B\u044C\u043D\u044B\u0439 \u0448\u0430\u0431\u043B\u043E\u043D "),
                  createVNode($setup["AutoComplete"], {
                    modelValue: $setup.selectedTemplate,
                    "onUpdate:modelValue": ($event) => $setup.selectedTemplate = $event,
                    suggestions: $setup.templateSuggestions,
                    onComplete: $setup.filterTemplates,
                    "option-label": "title",
                    placeholder: "\u041F\u043E\u0438\u0441\u043A \u0448\u0430\u0431\u043B\u043E\u043D\u0430...",
                    class: "w-full",
                    dropdown: ""
                  }, {
                    option: withCtx(({ option }) => [
                      createVNode("div", { class: "flex items-center gap-2" }, [
                        createVNode($setup["Icon"], {
                          name: $setup.getDataTypeIcon(option.dataType),
                          class: "text-primary w-4 h-4"
                        }, null, 8, ["name"]),
                        createVNode("div", { class: "flex-1" }, [
                          createVNode("div", { class: "font-medium" }, toDisplayString(option.title), 1),
                          createVNode("div", { class: "text-sm text-surface-600" }, toDisplayString(option.group?.name) + " \u2022 " + toDisplayString($setup.getDataTypeDisplayName(option.dataType)), 1)
                        ])
                      ])
                    ]),
                    _: 1
                  }, 8, ["modelValue", "onUpdate:modelValue", "suggestions"])
                ]),
                createVNode("div", { class: "flex items-end gap-2" }, [
                  createVNode($setup["Button"], {
                    onClick: $setup.loadSelectedGroupTemplates,
                    size: "small",
                    outlined: "",
                    disabled: !$setup.selectedTemplateGroup || $setup.loadingTemplates,
                    loading: $setup.loadingTemplates,
                    label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0433\u0440\u0443\u043F\u043F\u0443",
                    class: "flex-1"
                  }, null, 8, ["disabled", "loading"]),
                  createVNode($setup["Button"], {
                    onClick: $setup.addSingleTemplate,
                    size: "small",
                    outlined: "",
                    disabled: !$setup.selectedTemplate,
                    label: "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
                    class: "flex-1"
                  }, null, 8, ["disabled"])
                ])
              ])
            ])
          ];
        }
      }),
      _: 1
    }, _parent));
  } else {
    _push(`<!---->`);
  }
  if ($setup.hasAttributes) {
    _push(`<div data-v-2cbb67a9>`);
    if ($setup.viewMode === "table") {
      _push(`<div data-v-2cbb67a9>`);
      _push(ssrRenderComponent($setup["EquipmentAttributesList"], {
        attributes: $setup.visibleAttributes,
        readonly: $props.readonly,
        compact: !$setup.showAllAttributes && $setup.shouldShowExpandButton,
        onEditAttribute: $setup.handleEditAttribute,
        onDeleteAttribute: $setup.handleDeleteAttribute
      }, null, _parent));
      if ($setup.shouldShowExpandButton) {
        _push(`<div class="text-center pt-4" data-v-2cbb67a9>`);
        _push(ssrRenderComponent($setup["Button"], {
          onClick: $setup.toggleShowAll,
          text: "",
          size: "small",
          class: "text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              if (!$setup.showAllAttributes) {
                _push2(`<!--[-->`);
                _push2(ssrRenderComponent($setup["ChevronDownIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
                _push2(` \u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0432\u0441\u0435 (\u0435\u0449\u0435 ${ssrInterpolate($setup.hiddenAttributesCount)}) <!--]-->`);
              } else {
                _push2(`<!--[-->`);
                _push2(ssrRenderComponent($setup["ChevronUpIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
                _push2(` \u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C <!--]-->`);
              }
            } else {
              return [
                !$setup.showAllAttributes ? (openBlock(), createBlock(Fragment, { key: 0 }, [
                  createVNode($setup["ChevronDownIcon"], { class: "w-4 h-4 mr-1" }),
                  createTextVNode(" \u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0432\u0441\u0435 (\u0435\u0449\u0435 " + toDisplayString($setup.hiddenAttributesCount) + ") ", 1)
                ], 64)) : (openBlock(), createBlock(Fragment, { key: 1 }, [
                  createVNode($setup["ChevronUpIcon"], { class: "w-4 h-4 mr-1" }),
                  createTextVNode(" \u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C ")
                ], 64))
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    } else {
      _push(`<div data-v-2cbb67a9><!--[-->`);
      ssrRenderList($setup.visibleGroupedAttributes, (groupAttributes2, groupName) => {
        _push(`<div class="attribute-group mb-4" data-v-2cbb67a9><div class="mb-3 pb-2 border-b border-surface-200 dark:border-surface-700" data-v-2cbb67a9><h6 class="text-sm font-medium text-surface-700 dark:text-surface-300 flex items-center gap-2" data-v-2cbb67a9>`);
        _push(ssrRenderComponent($setup["Icon"], {
          name: "pi pi-folder",
          class: "w-3 h-3"
        }, null, _parent));
        _push(` ${ssrInterpolate(groupName)} `);
        _push(ssrRenderComponent($setup["Tag"], {
          severity: "info",
          value: groupAttributes2.length.toString(),
          class: "text-xs"
        }, null, _parent));
        _push(`</h6></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3" data-v-2cbb67a9><!--[-->`);
        ssrRenderList(groupAttributes2, (attribute) => {
          _push(`<div class="attribute-card p-3 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow" data-v-2cbb67a9><div class="flex items-start justify-between" data-v-2cbb67a9><div class="flex-1 min-w-0" data-v-2cbb67a9><div class="flex items-center gap-2 mb-1" data-v-2cbb67a9><span class="text-sm font-medium text-surface-900 dark:text-surface-0 truncate" data-v-2cbb67a9>${ssrInterpolate(attribute.template.title)}</span>`);
          if (attribute.template.isRequired) {
            _push(ssrRenderComponent($setup["Tag"], {
              severity: "danger",
              class: "text-xs flex-shrink-0"
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(` \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 `);
                } else {
                  return [
                    createTextVNode(" \u041E\u0431\u044F\u0437\u0430\u0442\u0435\u043B\u044C\u043D\u044B\u0439 ")
                  ];
                }
              }),
              _: 2
            }, _parent));
          } else {
            _push(`<!---->`);
          }
          _push(`</div><div class="text-sm text-surface-600 dark:text-surface-400 mb-2 break-words" data-v-2cbb67a9>${ssrInterpolate($setup.formatAttribute(attribute).displayValue)}</div>`);
          if (attribute.template.description) {
            _push(`<div class="text-xs text-surface-500 dark:text-surface-400 line-clamp-2"${ssrRenderAttr("title", attribute.template.description)} data-v-2cbb67a9>${ssrInterpolate(attribute.template.description)}</div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
          if ($setup.canManageAttributes) {
            _push(`<div class="ml-2 flex flex-col gap-1 flex-shrink-0" data-v-2cbb67a9>`);
            _push(ssrRenderComponent($setup["Button"], {
              onClick: ($event) => $setup.handleEditAttribute(attribute.id),
              text: "",
              size: "small",
              class: "p-1 text-xs",
              title: `\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ${attribute.template.title}`
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(ssrRenderComponent($setup["Icon"], {
                    name: "pi pi-pencil",
                    class: "w-3 h-3"
                  }, null, _parent2, _scopeId));
                } else {
                  return [
                    createVNode($setup["Icon"], {
                      name: "pi pi-pencil",
                      class: "w-3 h-3"
                    })
                  ];
                }
              }),
              _: 2
            }, _parent));
            _push(ssrRenderComponent($setup["Button"], {
              onClick: ($event) => $setup.handleDeleteAttribute(attribute.id),
              text: "",
              severity: "danger",
              size: "small",
              class: "p-1 text-xs",
              title: `\u0423\u0434\u0430\u043B\u0438\u0442\u044C ${attribute.template.title}`,
              disabled: attribute.template.isRequired
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(ssrRenderComponent($setup["Icon"], {
                    name: "pi pi-trash",
                    class: "w-3 h-3"
                  }, null, _parent2, _scopeId));
                } else {
                  return [
                    createVNode($setup["Icon"], {
                      name: "pi pi-trash",
                      class: "w-3 h-3"
                    })
                  ];
                }
              }),
              _: 2
            }, _parent));
            _push(`</div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div>`);
        });
        _push(`<!--]--></div></div>`);
      });
      _push(`<!--]-->`);
      if ($setup.shouldShowExpandButton) {
        _push(`<div class="text-center pt-2" data-v-2cbb67a9>`);
        _push(ssrRenderComponent($setup["Button"], {
          onClick: $setup.toggleShowAll,
          text: "",
          size: "small",
          class: "text-sm text-surface-600 dark:text-surface-400 hover:text-surface-900 dark:hover:text-surface-0"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              if (!$setup.showAllAttributes) {
                _push2(`<!--[-->`);
                _push2(ssrRenderComponent($setup["ChevronDownIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
                _push2(` \u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0432\u0441\u0435 (\u0435\u0449\u0435 ${ssrInterpolate($setup.hiddenAttributesCount)}) <!--]-->`);
              } else {
                _push2(`<!--[-->`);
                _push2(ssrRenderComponent($setup["ChevronUpIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
                _push2(` \u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C <!--]-->`);
              }
            } else {
              return [
                !$setup.showAllAttributes ? (openBlock(), createBlock(Fragment, { key: 0 }, [
                  createVNode($setup["ChevronDownIcon"], { class: "w-4 h-4 mr-1" }),
                  createTextVNode(" \u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0432\u0441\u0435 (\u0435\u0449\u0435 " + toDisplayString($setup.hiddenAttributesCount) + ") ", 1)
                ], 64)) : (openBlock(), createBlock(Fragment, { key: 1 }, [
                  createVNode($setup["ChevronUpIcon"], { class: "w-4 h-4 mr-1" }),
                  createTextVNode(" \u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C ")
                ], 64))
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    }
    _push(`</div>`);
  } else {
    _push(`<div class="text-center py-6 text-surface-500 dark:text-surface-400" data-v-2cbb67a9>`);
    _push(ssrRenderComponent($setup["Icon"], {
      name: "pi pi-info-circle",
      class: "text-2xl mb-2 inline-block"
    }, null, _parent));
    _push(`<p class="text-sm mb-3" data-v-2cbb67a9>\u0423 \u0434\u0430\u043D\u043D\u043E\u0439 \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u043D\u0435\u0442 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432</p>`);
    if ($setup.canManageAttributes) {
      _push(ssrRenderComponent($setup["Button"], {
        onClick: $setup.handleAddAttribute,
        outlined: "",
        size: "small"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-4 h-4 mr-1" }, null, _parent2, _scopeId));
            _push2(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u0435\u0440\u0432\u044B\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 `);
          } else {
            return [
              createVNode($setup["PlusIcon"], { class: "w-4 h-4 mr-1" }),
              createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u0435\u0440\u0432\u044B\u0439 \u0430\u0442\u0440\u0438\u0431\u0443\u0442 ")
            ];
          }
        }),
        _: 1
      }, _parent));
    } else {
      _push(`<!---->`);
    }
    _push(`</div>`);
  }
  _push(ssrRenderComponent($setup["AddEquipmentAttributeDialog"], {
    visible: $setup.showAddDialog,
    "onUpdate:visible": ($event) => $setup.showAddDialog = $event,
    "equipment-id": $props.equipmentId,
    "existing-attributes": $props.attributes || [],
    onSave: $setup.handleSaveAttribute,
    onCancel: $setup.handleCancelAddAttribute
  }, null, _parent));
  _push(ssrRenderComponent($setup["Dialog"], {
    visible: $setup.showGroupDialog,
    "onUpdate:visible": ($event) => $setup.showGroupDialog = $event,
    modal: "",
    header: "\u0412\u044B\u0431\u043E\u0440 \u0433\u0440\u0443\u043F\u043F\u044B \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432",
    style: { width: "40rem" }
  }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="space-y-4" data-v-2cbb67a9${_scopeId}><!--[-->`);
        ssrRenderList($setup.templateGroups, (group) => {
          _push2(`<div class="border border-surface-200 dark:border-surface-700 rounded-lg p-4" data-v-2cbb67a9${_scopeId}><div class="flex items-center justify-between" data-v-2cbb67a9${_scopeId}><div data-v-2cbb67a9${_scopeId}><h4 class="font-medium text-surface-900 dark:text-surface-0" data-v-2cbb67a9${_scopeId}>${ssrInterpolate(group.name)}</h4><p class="text-sm text-surface-600 dark:text-surface-400" data-v-2cbb67a9${_scopeId}>${ssrInterpolate(group.description)}</p><small class="text-surface-500" data-v-2cbb67a9${_scopeId}>${ssrInterpolate(group._count?.templates || 0)} \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432</small></div>`);
          _push2(ssrRenderComponent($setup["Button"], {
            onClick: ($event) => $setup.loadTemplatesByGroupId(group.id),
            size: "small",
            loading: $setup.loadingTemplates
          }, {
            default: withCtx((_2, _push3, _parent3, _scopeId2) => {
              if (_push3) {
                _push3(` \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432\u0441\u0435 `);
              } else {
                return [
                  createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432\u0441\u0435 ")
                ];
              }
            }),
            _: 2
          }, _parent2, _scopeId));
          _push2(`</div></div>`);
        });
        _push2(`<!--]--></div>`);
      } else {
        return [
          createVNode("div", { class: "space-y-4" }, [
            (openBlock(true), createBlock(Fragment, null, renderList($setup.templateGroups, (group) => {
              return openBlock(), createBlock("div", {
                key: group.id,
                class: "border border-surface-200 dark:border-surface-700 rounded-lg p-4"
              }, [
                createVNode("div", { class: "flex items-center justify-between" }, [
                  createVNode("div", null, [
                    createVNode("h4", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(group.name), 1),
                    createVNode("p", { class: "text-sm text-surface-600 dark:text-surface-400" }, toDisplayString(group.description), 1),
                    createVNode("small", { class: "text-surface-500" }, toDisplayString(group._count?.templates || 0) + " \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432", 1)
                  ]),
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.loadTemplatesByGroupId(group.id),
                    size: "small",
                    loading: $setup.loadingTemplates
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" \u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432\u0441\u0435 ")
                    ]),
                    _: 2
                  }, 1032, ["onClick", "loading"])
                ])
              ]);
            }), 128))
          ])
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/equipment/EquipmentAttributesSection.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const EquipmentAttributesSection = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender$1], ["__scopeId", "data-v-2cbb67a9"]]);

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "EquipmentList",
  props: {
    initialData: {}
  },
  setup(__props, { expose: __expose }) {
    __expose();
    const searchValue = ref("");
    const dialogVisible = ref(false);
    const editingEquipment = ref(null);
    const expandedRows = ref([]);
    const equipmentPartsCache = ref({});
    const equipmentAttributesCache = ref({});
    const props = __props;
    const items = ref(props.initialData);
    const keyMapping = {
      id: "ID",
      name: "\u041D\u0430\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u043D\u0438\u0435",
      brandId: "\u0411\u0440\u0435\u043D\u0434 ID",
      createdAt: "\u0421\u043E\u0437\u0434\u0430\u043D\u043E",
      updatedAt: "\u041E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u043E"
    };
    const columnKeys = [
      "id",
      "name",
      "createdAt"
    ];
    function createEquipment() {
      editingEquipment.value = {};
      dialogVisible.value = true;
    }
    function editEquipment(data) {
      editingEquipment.value = { ...data };
      dialogVisible.value = true;
    }
    async function handleSave(equipmentData) {
      if (!equipmentData) return;
      try {
        if (equipmentData.id) {
          const { id, ...dataToUpdate } = equipmentData;
          await trpc.crud.equipmentModel.update.mutate({
            where: { id },
            data: dataToUpdate
          });
        } else {
          if (equipmentData.name) {
            await trpc.crud.equipmentModel.create.mutate({
              data: {
                name: equipmentData.name,
                brandId: equipmentData.brandId || null
              }
            });
          } else {
            console.error("Name is required to create an equipment model.");
            return;
          }
        }
        navigate(window.location.href);
      } catch (error) {
        console.error("Failed to save equipment model:", error);
      } finally {
        dialogVisible.value = false;
      }
    }
    function handleCancel() {
      dialogVisible.value = false;
      editingEquipment.value = null;
    }
    async function deleteEquipment(data) {
      dialogVisible.value = false;
      await trpc.crud.equipmentModel.delete.mutate({
        where: {
          id: data.id
        }
      });
      navigate();
    }
    watch(searchValue, (newValue) => {
      debouncedSearch(newValue);
    });
    async function debouncedSearch(value = "") {
      console.log("value", value);
      items.value = await trpc.crud.equipmentModel.findMany.query({
        where: {
          OR: [
            {
              name: {
                contains: value
              }
            },
            {
              brand: {
                name: {
                  contains: value
                }
              }
            }
          ]
        },
        include: {
          brand: {
            select: {
              name: true
            }
          },
          _count: {
            select: {
              partApplicabilities: true,
              attributes: true
            }
          }
        }
      });
    }
    function formatDate(date) {
      return new Date(date).toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      });
    }
    async function loadEquipmentParts(equipmentId) {
      if (equipmentPartsCache.value[equipmentId]) {
        return equipmentPartsCache.value[equipmentId];
      }
      try {
        const parts = await trpc.crud.equipmentApplicability.findMany.query({
          where: {
            equipmentModelId: equipmentId
          },
          include: {
            part: {
              include: {
                partCategory: true,
                applicabilities: {
                  include: {
                    catalogItem: {
                      include: {
                        brand: true
                      }
                    }
                  }
                },
                _count: {
                  select: {
                    attributes: true,
                    applicabilities: true
                  }
                }
              }
            }
          },
          orderBy: {
            part: {
              name: "asc"
            }
          }
        });
        equipmentPartsCache.value[equipmentId] = parts || [];
        return parts || [];
      } catch (error) {
        console.error("Failed to load equipment parts:", error);
        return [];
      }
    }
    async function loadEquipmentAttributes(equipmentId) {
      if (equipmentAttributesCache.value[equipmentId]) {
        return equipmentAttributesCache.value[equipmentId];
      }
      try {
        const attributes = await trpc.crud.equipmentModelAttribute.findMany.query({
          where: {
            equipmentModelId: equipmentId
          },
          include: {
            template: {
              include: {
                group: true
              }
            }
          },
          orderBy: [
            {
              template: {
                group: {
                  name: "asc"
                }
              }
            },
            {
              template: {
                title: "asc"
              }
            }
          ]
        });
        const attributesWithTemplate = attributes.map((attr) => ({
          ...attr,
          template: {
            ...attr.template,
            group: attr.template.group
          }
        }));
        equipmentAttributesCache.value[equipmentId] = attributesWithTemplate;
        return attributesWithTemplate;
      } catch (error) {
        console.error("Failed to load equipment attributes:", error);
        return [];
      }
    }
    async function onRowExpand(event) {
      if (event.data._count.partApplicabilities > 0) {
        await loadEquipmentParts(event.data.id);
      }
      if (event.data._count.attributes > 0) {
        await loadEquipmentAttributes(event.data.id);
      }
    }
    function getAccuracyLabel(accuracy) {
      const labels = {
        "EXACT_MATCH": "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435",
        "MATCH_WITH_NOTES": "\u0421 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438",
        "REQUIRES_MODIFICATION": "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438",
        "PARTIAL_MATCH": "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435"
      };
      return labels[accuracy] || accuracy;
    }
    function getAccuracySeverity(accuracy) {
      const severities = {
        "EXACT_MATCH": "success",
        "MATCH_WITH_NOTES": "info",
        "REQUIRES_MODIFICATION": "warning",
        "PARTIAL_MATCH": "secondary"
      };
      return severities[accuracy] || "secondary";
    }
    function editPart(partId) {
      console.log("Edit part:", partId);
    }
    function groupAttributesByGroup(attributes) {
      const grouped = {};
      attributes.forEach((attribute) => {
        const groupName = attribute.template.group?.name || "\u041E\u0431\u0449\u0438\u0435";
        if (!grouped[groupName]) {
          grouped[groupName] = [];
        }
        grouped[groupName].push(attribute);
      });
      return grouped;
    }
    function formatAttributeValue(attribute) {
      const { value, template } = attribute;
      const { dataType, unit } = template;
      switch (dataType) {
        case "STRING":
          return value;
        case "NUMBER":
          const num = parseFloat(value);
          const formatted = isNaN(num) ? value : num.toLocaleString("ru-RU");
          return unit ? `${formatted} ${getUnitDisplayName(unit)}` : formatted;
        case "BOOLEAN":
          return value.toLowerCase() === "true" ? "\u0414\u0430" : "\u041D\u0435\u0442";
        case "DATE":
          const date = new Date(value);
          return isNaN(date.getTime()) ? value : date.toLocaleDateString("ru-RU");
        case "JSON":
          try {
            return JSON.stringify(JSON.parse(value), null, 2);
          } catch {
            return value;
          }
        default:
          return value;
      }
    }
    function getUnitDisplayName(unit) {
      if (!unit) return "";
      const unitNames = {
        MM: "\u043C\u043C",
        INCH: "\u0434\u044E\u0439\u043C",
        FT: "\u0444\u0442",
        G: "\u0433",
        KG: "\u043A\u0433",
        T: "\u0442",
        LB: "\u0444\u0443\u043D\u0442",
        ML: "\u043C\u043B",
        L: "\u043B",
        GAL: "\u0433\u0430\u043B",
        SEC: "\u0441\u0435\u043A",
        MIN: "\u043C\u0438\u043D",
        H: "\u0447",
        PCS: "\u0448\u0442",
        SET: "\u043A\u043E\u043C\u043F\u043B\u0435\u043A\u0442",
        PAIR: "\u043F\u0430\u0440\u0430",
        BAR: "\u0431\u0430\u0440",
        PSI: "psi",
        KW: "\u043A\u0412\u0442",
        HP: "\u043B.\u0441.",
        NM: "\u041D\u22C5\u043C",
        RPM: "\u043E\u0431/\u043C\u0438\u043D",
        C: "\xB0C",
        F: "\xB0F",
        PERCENT: "%"
      };
      return unitNames[unit] || unit;
    }
    async function expandRowForAttributes(data) {
      const isExpanded = expandedRows.value.some((row) => row.id === data.id);
      if (!isExpanded) {
        expandedRows.value.push(data);
        if (data._count.attributes > 0 && !equipmentAttributesCache.value[data.id]) {
          await loadEquipmentAttributes(data.id);
        }
      }
    }
    async function handleAddAttribute(data) {
      try {
        await trpc.crud.equipmentModelAttribute.create.mutate({
          data: {
            equipmentModelId: data.equipmentId || data.equipmentModelId,
            templateId: data.templateId,
            value: String(data.value)
          }
        });
        if (data.equipmentId) {
          delete equipmentAttributesCache.value[data.equipmentId];
          await loadEquipmentAttributes(data.equipmentId);
        }
        console.log("Attribute added successfully");
      } catch (error) {
        console.error("Failed to add attribute:", error);
      }
    }
    function handleEditAttribute(attributeId) {
      console.log("Edit attribute:", attributeId);
    }
    function handleDeleteAttribute(attributeId) {
      console.log("Delete attribute:", attributeId);
    }
    const __returned__ = { searchValue, dialogVisible, editingEquipment, expandedRows, equipmentPartsCache, equipmentAttributesCache, props, items, keyMapping, columnKeys, createEquipment, editEquipment, handleSave, handleCancel, deleteEquipment, debouncedSearch, formatDate, loadEquipmentParts, loadEquipmentAttributes, onRowExpand, getAccuracyLabel, getAccuracySeverity, editPart, groupAttributesByGroup, formatAttributeValue, getUnitDisplayName, expandRowForAttributes, handleAddAttribute, handleEditAttribute, handleDeleteAttribute, Button, DataTable, get PencilIcon() {
      return PencilIcon;
    }, get TrashIcon() {
      return TrashIcon;
    }, get PlusIcon() {
      return PlusIcon;
    }, get ExternalLinkIcon() {
      return ExternalLinkIcon;
    }, get Column() {
      return script;
    }, EditEquipmentDialog, EquipmentAttributesSection, get navigate() {
      return navigate;
    }, InputText, Tag, Icon };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
});
function _sfc_ssrRender(_ctx, _push, _parent, _attrs, $props, $setup, $data, $options) {
  _push(`<div${ssrRenderAttrs(_attrs)}><div class="flex justify-between items-center mb-4"><h1 class="text-2xl font-bold">\u041C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438</h1>`);
  _push(ssrRenderComponent($setup["Button"], { onClick: $setup.createEquipment }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["PlusIcon"], { class: "w-5 h-5 mr-2" }, null, _parent2, _scopeId));
        _push2(` \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043C\u043E\u0434\u0435\u043B\u044C `);
      } else {
        return [
          createVNode($setup["PlusIcon"], { class: "w-5 h-5 mr-2" }),
          createTextVNode(" \u0421\u043E\u0437\u0434\u0430\u0442\u044C \u043C\u043E\u0434\u0435\u043B\u044C ")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</div>`);
  _push(ssrRenderComponent($setup["DataTable"], {
    "show-headers": "",
    value: $setup.items,
    expandedRows: $setup.expandedRows,
    "onUpdate:expandedRows": ($event) => $setup.expandedRows = $event,
    onRowExpand: $setup.onRowExpand,
    rowHover: true
  }, {
    header: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="flex justify-end"${_scopeId}>`);
        _push2(ssrRenderComponent($setup["InputText"], {
          modelValue: $setup.searchValue,
          "onUpdate:modelValue": ($event) => $setup.searchValue = $event,
          placeholder: "\u041F\u043E\u0438\u0441\u043A"
        }, null, _parent2, _scopeId));
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "flex justify-end" }, [
            createVNode($setup["InputText"], {
              modelValue: $setup.searchValue,
              "onUpdate:modelValue": ($event) => $setup.searchValue = $event,
              placeholder: "\u041F\u043E\u0438\u0441\u043A"
            }, null, 8, ["modelValue", "onUpdate:modelValue"])
          ])
        ];
      }
    }),
    expansion: withCtx(({ data }, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`<div class="p-4 bg-surface-50 dark:bg-surface-800"${_scopeId}>`);
        if (data._count.attributes > 0 || true) {
          _push2(`<div class="mb-6"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["EquipmentAttributesSection"], {
            "equipment-id": data.id,
            attributes: $setup.equipmentAttributesCache[data.id],
            onAddAttribute: (attributeData) => $setup.handleAddAttribute({ ...attributeData, equipmentId: data.id }),
            onEditAttribute: $setup.handleEditAttribute,
            onDeleteAttribute: $setup.handleDeleteAttribute
          }, null, _parent2, _scopeId));
          _push2(`</div>`);
        }
        if (data._count.partApplicabilities > 0) {
          _push2(`<div${_scopeId}><h5 class="mb-3 font-semibold flex items-center gap-2"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-wrench",
            class: "text-blue-600 w-4 h-4"
          }, null, _parent2, _scopeId));
          _push2(` \u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438 \u0434\u043B\u044F: ${ssrInterpolate(data.name)}</h5>`);
          if ($setup.equipmentPartsCache[data.id] && $setup.equipmentPartsCache[data.id].length > 0) {
            _push2(`<div${_scopeId}><div class="grid gap-3"${_scopeId}><!--[-->`);
            ssrRenderList($setup.equipmentPartsCache[data.id], (applicability) => {
              _push2(`<div class="p-4 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"${_scopeId}><div class="flex items-start justify-between"${_scopeId}><div class="flex-1"${_scopeId}><div class="flex items-center gap-2 mb-2"${_scopeId}><h6 class="font-medium text-surface-900 dark:text-surface-0"${_scopeId}>${ssrInterpolate(applicability.part?.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F")}</h6>`);
              if (applicability.part?.partCategory) {
                _push2(ssrRenderComponent($setup["Tag"], {
                  severity: "info",
                  class: "text-xs"
                }, {
                  default: withCtx((_, _push3, _parent3, _scopeId2) => {
                    if (_push3) {
                      _push3(`${ssrInterpolate(applicability.part.partCategory.name)}`);
                    } else {
                      return [
                        createTextVNode(toDisplayString(applicability.part.partCategory.name), 1)
                      ];
                    }
                  }),
                  _: 2
                }, _parent2, _scopeId));
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2"${_scopeId}><span class="flex items-center gap-1"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-list",
                class: "w-3 h-3"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(applicability.part?._count?.attributes || 0)} \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 </span><span class="flex items-center gap-1"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Icon"], {
                name: "pi pi-box",
                class: "w-3 h-3"
              }, null, _parent2, _scopeId));
              _push2(` ${ssrInterpolate(applicability.part?._count?.applicabilities || 0)} \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439 </span></div>`);
              if (applicability.notes) {
                _push2(`<div class="mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400"${_scopeId}>`);
                _push2(ssrRenderComponent($setup["Icon"], {
                  name: "pi pi-info-circle",
                  class: "mr-1 w-4 h-4 inline-block"
                }, null, _parent2, _scopeId));
                _push2(` ${ssrInterpolate(applicability.notes)}</div>`);
              } else {
                _push2(`<!---->`);
              }
              if (applicability.part?.applicabilities?.length > 0) {
                _push2(`<div class="mt-3"${_scopeId}><div class="text-xs font-medium text-surface-700 dark:text-surface-300 mb-2"${_scopeId}> \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438: </div><div class="flex flex-wrap gap-2"${_scopeId}><!--[-->`);
                ssrRenderList(applicability.part.applicabilities.slice(0, 3), (partApp) => {
                  _push2(`<div class="flex items-center gap-1 px-2 py-1 bg-surface-100 dark:bg-surface-800 rounded text-xs"${_scopeId}><span class="font-medium"${_scopeId}>${ssrInterpolate(partApp.catalogItem?.sku)}</span><span class="text-surface-500"${_scopeId}>${ssrInterpolate(partApp.catalogItem?.brand?.name)}</span>`);
                  _push2(ssrRenderComponent($setup["Tag"], {
                    severity: $setup.getAccuracySeverity(partApp.accuracy),
                    class: "text-xs"
                  }, {
                    default: withCtx((_, _push3, _parent3, _scopeId2) => {
                      if (_push3) {
                        _push3(`${ssrInterpolate($setup.getAccuracyLabel(partApp.accuracy))}`);
                      } else {
                        return [
                          createTextVNode(toDisplayString($setup.getAccuracyLabel(partApp.accuracy)), 1)
                        ];
                      }
                    }),
                    _: 2
                  }, _parent2, _scopeId));
                  _push2(`</div>`);
                });
                _push2(`<!--]-->`);
                if (applicability.part.applicabilities.length > 3) {
                  _push2(`<div class="px-2 py-1 bg-surface-200 dark:bg-surface-700 rounded text-xs text-surface-600 dark:text-surface-400"${_scopeId}> +${ssrInterpolate(applicability.part.applicabilities.length - 3)} \u0435\u0449\u0435 </div>`);
                } else {
                  _push2(`<!---->`);
                }
                _push2(`</div></div>`);
              } else {
                _push2(`<!---->`);
              }
              _push2(`</div><div class="ml-4 flex flex-col gap-2"${_scopeId}>`);
              _push2(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.editPart(applicability.part.id),
                outlined: "",
                size: "small",
                class: "text-xs"
              }, {
                default: withCtx((_, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["PencilIcon"], { class: "w-3 h-3 mr-1" }, null, _parent3, _scopeId2));
                    _push3(` \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C `);
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "w-3 h-3 mr-1" }),
                      createTextVNode(" \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(ssrRenderComponent($setup["Button"], {
                onClick: () => $setup.navigate(`/admin/parts/${applicability.part.id}`),
                outlined: "",
                severity: "secondary",
                size: "small",
                class: "text-xs"
              }, {
                default: withCtx((_, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(ssrRenderComponent($setup["ExternalLinkIcon"], { class: "w-3 h-3 mr-1" }, null, _parent3, _scopeId2));
                    _push3(` \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 `);
                  } else {
                    return [
                      createVNode($setup["ExternalLinkIcon"], { class: "w-3 h-3 mr-1" }),
                      createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></div></div>`);
            });
            _push2(`<!--]--></div></div>`);
          } else {
            _push2(`<div class="text-center py-6 text-surface-500 dark:text-surface-400"${_scopeId}>`);
            _push2(ssrRenderComponent($setup["Icon"], {
              name: "pi pi-info-circle",
              class: "text-2xl mb-2 inline-block"
            }, null, _parent2, _scopeId));
            _push2(` \u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438 \u0434\u043B\u044F \u0434\u0430\u043D\u043D\u043E\u0439 \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B </div>`);
          }
          _push2(`</div>`);
        } else {
          _push2(`<!---->`);
        }
        if (data._count.attributes === 0 && data._count.partApplicabilities === 0) {
          _push2(`<div class="text-center py-6 text-surface-500 dark:text-surface-400"${_scopeId}>`);
          _push2(ssrRenderComponent($setup["Icon"], {
            name: "pi pi-info-circle",
            class: "text-2xl mb-2 inline-block"
          }, null, _parent2, _scopeId));
          _push2(` \u0414\u043B\u044F \u0434\u0430\u043D\u043D\u043E\u0439 \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u043D\u0435\u0442 \u0434\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u043E\u0439 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 </div>`);
        } else {
          _push2(`<!---->`);
        }
        _push2(`</div>`);
      } else {
        return [
          createVNode("div", { class: "p-4 bg-surface-50 dark:bg-surface-800" }, [
            data._count.attributes > 0 || true ? (openBlock(), createBlock("div", {
              key: 0,
              class: "mb-6"
            }, [
              createVNode($setup["EquipmentAttributesSection"], {
                "equipment-id": data.id,
                attributes: $setup.equipmentAttributesCache[data.id],
                onAddAttribute: (attributeData) => $setup.handleAddAttribute({ ...attributeData, equipmentId: data.id }),
                onEditAttribute: $setup.handleEditAttribute,
                onDeleteAttribute: $setup.handleDeleteAttribute
              }, null, 8, ["equipment-id", "attributes", "onAddAttribute"])
            ])) : createCommentVNode("", true),
            data._count.partApplicabilities > 0 ? (openBlock(), createBlock("div", { key: 1 }, [
              createVNode("h5", { class: "mb-3 font-semibold flex items-center gap-2" }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-wrench",
                  class: "text-blue-600 w-4 h-4"
                }),
                createTextVNode(" \u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438 \u0434\u043B\u044F: " + toDisplayString(data.name), 1)
              ]),
              $setup.equipmentPartsCache[data.id] && $setup.equipmentPartsCache[data.id].length > 0 ? (openBlock(), createBlock("div", { key: 0 }, [
                createVNode("div", { class: "grid gap-3" }, [
                  (openBlock(true), createBlock(Fragment, null, renderList($setup.equipmentPartsCache[data.id], (applicability) => {
                    return openBlock(), createBlock("div", {
                      key: applicability.id,
                      class: "p-4 bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 hover:shadow-sm transition-shadow"
                    }, [
                      createVNode("div", { class: "flex items-start justify-between" }, [
                        createVNode("div", { class: "flex-1" }, [
                          createVNode("div", { class: "flex items-center gap-2 mb-2" }, [
                            createVNode("h6", { class: "font-medium text-surface-900 dark:text-surface-0" }, toDisplayString(applicability.part?.name || "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F"), 1),
                            applicability.part?.partCategory ? (openBlock(), createBlock($setup["Tag"], {
                              key: 0,
                              severity: "info",
                              class: "text-xs"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(applicability.part.partCategory.name), 1)
                              ]),
                              _: 2
                            }, 1024)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", { class: "flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2" }, [
                            createVNode("span", { class: "flex items-center gap-1" }, [
                              createVNode($setup["Icon"], {
                                name: "pi pi-list",
                                class: "w-3 h-3"
                              }),
                              createTextVNode(" " + toDisplayString(applicability.part?._count?.attributes || 0) + " \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 ", 1)
                            ]),
                            createVNode("span", { class: "flex items-center gap-1" }, [
                              createVNode($setup["Icon"], {
                                name: "pi pi-box",
                                class: "w-3 h-3"
                              }),
                              createTextVNode(" " + toDisplayString(applicability.part?._count?.applicabilities || 0) + " \u043A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0445 \u043F\u043E\u0437\u0438\u0446\u0438\u0439 ", 1)
                            ])
                          ]),
                          applicability.notes ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400"
                          }, [
                            createVNode($setup["Icon"], {
                              name: "pi pi-info-circle",
                              class: "mr-1 w-4 h-4 inline-block"
                            }),
                            createTextVNode(" " + toDisplayString(applicability.notes), 1)
                          ])) : createCommentVNode("", true),
                          applicability.part?.applicabilities?.length > 0 ? (openBlock(), createBlock("div", {
                            key: 1,
                            class: "mt-3"
                          }, [
                            createVNode("div", { class: "text-xs font-medium text-surface-700 dark:text-surface-300 mb-2" }, " \u041A\u0430\u0442\u0430\u043B\u043E\u0436\u043D\u044B\u0435 \u043F\u043E\u0437\u0438\u0446\u0438\u0438: "),
                            createVNode("div", { class: "flex flex-wrap gap-2" }, [
                              (openBlock(true), createBlock(Fragment, null, renderList(applicability.part.applicabilities.slice(0, 3), (partApp) => {
                                return openBlock(), createBlock("div", {
                                  key: partApp.id,
                                  class: "flex items-center gap-1 px-2 py-1 bg-surface-100 dark:bg-surface-800 rounded text-xs"
                                }, [
                                  createVNode("span", { class: "font-medium" }, toDisplayString(partApp.catalogItem?.sku), 1),
                                  createVNode("span", { class: "text-surface-500" }, toDisplayString(partApp.catalogItem?.brand?.name), 1),
                                  createVNode($setup["Tag"], {
                                    severity: $setup.getAccuracySeverity(partApp.accuracy),
                                    class: "text-xs"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(toDisplayString($setup.getAccuracyLabel(partApp.accuracy)), 1)
                                    ]),
                                    _: 2
                                  }, 1032, ["severity"])
                                ]);
                              }), 128)),
                              applicability.part.applicabilities.length > 3 ? (openBlock(), createBlock("div", {
                                key: 0,
                                class: "px-2 py-1 bg-surface-200 dark:bg-surface-700 rounded text-xs text-surface-600 dark:text-surface-400"
                              }, " +" + toDisplayString(applicability.part.applicabilities.length - 3) + " \u0435\u0449\u0435 ", 1)) : createCommentVNode("", true)
                            ])
                          ])) : createCommentVNode("", true)
                        ]),
                        createVNode("div", { class: "ml-4 flex flex-col gap-2" }, [
                          createVNode($setup["Button"], {
                            onClick: ($event) => $setup.editPart(applicability.part.id),
                            outlined: "",
                            size: "small",
                            class: "text-xs"
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["PencilIcon"], { class: "w-3 h-3 mr-1" }),
                              createTextVNode(" \u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C ")
                            ]),
                            _: 2
                          }, 1032, ["onClick"]),
                          createVNode($setup["Button"], {
                            onClick: () => $setup.navigate(`/admin/parts/${applicability.part.id}`),
                            outlined: "",
                            severity: "secondary",
                            size: "small",
                            class: "text-xs"
                          }, {
                            default: withCtx(() => [
                              createVNode($setup["ExternalLinkIcon"], { class: "w-3 h-3 mr-1" }),
                              createTextVNode(" \u041F\u043E\u0434\u0440\u043E\u0431\u043D\u0435\u0435 ")
                            ]),
                            _: 2
                          }, 1032, ["onClick"])
                        ])
                      ])
                    ]);
                  }), 128))
                ])
              ])) : (openBlock(), createBlock("div", {
                key: 1,
                class: "text-center py-6 text-surface-500 dark:text-surface-400"
              }, [
                createVNode($setup["Icon"], {
                  name: "pi pi-info-circle",
                  class: "text-2xl mb-2 inline-block"
                }),
                createTextVNode(" \u0417\u0430\u043F\u0447\u0430\u0441\u0442\u0438 \u0434\u043B\u044F \u0434\u0430\u043D\u043D\u043E\u0439 \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B ")
              ]))
            ])) : createCommentVNode("", true),
            data._count.attributes === 0 && data._count.partApplicabilities === 0 ? (openBlock(), createBlock("div", {
              key: 2,
              class: "text-center py-6 text-surface-500 dark:text-surface-400"
            }, [
              createVNode($setup["Icon"], {
                name: "pi pi-info-circle",
                class: "text-2xl mb-2 inline-block"
              }),
              createTextVNode(" \u0414\u043B\u044F \u0434\u0430\u043D\u043D\u043E\u0439 \u043C\u043E\u0434\u0435\u043B\u0438 \u0442\u0435\u0445\u043D\u0438\u043A\u0438 \u043D\u0435\u0442 \u0434\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u043E\u0439 \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u0438 ")
            ])) : createCommentVNode("", true)
          ])
        ];
      }
    }),
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(ssrRenderComponent($setup["Column"], {
          expander: true,
          headerStyle: "width: 3rem"
        }, null, _parent2, _scopeId));
        _push2(`<!--[-->`);
        ssrRenderList($setup.columnKeys, (key) => {
          _push2(ssrRenderComponent($setup["Column"], {
            key,
            field: key,
            header: $setup.keyMapping[key] || key
          }, createSlots({ _: 2 }, [
            key === "createdAt" ? {
              name: "body",
              fn: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`${ssrInterpolate($setup.formatDate(data[key]))}`);
                } else {
                  return [
                    createTextVNode(toDisplayString($setup.formatDate(data[key])), 1)
                  ];
                }
              }),
              key: "0"
            } : key === "id" ? {
              name: "body",
              fn: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="font-mono text-sm"${_scopeId2}>${ssrInterpolate(data[key].substring(0, 8))}... </div>`);
                } else {
                  return [
                    createVNode("div", { class: "font-mono text-sm" }, toDisplayString(data[key].substring(0, 8)) + "... ", 1)
                  ];
                }
              }),
              key: "1"
            } : void 0
          ]), _parent2, _scopeId));
        });
        _push2(`<!--]-->`);
        _push2(ssrRenderComponent($setup["Column"], {
          field: "brand.name",
          header: "\u0411\u0440\u0435\u043D\u0434"
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`${ssrInterpolate(data.brand?.name || "-")}`);
            } else {
              return [
                createTextVNode(toDisplayString(data.brand?.name || "-"), 1)
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "_count.partApplicabilities",
          header: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              if (data._count.partApplicabilities > 0) {
                _push3(ssrRenderComponent($setup["Tag"], {
                  severity: "info",
                  value: data._count.partApplicabilities.toString()
                }, null, _parent3, _scopeId2));
              } else {
                _push3(`<span class="text-surface-500"${_scopeId2}>0</span>`);
              }
            } else {
              return [
                data._count.partApplicabilities > 0 ? (openBlock(), createBlock($setup["Tag"], {
                  key: 0,
                  severity: "info",
                  value: data._count.partApplicabilities.toString()
                }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                  key: 1,
                  class: "text-surface-500"
                }, "0"))
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], {
          field: "_count.attributes",
          header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B"
        }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              if (data._count.attributes > 0) {
                _push3(ssrRenderComponent($setup["Tag"], {
                  severity: "secondary",
                  value: data._count.attributes.toString(),
                  class: "cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors",
                  onClick: ($event) => $setup.expandRowForAttributes(data)
                }, null, _parent3, _scopeId2));
              } else {
                _push3(`<span class="text-surface-500"${_scopeId2}>0</span>`);
              }
            } else {
              return [
                data._count.attributes > 0 ? (openBlock(), createBlock($setup["Tag"], {
                  key: 0,
                  severity: "secondary",
                  value: data._count.attributes.toString(),
                  class: "cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors",
                  onClick: ($event) => $setup.expandRowForAttributes(data)
                }, null, 8, ["value", "onClick"])) : (openBlock(), createBlock("span", {
                  key: 1,
                  class: "text-surface-500"
                }, "0"))
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
        _push2(ssrRenderComponent($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
          body: withCtx(({ data }, _push3, _parent3, _scopeId2) => {
            if (_push3) {
              _push3(`<div class="flex gap-2"${_scopeId2}>`);
              _push3(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.editEquipment(data),
                outlined: "",
                size: "small"
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["PencilIcon"], { class: "w-5 h-5" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(ssrRenderComponent($setup["Button"], {
                onClick: ($event) => $setup.deleteEquipment(data),
                outlined: "",
                severity: "danger",
                size: "small"
              }, {
                default: withCtx((_2, _push4, _parent4, _scopeId3) => {
                  if (_push4) {
                    _push4(ssrRenderComponent($setup["TrashIcon"], { class: "w-5 h-5" }, null, _parent4, _scopeId3));
                  } else {
                    return [
                      createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                    ];
                  }
                }),
                _: 2
              }, _parent3, _scopeId2));
              _push3(`</div>`);
            } else {
              return [
                createVNode("div", { class: "flex gap-2" }, [
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.editEquipment(data),
                    outlined: "",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"]),
                  createVNode($setup["Button"], {
                    onClick: ($event) => $setup.deleteEquipment(data),
                    outlined: "",
                    severity: "danger",
                    size: "small"
                  }, {
                    default: withCtx(() => [
                      createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                    ]),
                    _: 2
                  }, 1032, ["onClick"])
                ])
              ];
            }
          }),
          _: 1
        }, _parent2, _scopeId));
      } else {
        return [
          createVNode($setup["Column"], {
            expander: true,
            headerStyle: "width: 3rem"
          }),
          (openBlock(), createBlock(Fragment, null, renderList($setup.columnKeys, (key) => {
            return createVNode($setup["Column"], {
              key,
              field: key,
              header: $setup.keyMapping[key] || key
            }, createSlots({ _: 2 }, [
              key === "createdAt" ? {
                name: "body",
                fn: withCtx(({ data }) => [
                  createTextVNode(toDisplayString($setup.formatDate(data[key])), 1)
                ]),
                key: "0"
              } : key === "id" ? {
                name: "body",
                fn: withCtx(({ data }) => [
                  createVNode("div", { class: "font-mono text-sm" }, toDisplayString(data[key].substring(0, 8)) + "... ", 1)
                ]),
                key: "1"
              } : void 0
            ]), 1032, ["field", "header"]);
          }), 64)),
          createVNode($setup["Column"], {
            field: "brand.name",
            header: "\u0411\u0440\u0435\u043D\u0434"
          }, {
            body: withCtx(({ data }) => [
              createTextVNode(toDisplayString(data.brand?.name || "-"), 1)
            ]),
            _: 1
          }),
          createVNode($setup["Column"], {
            field: "_count.partApplicabilities",
            header: "\u041F\u0440\u0438\u043C\u0435\u043D\u0438\u043C\u043E\u0441\u0442\u044C \u0434\u0435\u0442\u0430\u043B\u0435\u0439"
          }, {
            body: withCtx(({ data }) => [
              data._count.partApplicabilities > 0 ? (openBlock(), createBlock($setup["Tag"], {
                key: 0,
                severity: "info",
                value: data._count.partApplicabilities.toString()
              }, null, 8, ["value"])) : (openBlock(), createBlock("span", {
                key: 1,
                class: "text-surface-500"
              }, "0"))
            ]),
            _: 2
          }, 1024),
          createVNode($setup["Column"], {
            field: "_count.attributes",
            header: "\u0410\u0442\u0440\u0438\u0431\u0443\u0442\u044B"
          }, {
            body: withCtx(({ data }) => [
              data._count.attributes > 0 ? (openBlock(), createBlock($setup["Tag"], {
                key: 0,
                severity: "secondary",
                value: data._count.attributes.toString(),
                class: "cursor-pointer hover:bg-surface-200 dark:hover:bg-surface-700 transition-colors",
                onClick: ($event) => $setup.expandRowForAttributes(data)
              }, null, 8, ["value", "onClick"])) : (openBlock(), createBlock("span", {
                key: 1,
                class: "text-surface-500"
              }, "0"))
            ]),
            _: 2
          }, 1024),
          createVNode($setup["Column"], { header: "\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044F" }, {
            body: withCtx(({ data }) => [
              createVNode("div", { class: "flex gap-2" }, [
                createVNode($setup["Button"], {
                  onClick: ($event) => $setup.editEquipment(data),
                  outlined: "",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["PencilIcon"], { class: "w-5 h-5" })
                  ]),
                  _: 2
                }, 1032, ["onClick"]),
                createVNode($setup["Button"], {
                  onClick: ($event) => $setup.deleteEquipment(data),
                  outlined: "",
                  severity: "danger",
                  size: "small"
                }, {
                  default: withCtx(() => [
                    createVNode($setup["TrashIcon"], { class: "w-5 h-5" })
                  ]),
                  _: 2
                }, 1032, ["onClick"])
              ])
            ]),
            _: 1
          })
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(ssrRenderComponent($setup["EditEquipmentDialog"], {
    isVisible: $setup.dialogVisible,
    "onUpdate:isVisible": ($event) => $setup.dialogVisible = $event,
    equipment: $setup.editingEquipment,
    onSave: $setup.handleSave,
    onCancel: $setup.handleCancel
  }, null, _parent));
  _push(`</div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("src/components/admin/equipment/EquipmentList.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const EquipmentList = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

const $$Astro = createAstro();
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const page = Astro2.url.searchParams.get("page") || "1";
  const pageSize = Astro2.url.searchParams.get("pageSize") || "100";
  const equipment = await trpc.crud.equipmentModel.findMany.query({
    take: Number(pageSize),
    skip: (Number(page) - 1) * Number(pageSize),
    include: {
      brand: {
        select: {
          name: true
        }
      },
      _count: {
        select: {
          partApplicabilities: true,
          attributes: true
        }
      }
    },
    orderBy: [
      { name: "asc" }
    ]
  });
  return renderTemplate`${renderComponent($$result, "AdminLayout", $$AdminLayout, {}, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<h1>Equipment Models</h1> ${renderComponent($$result2, "EquipmentList", EquipmentList, { "client:load": true, "initialData": equipment, "client:component-hydration": "load", "client:component-path": "@/components/admin/equipment/EquipmentList.vue", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/equipment/index.astro", void 0);

const $$file = "D:/Dev/PARTTEC/parttec3/frontend/src/pages/admin/equipment/index.astro";
const $$url = "/admin/equipment";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

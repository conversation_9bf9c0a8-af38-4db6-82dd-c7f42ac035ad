const Re=e=>{const n=Me(e),{conflictingClassGroups:s,conflictingClassGroupModifiers:t}=e;return{getClassGroupId:i=>{const u=i.split("-");return u[0]===""&&u.length!==1&&u.shift(),be(u,n)||Se(i)},getConflictingClassGroupIds:(i,u)=>{const p=s[i]||[];return u&&t[i]?[...p,...t[i]]:p}}},be=(e,n)=>{if(e.length===0)return n.classGroupId;const s=e[0],t=n.nextPart.get(s),c=t?be(e.slice(1),t):void 0;if(c)return c;if(n.validators.length===0)return;const m=e.join("-");return n.validators.find(({validator:i})=>i(m))?.classGroupId},me=/^\[(.+)\]$/,Se=e=>{if(me.test(e)){const n=me.exec(e)[1],s=n?.substring(0,n.indexOf(":"));if(s)return"arbitrary.."+s}},Me=e=>{const{theme:n,classGroups:s}=e,t={nextPart:new Map,validators:[]};for(const c in s)re(s[c],t,c,n);return t},re=(e,n,s,t)=>{e.forEach(c=>{if(typeof c=="string"){const m=c===""?n:pe(n,c);m.classGroupId=s;return}if(typeof c=="function"){if(Ie(c)){re(c(t),n,s,t);return}n.validators.push({validator:c,classGroupId:s});return}Object.entries(c).forEach(([m,i])=>{re(i,pe(n,m),s,t)})})},pe=(e,n)=>{let s=e;return n.split("-").forEach(t=>{s.nextPart.has(t)||s.nextPart.set(t,{nextPart:new Map,validators:[]}),s=s.nextPart.get(t)}),s},Ie=e=>e.isThemeGetter,Ce=e=>{if(e<1)return{get:()=>{},set:()=>{}};let n=0,s=new Map,t=new Map;const c=(m,i)=>{s.set(m,i),n++,n>e&&(n=0,t=s,s=new Map)};return{get(m){let i=s.get(m);if(i!==void 0)return i;if((i=t.get(m))!==void 0)return c(m,i),i},set(m,i){s.has(m)?s.set(m,i):c(m,i)}}};const Pe=e=>{const{prefix:n,experimentalParseClassName:s}=e;let t=c=>{const m=[];let i=0,u=0,p=0,g;for(let k=0;k<c.length;k++){let y=c[k];if(i===0&&u===0){if(y===":"){m.push(c.slice(p,k)),p=k+1;continue}if(y==="/"){g=k;continue}}y==="["?i++:y==="]"?i--:y==="("?u++:y===")"&&u--}const h=m.length===0?c:c.substring(p),R=Te(h),F=R!==h,V=g&&g>p?g-p:void 0;return{modifiers:m,hasImportantModifier:F,baseClassName:R,maybePostfixModifierPosition:V}};if(n){const c=n+":",m=t;t=i=>i.startsWith(c)?m(i.substring(c.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(s){const c=t;t=m=>s({className:m,parseClassName:c})}return t},Te=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,Ee=e=>{const n=Object.fromEntries(e.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;const c=[];let m=[];return t.forEach(i=>{i[0]==="["||n[i]?(c.push(...m.sort(),i),m=[]):m.push(i)}),c.push(...m.sort()),c}},Ge=e=>({cache:Ce(e.cacheSize),parseClassName:Pe(e),sortModifiers:Ee(e),...Re(e)}),Oe=/\s+/,_e=(e,n)=>{const{parseClassName:s,getClassGroupId:t,getConflictingClassGroupIds:c,sortModifiers:m}=n,i=[],u=e.trim().split(Oe);let p="";for(let g=u.length-1;g>=0;g-=1){const h=u[g],{isExternal:R,modifiers:F,hasImportantModifier:V,baseClassName:k,maybePostfixModifierPosition:y}=s(h);if(R){p=h+(p.length>0?" "+p:p);continue}let T=!!y,M=t(T?k.substring(0,y):k);if(!M){if(!T){p=h+(p.length>0?" "+p:p);continue}if(M=t(k),!M){p=h+(p.length>0?" "+p:p);continue}T=!1}const $=m(F).join(":"),j=V?$+"!":$,E=j+M;if(i.includes(E))continue;i.push(E);const G=c(M,T);for(let I=0;I<G.length;++I){const B=G[I];i.push(j+B)}p=h+(p.length>0?" "+p:p)}return p};function Ne(){let e=0,n,s,t="";for(;e<arguments.length;)(n=arguments[e++])&&(s=ge(n))&&(t&&(t+=" "),t+=s);return t}const ge=e=>{if(typeof e=="string")return e;let n,s="";for(let t=0;t<e.length;t++)e[t]&&(n=ge(e[t]))&&(s&&(s+=" "),s+=n);return s};function Le(e,...n){let s,t,c,m=i;function i(p){const g=n.reduce((h,R)=>R(h),e());return s=Ge(g),t=s.cache.get,c=s.cache.set,m=u,u(p)}function u(p){const g=t(p);if(g)return g;const h=_e(p,s);return c(p,h),h}return function(){return m(Ne.apply(null,arguments))}}const f=e=>{const n=s=>s[e]||[];return n.isThemeGetter=!0,n},he=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,xe=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Fe=/^\d+\/\d+$/,Ve=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,je=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Be=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,We=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,$e=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,_=e=>Fe.test(e),d=e=>!!e&&!Number.isNaN(Number(e)),S=e=>!!e&&Number.isInteger(Number(e)),ee=e=>e.endsWith("%")&&d(e.slice(0,-1)),z=e=>Ve.test(e),De=()=>!0,Ue=e=>je.test(e)&&!Be.test(e),we=()=>!1,qe=e=>We.test(e),He=e=>$e.test(e),Je=e=>!o(e)&&!r(e),Xe=e=>N(e,ve,we),o=e=>he.test(e),P=e=>N(e,Ae,Ue),oe=e=>N(e,eo,d),ue=e=>N(e,ke,we),Ke=e=>N(e,ye,He),X=e=>N(e,ze,qe),r=e=>xe.test(e),W=e=>L(e,Ae),Qe=e=>L(e,oo),fe=e=>L(e,ke),Ye=e=>L(e,ve),Ze=e=>L(e,ye),K=e=>L(e,ze,!0),N=(e,n,s)=>{const t=he.exec(e);return t?t[1]?n(t[1]):s(t[2]):!1},L=(e,n,s=!1)=>{const t=xe.exec(e);return t?t[1]?n(t[1]):s:!1},ke=e=>e==="position"||e==="percentage",ye=e=>e==="image"||e==="url",ve=e=>e==="length"||e==="size"||e==="bg-size",Ae=e=>e==="length",eo=e=>e==="number",oo=e=>e==="family-name",ze=e=>e==="shadow",ro=()=>{const e=f("color"),n=f("font"),s=f("text"),t=f("font-weight"),c=f("tracking"),m=f("leading"),i=f("breakpoint"),u=f("container"),p=f("spacing"),g=f("radius"),h=f("shadow"),R=f("inset-shadow"),F=f("text-shadow"),V=f("drop-shadow"),k=f("blur"),y=f("perspective"),T=f("aspect"),M=f("ease"),$=f("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],E=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],G=()=>[...E(),r,o],I=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],l=()=>[r,o,p],v=()=>[_,"full","auto",...l()],te=()=>[S,"none","subgrid",r,o],se=()=>["auto",{span:["full",S,r,o]},S,r,o],D=()=>[S,"auto",r,o],ne=()=>["auto","min","max","fr",r,o],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...l()],C=()=>[_,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...l()],a=()=>[e,r,o],ae=()=>[...E(),fe,ue,{position:[r,o]}],ie=()=>["no-repeat",{repeat:["","x","y","space","round"]}],le=()=>["auto","cover","contain",Ye,Xe,{size:[r,o]}],Y=()=>[ee,W,P],x=()=>["","none","full",g,r,o],w=()=>["",d,W,P],U=()=>["solid","dashed","dotted","double"],ce=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>[d,ee,fe,ue],de=()=>["","none",k,r,o],q=()=>["none",d,r,o],H=()=>["none",d,r,o],Z=()=>[d,r,o],J=()=>[_,"full",...l()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[De],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[Je],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",d],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",_,o,r,T]}],container:["container"],columns:[{columns:[d,o,r,u]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:G()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:v()}],"inset-x":[{"inset-x":v()}],"inset-y":[{"inset-y":v()}],start:[{start:v()}],end:[{end:v()}],top:[{top:v()}],right:[{right:v()}],bottom:[{bottom:v()}],left:[{left:v()}],visibility:["visible","invisible","collapse"],z:[{z:[S,"auto",r,o]}],basis:[{basis:[_,"full","auto",u,...l()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[d,_,"auto","initial","none",o]}],grow:[{grow:["",d,r,o]}],shrink:[{shrink:["",d,r,o]}],order:[{order:[S,"first","last","none",r,o]}],"grid-cols":[{"grid-cols":te()}],"col-start-end":[{col:se()}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":te()}],"row-start-end":[{row:se()}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ne()}],"auto-rows":[{"auto-rows":ne()}],gap:[{gap:l()}],"gap-x":[{"gap-x":l()}],"gap-y":[{"gap-y":l()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:l()}],px:[{px:l()}],py:[{py:l()}],ps:[{ps:l()}],pe:[{pe:l()}],pt:[{pt:l()}],pr:[{pr:l()}],pb:[{pb:l()}],pl:[{pl:l()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":l()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":l()}],"space-y-reverse":["space-y-reverse"],size:[{size:C()}],w:[{w:[u,"screen",...C()]}],"min-w":[{"min-w":[u,"screen","none",...C()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[i]},...C()]}],h:[{h:["screen","lh",...C()]}],"min-h":[{"min-h":["screen","lh","none",...C()]}],"max-h":[{"max-h":["screen","lh",...C()]}],"font-size":[{text:["base",s,W,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,r,oe]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ee,o]}],"font-family":[{font:[Qe,o,n]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[c,r,o]}],"line-clamp":[{"line-clamp":[d,"none",r,oe]}],leading:[{leading:[m,...l()]}],"list-image":[{"list-image":["none",r,o]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",r,o]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:a()}],"text-color":[{text:a()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:[d,"from-font","auto",r,P]}],"text-decoration-color":[{decoration:a()}],"underline-offset":[{"underline-offset":[d,"auto",r,o]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:l()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",r,o]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",r,o]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ae()}],"bg-repeat":[{bg:ie()}],"bg-size":[{bg:le()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},S,r,o],radial:["",r,o],conic:[S,r,o]},Ze,Ke]}],"bg-color":[{bg:a()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:a()}],"gradient-via":[{via:a()}],"gradient-to":[{to:a()}],rounded:[{rounded:x()}],"rounded-s":[{"rounded-s":x()}],"rounded-e":[{"rounded-e":x()}],"rounded-t":[{"rounded-t":x()}],"rounded-r":[{"rounded-r":x()}],"rounded-b":[{"rounded-b":x()}],"rounded-l":[{"rounded-l":x()}],"rounded-ss":[{"rounded-ss":x()}],"rounded-se":[{"rounded-se":x()}],"rounded-ee":[{"rounded-ee":x()}],"rounded-es":[{"rounded-es":x()}],"rounded-tl":[{"rounded-tl":x()}],"rounded-tr":[{"rounded-tr":x()}],"rounded-br":[{"rounded-br":x()}],"rounded-bl":[{"rounded-bl":x()}],"border-w":[{border:w()}],"border-w-x":[{"border-x":w()}],"border-w-y":[{"border-y":w()}],"border-w-s":[{"border-s":w()}],"border-w-e":[{"border-e":w()}],"border-w-t":[{"border-t":w()}],"border-w-r":[{"border-r":w()}],"border-w-b":[{"border-b":w()}],"border-w-l":[{"border-l":w()}],"divide-x":[{"divide-x":w()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":w()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...U(),"hidden","none"]}],"divide-style":[{divide:[...U(),"hidden","none"]}],"border-color":[{border:a()}],"border-color-x":[{"border-x":a()}],"border-color-y":[{"border-y":a()}],"border-color-s":[{"border-s":a()}],"border-color-e":[{"border-e":a()}],"border-color-t":[{"border-t":a()}],"border-color-r":[{"border-r":a()}],"border-color-b":[{"border-b":a()}],"border-color-l":[{"border-l":a()}],"divide-color":[{divide:a()}],"outline-style":[{outline:[...U(),"none","hidden"]}],"outline-offset":[{"outline-offset":[d,r,o]}],"outline-w":[{outline:["",d,W,P]}],"outline-color":[{outline:a()}],shadow:[{shadow:["","none",h,K,X]}],"shadow-color":[{shadow:a()}],"inset-shadow":[{"inset-shadow":["none",R,K,X]}],"inset-shadow-color":[{"inset-shadow":a()}],"ring-w":[{ring:w()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:a()}],"ring-offset-w":[{"ring-offset":[d,P]}],"ring-offset-color":[{"ring-offset":a()}],"inset-ring-w":[{"inset-ring":w()}],"inset-ring-color":[{"inset-ring":a()}],"text-shadow":[{"text-shadow":["none",F,K,X]}],"text-shadow-color":[{"text-shadow":a()}],opacity:[{opacity:[d,r,o]}],"mix-blend":[{"mix-blend":[...ce(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ce()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[d]}],"mask-image-linear-from-pos":[{"mask-linear-from":b()}],"mask-image-linear-to-pos":[{"mask-linear-to":b()}],"mask-image-linear-from-color":[{"mask-linear-from":a()}],"mask-image-linear-to-color":[{"mask-linear-to":a()}],"mask-image-t-from-pos":[{"mask-t-from":b()}],"mask-image-t-to-pos":[{"mask-t-to":b()}],"mask-image-t-from-color":[{"mask-t-from":a()}],"mask-image-t-to-color":[{"mask-t-to":a()}],"mask-image-r-from-pos":[{"mask-r-from":b()}],"mask-image-r-to-pos":[{"mask-r-to":b()}],"mask-image-r-from-color":[{"mask-r-from":a()}],"mask-image-r-to-color":[{"mask-r-to":a()}],"mask-image-b-from-pos":[{"mask-b-from":b()}],"mask-image-b-to-pos":[{"mask-b-to":b()}],"mask-image-b-from-color":[{"mask-b-from":a()}],"mask-image-b-to-color":[{"mask-b-to":a()}],"mask-image-l-from-pos":[{"mask-l-from":b()}],"mask-image-l-to-pos":[{"mask-l-to":b()}],"mask-image-l-from-color":[{"mask-l-from":a()}],"mask-image-l-to-color":[{"mask-l-to":a()}],"mask-image-x-from-pos":[{"mask-x-from":b()}],"mask-image-x-to-pos":[{"mask-x-to":b()}],"mask-image-x-from-color":[{"mask-x-from":a()}],"mask-image-x-to-color":[{"mask-x-to":a()}],"mask-image-y-from-pos":[{"mask-y-from":b()}],"mask-image-y-to-pos":[{"mask-y-to":b()}],"mask-image-y-from-color":[{"mask-y-from":a()}],"mask-image-y-to-color":[{"mask-y-to":a()}],"mask-image-radial":[{"mask-radial":[r,o]}],"mask-image-radial-from-pos":[{"mask-radial-from":b()}],"mask-image-radial-to-pos":[{"mask-radial-to":b()}],"mask-image-radial-from-color":[{"mask-radial-from":a()}],"mask-image-radial-to-color":[{"mask-radial-to":a()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":E()}],"mask-image-conic-pos":[{"mask-conic":[d]}],"mask-image-conic-from-pos":[{"mask-conic-from":b()}],"mask-image-conic-to-pos":[{"mask-conic-to":b()}],"mask-image-conic-from-color":[{"mask-conic-from":a()}],"mask-image-conic-to-color":[{"mask-conic-to":a()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ae()}],"mask-repeat":[{mask:ie()}],"mask-size":[{mask:le()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",r,o]}],filter:[{filter:["","none",r,o]}],blur:[{blur:de()}],brightness:[{brightness:[d,r,o]}],contrast:[{contrast:[d,r,o]}],"drop-shadow":[{"drop-shadow":["","none",V,K,X]}],"drop-shadow-color":[{"drop-shadow":a()}],grayscale:[{grayscale:["",d,r,o]}],"hue-rotate":[{"hue-rotate":[d,r,o]}],invert:[{invert:["",d,r,o]}],saturate:[{saturate:[d,r,o]}],sepia:[{sepia:["",d,r,o]}],"backdrop-filter":[{"backdrop-filter":["","none",r,o]}],"backdrop-blur":[{"backdrop-blur":de()}],"backdrop-brightness":[{"backdrop-brightness":[d,r,o]}],"backdrop-contrast":[{"backdrop-contrast":[d,r,o]}],"backdrop-grayscale":[{"backdrop-grayscale":["",d,r,o]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d,r,o]}],"backdrop-invert":[{"backdrop-invert":["",d,r,o]}],"backdrop-opacity":[{"backdrop-opacity":[d,r,o]}],"backdrop-saturate":[{"backdrop-saturate":[d,r,o]}],"backdrop-sepia":[{"backdrop-sepia":["",d,r,o]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":l()}],"border-spacing-x":[{"border-spacing-x":l()}],"border-spacing-y":[{"border-spacing-y":l()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",r,o]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[d,"initial",r,o]}],ease:[{ease:["linear","initial",M,r,o]}],delay:[{delay:[d,r,o]}],animate:[{animate:["none",$,r,o]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,r,o]}],"perspective-origin":[{"perspective-origin":G()}],rotate:[{rotate:q()}],"rotate-x":[{"rotate-x":q()}],"rotate-y":[{"rotate-y":q()}],"rotate-z":[{"rotate-z":q()}],scale:[{scale:H()}],"scale-x":[{"scale-x":H()}],"scale-y":[{"scale-y":H()}],"scale-z":[{"scale-z":H()}],"scale-3d":["scale-3d"],skew:[{skew:Z()}],"skew-x":[{"skew-x":Z()}],"skew-y":[{"skew-y":Z()}],transform:[{transform:[r,o,"","none","gpu","cpu"]}],"transform-origin":[{origin:G()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:J()}],"translate-x":[{"translate-x":J()}],"translate-y":[{"translate-y":J()}],"translate-z":[{"translate-z":J()}],"translate-none":["translate-none"],accent:[{accent:a()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:a()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",r,o]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":l()}],"scroll-mx":[{"scroll-mx":l()}],"scroll-my":[{"scroll-my":l()}],"scroll-ms":[{"scroll-ms":l()}],"scroll-me":[{"scroll-me":l()}],"scroll-mt":[{"scroll-mt":l()}],"scroll-mr":[{"scroll-mr":l()}],"scroll-mb":[{"scroll-mb":l()}],"scroll-ml":[{"scroll-ml":l()}],"scroll-p":[{"scroll-p":l()}],"scroll-px":[{"scroll-px":l()}],"scroll-py":[{"scroll-py":l()}],"scroll-ps":[{"scroll-ps":l()}],"scroll-pe":[{"scroll-pe":l()}],"scroll-pt":[{"scroll-pt":l()}],"scroll-pr":[{"scroll-pr":l()}],"scroll-pb":[{"scroll-pb":l()}],"scroll-pl":[{"scroll-pl":l()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",r,o]}],fill:[{fill:["none",...a()]}],"stroke-w":[{stroke:[d,W,P,oe]}],stroke:[{stroke:["none",...a()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},to=Le(ro);export{to as t};

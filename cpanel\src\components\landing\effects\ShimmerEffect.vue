<template>
  <div class="relative overflow-hidden">
    <slot />
    <div 
      class="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/10 to-transparent"
      :style="{
        animationDuration: `${duration}s`,
        animationDelay: `${delay}s`
      }"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  duration?: number
  delay?: number
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2,
  delay: 0
})
</script>

<style scoped>
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer var(--duration, 2s) infinite;
}
</style>